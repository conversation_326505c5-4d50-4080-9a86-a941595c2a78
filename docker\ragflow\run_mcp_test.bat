@echo off
echo ========================================
echo RAGFlow MCP Server 测试脚本
echo ========================================
echo.

echo 🔍 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或不在PATH中
    echo 请安装Python 3.7+
    pause
    exit /b 1
)

echo ✅ Python环境正常

echo.
echo 📦 检查依赖包...
python -c "import requests" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  requests包未安装，正在安装...
    pip install requests
    if errorlevel 1 (
        echo ❌ 安装requests失败
        pause
        exit /b 1
    )
)

echo ✅ 依赖包检查完成

echo.
echo 🚀 开始测试RAGFlow MCP Server...
echo.

echo 选择测试模式:
echo 1. 快速测试 (推荐)
echo 2. 完整测试
echo.
set /p choice="请选择 (1 或 2): "

if "%choice%"=="1" (
    echo.
    echo 🏃 运行快速测试...
    python quick_test_mcp.py
) else if "%choice%"=="2" (
    echo.
    echo 🔬 运行完整测试...
    python test_mcp.py
) else (
    echo.
    echo 🏃 默认运行快速测试...
    python quick_test_mcp.py
)

echo.
echo ========================================
echo 测试完成！
echo ========================================
pause
