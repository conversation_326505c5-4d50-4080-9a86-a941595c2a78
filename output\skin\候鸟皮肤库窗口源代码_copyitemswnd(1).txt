﻿<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Window size="648,760" caption="0,0,0,50" roundcorner="9,9,9,9" showshadow="true" shadowsize="5" shadowposition="0,0" shadowcolor="#ff6272A4" shadowcorner="3,3,3,3" shadowdarkness="50"  fademode="true">
	<Include source="Default.xml" />
  <VerticalLayout width="648" height="750" bkcolor="#FF282A36" bordersize="1" bordercolor="#FF6272A4" borderround="5,5">





  	    <HorizontalLayout height="37">

    	 <Button name="login" bkimage="icon.png" padding="8,6,0,0" width="28" height="28"/>
      <Label name="title" padding="6,4,0,0" text="批量创建/克隆会话环境" width="260" textcolor="#FF616161" hottextcolor="#ff000000" align="left" font="8"></Label>

      <Control />
      <Button name="minbtn" width="28" height="26" tooltip="最小化" normalimage="minbtn.png" hotimage="minbtn_hover.png" pushedimage="minbtnpush.png" />
      <Button name="closebtn" width="28" height="26"  tooltip="关闭" normalimage="closebtn.png" hotimage="closebtn_hover.png" pushedimage="closebtnpush.png" />

    </HorizontalLayout>



  		<HorizontalLayout height="4">
			</HorizontalLayout>
      <HorizontalLayout height="36">
      	<VerticalLayout width="84">
          <Label name="SelectItemGroupM" width="80" texttooltip="true" endellipsis="true" textpadding="22,0,0,0" text="环境分组"  font="8" height="32" textcolor="#FF333333"/>
        </VerticalLayout>
        <VerticalLayout width="220">
          <Combo name="group" bordersize="0" padding="16,0,0,0" width="200" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" bkcolor="#ffdce1e7"
          normalimage="file='Profile\Proxy_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\proxy_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\proxy_Combox_Click.png' corner='5,5,25,10'"
          combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
          itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0" textpadding="0,0,20,0" endellipsis="true">
          </Combo>
        </VerticalLayout>


        <VerticalLayout width="88">
          <Label name="SelectItemMsg" visible="false" textpadding="26,0,0,0" text="环境模板"  font="8" height="32" width="320" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD"/>
        </VerticalLayout>
        <VerticalLayout width="220">
          <Combo name="templates" visible="false" bordersize="0" padding="21,0,0,0" textpadding="8,2,60,0" texttooltip="true"  endellipsis="true" width="200" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="8,2,0,0" bkcolor="#ffdce1e7"
                          normalimage="file='Profile\Setting_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\Setting_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\Setting_Combox_Click.png' corner='5,5,25,10'"
                          combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
                          itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0">
            <ListLabelElement height="36" pos="66,0,0,0" textpadding="10,0,0,0" text="点击并选择模板..." font="0" selected="true">
              <Label name="textLab" pos="66,0,0,0" textpadding="10,0,0,0" text="点击并选择模板..."  height="36" width="260" textcolor="#FF000000"/>
            </ListLabelElement>
          </Combo>
         </VerticalLayout>

      </HorizontalLayout>

			<HorizontalLayout height="14">
			</HorizontalLayout>

  	 <HorizontalLayout inset="20,0,26,0" height="2">
        <Control height="2" bkcolor="#ffc6c9cd"/>
      </HorizontalLayout>
  			<HorizontalLayout height="4">
			</HorizontalLayout>
			<HorizontalLayout height="36">
				<VerticalLayout width="220">
			      <Label name="lname" padding="22,6,0,0" text="会话环境名称_起始序号" texttooltip="true" endellipsis="true" maxwidth="220"   textcolor="#FF333333" hottextcolor="ffFF0000" font="8"></Label>
			  </VerticalLayout>

			   <VerticalLayout width="84" height="40">
			      <Button name="advance_option" padding="70,16,0,0" visible="false" textpadding="0,6,0,0" text=""  align="right" width="145" height="32" font="0"  textcolor="#FFcccccc" hottextcolor="#ff000000" />
			  </VerticalLayout>


			  <VerticalLayout width="98" height="40">
			      <Label name="system_pad" padding="16,7,0,0" text="系统" width="98"   textcolor="#FF333333" hottextcolor="ffFF0000" font="8"></Label>
			  </VerticalLayout>

			  <VerticalLayout width="68" height="40">
              <CheckBox name="system_pad_switch" padding="16,8,0,10" width="52" height="24"  normalimage="switch_off.png" selectedimage="switch_on.png" disabledimage="switch_off.png" />
			  </VerticalLayout>
			    <VerticalLayout width="12" height="40">
			  </VerticalLayout>
			  <VerticalLayout width="90" height="40">
			      <Label name="reso_pad" padding="10,7,10,0" text="分辨率" texttooltip="true" endellipsis="true" width="90"   textcolor="#FF333333" hottextcolor="ffFF0000" font="8"></Label>
			  </VerticalLayout>

         <VerticalLayout width="67" height="40">
           <CheckBox name="reso_pad_switch" padding="2,8,0,10" width="52" height="24"  normalimage="switch_off.png" selectedimage="switch_on.png" disabledimage="switch_off.png" />
         </VerticalLayout>
			</HorizontalLayout>

			<HorizontalLayout height="36" >
			    	 <VerticalLayout width="180">
				     		<RichEdit name="configname" wanttab="false" maxchar="18" padding="21,0,0,10" height="36" width="180" tipvaluecolor="#FF333333" borderround="7,7" bkcolor="#ffdce1e7" font="8" textpadding="10,8,20,0" tipvalue="请输入环境名称" multiline="false" textcolor="#ff333333" rich="false" transparent="false">
				      		</RichEdit>
				     </VerticalLayout>
				       <VerticalLayout width="10">
			        </VerticalLayout>
				      <VerticalLayout width="30">
				     		<RichEdit name="configname_mid" text="_" wanttab="false" padding="0,0,0,30" height="36" width="30" tipvaluecolor="#FF333333" borderround="7,7" bkcolor="#ffdce1e7" font="8" textpadding="10,8,10,0" maxchar="3" tipvalue="_" multiline="false" textcolor="#ff333333" rich="false" transparent="false">
				      		</RichEdit>
				     </VerticalLayout>
				      <VerticalLayout width="10">
			        </VerticalLayout>
				     <VerticalLayout width="50">
				     		<RichEdit name="configname_num" text="1" wanttab="false" padding="0,0,0,0" height="36" width="50" tipvaluecolor="#FF333333" borderround="7,7" bkcolor="#ffdce1e7" font="8" textpadding="10,8,10,0" maxchar="3" tipvalue="1" multiline="false" textcolor="#ff333333" rich="false" transparent="false">
				      		</RichEdit>
				     </VerticalLayout>


				     <VerticalLayout width="40">
			        </VerticalLayout>
				     <VerticalLayout width="150">
				     		<Combo name="system" bordersize="0" padding="0,0,0,10" width="150" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="0,0,0,0" bkcolor="#ffdce1e7"
										normalimage="file='Profile\Proxy_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\proxy_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\proxy_Combox_Click.png' corner='5,5,25,10'"
										combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
										itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0" >
                 </Combo>
				     </VerticalLayout>
				     <VerticalLayout width="20"/>
				     <VerticalLayout width="140">
				     				<Combo name="reso" bordersize="0" padding="0,0,0,10" width="130" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="0,0,0,0" bkcolor="#ffdce1e7"
										normalimage="file='Profile\Proxy_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\proxy_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\proxy_Combox_Click.png' corner='5,5,25,10'"
										combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
										itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0" >
										</Combo>
                   <Edit name="resotext" pos="0,0,0,10" height="36" width="98" tipvaluecolor="#FF6272A4" nativebkcolor="#FF21222C" borderround="7,7" bkcolor="#FF21222C" font="0" textpadding="10,0,0,0" maxchar="6000" multiline="false" textcolor="#FFF8F8F2" rich="false" transparent="false" float="true"/>
				     </VerticalLayout>
			</HorizontalLayout>

			<HorizontalLayout width="300" height="22">
        <Label name="lmsg" padding="21,3,0,0" visible="false" texttooltip="true" endellipsis="true" text="会话环境已存在此实例名,请更换当前环境名称." width="300" textcolor="#FFFF5555"></Label>
      </HorizontalLayout>



			<HorizontalLayout height="32">
				<VerticalLayout width="100">
			      <Label padding="22,0,0,0" text="UserAgent" width="120" textcolor="#FF333333" hottextcolor="ffFF0000" font="8"></Label>
			  </VerticalLayout>

			  <VerticalLayout width="60">
			      <Button name="random_useragent" padding="4,5,0,0" align="left" height="20" width="60" text="随机" font="5" textcolor="#FF519cff" hottextcolor="#FF005ed3" />
			  </VerticalLayout>

			    <VerticalLayout width="288">

			  </VerticalLayout>

			 <VerticalLayout width="110">

			      <Button name="UA_manager" padding="0,8,0,0" align="right" height="20" width="110" text="UA管理" font="5" textcolor="#FF519cff" hottextcolor="#FF005ed3" />

			  </VerticalLayout>
			   <VerticalLayout width="67" height="40">
           <CheckBox name="UserAgent_switch" padding="14,4,0,10" width="52" height="24"  normalimage="switch_off.png" selectedimage="switch_on.png" disabledimage="switch_off.png" />
			  </VerticalLayout>
			</HorizontalLayout>

			<HorizontalLayout height="36" >
							<VerticalLayout width="630">
											 <Combo name="agent" bordersize="0" padding="21,0,0,10" width="600" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="4,2,0,0" bkcolor="#ffdce1e7"
														normalimage="file='Profile\Setting_Combox_Normal_big_large.png' corner='5,5,25,10'" hotimage="file='Profile\Setting_Combox_Hover_large.png' corner='5,5,25,10'" pushedimage="file='Profile\Setting_Combox_Click_large.png' corner='5,5,25,10'"
												combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
												itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0">
											</Combo>
				     </VerticalLayout>
			</HorizontalLayout>


	    <HorizontalLayout height="16">
			</HorizontalLayout>


			<HorizontalLayout height="32">
				<VerticalLayout width="106">
			      <Label name="lproxy" padding="22,0,0,0" text="代理Porxy" autocalcwidth="true" width="106" textcolor="#FF333333" hottextcolor="ffFF0000" font="8"></Label>
			  </VerticalLayout>
           <VerticalLayout width="100">
            <Button name="define_proxy" padding="14,5,0,0" textpadding="0,0,10,0" align="left" height="20" width="100" text="自定义代理" texttooltip="true" endellipsis="true" font="5" textcolor="#FF519cff" hottextcolor="#FF005ed3" />
           </VerticalLayout>
        <Combo name="proxys" autocalcwidth="true" maxwidth="180" height="20" padding="0,4,0,0" dropboxsize="310,200" endellipsis="true" textpadding="10,0,20,0" itemtextpadding="10,0,0,0" itemalign="left" bkimage="configpad_list_normal.png" hotimage="configpad_list_hot.png" pushedimage="configpad_list_push.png" itemhotbkcolor="fff1f1f1" itemselectedbkcolor="ffffffff">
        </Combo>
          <VerticalLayout width="8">
			  </VerticalLayout>
                  <VerticalLayout width="67" height="40">
                     <CheckBox name="lproxy_switch" visible="false" padding="16,8,0,10" width="52" height="24"  normalimage="switch_off.png" selectedimage="switch_on.png" disabledimage="switch_off.png" />
		         </VerticalLayout>
           <Control />
          <VerticalLayout width="160" height="40">
            <Label  name="checkproxyInfo"  align="right" text="" textpadding="0,2,10,0" width="160" height="40" textcolor="#FF333333"  font="8"></Label>

          </VerticalLayout>
		      <VerticalLayout width="10" height="40"></VerticalLayout>


			</HorizontalLayout>

			<HorizontalLayout height="36" >
			    	 <VerticalLayout width="130">
							 <Combo name="proxy_type" bordersize="0" padding="21,0,0,10" width="100" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="0,0,0,0" bkcolor="#ffdce1e7"
										normalimage="file='Profile\Proxy_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\proxy_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\proxy_Combox_Click.png' corner='5,5,25,10'"
										combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
										itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0" >

										</Combo>

				     </VerticalLayout>
				     <VerticalLayout width="130">
							<Edit name="proxyipe" padding="0,0,0,10" height="36" width="126" tipvaluecolor="#FF5f5f5f" nativebkcolor="#FFDCE1E7" borderround="7,7" bkcolor="#ffdce1e7" font="8" textpadding="10,0,20,0" tipvalue="      .      .      .      " maxchar="6000" multiline="false" textcolor="#ff333333" rich="false" transparent="false" />
				     </VerticalLayout>
				     <VerticalLayout width="16">
				     	  <Label padding="4,4,0,8" text=":" width="20" textcolor="#FF000000" font="9"></Label>
				     </VerticalLayout>
				     <VerticalLayout width="70">
				     	   <RichEdit name="proxy_port" disabledtextcolor="#FF5f5f5f" wanttab="false" padding="0,0,0,10" height="36" width="64" tipvaluecolor="#FF5f5f5f" borderround="7,7" bkcolor="#ffdce1e7" font="0" textpadding="10,10,0,0" maxchar="5" tipvalue="Port" multiline="false" textcolor="#ff000000" rich="false" transparent="false"></RichEdit>
				     </VerticalLayout>
        <VerticalLayout width="96">
          <!--Edit name="proxynameinput" disabledtextcolor="#FF5f5f5f" wanttab="false" padding="0,0,0,10" height="36" width="90" tipvaluecolor="#FF5f5f5f" borderround="7,7" bkcolor="#ffdce1e7" font="0" textpadding="10,10,20,0" maxchar="120" tipvalue="Account" multiline="false" textcolor="#ff000000" rich="false" transparent="false"></Edit-->
          <Edit name="proxynameinput" padding="0,0,0,10" height="36" width="90" tipvaluecolor="#FF5f5f5f" nativebkcolor="#FFDCE1E7" borderround="7,7" bkcolor="#ffdce1e7" font="0" textpadding="10,0,20,0" tipvalue="Account" maxchar="6000" multiline="false" textcolor="#ff333333" rich="false" transparent="false" />
        </VerticalLayout>
        <VerticalLayout width="96">
          <!--Edit name="proxypassinput" disabledtextcolor="#FF5f5f5f" wanttab="false" padding="0,0,0,10" height="36" width="92" tipvaluecolor="#FF5f5f5f" borderround="7,7" bkcolor="#ffdce1e7" font="0" textpadding="10,10,20,0" maxchar="120" tipvalue="Password" multiline="false" textcolor="#ff000000" rich="false" transparent="false"></Edit-->
          <Edit name="proxypassinput" padding="0,0,0,10" height="36" width="92" tipvaluecolor="#FF5f5f5f" nativebkcolor="#FFDCE1E7" borderround="7,7" bkcolor="#ffdce1e7" font="0" textpadding="10,0,10,0" tipvalue="Password" maxchar="6000" multiline="false" textcolor="#ff333333" rich="false" transparent="false" />
        </VerticalLayout>
        <VerticalLayout width="88">
          <Button name="checkproxy"  textcolor="#FFe6ebf2" hottextcolor="#FFFFFFFF" texttooltip="true" endellipsis="true" bkcolor="#FF006fdf" padding="2,0,0,0" textpadding="5,1,5,0"  width="82" height="36" text="检查代理" borderround="7,7" hotbkcolor="#ff0055ab" />
        </VerticalLayout>

			</HorizontalLayout>

			   <HorizontalLayout height="16">
			</HorizontalLayout>

			 <HorizontalLayout inset="20,0,26,0" height="2">
        <Control height="2" bkcolor="#ffc6c9cd"/>
      </HorizontalLayout>
      <HorizontalLayout inset="20,0,26,0" height="10">
      </HorizontalLayout>

      <HorizontalLayout height="32">

      	<VerticalLayout width="40">
      	<CheckBox name="WEBRTC" width="18" height="18"  padding="22,4,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="true" />
      	</VerticalLayout>

				<VerticalLayout width="150">
			      <Label padding="8,0,0,0" name="LBrtcFinger" text="WebRTC指纹" texttooltip="true" endellipsis="true" autocalcwidth="true" textcolor="#FF333333" hottextcolor="ffFF0000" font="8"></Label>
			  </VerticalLayout>

			  <VerticalLayout width="350">
			      <Label padding="0,4,0,0" name="LBrtcFingerInfo" text="设定WebRTC指纹环境" texttooltip="true" endellipsis="true" width="350" textcolor="#FF9ea2a8" hottextcolor="ffFF0000" font="0"></Label>