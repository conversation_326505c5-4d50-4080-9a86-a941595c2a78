详细内容分析报告
URL: https://www.mbbrowser.com/api/help
分析时间: 2025-07-28 12:50:23
============================================================

发现的API功能:
------------------------------
1. 帐号登录
2. 获取成员列表
3. 环境开启/关闭
4. 环境管理
5. 分组管理
6. 脚本管理
7. 插件管理
8. 附录（国家码、时区、语言、系统和分辨率）
9. 错误码对照表
1. 获取API凭证
2. 查看API凭证
2. 获取环境ID

API相关描述:
------------------------------
1. API使用须知
简介
• 使用须知
• HTTP模式说明
• 常见问题
• API接口文档
1、帐号登录
• 2、获取成员列表
• 3、环境开启/关闭
• 4、环境管理
• 5、分组管理
• 6、脚本管理
• 7、插件管理
• 8、附录（国家码、时区、语言、系统和分辨率）
• 9、错误码对照表
• 候鸟API接口实时调试工具
POSTMAN下载及安装
• POSTMAN调试候鸟API接口
• 调试接口JSON数据官方更新、下载
• 多种语言脚本示例
• JSON在线格式化工具

2. 使用须知
====================
• 候鸟浏览器支持本地API的功能，帮助用户通过程序化的方式来启动和关闭浏览器等基础API功能，还可以配合Selenium和Puppeteer等自动化框架来实现浏览器操作的自动化。

3. 1、获取API凭证
====================
• API启动候鸟浏览器需要占用1个API凭证，即1个API凭证同一时间只能允许1台设备使用。使用前请确保账号至少有1个API凭证。

4. 2、查看API凭证
====================
• 打开候鸟控制台，点击API-查看凭证获取 APP_ID 和 APP_KEY

5. 2、获取环境ID
====================
• API通过 Session_ID（环境ID）打开环境，环境ID如图，也可以通过“获取环境列表接口”（Path：/api/v1/session/listid）获取：

发现的API模式:
------------------------------
api_endpoints: 1 个匹配
  - /api/v1/session/listid）获取：

完整页面内容:
============================================================
API使用文档-候鸟防关联浏览器•

首页

应用

价格

下载

APInew
使用教程

常见问题

佣金计划

博客中心

登录&注册
简体中文

首页

应用

价格

下载

API

使用教程

佣金计划

博客中心

登录&注册

API
====================
候鸟浏览器API使用文档

API使用须知
简介
• 使用须知
• HTTP模式说明
• 常见问题
• API接口文档
1、帐号登录
• 2、获取成员列表
• 3、环境开启/关闭
• 4、环境管理
• 5、分组管理
• 6、脚本管理
• 7、插件管理
• 8、附录（国家码、时区、语言、系统和分辨率）
• 9、错误码对照表
• 候鸟API接口实时调试工具
POSTMAN下载及安装
• POSTMAN调试候鸟API接口
• 调试接口JSON数据官方更新、下载
• 多种语言脚本示例
• JSON在线格式化工具

使用须知
====================
• 候鸟浏览器支持本地API的功能，帮助用户通过程序化的方式来启动和关闭浏览器等基础API功能，还可以配合Selenium和Puppeteer等自动化框架来实现浏览器操作的自动化。

• 后续客户端 API将采用HTTP模式连接持续更新。

使用前请根据以下指引完成操作并获取信息，然后参照【http模式说明】启动客户端并开始使用API

1、获取API凭证
====================
• API启动候鸟浏览器需要占用1个API凭证，即1个API凭证同一时间只能允许1台设备使用。使用前请确保账号至少有1个API凭证。

• 非团队版用户只能申请1个凭证，团队版仅限主账户申请，可申请的凭证数量等于团队成员总数

2、查看API凭证
====================
• 打开候鸟控制台，点击API-查看凭证获取 APP_ID 和 APP_KEY

2、获取环境ID
====================
• API通过 Session_ID（环境ID）打开环境，环境ID如图，也可以通过“获取环境列表接口”（Path：/api/v1/session/listid）获取：

进入我的控制台

支持邮箱: <EMAIL>

©MBBROWSER @2025

京ICP备 2020047947号

本系统不提供代理IP服务，禁止用户使用本系统进行任何违法犯罪活动，用户使用本系统带来的任何责任由用户自行承担。

MBbrowser.com  All Rights Reserved. 候鸟防关联浏览器对网站内容拥有最终解释权。
工作日客服(微信)
工作日09-18点

夜间/周末客服(微信)

工作日 18-24点，周末全天

商务(微信)

mbbrowser_official

全国咨询服务热线
====================

400-112-6050

在线咨询

微信咨询

电话咨询

售后咨询
