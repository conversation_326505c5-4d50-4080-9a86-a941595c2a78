环境开启关闭 - 4、环境管理
===============

API文档: 环境开启关闭 - 4、环境管理
URL: https://www.mbbrowser.com/api/browser
抓取时间: 2025-07-28 12:33:54






APInew












# API

• 使用须知
• HTTP模式说明
• 常见问题
• 2、获取成员列表
• 3、环境开启/关闭
• 4、环境管理
• 5、分组管理
• 6、脚本管理
• 7、插件管理
• 8、附录（国家码、时区、语言、系统和分辨率）
• 9、错误码对照表
• POSTMAN调试候鸟API接口
• 调试接口JSON数据官方更新、下载

## 环境开启/关闭
1、打开环境2、关闭环境3、强制终止环境• Path：/api/v1/browser/start

• Method：POST

• Content-Type：application/json

• 接口描述：用于启动指定的环境，启动成功后可以获取浏览器 debug 接口用于执行 selenium 和 puppeteer 自动化脚本。

请求参数

参数名称 类型 必传 样例串/默认值 说明 Session_ID array 是 373808cb37bd63f5f7d92415e736e85f,705cc4c139e69b729a2fd277f30e1863 环境ID isHeadless boolean 否 true true：默认浏览器器无头模式

false：浏览器有头模式

args array 否 "args": ["--disable-extensions", "--blink-settings=imagesEnabled=false"] 启动参数 单个环境请求示例

"Session_ID": [
"373808cb37bd63f5f7d92415e736e85f"
"args": [
"--disable-extensions",
"--enable-logging",
"--v=1",
"--blink-settings=imagesEnabled=false"
}` 复制 多个环境请求示例

"Session_ID": [
"373808cb37bd63f5f7d92415e736e85f",
"705cc4c139e69b729a2fd277f30e1863"
"args": [
"--disable-extensions",
"--blink-settings=imagesEnabled=false",
"--interval-seconds=3"
}` 复制 --disable-extensions 禁用插件

--blink-settings=imagesEnabled=false 禁止加载图片

--interval-seconds 启动各浏览器间隔时间(秒)

执行成功返回

"message": "Success",
"code": 0,
"data": {
"listid": [{
"Session_Name": "商用业务环境一",
"Session_ID": "373808cb37bd63f5f7d92415e736e85f",
"Group_Name": "default",
"Actived_script_id": "O73808cb37bd63f5f7d92415e736e999",
"Actiived_script_name": "这是一个脚本例子",
"Actiived_script_encode": "true",
"Weblogin_Account_Count": "4",
"Weblogin_Account_name": "<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>",
"Plugins_Count": "4",
"Plugin_Id": "jjbnhpnlakcdgfnnldamfeinfmahhdlm,jjbnhpnlakcdgfnnldamfeinfmahhdlm,jjbnhpnlakcdgfnnldamfeinfmahhdlm,jjbnhpnlakcdgfnnldamfeinfmahhdlm",
"template_id": "123456",
"template_name": "抖音国际版",
"browser_Path": "D:\\mbbrowser\\Chromium_x64\\chromium.exe",
"browser_CDP_Port": 46973,
"MBData_Path": "C:\\MBDATA\xxxxxxxxxx\xxxxxxxxxx\xxxxxxxxxxx",
"Public_ip": "************",
"Internel_ip": "**************",
"isDynamicIp": false,
"StartPage": "about:blank",
"proxyType": "socks5",
"proxy_ip": "127.0.0.1",
"proxy_port": "1080",
"isHeadless": "true",
"webdriver": "C:\\Users\\<USER>\\houniao\\Driver\\100\\chromedriver.exe", //根据当前打开环境的内核返回对应内核 webdriver 驱动路径
"status": 0
}],
"total": 1
}` 复制 使用POSTMAN调试此接口

支持邮箱: <EMAIL>
©MBBROWSER @2025

京ICP备 **********号

本系统不提供代理IP服务，禁止用户使用本系统进行任何违法犯罪活动，用户使用本系统带来的任何责任由用户自行承担。

MBbrowser.com  All Rights Reserved. 候鸟防关联浏览器对网站内容拥有最终解释权。
工作日客服(微信)
工作日09-18点

夜间/周末客服(微信)

工作日 18-24点，周末全天

商务(微信)

mbbrowser_official

###### 全国咨询服务热线

400-112-6050
在线咨询

微信咨询

电话咨询

售后咨询