5、强制终止环境

Path：/api/v1/browser/kill    
Method： POST
接口描述：关闭指定环境。

5、强制终止环境

Path：/api/v1/browser/kill    
Method： POST
接口描述：关闭指定环境。

请求参数：(用户脚本)

执行成功返回：(APISERVER CONSOLE)

注：在您使用部份浏览器插件扩展后，普通的关闭浏览器会导致浏览器并未真正退出，而是转为在后台运行，因此，您需要使用强制终止环境接口。

6、查询/获取符合条件的环境ID集合

Path：/api/v1/session/listid    
Method： POST
接口描述：查询所有符合条件的环境唯一ID，用户仅能查询自有环境及包含收到其它帐户分享过来的环境。

请求参数：(用户脚本)

执行成功返回：(APISERVER CONSOLE)

7、查询指定环境id的配置数据

Path：/api/v1/session/id_container
Method： POST
接口描述：查询并返回指定环境ID集合的环境内部信息。

请求参数：(用户脚本)

Session_container_type = 1 执行成功返回：(APISERVER CONSOLE)

Session_container_type = 2 执行成功返回：(APISERVER CONSOLE)

Session_container_type = 3 执行成功返回：(APISERVER CONSOLE)

8、创建环境

【限流：40次/分钟】
(还缺少插件，帐户，脚本的部份)
Path：/api/v1/session/create
Method： POST
接口描述：创建环境，支持配置环境的名称、备注、分组和代理信息。创建成功后返回环境ID。

请求参数：(用户脚本)

说明：
1、如Automatic_Configure的值为0，您需要设定环境高级指纹参数，详见第8项。

2、HomePage_url 参数 在 4.8.36.140 版本后有效。

执行成功返回：(APISERVER CONSOLE)

3、执行成功返回JSON中新增包含 Porxy_Check_Result在 4.9.42.146 版本后有效。

9、更新环境高级指纹参数
【限流：40次/分钟】
Path：/api/v1/session/adv_setting
Method： POST
接口描述：手工设定环境高级指纹等参数，设定成功后返回环境ID。

请求参数：(用户脚本)

执行成功返回：(APISERVER CONSOLE)

10、更新环境

【限流：40次/分钟】
(还缺少插件，帐户，脚本的部份)[注：已不缺少，2023/6月已加]

Path：/api/v1/session/update
Method： POST
接口描述：更新环境配置，更新成功后返回环境ID。

请求参数：(用户脚本)

说明：
1、如Automatic_Configure的值为0，您需要设定环境高级指纹参数，详见第8项。

2、HomePage_url 参数 在 4.8.36.140 版本后有效。

执行成功返回：(APISERVER CONSOLE)

11、更新环境代理
【限流：50次/分钟】
Path：/api/v1/session/proxy/update
Method： POST
接口描述：更新环境代理配置（将单个代理附加到指定环境集合），更新成功后返回环境ID。

请求参数：(用户脚本)

执行成功返回：(APISERVER CONSOLE)

11（2）、批量添加环境代理【20231130更新】
【限流：50次/分钟】
Path：/api/v1/session/proxy/add

Method： POST
接口描述：批量添加环境代理，添加成功后返回成功。

【原始数据：】

请求参数：(用户脚本)

执行成功返回：(APISERVER CONSOLE)

12、删除环境

Path：/api/v1/session/delete
Method： POST

Content-Type： application/json
接口描述：删除指定环境。删除成功返回code:0 ,  message: Delete Session Success。一次性支持删除环境2000个，删除的环境在本地客户端里将完全彻底删除，支持在WEB控制台的 “历史环境” 里找回已删除的环境。

请求参数：(用户脚本)

指定Session_ID 执行成功、失败返回：(APISERVER CONSOLE)

Is_Delete_All 执行成功、失败返回：(APISERVER CONSOLE)

成功：

失败：

13、导入Cookie
Path：/api/v1/session/import-cookie
Method： POST

Content-Type： application/json
接口描述：向指定环境导入cookie。导入成功返回Code:0 , Message:Import Session Success。

请求参数：(用户脚本)

执行成功返回：(APISERVER CONSOLE)

执行失败返回：(APISERVER CONSOLE)

14、导出Cookie
Path：/api/v1/session/export-cookie
Method： POST

Content-Type： application/json
接口描述：导出指定环境的cookie。导出成功返回code:0 , message: Export Session Success。

请求参数：(用户脚本)

注：如Export_Cookie_File 的参数值，例： c:\cookie.json ，则导出为json格式cookie。

执行成功返回：(APISERVER CONSOLE)

执行失败返回：(APISERVER CONSOLE)

15、获取随机UA
Path：/api/v1/session/random-user-agent
Method： POST

Content-Type： application/json
接口描述：从候鸟UA商业库和用户自定义UA库中，获取随机UA，获取成功返回UA。

请求参数：(用户脚本)

执行成功返回：(APISERVER CONSOLE)

16、清除环境本地缓存
Path：/api/v1/session/BrowserCache-clean

Method： POST

Content-Type： application/json
接口描述：清除环境本地缓存。

请求参数：(用户脚本)

执行成功返回：(APISERVER CONSOLE)

17、获取环境分组列表
Path：/api/v1/group/list

Method： POST

Content-Type： application/json
接口描述：返回当前帐户凭据中环境所有分组列表。

请求参数：(用户脚本)

注1：不提供参数，默认返回所有分组名称。

注2：通过分组名称查询分组下所有环境ID参见接口：/api/v1/session/listid

执行成功返回：(APISERVER CONSOLE)

18、新建环境分组
Path：/api/v1/group/create

Method： POST


================================================== 表格内容 ==================================================

参数名称 | 类型 | 必传 | 样例串/默认值 | 说明
Group_Name | string | 是 | 分组名称 | 新创建的分组名称

{
"message "New Group Create Success",
"code": 0,
"data": true
}