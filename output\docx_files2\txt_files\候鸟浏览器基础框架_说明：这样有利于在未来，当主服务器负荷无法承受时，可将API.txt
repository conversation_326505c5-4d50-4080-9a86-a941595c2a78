说明：这样有利于在未来，当主服务器负荷无法承受时，可将API业务剥离出来，快速无缝转移到其它高性能服务器上，同时用户端无需有任何改动。【2022.12.23新增】

说明：这样有利于在未来，当主服务器负荷无法承受时，可将API业务剥离出来，快速无缝转移到其它高性能服务器上，同时用户端无需有任何改动。【2022.12.23新增】

参数说明：hide: on / off

ON:  APISERVER.EXE 将mbbrowser.exe置为无头模式下运行（隐藏模式下运行），默认为hide:on。（当用户未填写此参数时，默认为ON）

OFF:  APISERVER.EXE 将mbbrowser.exe置为有头模式下运行（客户端处于可操作模式下运行），用户可以同时通过脚本和客户端对同一个环境进行操作。
【2023.06.19新增】

1、请求为空或非法串时，ApiServer.exe 默认返回JSON格式：

{"message":"无效的API请求, 请检查请求方式和请求路径是否正确","status":-1,"data":null}

MESSAGE:系统返回报错内容。
STATUS: 返回消息状态值
DATA: 返回数据

2、当命令行运行成功同时登录成功后，已启动的对应帐户的候鸟有头客户端自动消失，变为无头模式运行。

在CONSOLE界面中，要支持滚动条滚动数据。

为兼容后期候鸟多语言版本，CONSOLE界面中的返回，除MESSAGE节点值允许为中文，其它默认全部使用英文显示。

5、   所有的返回信息串前必须带有日期时间。

6、客户端以无头模式运行时，需将主ICON显示在TRAY区，此时，ICON右键菜单变更为：退出。【光标移在ICON上原显示内容不变】

【此项变更为：hide=on默认模式下有效。当hide=off时，无效】
【2023-06-19新增】

7、当用户在无头模式已运行状态下，此时用户双击运行另一个候鸟客户端实例并以相同的用户名密码登录时，需弹出上述窗口。【使用服务器返回作判断依据】此时服务器返回必须在原返回基础上增加API标志位。

【此项变更为：hide=on默认模式下有效。当hide=off时，无效】
【2023-06-19新增】

8、有头先登录时,无头登录会将有头自动转换为无头。【只有是 同一台机器里，同一个帐户，才可以无条件将有头关闭，退出登录 和 无头开启重新登录服务器流程】

【此项变更为：hide=on默认模式下有效。当hide=off时，无效】
【2023-06-19新增】

9、APISERVER仅与本地mbbrowse通讯不与任何服务器通讯。

10、如果用户当前机器里跑了多个有头客户端，apiserver需要知道是哪个客户端的帐户与无头是一致。

11、无头模式运行周期里，服务器端需要将APP_ID与这个用户的UID绑定在一起。（业务策略约定了当前周期内，一个UID只能使用一个APP_ID，此APP_ID被占用后，其它UID无法使用此凭据）

12、在无头模式下，心跳增加appid和appkey是必须的，客户端无论是有头还是无头运行状态下，都默认支持与APISERVER.EXE进行通讯的职能。 APISERVER启动的登录串提交给服务器相当于进行了一次二次校验身份合法性，同时在二次身份校验通过后，成功变更了服务器端记录的客户端运行状态（有头、无头）。

9、 APP_KEY的值只有客户端发往服务器端的逻辑，没有服务器端将APP_KEY发往客户端的逻辑。

10、服务器端通过心跳通讯和登录接口能全时段完全知晓用户当前是以什么模式（有头、无头）在运行。

ApiServer.exe基于HTTPD源码工程开发：

https://blog.csdn.net/GG_SiMiDa/article/details/104055509

四、服务器端与客户端验证

1、候鸟客户端在有TOKEN的情况下，与服务器通讯带客户端在无头模式下运行时，如断线后，重连上来增加提交aes(token+app_id+app_key)，如客户端重连上来后，没有app_id + app_key给到服务器时，则默认为用户将客户端在有头模式下运行。

2、控制台强制关闭客户端按钮，要求 对无头模式下的运行 同样产生效力。

3、有头模式下登录窗口显示的信息（如FULL包同步的信息），在无头API登录模式中，要显示在APPSERVER的CONSOLE窗口中，并不受RETRUN: on / off 开关限制。【2022-12-16号新增】

4、有头模式下的环境运行显示的信息（如插件下载等信息），在无头API登录模式中，要显示在APPSERVER的CONSOLE窗口中，受RETRUN: on / off 开关限制。【2022-12-16号新增】

5、凡是有需要直接将服务器json返回信息显示到apiserver console里的指令集，服务器端应按文档的json结构进行返回，这样客户端的工作无须再次解析服务器端json,也无需再次重新拼接组合json格式进行返回【2023-01-28号新增】

五、服务器端API无头模式下，强制下发客户端CONSOLE端 流程说明

心跳指令下发增加强制显示指令前缀：force_msg
    1、对服务器端无要求，仅在需要强制显示时增加此前缀指令下发。

2、对客户端要求首个API集成版本必须支持此指令前缀。

例：各种套餐到期与即将到期，超出使用限制范围等

在无头模式下，服务器端通过心跳下发的涉及到续费和套餐信息，不受RETRUN/OFF限制，客户端需无条件显示到CONSOLE中。

此类信息前缀: force_msg

客户端收到的任意指令如带有此前缀的，一律强制显示在CONSOLE中。

APISERVER与MBBROWSER接口流程说明：

1、下载postman桌面版，https://www.postman.com/downloads/

2、启动并登录postman

3、进入命令行界面，进入apiserver.exe目录，执行

apiserver  --port=8186 --app_id=202211291047058133354147840 --app_key=UHt3cCgYEAyF6r5F+exuzbOp1kcfaB8d4MVkEXM --return=on --logs=on --hide=off  [2023-06-19新增，支持客户端有头运行]

注：默认--hide=on，只有用户指定--hide=off时，APISERVER与客户端在有头模式下运行。

4、在postman界面下执行post命令

http://127.0.0.1:8186/login?Account=<EMAIL>&password=123456&APP_ID=202211291047058133354147840&APP_KEY=UHt3cCgYEAyF6r5F+exuzbOp1kcfaB8d4MVkEXM&return=on&logs=on

（注意：启动apiserver时的appid必须和post中一致，启动时可以不用带appid等参数，每个apiserver实例启动后只能绑定同一个appid不能更换，要更换appid须重启和新启动apiserver）

【上述注意补充说明：如果用户仅仅是双击执行了apiserver.exe，则你仍旧要跑起来apiserver, 此时仅运行apiserver即可。此时不去启动mbbrowser.exe，待用户post了合法的数据login后，再启动mbbrowser.exe】

六、HTTP REST API 指令集

1、帐号登录 / 帐号切换
【限流：10次/分钟】
http模式需配合使用CLI命令行启动客户端

重新登录会重启客户端

Path：/login    
Method： POST

请求参数：(用户脚本)

返回：(APISERVER CONSOLE)

2、获取成员列表

获取所有和此帐户相关联的主帐号与子帐号数据。

【限流：10次/分钟】
Path：/members    
Method： POST

请求参数：(用户脚本)

不填入account返回此帐户下所有关联帐户。

返回：(APISERVER CONSOLE)

0表示主帐户  1表示子帐户。

如填入 Account 则返回此account注册相关信息：

返回：(APISERVER CONSOLE)

3、打开环境

http模式需配合使用CLI命令行启动客户端

重新登录会重启客户端

Path：/api/v1/browser/start    
Method： POST
接口描述：用于启动指定的环境，启动成功后可以获取浏览器debug接口用于执行selenium和puppeteer自动化脚本。

请求参数：(用户脚本)

单个环境请求示例：

多个环境请求示例：

--disable-extensions 禁用插件

--blink-settings=imagesEnabled=false 禁止加载图片

--interval-seconds 启动各浏览器间隔时间(秒)

执行成功返回：(APISERVER CONSOLE)

CODE 错误码表：

Status 错误码表：

4、关闭环境

Path：/api/v1/browser/stop    
Method： POST
接口描述：关闭指定环境。

请求参数：(用户脚本)

执行成功返回：(APISERVER CONSOLE)


================================================== 表格内容 ==================================================

参数名称 | 类型 | 必传 | 样例串/默认值 | 说明
Session_ID | string | 否 | 373808cb37bd63f5f7d92415e736e85f | 返回指定环境ID所属分组
Session_Name | string | 否 | 环境名称一 | 返回指定环境名称所属分组

{
"message "Group List Return Success",
"code": 0,
"data": {
            "Group_Name_List" : "我的分组一, 我的分组二, 我的分组三" 	//分组名称
       }
}