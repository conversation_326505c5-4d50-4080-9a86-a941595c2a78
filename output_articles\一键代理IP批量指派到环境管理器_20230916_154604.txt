标题: 一键代理IP批量指派到环境管理器
英文标题: One-click proxy IP batch assignment to Environment Manager
ID: 118
分类ID: 7
添加时间: 1694850364
更新时间: 1702021181
访问次数: 0
SEO标题: 候鸟浏览器一键代理IP批量指派到环境管理器
SEO关键词: 候鸟浏览器一键代理IP批量指派到环境管理器
SEO描述: 候鸟浏览器一键代理IP批量指派到环境管理器

================================================== 内容 ==================================================
### 候鸟 一键代理IP批量指派到环境管理器 使用前述
在批量创建环境的场景中，当前候鸟支持您通过EXCEL批量导入自有环境和一键批量创建新环境两大场景。实际在商业运营中，您如果没有自行创建EXCEL，而是通过候鸟的一键批量创建新环境来进行首批的环境创建。在此过程中，如未在EXCEL中将代理IP填入，您将有可能希望将自已的多个代理服务器IP灵活的匹配到已经新创建的环境中，一键代理IP批量指派到环境管理器 提供这块支持，此管理器可以避免您在早期版本中【********** 之前版本】不得不在批量创建环境后，将自已的代理IP逐个添加到环境里。

### 视频教程
|  一键代理IP批量指派到环境管理器 |
| ------------ |
| <video controls="" preload style="width:100%"><source src="https://help.mbbrowser.com/video/agent_allocation.mp4" type="video/mp4"></video> |

### 一、进入 一键代理 IP 批量指派到环境管理器

<p><img src="6d7b2882624511f09a0d0242ac130006/images/image_33966dd72525.png" width="420" /></p>

方式一：在候鸟主面板上点击 设置 -> 网络代理管理 -> 批量指定代理 IP 到环境。

<p><img src="6d7b2882624511f09a0d0242ac130006/images/image_e1e673748175.png" width="360" /></p>

方式一：在候鸟主面板的环境上为右键，选择 更换环境代理 IP

方式二：在候鸟主面板的环境上按住 CTRL 键，鼠标左键逐个点击环境，以多选方式选中需要更换 IP 的环境，再点击更换环境代理 IP

### 二、一键代理 IP 批量指派到环境管理器 使用方法 和注意事项。

![](6d7b2882624511f09a0d0242ac130006/images/image_908b6108e00a.png)

![](6d7b2882624511f09a0d0242ac130006/images/image_24d262cdb49a.png)

约定惯例：从左到右，从上到下，列表区域分为 A,B,C 大区域。

**箭头 1**：允许您通过搜索代理关键字进行搜索。

**箭头 2**：点刷新按钮，重新加载 PROXY 代理列表 A 区数据，实时更新列表。

**箭头 3**：当您直接进入到此界面时，默认显示 已有的代理 下拉项。

**箭头 4**：此列显示代理类别，如果此环境不存在代理，则显示 N/A。

**箭头 5**：C 区列出已勾选的环境中的代理，删除代理功能将已勾选环境中的已勾选代理删除。删除代理按钮在您勾选要删除的代理时自动亮起。

**箭头 7**：随机指派模式 为默认模式：当用户从 A 区指派到 B 区时，将所有代理 IP 随机分配到各个环境中，**如果 A 区数量小于 B 区，则 A 区的代理 IP 一定会全部指派到 B 区的环境中。**

**箭头 8**：顺序指派模式，当用户从 A 区指派到 B 区时，将所有代理 IP，顺序分配到各个环境中，特点为：
**如果 A 区数量小于 B 区，则 A 区分配到 B 区后，B 区还会有部份未被分配，此时您需要拿 A 区的代理 IP 继续分配 B 区剩余的部份。**

**箭头 6**：执行上述 从 A 区-> B 区->C 区 ，箭头 1-8 的设定过程。
<p></p>
<p></p>
注：

1、如您的环境中已存有代理 IP，经过上述操作后，原存有的代理 IP 将会被替换。

2、您通过此管理器批量指派您的有效代理 IP 到环境后，建议在每个环境使用前查看环境配置，请确保您指派的代理 IP 与您的环境正确匹配。

================================================== 英文内容 ==================================================
### Mbbrowser One-click Proxy IP Batch Assigned Yo The Environment Manager Use The Foregoing
In the scenario of creating environments in batches, the current mbbrowser supports two scenarios: importing your own environments in batches through EXCEL and creating new environments in batches with one click. In actual business operations, if you do not create EXCEL yourself, you do the first environment creation through the one-click batch creation of new environments by migratory birds. During this process, if you do not enter the proxy IP address in EXCEL, you may want to flexibly match your multiple proxy server IP addresses to the newly created environment. The one-click proxy IP address is assigned to the environment manager to provide this support. This manager prevents you from having to add your own proxy IP to the environment one by one after creating the environment in bulk in earlier releases [before **********].

###  Video Tutorial
|  One-click proxy IP batch assignment to Environment Manager |
| ------------ |
| <video controls="" preload style="width:100%"><source src="https://help.mbbrowser.com/video/agent_allocation.mp4" type="video/mp4"></video> |

###  I、Enter One-click Proxy IP Batch Assigned Yo The Environment Manager

<p><img src="6d7b2882624511f09a0d0242ac130006/images/image_4cca9344b3b8.png" width="510" /></p>

Method 1: On the main panel of Mbbrowser, click List -> Proxy Server Manager -> Batch Specify Proxy to Session

<p><img src="6d7b2882624511f09a0d0242ac130006/images/image_836949d3e740.png" width="360" /></p>

Method 1: Right-click the environment on the Mbbrowser main panel and choose Change the environment agent IP address

Method 2: Hold down the CTRL key on the environment of the main panel of the Mbbrowser, click the environment one by one, select the environment to be changed in multiple selection mode, and click Change the environment agent IP address

### II、One-click Proxy IP Batch Assigned Yo The Environment Manager Use Methods And Precautions.

![](6d7b2882624511f09a0d0242ac130006/images/image_7792b58a72c5.png)

![](6d7b2882624511f09a0d0242ac130006/images/image_12222990a585.png)

Convention: From left to right, from top to bottom, the list area is divided into large areas A,B, and C.

**Arrow 1**：Allows you to search by searching proxy keywords.

**Arrow 2**：Click the refresh button to reload the data in area A of the PROXY list and update the list in real time.

**Arrow 3**：When you go directly to this screen, the existing proxy drop-down list is displayed by default.

**Arrow 4**：This column shows the agent category, or N/A if no agent exists in this environment.

**Arrow 5**：The agents in the selected environment are listed in area C. The agent deletion function deletes the agents in the selected environment. The Delete Agent button lights up automatically when you select the agent you want to delete.

**Arrow 7**：The random assignment mode is the default mode: When A user is assigned from zone A to Zone B, all proxy IP addresses are randomly assigned to each environment.**If the number of zone A is smaller than that of zone B, all proxy IP addresses of zone A must be assigned to the environment of zone B.**

**Arrow 8**：In the sequential assignment mode, when A user is assigned from zone A to zone B, all proxy IP addresses are sequentially assigned to each environment. The characteristics are as follows:
**If the number of area A is less than that of area B, after area A is allocated to Area B, there will still be some unallocated parts in area B. At this time, you need to take the proxy IP address of Area A to continue to allocate the remaining parts in area B.**

**Arrow  6**：Perform the above setup procedure from Zone A -> Zone B -> Zone C, arrows 1-8.
<p></p>
<p></p>
Note:

1. If the proxy IP address already exists in your environment, the existing proxy IP address will be replaced after the preceding operations.

2. After you assign your valid proxy IP to the environment in batches through this manager, it is recommended to check the environment configuration before using each environment, and make sure that the proxy IP you assign matches your environment correctly.