默认在服务器端strlock字段中此串值为：“0|0|0”

默认在服务器端strlock字段中此串值为：“0|0|0”

为兼容旧版本，无此参数的XML需加上此参数，默认值为 “0|0|0”

为减少服务器字段和保证环境锁后续需求可扩展性，使用“|”分隔符进行分隔。

3、界面判定与显示：

PC端判断configdata.xml中 str_lock是否不存在（或为空），或值为 “0|0|0”时，界面运行按钮正常显示。

如str_lock值存在，且不为0|0|0时,运行按钮应显示为：

即显示带锁的按钮。

4、PC端、服务器端交互接口详述：

结构约定：

一、表结构约定：（图四）

此表：

增加二个字段

字段：strlock，类型：char

strlock默认初始值：0|0|0 用来进行strlock规则(规则详见第四节第二条)

字段：runstatus 类型：bit 值： 1 or 0，默认为0

Runstatus 为当前此环境是否已处于使用打开状态。

1 表示已打开。 0表示未打开。

二、原历史接口约定（图五）

场景1：用户运行已接受到分享的环境。（重点）

1、客户端在用户打开环境时，遵循原逻辑，仍请求版本号，如果fromuid大于0，则要增加二个参数请求值：open=1&fromuid=xxxxx给上述接口, 之后根据版本号返回的strlock值和runstatus(deny/allow)值判定是否允许修改环境和运行环境。（strlock规则详见第四节第二条），如果非”0|0|0”说明已上锁，此时需更新xml,并将用户此环境的运行按钮置为如图：

如果客户端收到的strlock值中trim之后，首个数字为1。 则必须判断json返回中的deny/allow，来判定是否运行此环境。

2、服务器端版本号接口收到客户端请求，在json返回之前，判断fromuid是否有值，同时open=1的，如果fromuid有值且open=1表示此请求为分享的ITEM并请求打开浏览器，此时服务器端根据session_id 查询是否存在runstatus=1的， 同时根据fromuid将主帐户的strlock附加在原json串后返回，如存在runstatus=1的, json附加返回deny，其它不变。【客户端收到deny则弹出窗口提示此环境已经被使用中】。如果服务器端判定有sessiond_id集合中均没有runstatus=1的，表示此sessionid环境未被创建帐户和被分享的帐户使用，此时服务器端将此token对应的sessiond中的runstatus置1【更新为1表示此用户已经开启环境】，在json里附加返回allow 给客户端。客户端此时可以正常开启此环境。【注意：此流程均应在瞬间完成，如果卡顿则需要考虑采用redis】

3、客户端关闭环境

此项讨论

4、客户端由在线变为离线：

服务器端判定客户端已离线，将此帐户对应表中runstatus为1的置0。

附加说明：客户端在用户打开环境时，需要支持用户的批量打开（在会话环境管理器里批量打开）。（strlock规则详见第四节第二条）

新增接口一【环境锁设定接口】：

Set_itemlock请求串：

注：此接口需支持多个批量请求。

接口一流程详述：

场景2：用户在PC端设置指定环境为上锁环境。

1、用户（图二、图三）在客户端对单个或多个环境上锁（注意：仅用户自创建的环境，上锁项为可用状态，用户接收到的分享环境，上锁按钮应置灰），客户端更新完本地xml（full和item xml均要更新），之后触发同步逻辑，同时线程方式将更新成功的环境请求此接口,将strlock值交给服务器。

2、服务器端接收到客户端的单个/批量请求后，单个/批量更新s_session_data表中对应的记录，将strlock字段更新。并返回成功状态报告给客户端。

3、客户端接收到来自服务器的成功状态后，将对应的环境集合的运行按钮置为：

场景3： 用户在控制台的分享列表界面，设置指定环境为上锁环境。(图一)

流程：

服务器将环境sessionid对应的strlock值在s_session_data表中进行更新。

同时将环境sessionid对应的strlock值在s_session_share表中进行更新。

3、   分享列表界面更新，按图一的例图进行显示。

场景4：用户修改已接受到分享的环境。

客户端在用户修改环境时，原即定逻辑请求版本号。

客户端版本请求流程与处理流程与场景1一致。

同时段仅允许单个帐户运行：默认值 0

禁止其它帐户修改此环境：默认值 0

实时更新子帐户当前环境：默认值 0

修改分享的环境：

客户端判定如果strlock值中，禁止其它帐户修改此环境 =1，则弹出提示窗口：此环境被设定为禁止修改。

场景5：用户设定环境为 实时更新子帐户当前环境 =1。

用户设定环节中：

客户端根据 新增接口一，进行处理。

服务器端根据 新增接口一，进行处理，其它不变。

用户使用环节中：

客户端根据客户端版本请求流程与处理流程与场景1一致。

针对分享的环境：

客户端判定如果strlock值中， 实时更新子帐户当前环境=1，则无条件请求服务器下载主帐户（环境创建者）ITEM包，并更新到本地。【注意：此时版本号对比是环境接受者与主帐户的此ITEM包的版本号之间进行对比】。

问题一：是否存在如网络原因传参失败，则无法二次运行的问题。

回答：此情况不存在，如果版本校验失败，仍旧走原校验失败的流程（即集体讨论通过的 -> 允许用户使用环境）。

问题二：fromuid是否是环境创建者的唯一ID，还是记录的是上个拥有者的ID。

需an来回答。

[2021-12-01 新增]

第二十三章 候鸟浏览器

客户端/服务器端/新版内核

商业会话模板 - 客户增值服务
数据接口与详细图文逻辑流程及约定。

十三章 候鸟浏览器 客户端/服务器端/新版内核 商业会话模板 - 客户增值服务数据接口与详细图文逻辑流程及约定。[2021-12-01 新增]

第一节：

前述：

基于新版与旧版的chromium浏览器内核（87版）模式下的客户群体：

在候鸟产品推出以来，浏览器针对一些用户自访问的部份网站出现各种异常。如图：

针对各种网站无法登录，无法注册，无法支付，页面显示不全、无法点赞，无法评论等各种付费客户反馈或投诉过来时、其态度是较强硬和激烈的。（已有多个历史客户投诉截图）

2020年中旬至目前，中高阶客户（即大客户）较多以指责的方式认定候鸟产品的浏览器内核在这一块功能的不完善给其带来了业务运营隐患，即未来可能发生的其自身客户业务风险。

2021年这类情况导致了大量已付费用户的流失。

产品各方面功能的不断完善和不断推进过程中的历史因素：

浏览器内核的源码购买（彻底解决浏览器内核旧版中在2020年自有代码通过绕过的方式临时解决问题，改用采用原生的代码【扫地僧编写】彻底保证内核的完善度），之后，基于购买的完整chromium指纹源码上，进行二次迭代开发来支持旧版内核用户数据的前题下，方可进行 商业模板 之功能的历史缘由。

至此，目前实施商业模板对外公开服务的条件，已基本具备。

综述：

针对客户自有业务的开展，对于候鸟内核针对部份网站出现的异常现像，采用针对性的模板进行对应的服务提供，减少用户的操作繁琐度，大幅降低客户的学习成本。

候鸟官方客户服务过程中，可及时提供解决方案，并生成模板，供客户直接使用，迅速帮客户解决实际业务问题。

解决候鸟内核与产品发布出去后，无法进行产品回收和产品交付到客户手中，产品已固化后，无法变更内核的局面。通过商业模板，来解决全球各类网站出现的异常，并提供有效的解决方案。

通过服务器端统一管理，加强内核的可扩展性，大幅提高内核针对商业网站的兼容性。

通过商业模板，可扩展对客户的管理手段，通过有效的增加模板方案供用户选择，形成历史积累，并与竞争对手的产品产生较强的差异化，通过海量的业务模板与商业竞争对手拉开距离。

第二节：

全局架构简述：

1、在控制台里，将各个域名和域名对应的模板配置文件做成一个列表供用户来选择是否启用。（好处是只有购买过的用户才有权利来使用，外界不知道）

2、用户勾选和启用这些模板后，在客户端的配置面板里自动显示出一个下拉列表，供用户在各个环境中来加载这个用户已启用的各个模板配置。

3、环境通过加载用户指定的配置数据来彻底解决各种业务下网站不同的情况。

第三节：

界面说明及功能纲要、流程详述：

候鸟控制台：

控制台模板界面说明： 用户在控制台中进入业务模板，可以看到官方提供的所有对外开放的业务模板列表和每个模板的详细介绍和模板提供时间，用户针对自有业务，在业务出现疑难问题时，可针对性选择不同的模板进行启用或停止。

管理员控制台：

提供一整套模板增删改人机界面，新增一个栏目叫：模板管理。 模板管理放到内核更新栏目后面。

模板管理由二部份组成：新增模板页面、已添加的模板列表页面。

显示模板列表，供客服可以增删改。

提供新增模板，支持客服增加新的模板。

模板主表结构：

ID， 模板ID （唯一不重复ID，此ID作为模板唯一值进行约定）

Template_Name,  模板名称

Template_LOGO,  模板对应的LOGO图示

Template_DEC,    模板文字描述(告诉用户这套模板是干嘛的)

Template_Data,   模板具体数据(小宁给的数据)

Template_domain, 模板对应的域名(此字段加上，先不用上)

is_valid, 模板是否有效

update_time, 模板最近修改时间

create_time, 模板创建时间

模板与主用户表关联说明：

用户主表或用户子表增加一个字段user_template：存储用户已开启/已使用的模板ID集合，用逗号分隔：例1,2,3,4 ，在用户进入控制台里后，可以看到其已启用的模板。

补充流程：


================================================== 表格内容 ==================================================

参数名称 | 类型 | 必传 | 样例串/默认值 | 说明
Session_Name | string | 是 | 商业环境业务一 | 环境名称，最大长度60字
Session_Desc | string | 否 | 环境描述 | 最大长度150字
Session_Group | string | 否 | 环境所属分组 | 最大长度30字，如未填写值则为默认分组
Session_System | string | 否 | Windows | 设定环境的操作系统
Session_Resolution | string | 否 | 1024x768 | 设定环境的分辨率
Session_User_Agent | string | 否 | Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 | 设定环境指定UA值
[2023/07/03新增]
Proxy_Type | string | 否 | HTTP,HTTPS,SSH,SOCKS4,SOCKS4A,SOCKS5,
Oxylabsauto,Lumauto,Luminati_HTTP,
Luminati_HTTPS,smartproxy,noproxy | 指定代理类型
[无代理:noproxy]
Proxy_Ip | string | 否 | ************* | 指定代理IP
Proxy_Port | string | 否 | 8080 | 指定代理端口
Proxy_Username | string | 否 | admin | 指定代理帐户
Proxy_Password | string | 否 | Password | 指定代理密码
TimeZone | string | 否 | US/Alaska -09:000 | 时区
CountryCode | string | 否 | US | 国家CODE
CityCode | string | 否 | 城市CODE
RegionCode | string | 否 | 州CODE
LanguageCode | string | 否 | En-US;en;q=0.9 | 环境默认语言
Cookie | string | 否 | Cookie内容 | Json格式cookie文本
[此项与Cookie_File只能选其一]
Cookie_File | String | 否 | C:\cookie.txt | 填入cookie文件路径
支持text,json格式
[此项与Cookie只能选其一]
Automatic_Configure | bit | 是 | 1 or 0 | 设置自动配置环境高级指纹参数
1:依据候鸟商用库自动通过给定的代理匹配所有其它配置信息。
0:手工设定环境配置信息。【当此值为0时，本列表下面参数要人工给值】
HomePage_url | string | 否 | https://www.baidu.com | 设置环境开启起始页，未设置时默认值:https://www.yalala.com/?wd=mb
Disable_img | bit | 否 | 0 | 图片限流 0关闭 1开启 默认关闭
Disable_img | bit | 否 | 0 | 图片限流 0关闭 1开启 默认关闭

{
"requestId": "8b558e5c5d1c437183c34aa03a09a368",
"message": "Added Success",
"code": 0,
"data": {
            "Session_Id" : 373808cb37bd63f5f7d92415e736e85f, 	//环境ID
            "Porxy_Check_Result" : "success" 	//代理检测报告 success/failed

       }
}