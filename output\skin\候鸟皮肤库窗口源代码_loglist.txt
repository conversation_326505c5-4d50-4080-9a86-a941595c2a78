﻿<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Window size="960,590" sizebox="4,4,4,4" caption="0,0,0,50" mininfo="1100,590" roundcorner="5,5,5,5" showshadow="true" shadowsize="3" shadowposition="0,0" shadowcolor="#FF44475A" fademode="true">
  <Include source="Default.xml" />

  <VerticalLayout width="953" height="590" bkcolor="#FF282A36">
    <HorizontalLayout height="37">

    	 <Button name="login" bkimage="icon.png" padding="8,6,0,0" width="28" height="28"/>
      <Label name="session_list_title" padding="6,4,0,0" text="日志管理器" width="260" textcolor="#FFF8F8F2" hottextcolor="#FFBD93F9" align="left" font="8"></Label>

      <Control />
      <Button name="minbtn" width="28" height="26" tooltip="最小化" normalimage="minbtn.png" hotimage="minbtn_hover.png" pushedimage="minbtnpush.png" />
      <Button name="maxbtn" width="28" height="26" tooltip="最大化" normalimage="maxbtn.png" hotimage="maxbtn_hover.png" pushedimage="maxbtnpush.png" />
      <Button name="restorebtn" visible="false" width="28" height="26" tooltip="还原" normalimage="restorebtn.png" hotimage="restorebtn_hover.png" pushedimage="restorebtnpush.png" />
      <Button name="closebtn" width="28" height="26"  tooltip="关闭" normalimage="closebtn.png" hotimage="closebtn_hover.png" pushedimage="closebtnpush.png" />

    </HorizontalLayout>
<!--<HorizontalLayout name="bkground">-->


 <HorizontalLayout name="loading_data" bkcolor="#FF21222C" visible="false">

	    <VerticalLayout >

					     <HorizontalLayout name="loading_data" height="240">
					    	 <Control />
					    		<GifAnim name="data_loading" bkimage="dataloading.gif" height="200" width="200" padding="0,40,0,0" auto="true"/>
                 <Control name="success" visible="false" padding="0,100,0,0"  bkimage="success.png" width="120" height="120" align="center" />
					    	 <Control />
					     </HorizontalLayout>


					     <HorizontalLayout height="30" >
					    	 <Control />
					    		  <Label name="data_percent" text="55%" width="300" textcolor="#FFF8F8F2" hottextcolor="#FFBD93F9" align="center" font="10"></Label>
					    	 <Control />
					     </HorizontalLayout>

					     <HorizontalLayout height="60" >
					    	 <Control />
					    		  <Label name="process_description" text="会话环境正在版本验证中，请稍侯.. " width="953" textcolor="#FFF8F8F2" hottextcolor="#FFBD93F9" align="center" font="8"></Label>
					    	 <Control />
					     </HorizontalLayout>

              <HorizontalLayout height="40" >
              </HorizontalLayout>

              <HorizontalLayout name="backarea" height="60" visible="false">
                <Control />
                <Button text="返回" name="back" width="120" height="30" textcolor="#FFF8F8F2" disabledtextcolor="#FF6272A4" align="center" font="14" bkcolor="#FF44475A" hotbkcolor="#FF6272A4" pushedbkcolor="#FF6272A4" borderround="5,5" />
                <Control width="100" />
                <Button text="退出" name="closewnd1" width="120" height="30" textcolor="#FFF8F8F2" disabledtextcolor="#FF6272A4" align="center" font="14" bkcolor="#FF44475A" hotbkcolor="#FF6272A4" pushedbkcolor="#FF6272A4" borderround="5,5" />
                <Control />
              </HorizontalLayout>
      </VerticalLayout>

 </HorizontalLayout>




		<VerticalLayout name="data" visible="true">


			<HorizontalLayout height="56" >
			    	 <VerticalLayout width="440">
               <Combo name="searchlist" reselect="true" dropboxsize="0,450" bordersize="0" padding="21,10,0,10" width="420" height="36" borderround="5,5" textcolor="#FFF8F8F2" disabledtextcolor="#FF6272A4" itemalign="left" itemfont="0" textpadding="4,2,0,0" bkcolor="#FF21222C"
														normalimage="file='Profile\Setting_Combox_Normal_big_large.png' corner='5,5,30,5'" hotimage="file='Profile\Setting_Combox_Hover_large.png' corner='5,5,30,5'" pushedimage="file='Profile\Setting_Combox_Click_large.png' corner='5,5,25,10'"
												combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,30,5'"
												itemhotbkcolor="#FF44475A" itemselectedbkcolor="#FF6272A4" itemtextpadding="10,0,0,0">
               </Combo>
				         <RichEdit name="session_search" pos="20,10,0,10" height="36" width="378" tipvaluecolor="#FF6272A4" borderround="5,5" bkcolor="#FF21222C" font="8" textpadding="10,8,20,0" maxchar="300" tipvalue="请输入关键字查找会话.." multiline="false" textcolor="#FFF8F8F2" rich="false" transparent="false" float="true">
				      </RichEdit>
				     </VerticalLayout>
				     <VerticalLayout width="30">
				        <CheckBox name="opt_searchl" selected="false"  visible="true" padding="10,26,0,0" height="18" width="16" normalimage="file='list_check_normal.png'" hotimage="list_check_hover.png" selectedimage="file='list_checked_actvice_normal.png'" ></CheckBox>
				     </VerticalLayout>
				     <VerticalLayout width="180">
				        <Label name="searchl" padding="4,16,0,0" text="搜索包含在会话中的关键词" width="180" textcolor="#FFF8F8F2" hottextcolor="#FFBD93F9" align="left" font="16"></Label>
				     </VerticalLayout>
        <Control />
         <VerticalLayout width="180">
         	<Control />
        <Button text="点击选择日期" name="Btndaterange" tooltip="点击选择日期" padding="0,6,0,0" width="180" height="36" textcolor="#FFF8F8F2" disabledtextcolor="#FF6272A4" align="center" font="14" bkcolor="#FF21222C" bordersize="1" bordercolor="#FF44475A" borderround="5,5" hotbkcolor="#FF44475A" />
        	<DateRangeUI name="daterange" padding="0,0,0,0" width="180" height="1" bkcolor="#FF282A36" bordercolor="#FF44475A" textcolor="#FFF8F8F2" visible="true" autosize="true"  align="center"/>


          <Control />
         </VerticalLayout>
        <Control width="20"/>
        <VerticalLayout width="180">
          <Combo name="type" bordersize="0" padding="0,12,0,10" width="180" height="36" borderround="3,3" textcolor="#FFF8F8F2" disabledtextcolor="#FF6272A4" itemalign="left" itemfont="0" bkcolor="#FF21222C"
              normalimage="file='Profile\Proxy_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\proxy_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\proxy_Combox_Click.png' corner='5,5,25,10'"
              combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
              itemhotbkcolor="#FF44475A" itemselectedbkcolor="#FF6272A4" itemtextpadding="10,0,0,0" textpadding="0,0,20,0" endellipsis="true">
          </Combo>
        </VerticalLayout>



        <Control width="10"/>
      </HorizontalLayout>


      <HorizontalLayout bkcolor="#FF282A36">
        <List name="list_session_item" scrollwheel="true" reselect="true" bordersize="1,1,1,1" itembkcolor="#FF282A36" itemselectedbkcolor="#FF44475A" itemhotbkcolor="#FF21222C" bordercolor="#FF44475A" vscrollbar="true">
          <ListHeader height="36" bordersize="1" bordercolor="#FF44475A" bkcolor="#FF21222C">
            <ListHeaderItem text="操作" name="header_device_choice" width="60" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
            <ListHeaderItem text="环境名称" name="header_name" width="156" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
            <ListHeaderItem text="动作" name="header_act" width="100" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
            <ListHeaderItem text="操作说明" name="header_memo" width="420" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
            <ListHeaderItem text="状态" name="header_status" width="120" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
            <ListHeaderItem text="记录时间" name="header_ctime" width="140" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
            <ListHeaderItem text="账户信息" name="header_user" width="100" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
          </ListHeader>
        </List>
        <List name="list_session_manager" visible="false" scrollwheel="true" reselect="true" bordersize="1,1,1,1" itembkcolor="#FF282A36" itemselectedbkcolor="#FF44475A" itemhotbkcolor="#FF21222C" bordercolor="#FF44475A" vscrollbar="true">
          <ListHeader height="36" bordersize="1" bordercolor="#FF44475A" bkcolor="#FF21222C">
            <ListHeaderItem text="操作" name="header_device_choice" width="60" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
            <ListHeaderItem text="环境名称" name="header_name" width="320" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
            <ListHeaderItem text="动作" name="header_act" width="130" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
            <ListHeaderItem text="状态" name="header_status" width="130" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
            <ListHeaderItem text="本机网络" name="header_ip" width="150" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
            <ListHeaderItem text="地点" name="header_area" width="150" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
            <ListHeaderItem text="记录时间" name="header_ctime" width="150" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
          </ListHeader>
        </List>
      </HorizontalLayout>

      <HorizontalLayout height="52" bkcolor="#FF21222C" inset="20,0,0,0">
        <HorizontalLayout>
          <CheckBox name="opt_checkAll" text="" textpadding="57,1,0,0" selected="false"  visible="true" padding="3,16,0,0" height="18" width="16" normalimage="file='list_check_normal.png'" hotimage="list_check_hover.png" selectedimage="file='list_checked_actvice_normal.png'" ></CheckBox>
          <!--<Button name="refresh" padding="50,18,0,0" align="left" height="20" width="50" text="刷新" font="5" textcolor="#FF005ed3" hottextcolor="#FFff4444" />-->
        </HorizontalLayout>

        <VerticalLayout width="200" >
          <Control />
          <Button text="导出日志" name="exportselected" tooltip="导出日志" enabled="false" padding="0,2,0,0" width="180" height="30" textcolor="#FFF8F8F2" disabledtextcolor="#FF6272A4" align="center" font="14" bkcolor="#FF44475A" bordersize="1" bordercolor="#FF44475A" borderround="5,5" hotbkcolor="#FF6272A4" />
          <Control />
        </VerticalLayout>
        <VerticalLayout width="240">
          <Control />
          <Button text="删除已选择日志" name="deleteselected" tooltip="删除已选择日志" enabled="false"  padding="0,2,0,0" width="220" height="30" textcolor="#FFF8F8F2" disabledtextcolor="#FF6272A4" align="center" font="14" bkcolor="#FF44475A" bordersize="1" bordercolor="#FF44475A" borderround="5,5" hotbkcolor="#FF6272A4" />
          <Control />
        </VerticalLayout>
        <VerticalLayout width="240">
          <Control />
          <Button text="清除当前所有日志记录" name="clear" tooltip="清除当前所有日志记录" padding="0,2,0,0" width="220" height="30" textcolor="#FFF8F8F2" disabledtextcolor="#FF6272A4" align="center" font="14" bkcolor="#FF44475A" bordersize="1" bordercolor="#FF44475A" borderround="5,5" hotbkcolor="#FF6272A4"/>
          <Control />
        </VerticalLayout>


        <Control width="30"/>
      </HorizontalLayout>
		</VerticalLayout>

	<!--</HorizontalLayout>-->

    <Control name="dragicon" float="true" width="14" height="16" bkimage="dragicon.png"/>
  </VerticalLayout>
</Window>
