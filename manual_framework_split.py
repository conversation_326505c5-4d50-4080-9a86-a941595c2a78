#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动分类候鸟浏览器基础框架文档
根据内容特征进行精确分类
"""

import os
import re
from pathlib import Path
from docx import Document

def analyze_and_split_framework_doc(source_file, output_dir):
    """分析并切分候鸟浏览器基础框架文档"""
    try:
        doc = Document(source_file)
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        print(f"📄 分析文档: {Path(source_file).name}")
        print(f"📊 总段落数: {len(doc.paragraphs)}")
        print(f"📊 总表格数: {len(doc.tables)}")
        
        # 定义主题分类规则
        themes = {
            "API接口系统": {
                "keywords": ["API", "接口", "HTTP", "REST", "指令集", "凭据", "APISERVER", "JSON"],
                "content": []
            },
            "环境管理系统": {
                "keywords": ["环境", "ITEM", "SESSION", "配置", "管理", "创建", "更新", "删除", "FULL"],
                "content": []
            },
            "代理服务系统": {
                "keywords": ["代理", "PROXY", "SSL", "IP", "服务器", "检测", "隧道"],
                "content": []
            },
            "插件脚本管理": {
                "keywords": ["插件", "PLUGIN", "脚本", "SCRIPT", "自动化", "AUTOSCRIPTS", "安装"],
                "content": []
            },
            "数据同步机制": {
                "keywords": ["同步", "上传", "下载", "ZIP", "备份", "RSYNC", "版本"],
                "content": []
            },
            "用户认证安全": {
                "keywords": ["登录", "认证", "TOKEN", "密码", "验证", "加密", "AES", "HASH"],
                "content": []
            },
            "客户端架构": {
                "keywords": ["客户端", "PC端", "无头", "有头", "CONSOLE", "界面"],
                "content": []
            },
            "服务器端架构": {
                "keywords": ["服务器端", "数据库", "存储", "处理", "SHOPXO"],
                "content": []
            },
            "网络通信协议": {
                "keywords": ["网络", "通信", "心跳", "连接", "协议", "KCP"],
                "content": []
            },
            "日志监控系统": {
                "keywords": ["日志", "LOG", "监控", "记录", "分析", "事件"],
                "content": []
            },
            "业务模板配置": {
                "keywords": ["模板", "业务", "指纹", "配置", "USER-AGENT"],
                "content": []
            },
            "团队协作功能": {
                "keywords": ["团队", "协作", "分享", "权限", "子账户", "主账户"],
                "content": []
            },
            "系统优化性能": {
                "keywords": ["优化", "性能", "GPU", "内存", "缓存", "NVIDIA"],
                "content": []
            }
        }
        
        # 分析每个段落
        current_section = None
        section_content = []
        
        for i, paragraph in enumerate(doc.paragraphs):
            text = paragraph.text.strip()
            if not text:
                continue
            
            # 检查是否是新的章节标题
            is_new_section = False
            if (len(text) < 100 and 
                (text.startswith(('一、', '二、', '三、', '四、', '五、', '六、', '七、', '八、', '九、', '十、')) or
                 text.startswith(('1.', '2.', '3.', '4.', '5.', '6.', '7.', '8.', '9.')) or
                 text.startswith(('第', '章节', '【')) or
                 paragraph.style.name.startswith('Heading'))):
                is_new_section = True
            
            if is_new_section and section_content:
                # 保存前一个章节
                if current_section:
                    # 分类到主题
                    classified = False
                    section_text = ' '.join(section_content)
                    
                    for theme, theme_data in themes.items():
                        keyword_count = sum(1 for keyword in theme_data["keywords"] 
                                          if keyword.lower() in section_text.lower())
                        if keyword_count >= 2:  # 至少匹配2个关键词
                            theme_data["content"].append({
                                "title": current_section,
                                "content": section_content.copy(),
                                "paragraph_index": i
                            })
                            classified = True
                            break
                    
                    if not classified:
                        # 默认分类到基础架构
                        if "基础架构文档" not in themes:
                            themes["基础架构文档"] = {"keywords": [], "content": []}
                        themes["基础架构文档"]["content"].append({
                            "title": current_section,
                            "content": section_content.copy(),
                            "paragraph_index": i
                        })
                
                # 开始新章节
                current_section = text
                section_content = [text]
            else:
                section_content.append(text)
        
        # 处理最后一个章节
        if current_section and section_content:
            section_text = ' '.join(section_content)
            classified = False
            
            for theme, theme_data in themes.items():
                keyword_count = sum(1 for keyword in theme_data["keywords"] 
                                  if keyword.lower() in section_text.lower())
                if keyword_count >= 2:
                    theme_data["content"].append({
                        "title": current_section,
                        "content": section_content.copy(),
                        "paragraph_index": len(doc.paragraphs)
                    })
                    classified = True
                    break
            
            if not classified:
                if "基础架构文档" not in themes:
                    themes["基础架构文档"] = {"keywords": [], "content": []}
                themes["基础架构文档"]["content"].append({
                    "title": current_section,
                    "content": section_content.copy(),
                    "paragraph_index": len(doc.paragraphs)
                })
        
        # 生成主题文件
        output_files = []
        table_index = 0
        tables_per_theme = len(doc.tables) // max(1, len([t for t in themes.values() if t["content"]]))
        
        for theme_name, theme_data in themes.items():
            if not theme_data["content"]:
                continue
            
            # 创建主题文档
            theme_doc = Document()
            theme_doc.add_heading(f'候鸟浏览器基础框架_{theme_name}', 0)
            
            # 添加主题说明
            intro_para = theme_doc.add_paragraph()
            intro_para.add_run(f"本文档包含候鸟浏览器基础框架中关于{theme_name}的详细内容。").bold = True
            
            # 添加章节内容
            for section in theme_data["content"]:
                # 添加章节标题
                theme_doc.add_heading(section["title"], 1)
                
                # 添加章节内容
                for content_line in section["content"][1:]:  # 跳过标题行
                    if content_line.strip():
                        theme_doc.add_paragraph(content_line)
            
            # 分配表格
            tables_to_add = min(tables_per_theme, len(doc.tables) - table_index)
            for _ in range(tables_to_add):
                if table_index < len(doc.tables):
                    source_table = doc.tables[table_index]
                    rows = len(source_table.rows)
                    cols = len(source_table.columns) if source_table.rows else 0
                    
                    if rows > 0 and cols > 0:
                        new_table = theme_doc.add_table(rows=rows, cols=cols)
                        new_table.style = 'Table Grid'
                        
                        for i, row in enumerate(source_table.rows):
                            for j, cell in enumerate(row.cells):
                                if i < len(new_table.rows) and j < len(new_table.rows[i].cells):
                                    new_table.cell(i, j).text = cell.text
                    
                    table_index += 1
            
            # 保存主题文件
            filename = f"候鸟浏览器基础框架_{theme_name}.docx"
            filepath = output_path / filename
            theme_doc.save(str(filepath))
            output_files.append(str(filepath))
            
            # 统计信息
            para_count = len(theme_doc.paragraphs)
            table_count = len(theme_doc.tables)
            file_size = filepath.stat().st_size / 1024
            section_count = len(theme_data["content"])
            
            print(f"✅ 创建主题文件: {filename}")
            print(f"   章节数: {section_count}")
            print(f"   段落数: {para_count}")
            print(f"   表格数: {table_count}")
            print(f"   文件大小: {file_size:.1f} KB")
            print()
        
        return output_files
        
    except Exception as e:
        print(f"❌ 处理失败: {str(e)}")
        return []

if __name__ == "__main__":
    source_file = r"F:\augment\output\docx_files\候鸟浏览器基础框架第七十七版.docx"
    output_directory = r"F:\augment\output\docx_files\framework_themes"
    
    print("📄 候鸟浏览器基础框架文档主题切分工具")
    print(f"📂 源文件: {source_file}")
    print(f"📂 输出目录: {output_directory}")
    print()
    
    # 检查源文件
    if not Path(source_file).exists():
        print(f"❌ 源文件不存在: {source_file}")
        exit(1)
    
    # 执行切分
    result_files = analyze_and_split_framework_doc(source_file, output_directory)
    
    if result_files:
        print("=" * 60)
        print(f"🎉 切分完成! 生成了 {len(result_files)} 个主题文件")
        print("\n📋 生成的文件:")
        
        total_size = 0
        for file_path in result_files:
            file_size = Path(file_path).stat().st_size / 1024
            total_size += file_size
            print(f"   📄 {Path(file_path).name} ({file_size:.1f} KB)")
        
        print(f"\n📊 总大小: {total_size:.1f} KB")
        print(f"📊 平均大小: {total_size/len(result_files):.1f} KB")
        print("\n🎯 主题文件已准备好用于RAGFlow向量库！")
    else:
        print("❌ 切分失败")
