﻿<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Window size="1640,590" sizebox="4,4,4,4" caption="0,0,0,50" mininfo="1100,590" roundcorner="5,5,5,5" showshadow="true" shadowsize="3" shadowposition="0,0" shadowcolor="#ff6272A4" fademode="true">
  <Include source="Default.xml" />

  <VerticalLayout width="953" height="590" bkcolor="#FF282A36">
    <HorizontalLayout height="37">

    	 <Button name="login" bkimage="icon.png" padding="8,6,0,0" width="28" height="28"/>
      <Label name="session_list_title" padding="6,4,0,0" text="会话环境管理器" width="260" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" align="left" font="8"></Label>

      <Control />
      <Button name="minbtn" width="28" height="26" tooltip="最小化" normalimage="minbtn.png" hotimage="minbtn_hover.png" pushedimage="minbtnpush.png" />
      <Button name="maxbtn" width="28" height="26" tooltip="最大化" normalimage="maxbtn.png" hotimage="maxbtn_hover.png" pushedimage="maxbtnpush.png" />
      <Button name="restorebtn" visible="false" width="28" height="26" tooltip="还原" normalimage="restorebtn.png" hotimage="restorebtn_hover.png" pushedimage="restorebtnpush.png" />
      <Button name="closebtn" width="28" height="26"  tooltip="关闭" normalimage="closebtn.png" hotimage="closebtn_hover.png" pushedimage="closebtnpush.png" />

    </HorizontalLayout>
<!--<HorizontalLayout name="bkground">-->


 <HorizontalLayout name="loading_data" bkcolor="#FF44475A" visible="false">

	    <VerticalLayout >

					     <HorizontalLayout name="loading_data" height="240">
					    	 <Control />
					    		<GifAnim name="data_loading" bkimage="dataloading.gif" height="200" width="200" padding="0,40,0,0" auto="true"/>
                 <Control name="success" visible="false" padding="0,100,0,0"  bkimage="success.png" width="120" height="120" align="center" />
					    	 <Control />
					     </HorizontalLayout>


					     <HorizontalLayout height="30" >
					    	 <Control />
					    		  <Label name="data_percent" text="55%" width="300" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" align="center" font="10"></Label>
					    	 <Control />
					     </HorizontalLayout>

					     <HorizontalLayout height="60" >
					    	 <Control />
					    		  <Label name="process_description" text="会话环境正在版本验证中，请稍侯.. " width="953" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" align="center" font="8"></Label>
					    	 <Control />
					     </HorizontalLayout>

              <HorizontalLayout height="40" >
              </HorizontalLayout>

              <HorizontalLayout name="backarea" height="60" visible="false">
                <Control />
                <Button text="返回" name="back" width="120" height="30" textcolor="#FFF8F8F2" disabledtextcolor="#FF6272A4" align="center" font="14"	normalimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
                <Control width="100" />
                <Button text="退出" name="closewnd1" width="120" height="30" textcolor="#FFF8F8F2" disabledtextcolor="#FF6272A4" align="center" font="14"	normalimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
                <Control />
              </HorizontalLayout>
      </VerticalLayout>

 </HorizontalLayout>

    <VerticalLayout name="verify_area" bkcolor="#FF44475A" visible="false">

      <HorizontalLayout bkcolor="#FF282A36">
        <List name="list_verify" scrollwheel="true" reselect="true" bordersize="1,1,1,1" itembkcolor="#FF282A36" itemselectedbkcolor="#FF44475A" itemhotbkcolor="#FF44475A" bordercolor="#FF6272A4" vscrollbar="true">
          <ListHeader height="36" bordersize="1" bordercolor="#FF6272A4" bkcolor="#FF44475A" textcolor="#FFF8F8F2">
            <ListHeaderItem text="操作" name="header_device_choice" width="60" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
            <ListHeaderItem text="会话名称" name="header_name" width="156" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
            <ListHeaderItem text="注释" name="header_desc" width="380" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
            <ListHeaderItem text="创建时间" name="header_ctime" width="150" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
            <ListHeaderItem text="最近使用" name="header_utime" width="150" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
            <ListHeaderItem text="本地版本号" name="header_lversion" width="157" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
            <ListHeaderItem text="云端版本号" name="header_sversion" width="157" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
            <ListHeaderItem text="状态报告" name="header_status" width="200" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
            <ListHeaderItem text="同步建议" name="header_recommend" width="200" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
          </ListHeader>
        </List>
      </HorizontalLayout>


        <HorizontalLayout height="52">
          <HorizontalLayout padding="20,0,0,0" >
            <CheckBox name="opt_checkAllv" text="" textpadding="57,1,0,0" selected="false"  visible="true" padding="3,16,0,0" height="18" width="16" normalimage="file='list_check_normal.png'" hotimage="list_check_hover.png" selectedimage="file='list_checked_actvice_normal.png'" ></CheckBox>
            <Button name="refreshverify" enabled="false" padding="50,18,0,0" align="left" height="20" width="50" text="刷新" font="5" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" />
          </HorizontalLayout>
          <Control />
          <Button text="返回" name="backmain" padding="0,10,0,0"  textpadding="10,0,10,0" tooltip="" endellipsis="true" width="160" height="30" textcolor="#FFF8F8F2" disabledtextcolor="#FF6272A4" align="center" font="14"	normalimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
          <Control width="50" />
          <Button text="上传环境" name="upload" padding="0,10,0,0" textpadding="10,0,10,0"  tooltip="" endellipsis="true" enabled="false" width="160" height="30" textcolor="#FFF8F8F2" disabledtextcolor="#FF6272A4" align="center" font="14"	normalimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
          <Control width="50" />
          <Button text="下载环境" name="download" padding="0,10,0,0" textpadding="10,0,10,0" tooltip="" endellipsis="true" enabled="false" width="160" height="30" textcolor="#FFF8F8F2" disabledtextcolor="#FF6272A4" align="center" font="14"	normalimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
          <Control width="50" />
          <Button text="退出" name="closewnd2" padding="0,10,0,0" textpadding="10,0,10,0"  tooltip="" endellipsis="true" width="160" height="30" textcolor="#FFF8F8F2" disabledtextcolor="#FF6272A4" align="center" font="14"	normalimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
          <Control width="60" />
        </HorizontalLayout>

     </VerticalLayout>


		<VerticalLayout name="data" visible="true">


			<HorizontalLayout height="56" >
			    	 <VerticalLayout width="440">
               <Combo name="searchlist" reselect="true" dropboxsize="0,450" bordersize="0" padding="21,10,0,10" width="420" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="4,2,0,0" bkcolor="#ffdce1e7"
														normalimage="file='Profile\Setting_Combox_Normal_big_large.png' corner='5,5,30,5'" hotimage="file='Profile\Setting_Combox_Hover_large.png' corner='5,5,30,5'" pushedimage="file='Profile\Setting_Combox_Click_large.png' corner='5,5,25,10'"
												combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,30,5'"
												itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0">
               </Combo>
				         <RichEdit name="session_search" pos="20,10,0,10" height="36" width="378" tipvaluecolor="#FFF8F8F2" borderround="7,7" bkcolor="#FF44475A" font="8" textpadding="10,8,20,0" maxchar="300" tipvalue="请输入关键字查找会话.." multiline="false" textcolor="#FFF8F8F2" rich="false" transparent="false" float="true">
				      </RichEdit>
				     </VerticalLayout>
				     <VerticalLayout width="30">
				        <CheckBox name="opt_searchl" selected="false"  visible="true" padding="10,26,0,0" height="18" width="16" normalimage="file='list_check_normal.png'" hotimage="list_check_hover.png" selectedimage="file='list_checked_actvice_normal.png'" ></CheckBox>
				     </VerticalLayout>
				     <VerticalLayout width="280">
				        <Label name="searchl" padding="4,16,0,0" text="搜索包含在会话中的关键词" width="280" textcolor="#FF616161" hottextcolor="#ff000000" align="left" font="16"></Label>
				     </VerticalLayout>
        <Control />

        <VerticalLayout width="250">
          <Combo name="group" bordersize="0" padding="0,12,0,10" width="250" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" bkcolor="#ffdce1e7"
              normalimage="file='Profile\Proxy_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\proxy_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\proxy_Combox_Click.png' corner='5,5,25,10'"
              combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
              itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0" textpadding="0,0,20,0" endellipsis="true">
          </Combo>
        </VerticalLayout>
        <Control width="20"/>
            <!--<VerticalLayout width="230">
              <Combo name="agent" bordersize="0" padding="21,0,0,10" width="200" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="4,2,0,0" bkcolor="#ffdce1e7"
                   normalimage="file='Profile\Setting_Combox_Normal_big_large.png' corner='5,5,25,10'" hotimage="file='Profile\Setting_Combox_Hover_large.png' corner='5,5,25,10'" pushedimage="file='Profile\Setting_Combox_Click_large.png' corner='5,5,25,10'"
               combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
               itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0">
              </Combo>
            </VerticalLayout>-->
        <VerticalLayout width="260">
          <Combo name="acttype" reselect="true" dropboxsize="0,250" bordersize="0" padding="0,12,0,10" width="250" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" bkcolor="#ffdce1e7"
              normalimage="file='Profile\Proxy_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\proxy_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\proxy_Combox_Click.png' corner='5,5,25,10'"
              combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
              itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0" textpadding="0,0,20,0" endellipsis="true">
          </Combo>
        </VerticalLayout>
        <Control width="10"/>
      </HorizontalLayout>


      <HorizontalLayout bkcolor="#FF282A36">
        	<List name="list_session_manager" scrollwheel="true" reselect="true" bordersize="1,1,1,1" itembkcolor="#FF282A36" itemselectedbkcolor="#FF44475A" itemhotbkcolor="#FF44475A" bordercolor="#FF6272A4" vscrollbar="true">
						<ListHeader height="36" bordersize="1" bordercolor="#FF6272A4" bkcolor="#FF44475A" textcolor="#FFF8F8F2">
							<ListHeaderItem text="操作" name="header_device_choice" width="60" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="会话名称" name="header_name" width="156" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="操作平台" name="header_system" width="100" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="代理类型" name="header_proxytype" width="120" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="代理主机" name="header_proxy" width="120" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="代理端口" name="header_proxyport" width="70" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="代理账号" name="header_proxyuser" width="80" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="代理密码" name="header_proxypass" width="80" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="时区" name="header_timezone" width="160" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="国家" name="header_country" width="60" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="语言" name="header_lng" width="80" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="注释" name="header_backup_datetime" width="100" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="创建时间" name="header_ctime" width="100" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="最近使用" name="header_utime" width="100" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="" name="header_sessionid" width="240" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
						</ListHeader>

          </List>
				</HorizontalLayout>

      <HorizontalLayout height="52" bkcolor="#FF44475A" inset="20,0,0,0">
        <HorizontalLayout>
          <CheckBox name="opt_checkAll" text="" textpadding="57,1,0,0" selected="false"  visible="true" padding="3,16,0,0" height="18" width="16" normalimage="file='list_check_normal.png'" hotimage="list_check_hover.png" selectedimage="file='list_checked_actvice_normal.png'" ></CheckBox>
          <Button name="refresh" padding="50,18,0,0" align="left" height="20" width="50" text="刷新" font="5" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" />
        </HorizontalLayout>
       <HorizontalLayout>
          <CheckBox name="opt_Sync" text="同步" textpadding="57,1,0,0" selected="false"  visible="true" padding="3,16,0,0" height="18" width="16" normalimage="file='list_check_normal.png'" hotimage="list_check_hover.png" selectedimage="file='list_checked_actvice_normal.png'" ></CheckBox>
        </HorizontalLayout>
        <VerticalLayout width="200" >
          <Control />
          <Button text="版本验证" name="exportselected" tooltip="批量验证本地与云端的数据版本一致性" enabled="false" padding="0,2,0,0" width="180" height="30" textcolor="#FFF8F8F2" disabledtextcolor="#FF6272A4" align="center" font="14"	bkimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
          <Control />
        </VerticalLayout>
        <VerticalLayout width="200">
          <Control />
          <Button text="打开" name="openselected" tooltip="候鸟浏览器批量调用勾选的会话配置环境" enabled="false"  padding="0,2,0,0" width="180" height="30" textcolor="#FFF8F8F2" disabledtextcolor="#FF6272A4" align="center" font="14"	bkimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
          <Control />
        </VerticalLayout>
        <VerticalLayout width="200">
          <Control />
          <Button text="关闭" name="closeselected" tooltip="候鸟浏览器批量关闭勾选的会话配置环境" enabled="false"  padding="0,2,0,0" width="180" height="30" textcolor="#FFF8F8F2" disabledtextcolor="#FF6272A4" align="center" font="14"	bkimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
          <Control />
        </VerticalLayout>
        <VerticalLayout width="200">
          <Control />
          <Button text="删除" name="deleteselected" tooltip="批量删除勾选的会话配置环境" enabled="false"  padding="0,2,0,0" width="180" height="30" textcolor="#FFF8F8F2" disabledtextcolor="#FF6272A4" align="center" font="14"	bkimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
          <Control />
        </VerticalLayout>
        <VerticalLayout width="200">
          <Control />
          <Button text="导出环境包" name="tosync" tooltip="将所有会话环境包批量导出到本地" enabled="false"  padding="0,2,0,0" width="180" height="30" textcolor="#FFF8F8F2" disabledtextcolor="#FF6272A4" align="center" font="14"	bkimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
          <Control />
        </VerticalLayout>
        <VerticalLayout width="200">
          <Control />
          <Button text="导入环境包" name="archiveselected" tooltip="将本地的会话环境包批量导入到候鸟客户端" padding="0,2,0,0" width="180" height="30" textcolor="#FFF8F8F2" disabledtextcolor="#FF6272A4" align="center" font="14"	bkimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
          <Control />
        </VerticalLayout>
        <Control width="30"/>
      </HorizontalLayout>
		</VerticalLayout>

	<!--</HorizontalLayout>-->

    <Control name="dragicon" float="true" width="14" height="16" bkimage="dragicon.png"/>
  </VerticalLayout>
</Window>
