标题: 浏览器指纹：AudioContext
英文标题: Browser Fingerprint：AudioContext
ID: 91
分类ID: 25
添加时间: 1613977980
更新时间: 1685607070
访问次数: 0
SEO标题: 浏览器指纹：AudioContext
SEO关键词: 浏览器指纹：AudioContext
SEO描述: 浏览器指纹：AudioContext

================================================== 内容 ==================================================
AudioContext指纹（也被称作“音频指纹”）是设备音频栈的哈希衍生值。它的工作原理如下。基于您的音频设置和硬件，网站要求您的浏览器把播放音频文件的方式模拟为一个正弦函数。这个正弦函数被转化为一个哈希函数并发送给服务器，作为浏览器指纹识别中的附加熵。

在候鸟浏览器中，自动为您的浏览器环境添加随机的持续性噪声来控制AudioContext的读出，网站不会获取您设备的真实音频指纹。

================================================== 英文内容 ==================================================
An AudioContext fingerprint (also known as an "audio fingerprint") is a hash derived value of the device's audio stack. Here's how it works. Depending on your audio Settings and hardware, the Web site asks your browser to simulate the way it plays an audio file as a sine function. This sine function is converted to a hash function and sent to the server as additional entropy in the browser fingerprint recognition.

In Migratory Bird Browser, random persistent noise is automatically added to your browser environment to control the readout of the AudioContext, and the website does not take the real audio fingerprint of your device.