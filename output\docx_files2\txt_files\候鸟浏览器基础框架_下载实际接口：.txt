下载实际接口：

下载实际接口：

此接口目前返回最新版本的下载地址，域名可以客户端补全，或由服务端补全后返回。待定。后续数据包可能使用不同服务器，以及CDN等加速。

接口地址：

/api/mbbrowser/download

参数：token

返回信息：

成功时：其中的sourceurl为下载地址

{"msg":"操作成功","code":0,"data":{"sourceurl":"\/upload\/userdata\/2\/11_cf9395daa4bcfdb29880488d880a9fd5_full.zip?a9cfd52509c8e13554176b785f939522"}}

失败时：

{"msg":"没有可用数据包","code":-1,"data":""}

同步日志：

日志记录URL：

例：

http://admin.mbbrowser.com/log/20200814.txt

上传、下发、查询版本号 三个接口的请求数据和返回内容 通过此URL txt进行查询。根据日期保存，并带有成功失败数据信息。

第四章 类别数据包全局面板的功能数据汇总与加载

第四章 类别数据包全局面板的功能数据汇总与加载

前述：

PC端将用户登录成功后的所有ITEM数据进行加载（加载第一类数据包），之后根据类别的不同（cookies,session package,proxylist,useragent,password）将各item包中的同类别数据进行汇总，并展示在PC列表面板上，供用户进行添加，修改，删除，同步。

服务器端：

产品第一版：仅将PC端发过来的此类型包存储在服务器即可。

产品第二版：建立用户主表下的子表，将数据包的结构+内容放到子表中。（支持GROUP模型）

本地客户端：

本地提供列出、增删改的面板。

列表数据来源及同步约定：

特点： 此章节所有数据上传，均由用户人工操作进行同步，无自动后台同步过程。

第四章所有相关数据同步，包括会话数据包管理器，均有VERSION版本号概念。用户服务器端此类别数据副本，和第三章逻辑相同。

2、用户登录成功后，多线程同时同步FULL包和类别数据包。

3、第四章所有相关数据包不参于非用户操作性质后台自动同步到服务器（上传）逻辑。

4、类别数据包为根据类别不同， 以多个包(zip)状态存在于服务器中，注意打包不是将多个类别打成单个包，而是每个类别一个单独的ZIP包。

*5、在各面板开启时，应无条件请求服务器获取指定的类别包的版本号，并与本地进行对比，如果版本号相同，不更新本地。如果本地版本号小于服务器端版本号，无条件下载并更新本地类别数据包。

会话数据包管理器

（支持指定单项同步到服务器和还原到本地）

列表数据来源:

1、以SESSION_UNIQUE_ID为唯一标识，以xml id为顺序加载列表数据。

2、列出configdata.xml中所有item和item对应已存在的文件夹的记录。

列表关键字查找 【已完成已测试通过】

搜索包含在会话中的关键词 【未完成】

描述：勾选后，支持对数据包中的数据进行文本搜索。

右键菜单：

全选，反选。【已完成未测试通过】

描述：全选，反选，和勾选，都将使下面的按钮亮起和置灰。

按钮类：

同步（ITEM项同步）

描述：将勾选的项对应的数据包上传到服务器（根据第二章的上传流程），

逻辑流程：

1、		在上传前判断数据包中是否有COOKIES数据。如果没有则禁止上传。（cookies数据为 item指向的default文件夹所有数据）

2、		对于不是自已创建的ITEM（如第三方分享过来的包），则禁止上传。

*（服务器接口待定）

打开选定项

描述：将此ITEM项添加到主面板中，并立即使用。

逻辑流程：

判断是否已经加载了此ITEM（根据SESSION_UNIQUE_ID）,如已加载则提示用户无需再加载。

如未加载过，将此ITEM项加入到ConfigData.xml，同时面板置亮此ITEM，ITEM对应浏览器打开。（也可考虑只加入到面板中，亮起此ITEM和浏览器默认打开交给用户，也可考虑将此设置做到控制面板中供用户设置）。

删除勾选项

描述：将此ITEM从CONFIG.dat中删除，同时将此ITEM对应的数据删除。

逻辑流程：

判断Configdata.xml中是否存在此项，如果存在，将此节点is_valid置为0。

全局控制面板，增设清理本地删除数据（提供列表显示is_valid为0记录项【提供恢复项】），点删除，将is_valid为0的XML节点和对应文件数据进行删除清理。

恢复勾选项

描述：不判断VERSION，将服务器的相同SESSION_UNIQUE_ID覆盖本地XML和文件夹数据。

PC端请求服务器（ SESSION_UNIQUE_ID）

服务器根据SESSION_UNIQUE_ID查找是否存在此数据包。

如果存在，PC端获取服务器返回VERSION,同时下载数据包覆盖本地，更新VERSION, SESSION 将 XML节点数据覆盖本地。

*（服务器接口待定）

会话数据包管理器流程与接口

流程：

1）窗口加载数据流程

当用户打开此窗体，窗体显示LOADING动画。

【线程模式】

PC端进行 –>

（一）版本请求：

服务器端：

Json格式：

{"filename":"x_xxx.dat","hash":"xxxxxxxx","version":"xx"}

Dat文件为加密的xml数据。

(二) PC端本地进行版本对比

1、如果本地版本与服务器版本相同，不下载不更新本地。

2、如果本地版本小于服务器版本，下载更新本地并加载。

（三）PC端下载请求 – 服务器响应：

1、当PC端请求过来，请求串格式：

http://domain/xxx?aes(token=xxxxxxx&type=session_package_list &act=download&msg=request)

2、服务器端返回动态url（JSON格式）

服务器进行AES解码，判定token是否合法。

如合法：

http://domain/download?aes(filename=1_xxxxxxxx.dat&hash=xxxx&type=session_package_list&size=xxxxxx&version=xx)

3、PC端下载更新到本地并加载。

面板LOADING动画消失，显示加载的数据列表。

2）用户点击“恢复勾选项”按钮流程

窗体显示LOADING动画。

【线程模式】

PC端进行 –>

（一）版本请求：

服务器端：

Json格式：

{"filename":"x_xxx.zip","hash":"xxxxxxxx","version":"xx"}

文件为加密的item zip包数据。

(二) PC端本地进行版本对比

1、如果本地版本与服务器版本相同，不下载不更新本地。

2、如果本地版本小于服务器版本，下载更新本地并加载。

（三）PC端下载请求 – 服务器响应：

1、当PC端请求过来，请求串格式：

http://domain/xxx?aes(token=xxxxxxx&type=session_package_item&uid= session_unique_id&act=download&msg=request)

2、服务器端返回动态url（JSON格式）

服务器进行AES解码，判定token是否合法。

如合法：

PC端下载更新到本地并加载。

4、面板LOADING动画消失，显示加载的数据列表。

3）用户点击“同步”按钮流程

窗体显示LOADING动画。

【线程模式】

PC端进行 –>

（三）PC端上传请求 – 服务器响应：

1、当PC端请求上传，请求串格式：

http://domain/xxx?aes(token=xxxxxxx&type=session_package_item&uid=session_unique_id&act=upload&msg=request)

上传交互过程见第三章标准上传流程。

2、服务器端返回动态url（JSON格式）

服务器进行AES解码，判定token是否合法。

如合法：

PC端上传到服务器成功。

5、面板LOADING动画消失，显示 带SESSION NAME的数据上传成功信息。

4）服务器端针对此数据存储说明及约定

根据item的session_unique_id进行数据保存(与FULL包的子ZIP ITEM包存储格式完全相同)。

服务器端以session_unique_id作为唯一判定标识。

ITEM数据不作15天备份机制。仅保留一份最新副本。

4、   用户提交的单个ITEM数据必须由服务器端异步非压缩打包流程，加入到服务器最新的FULL包中。（关键）（详情见第六章）


================================================== 表格内容 ==================================================

<?xml version="1.0" encoding="utf-8" ?>
<VERSION version="11">
<VER ID="1" LOGIN_ACCOUNT="" >
	<DOMAIN TITLE = "帐户名称"/>
<PLATFORM NAME = "21cn.com"/>
<DOMAIN NAME = "21cn.com"/>
	<LOGIN NAME="sztengli"/>
	<ORGIN_PASSWORD NAME="sztengli"/>
  <ENCRYPT_PASSWORD NAME="e$AepioIhfedkddaei"/>
	<IS_VALID VALUE="1" />
	<UPDATETIME VALUE ="2021-01-12 12:22:34"/>
	<CREATETIME VALUE ="2021-01-12 12:22:34"/>
</VER>
</VERSION>

<?xml version="1.0" encoding="utf-8" ?>
<VERSION version="11">
<VER ID="1" LOGIN_ACCOUNT="" SESSION_UNIQUE_ID="">
	<SESSION NAME="<EMAIL>" />
	<PROXY TYPE="HTTP"/>
	<CONNECT IP="*************"/>
	<PROXY IP="***************"/>
	<PORT VALUE="8080"/>
	<LOGIN NAME="admin"/>
	<PASSWORD VALUE="111111"/>
	<DNS VALUE="*******"/>
	<IS_VALID VALUE="1" />
	<CREATETIME VALUE="2021-01-12 12:22:34"/>
	<UPDATETIME VALUE="2021-01-12 12:22:34"/>
</VER>
</VERSION>