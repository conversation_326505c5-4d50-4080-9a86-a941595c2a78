第十七章【数据结构逻辑与规范2.0】 客户端本地数据格式存储位置详述

第十七章【数据结构逻辑与规范2.0】 客户端本地数据格式存储位置详述

第十七章【数据结构逻辑与规范2.0】 客户端本地数据格式存储位置详述[2021-02-19 新增]

【注1：除下面逻辑流程描述之外，其它仍严格遵循原1.0所有版本逻辑约定】

【注2： 2.0版客户端、服务器端所有逻辑均在测试服进行开发与调试，】

用户数据 存储根目录 位置：

C:\MBData\

默认存储位置文件夹结构说明：

C:\MBData\

Config： 客户端全局配置数据存放目录(不适用于注册表存储的数据放入此文件夹中)。

LocalStorage：用户数据存储文件夹

Logs：客户端日志文件夹(存放本地/网络/运行调试信息，dump数据等)

System：备用文件夹

注：上述客户端数据存储逻辑与1.0版一致，无任何变化。

一、Full包不再打包， 用户名下的自有各ITEM下浏览器数据【ITEM包数据】，即Full包里不再含有item包ZIP数据。

流程描述：

【原1.0流程 】 客户端在三大同步模块中：即时同步、定时同步、退出时同步中，将 此用户名加码目录下的所有数据进行打FULL包：
例： 帐户下所有数据，包含了item对应的各浏览器数据。在同步事件触发后进行打包，并以FULL包格式进行上传到服务器。

C:\MBData\LocalStorage\7zxjIqxxuQ28jnWdwVPQpA==\

【新2.0规范流程 】

FULL打包逻辑更新：

基于原1.0逻辑基础上变化如下：

客户端针对FULL包打包逻辑不再对 ChromeData 文件夹下所有数据进行打包。

即FULL包中仅包含除ChromeData 文件夹之外的所有数据。

数据结构版本向下兼容策略：

基于客户端所在机器1.0数据结构本地存储不变的原则下，兼容候鸟历史版本，如服务器返回full包如带有所有浏览器数据包的，则应仍旧按1.0版约定解压缩和解析所有数据包并进行存放，此逻辑无变化。

仅在再次生成full包的时侯，进行新版2.0数据结构逻辑打包。

二、2.0版FULL包数据、单ITEM包同步约定：

用户在主程序创建/修改item时（ITEM数据有变化时），主程序的即时同步模块、定时同步模块、退出时同步模块触发逻辑不变。

打包与同步逻辑变化如下：

只针对用户当前item包进行打包，并同步此item包数据和full包数据，原1.0版“FULL包版本对比流程”不变，增加item包版本对比流程。ITEM包版本对比逻辑与约定与FULL包完全一致。因此，item包需带版本号(版本对比逻辑与原full包流程相同)。

注： FULL包服务器端接口不变，同步逻辑不变。如图：

重点：单ITEM包同步逻辑遵循1.0版 ITEM同步服务器端接口。

如图：

依据1.0框架下的 候鸟第九版_团队协作版基础约定与接口流程说明手册

进行1.0现有逻辑描述：

服务器端在原1.0版中，定时计划任务、用户控制台点击刷新按钮来进行此用户full包的解压缩。

并通过解析full包中configdata.xml中的节点，通过节点的各item版本号来判定是否需要将full包中的各item包覆盖到本地此用户下 单item存储文件夹中。

同时服务器端提供上图中单ITEM包接口来进行客户端单item包的发送与更新。

2.0 流程详述如下： 依据第九版团队协作版接口文档，服务器端仍进行定时计划任务解析full包中的configdata.xml，原逻辑不变。

服务器端依据full包上传接口得到的客户端产品版本号，可判断出full包为2.0客户端提交上来，并且不再判断item版本号，也不再将full包中的各item zip文件覆盖到此用户名下的 单item存储文件夹。【2.0框架下的full包中不再存有各item子包zip文件】

2.0的item数据主要依赖上图服务器端，单item包接口 进行版本对比、服务器端获取与服务器端存储。 原1.0版的full包、单item包约定存储位置不变，原约定存储逻辑不变。

通过上图中，ITEM上传接口 服务器端：item包存储流程应与full包存储流程一致，即item包为递增式存储，而不应是覆盖式存储。Item包定时清理流程与full包定时清理流程相同。

其它未描述到的逻辑遵循1.0版本不作任何变化。

三、Configdata.xml   XML文件处理

2.0 逻辑描述：

full包中原 configdata.xml数据进行分离，原configdata.xml中 包含的  item的配置信息xml数据以XML加密格式文件，放置在item包中。每个ITEM包有且仅允许存在一份XML加密格式文件。

约定 此item配置xml信息统一命名为： ItemConfig.xml

（注：原1.0框架协议中，当前服务器端逻辑进行 解析configdata.xml原约定未解析过环境配置信息诸类节点，因此服务器端代码仍旧无须改动）。

ConfigData.xml仅保留基础主面板信息 仍保留SESSION_UNIQUE_ID,

ConfigData.xml中各节点 与 各item的xml配置文件之间通过SESSION_UNIQUE_ID进行关联，configdata.xml中保留item的版本号。

Configdata.xml与ItemConfig.xml和 单item包.zip之间的关系说明:

2.0版 数据相互关系说明

单Item包中的 ItemConfig.xml内容为：

<VER ID="2" VERSION="4" SESSION_UNIQUE_ID="f6b1ca4c003b1455a2dcca56a388ef5f" FROMUID="0" SHARETIME="">

<SESSION NAME="666" TOP="1" COLOR="#FFffffffff" SYSTEM="Win32"/>

<IS_ANONYMITY ANONYMITY = "0"/>

<COMMENT COMMENT_STRING=""/>

<SESSION_DEFINE_CODE SPHEREGL="0" ENABLE_LOCALHTML5_STORAGE="0" SAVE_LOCALHTML5_STORAGE="0" SAVE_INDEXDB_STORAGE="0"/>

<NETWORK TYPE="noProxy" PMODE="0" IP="N/A" PORT="0" USER="" PASSWORD="" PUBLIC_IP="*************" FAKEIP="**************" />

<NETWORK_CTR NA="0" FAKE_WRTC="0" SAME_IP="0" IPV6="0" WRTCOFF="0" DNS="" />

<USERAGENT_BROWSER CHROME="0" SAFARI="0" MSIE="0" OTHER="0" REGEN_CONFIG_USERAGENT="0" />

<USERAGENT_STRING UA="Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.183 Safari/537.36" UA_LNG="zh-TW" UA_LNG_STRING="[@en-US@,@US@]" />

<USERAGENT_CTR DISPOPUPS="0" ENABLE_SERVICE="0" BLOCKLNGINPUT="0"  />

<RESOLUTION WIGHT="1920" HEIGHT="1200" />

<RESOLUTION_CTR EMU_SCREEN="0" EMU_TOUCH="0" />

<POSITION LONGITUDE="0.000000" LATITUDE="0.000000" COUNTRY="TW" />

<TIMEZONE TIMEZONE_NAME="Asia/Taipei (-480)" ADD_VALUE=""/>

<FINGERPRINT_CODE AUDIO="1" CANVAS="1" FONTS="1" RETCS="1" DNS="1" AUTOIPCHECK="1"/>

<OTHER_SETTING PLUGINS_MIMETYPE="0" SAVE_ENCRYPT_COOKIES="0" ENABLE_FLASH="0" DYNAMIC_FINGERPRINTS="0" BLOCK_CANVAS_OUTPUT="0" />

<DYNAMIC_FINGERPRINTS_CTR D_AUDIO="0" D_CANVAS="0" D_FONTS="0" D_RETCS="0" D_MEDIA="0" D_WEBGL="0" D_MIME="0" D_PLUGINS="0" />

<IS_VALUED VALUED="0" />

<UPDATETIME VALUE="2021-02-01 10:47:26"/>

<CREATETIME VALUE="2020-12-09 23:10:39"/>

</VER>

*单Item包 (包含浏览器数据，UACONF文件，ItemConfig.xml) 

实际文件名为：

   392_0c28aea0abad7330447c2e9d8a9f107b_Default.zip

392为item版本号。

0c28aea0abad7330447c2e9d8a9f107b 为SESSION_UNIQUE_ID

Default 为 附加字。

1、Configdata.xml中包含有单ITEM包的SESSION_UNIQUE_ID，因此可通过SESSION_UNIQUE_ID、TOKEN告诉服务器需要哪个ITEM。

2.0版中 ConfigData.xml 内容如下：

<VER ID="2" VERSION="4" SESSION_UNIQUE_ID="f6b1ca4c003b1455a2dcca56a388ef5f" FROMUID="0" SHARETIME="">

<SESSION NAME="666" TOP="1" COLOR="#FFffffffff" SYSTEM="Win32"/>

<IS_VALUED VALUED="0" />

<UPDATETIME VALUE="2021-02-01 10:47:26"/>

<CREATETIME VALUE="2020-12-09 23:10:39"/>

</VER>

2、服务器端新增“单ITEM请求下载接口”， 客户端通过此接口进行单ITEM数据版本对比，单ITEM包 ZIP数据下载。

注：服务器端仅负责item包文件保存、返回版本号、供客户端下载指定单ITEM包，没有别的逻辑。原1.0约定均有效。

对于旧版本兼容：原定时解析xml逻辑不变即可。在某一天确定用户均没有使用旧版，可停止旧版本的下载，用户下载新版，安装新版。

三、客户端加载数据流程说明

主程序仍默认加载configdata.xml数据（原流程不变），根据conofigdata.xml显示数据在主面板，仅当用户使用面板中某个ITEM时（运行item，编辑item配置），再进行服务器交互，判断item版本号，并加载对应的item。

详述如下：

客户端加载configdata.xml 显示面板 –> 用户在面板上点击某个ITEM（如图），对此ITEM进行操作，操作触发后，客户端与服务器端针对此ITEM进行版本对比，（基于1.0版本对比流程）。

如需要同步到本地，即时进行单ITEM数据同步，保存本地并继续流程。

版本对比后，如无需同步到本地，直接继续流程。

此ITEM如被用户修改，仍需基于1.0与2.0上述流程，同步到服务器端。

四、 2.0 客户端、服务器端解压缩full包流程说明

     客户端可根据FULL包数据判定是否需要对item数据进行处理（在1.0用户已全部转移到 2.0后，此流程对应的代码可注释掉），原解析configdata.xml流程不变（xml数据格式不变）。

服务器端解析full包原流程不变，默认兼容1.0与2.0  full包格式即可。

五、服务器端 用户单ITEM包存储说明：

服务器端仅需将客户端通过接口进行版本对比后上传上来的item包根据用户登录名分门别类放置到对应的，正确的目录下即可。

2.0框架版本解决以下未来之隐患 并带来新的特点：

1、 解决 当item数达到400个以上的日在线用户数超过30-50个时（其中包含同时400个item均有打开浏览器过往史的用户），原1.0版本的full包将会达到50M左右，服务器带宽与服务器当前代码处理效率较难满足30个此类用户的数据传输需求。

2、 服务器端逻辑大幅简化，服务器大幅减少对full包文件的分析处理。

由于configdata.xml的文件体积大幅减小，原服务器端对configdata.xml解析逻辑将更快,服务器在未来可预见的压力将明显降低。

3、 服务器可承受更大的用户在线数量（可在1.0版本基础上，增加至少10倍用户基数）。

4、 客户端加载数据速度将明显更快。

5、 考虑到解析xml的效率大幅提升，可推算出2.0框架可大幅减少服务器端 用户控制台处理响应周期慢的历史问题，用户在控制台与客户端之间的响应速度将加快数倍。

[2021-02-25 新增]

第十八章 环境item包保存到本地 / ITEM包客户端本地导入导出（支持批量导入导出）

第十八章 环境item包保存到本地 / ITEM包客户端本地导入导出（支持批量导入导出）[2021-02-25 新增]

业务描述：

为保证产品与竞争对手的产品产生较大的差异化，候鸟产品经集体讨论已确认提供给用户环境ITEM包保存到本地的完整功能。

此功能提供后，会带来以下以下特点和优势：

1、用户可以在经营的过程中（养号等业务），用户自行确认已在目标站点产生有效的ITEM数据，自行进行导出到本地妥善保存。（服务器端也会存有历史副本，但人的自身本能，会认为他自已导出保存到本地的更安全）

2、有效减少客服压力，用户通过自行导入导出可以自行解决各种疑难杂症（如cookies失效，帐户被修改，用户自身因素导致的数据毁损等）

3、此功能与现有1.0框架不冲突，与2.0框架会产生冲突（处理方法在后面详述【对于本地导入的item，此item版本号要更新为高于当前服务器端版本号】）

4、用户如批量导出所有item项，即可安全清理本地浏览器所有数据，可腾出用户机器物理空间。

5、用户可以通过导出的ITEM自行售卖，或通过候鸟平台进行公开售卖。【导出ITEM流程，需提供用户自行进行密码打包的方式】

按钮更改后图示：

说明：

上传到云端 对应旧版  同步 按钮，功能不变。

更新到本地 对应旧版  更新所有项 按钮

打开选定项 要根据 2.0框架来完善原有流程。

删除勾选项 要根据 2.0框架来完善原有流程。

导出会话环境 新增按钮

导入会话环境 新增按钮

原：选定的存档 废除。


================================================== 表格内容 ==================================================

参数名称 | 类型 | 必传 | 样例串/默认值 | 说明
Session_ID | string | 是 | 373808cb37bd63f5f7d92415e736e85f | 环境ID

{"message": "Success",
"code": 0,
"data": {
        "action": "StopSession_ID",
        "status": 0
    }