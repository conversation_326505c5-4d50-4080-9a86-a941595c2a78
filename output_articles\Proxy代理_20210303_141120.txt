标题: Proxy代理
英文标题: Proxy
ID: 97
分类ID: 25
添加时间: 1614751880
更新时间: 1685430042
访问次数: 0
SEO标题: Proxy代理
SEO关键词: Proxy代理
SEO描述: Proxy代理

================================================== 内容 ==================================================
代理（英语：Proxy）也称网络代理，是一种特殊的网络服务，允许一个（一般为客户端）通过这个服务与另一个网络终端（一般为服务器）进行非直接的连接。一些网关、路由器等网络设备具备网络代理功能。一般认为代理服务有利于保障网络终端的隐私或安全，防止攻击。


提供代理服务的电脑系统或其它类型的网络终端称为代理服务器（英文：Proxy Server）。一个完整的代理请求过程为：客户端首先与代理服务器创建连接，接着根据代理服务器所使用的代理协议，请求对目标服务器创建连接、或者获得目标服务器的指定资源（如：文件）。在后一种情况中，代理服务器可能对目标服务器的资源下载至本地缓存，如果客户端所要获取的资源在代理服务器的缓存之中，则代理服务器并不会向目标服务器发送请求，而是直接传回已缓存的资源。一些代理协议允许代理服务器改变客户端的原始请求、目标服务器的原始响应，以满足代理协议的需要。代理服务器的选项和设置在计算机程序中，通常包括一个“防火墙”，允许用户输入代理地址，它会遮盖他们的网络活动，可以允许绕过互联网过滤实现网络访问。

================================================== 英文内容 ==================================================
Proxy, also known as a network proxy, is a special network service that allows one (usually a client) to communicate indirectly with another network terminal (usually a server) through this service. Some network devices, such as gateways and routers, have the network proxy function. It is generally believed that proxy services help to protect the privacy or security of network terminals and prevent attacks.


The computer system or other types of network terminals that provide Proxy services are called proxy servers. A complete proxy request process is as follows: the client first creates a connection with the proxy server, and then requests to create a connection to the target server or obtain a specified resource (such as a file) from the target server, depending on the proxy protocol used by the proxy server. In the latter case, the proxy server may download the resources of the target server to the local cache. If the resources that the client wants to obtain are in the cache of the proxy server, the proxy server will not send the request to the target server, but directly return the cached resources. Some proxy protocols allow the proxy server to change the original request of the client and the original response of the target server to meet the needs of the proxy protocol. Proxy server options and Settings in the computer program usually include a "firewall" that allows users to enter the proxy address, which masks their network activity and can allow Internet filtering to be bypassed to achieve network access.