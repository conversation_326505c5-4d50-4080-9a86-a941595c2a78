标题: 套餐购买
英文标题: Purchase Package
ID: 32
分类ID: 7
添加时间: 1606706259
更新时间: 1685416828
访问次数: 0
SEO标题: 候鸟浏览器购买套餐
SEO关键词: 候鸟浏览器购买套餐
SEO描述: 候鸟浏览器购买套餐

================================================== 内容 ==================================================
1、进入候鸟防关联浏览器首页，点击右上角“登录&amp;注册”按钮；

![](6d7b2882624511f09a0d0242ac130006/images/image_08a13d504868.png)


2、进入登录界面，点击“创建账号”；

![](6d7b2882624511f09a0d0242ac130006/images/image_26029be41ddd.png)


3、输入邮箱、密码等信息，点击下一步；

![](6d7b2882624511f09a0d0242ac130006/images/image_c172957b34ed.png)


4、注册成功后，点击控制台；

![](6d7b2882624511f09a0d0242ac130006/images/image_77e7dc9a4973.png)


5、在控制台首页点击“购买”；

![](6d7b2882624511f09a0d0242ac130006/images/image_943064019c22.png)


6、选择您要购买的套餐；

![](6d7b2882624511f09a0d0242ac130006/images/image_9c3bcdeca911.png)


7、确认套餐，点击下一步；

![](6d7b2882624511f09a0d0242ac130006/images/image_349e457275ed.png)


8、页面中出现付款码，扫描付款码进行支付，完成购买。

![](6d7b2882624511f09a0d0242ac130006/images/image_f3c9e88ac259.png)

================================================== 英文内容 ==================================================
1、Go to the home page of Mbbrowser and click "Login & Register";

![](6d7b2882624511f09a0d0242ac130006/images/image_bda583c9c30b.png)


2、Enter the login, click "Register";

![](6d7b2882624511f09a0d0242ac130006/images/image_9d941ad87e89.png)


3、Enter email, password and other information, click Next;

![](6d7b2882624511f09a0d0242ac130006/images/image_a76893ce3b2d.png)


4、After registration, log in to your registered email address to check the activation email, and then activate the account;

![](6d7b2882624511f09a0d0242ac130006/images/image_85a43a3491ef.png)


5、Log in to the console after the activation is successful, Click "Buy" on the console home page;

![](6d7b2882624511f09a0d0242ac130006/images/image_9f0a466447c7.png)


6、Select the package you want to purchase;

![](6d7b2882624511f09a0d0242ac130006/images/image_e3ca304d4d9e.png)


7、Confirm the package and click Next;

![](6d7b2882624511f09a0d0242ac130006/images/image_eef42e190df1.png)


8、The payment code appears on the page. Scan the payment code to make payment and complete the purchase.

![](6d7b2882624511f09a0d0242ac130006/images/image_0223e153a8a1.png)