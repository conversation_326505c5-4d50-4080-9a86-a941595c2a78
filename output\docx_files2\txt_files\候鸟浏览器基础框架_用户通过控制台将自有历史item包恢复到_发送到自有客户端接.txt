用户通过控制台将自有历史item包恢复到/发送到自有客户端接口

用户通过控制台将自有历史item包恢复到/发送到自有客户端接口

==========================================================================

用户通过控制台将自有历史item包发送到自有客户端接口

*     列表请求：connected|req_ownlist

(注: 服务器端、客户端逻辑 除参数外，流程与req_sharelist基本相同)

==========================================================================

接口职责与作用： 此接口支持和提供用户(包括普通用户和团队协作用户[即所有已付费周期内有效用户]) 通过控制台将自已的任意历史item实时添加入自有帐户登录的客户端中。

（2021-01-17 按钮名称或TIP应修改为：恢复到我的客户端）

流程详述：

1、客户端在心跳流程中收到connected|req_ownlist 标志位后，实时发起请求串:

ownlist请求串：

2、服务器端收到请求串，进行数据库查询，下发数据，返回Json格式：

成功：

{"msg":"操作成功","code":0,"data":{"sourceurl":"\/uid\/itemshare\/2\/11_cf9395daa4bcfdb29880488d880a9fd5_item.zip?a9cfd52509c8e13554176b785f939522",”version”:”11”,”itemid”:” SESSION_UNIQUE_ID”},{"sourceurl":"\/uid\/itemown\/2\/11_cf9395daa4bcfdb29880488d880a9fd5_item.zip?a9cfd52509c8e13554176b785f939522",”version”:”11”,”itemid”:” SESSION_UNIQUE_ID”},{"sourceurl":"\/uid\/itemown\/2\/11_cf9395daa4bcfdb29880488d880a9fd5_item.zip?a9cfd52509c8e13554176b785f939522",”version”:”11”,”itemid”:” SESSION_UNIQUE_ID”}}

失败：

{"msg":"没有可用数据包","code":-1,"data":""}

客户端通过获得的数据，进行下载完成数据hash校验、SESSION_UNIQUE_ID与本地configdata.xml数据校对。

数据hash校验：

客户端下载完成数据包后，获取文件hash码，与返回json串中的hash进行对比。一致表示下载成功。

客户端下载完成单item数据包需根据服务器端接口返回给服务器端成功/失败状态标志位。

6、用户从控制台任何时侯发过来的item自有包，客户端收到后不判断和不验证版本号，直接将item自有包更新/整合到本地configdata.xml中。

==========================================================================

*     子帐户单item项删除请求：connected|req_delitem[SESSION_UNIQUE_ID]

==========================================================================

客户端在心跳流程中收到connected| req_delitem[SESSION_UNIQUE_ID] 标志位后:

实时发起请求串:

这里描述为何不直接删除本地xml item节点，还需要再次请求服务器。因为心跳流程是明文传输，为防止外界对心跳模拟或产生恶意行为，需做二次确认。

iteminfo请求串：

服务器端收到串，可在后台客户界面显示“删除中”。

服务器端加码返回：

{“op”:”del”,"itemid":"SESSION_UNIQUE_ID","code":0,"date":"日期时间"}

客户端收到服务器端返回后，对串进行解码，解码成功后，根据code:0,json中的SESSION_UNIQUE_ID, 对本地configdata.xml中的SESSION_UNIQUE_ID对应的item节点xml进行删除。

删除成功/面板刷新完成。 请求服务器：

Iteminfo_report请求串：

服务器在客户后台显示：已删除。

==========================================================================

*    [此项在团队协作版第一版对外发布后，再进行] 子帐户单item项修改请求： connected|req_modiitem[SESSION_UNIQUE_ID]

==========================================================================

客户端在心跳流程中收到connected| req_modiitem [SESSION_UNIQUE_ID] 标志位后:

获取本地configdata.xml中此SESSION_UNIQUE_ID的版本号。

客户端实时发起请求串:

Iteminfo_report请求串：

服务器端获取到SESSION_UNIQUE_ID 对应的版本号后，对当前item的版本号进行+1，然后返回：

成功：

{“op”:”modi”,"msg":"操作成功","code":0,"data":{"sourceurl":"\/uid\/itemshare\/2\/11_cf9395daa4bcfdb29880488d880a9fd5_item.zip?a9cfd52509c8e13554176b785f939522",”version”:”12”,”itemid”:” SESSION_UNIQUE_ID”}}

失败：

{"msg":"没有可用数据包","code":-1,"data":""}

客户端收到返回json串：

判定op节点值为modi。

客户端的下载校验流程：Item项修改流程等同于connected|req_sharelist 。

A、将下载到的单item zip包数据覆盖本地已有数据。
B、根据SESSION_UNIQUE_ID将xml节点进行替换

对于用户从控制台任何时侯发过来的item分享包，客户端收到后不再判断和验证版本号，直接将item分享包更新/整合到本地configdata.xml中。

==========================================================================

*     客户端全局LOG上传接口

Log打包请求：connected|req_log

接口地址：/api/mbbrowser/logupload

注：此接口的按钮入口在客户管理平台提供（管理员后台）

通讯约定：通过POST form-data方式上传文件

Log数据存储约定：存放在单独的log路径下，不放在full包的数据区。

客户端在心跳流程中收到connected| req_log 标志位后:

对本地log日志进行zip打包(使用zip组件)实时发起上传串:

无需做二次确认。

（注：log包上传根据当前上传逻辑，可切片或不切片处理。）

成功：

{"msg":"文件上传成功","code":0,"data":""}

失败：

{"msg":"文件上传成功，但hash不一致，文件值：244d62094371ce2aeda0b999545e1b6a，POST值：244d62094371ce2aeda0b999545e1b60","code":-202,"data":""}

客户端对于首次上传失败，仅需要尝试上传一次。

3、上传接口流程遵循第十版候鸟浏览器文档关于full包的zip流程约定。

服务器/客户端 item分享数据包二次校验接口：

(遵循候鸟浏览器第十三版.docx所有约定)

接口前述： 多人团队协作版的多数场景为付费客户下的多人进行协同工作，在工作开展时，存在接收方（子帐户操作人员）未严格按照数据接收完成后再关机(休眠)的规范操作，[在实际使用中，客户通过自行关机等断网行为，导致在日常操作时很难查觉的数据出现中断，(接收中/同步中]，此接口有能力保证完全避免分享类数据因子帐号人为中断而导致母帐户分享数据与服务器端不一致的情况发生。

说明：此接口可保证用户分享的数据在接收端不会出现因用户各种操作(关机/休眠/意外断电/断网)等所有场景(除硬盘满之外)不会丢失由母帐户或其它帐户分享过来的item数据包。

==========================================================================

*     客户端主动发起请求：

请求参数:  Aes(token=xxxxxxx& act=itemcheck&rnd=rnd(datetime))

==========================================================================

接口约定：

客户端发起请求时机说明：

客户端登录成功后，在客户端主进程所有流程已加载运行完成之后(处于CPU空闲状态时)，使用线程(必须使用线程)进行一次此请求。【此请求不限于团队协作版本和其它版本，即所有套餐类型都需进行此请求】。

服务器端收到请求后动作说明：

返回所有有效的分享item数据包列表和下载包地址、分享item数据包每个包的文件hash、每个item的session_unique_id。

客户端收到数据后动作说明：

客户端进行session_unique_id对比本地configdata.xml中节点，如果本地configdata.xml中缺少的，进行下载(下载后进行hash校验)，对于configdata.xml中多出的分享item，进行删除[此删除动作需大家讨论后再进行]，下载失败重试三次，下载成功和校验成功失败需返回报告给服务器端。

客户端主面板点击刷新按钮动作说明：

用户在客户端面板点击刷新按钮，进行一次上述流程请求过程。

详细逻辑流程说明

依据上述接口约定，逻辑流程如下：

1、客户端发起请求：

请求参数:  aes(token=xxxxxxx& act=itemcheck&rnd=rnd(datetime))

服务器端收到请求对加码串进行解码，确认用户ID，查询此用户所有有效的分享包集合，并通过ase加码的json格式返回给客户端。如无有效集合，仍旧返回标准json格式。

{"msg":"操作成功","code":0,"data":{"sourceurl":"\/uid\/itemshare\/2\/11_cf9395daa4bcfdb29880488d880a9fd5_item.zip?a9cfd52509c8e13554176b785f939522",”version”:”11”,”itemid”:” SESSION_UNIQUE_ID”},{"sourceurl":"\/uid\/itemown\/2\/11_cf9395daa4bcfdb29880488d880a9fd5_item.zip?a9cfd52509c8e13554176b785f939522",”version”:”11”,”itemid”:” SESSION_UNIQUE_ID”},{"sourceurl":"\/uid\/itemown\/2\/11_cf9395daa4bcfdb29880488d880a9fd5_item.zip?a9cfd52509c8e13554176b785f939522",”version”:”11”,”itemid”:” SESSION_UNIQUE_ID”}}
注：此格式与sharelist/ownlist返回格式相同。

客户端通过获得的数据，进行下载完成数据hash校验、SESSION_UNIQUE_ID与本地configdata.xml数据校对。

数据hash校验：

客户端下载完成数据包后，获取文件hash码，与返回json串中的hash进行对比。一致表示下载成功。

客户端下载完成单item数据包需根据服务器端接口返回给服务器端成功/失败状态标志位。


================================================== 表格内容 ==================================================

{
"message": " Import Cookie failed",
"code": -13,
"data": {
            “Session_Id” : 373808cb37bd63f5f7d92415e736e85f 	//环境ID
       }
}

参数名称 | 类型 | 必传 | 样例串/默认值 | 说明
Session_ID | String | 是 | 373808cb37bd63f5f7d92415e736e85f | 指定导出的环境ID
Export_Cookie_File | String | 是 | C:\cookie.txt | 指定导出环境的COOKIE TXT到指定路径