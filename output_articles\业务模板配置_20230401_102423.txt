标题: 业务模板配置
英文标题: Business Template Configuration
ID: 116
分类ID: 7
添加时间: 1680315863
更新时间: 1736150215
访问次数: 0
SEO标题: 候鸟浏览器业务模板配置
SEO关键词: 候鸟浏览器业务模板配置
SEO描述: 候鸟浏览器业务模板配置

================================================== 内容 ==================================================
### 常量定义表

| 常量名 | 十六进制值 | 说明 |
|--------|------------|------|
| canvas_disabled | 0x00000001 | 关闭canvas指纹伪装 |
| webgl_disabled | 0x00000002 | 关闭webgl指纹伪装 |
| fake_webrtc_disabled | 0x00000004 | 关闭webrtc伪装。如果需要禁用webrtc，请勿设置这个标志位，只需要设置webrtc_disabled标志位 |
| audio_disabled | 0x00000008 | 关闭音频指纹伪装 |
| client_rect_disabled | 0x00000010 | 关闭client_rect伪装 |
| font_disabled | 0x00000020 | 关闭字体伪装 |
| timezone_disabled | 0x00000040 | 关闭时区伪装 |
| lang_disabled | 0x00000080 | 关闭语言伪装 |
| hardware_info_disabled | 0x00000100 | 关闭cpu内核数和内存大小的伪装 |
| geo_disabled | 0x00000200 | 关闭定位伪装，网络情况复杂的情况下建议关闭 |
| plugin_disabled | 0x00000400 | 关闭插件伪装 |
| battery_disabled | 0x00000800 | 关闭电池信息伪装 |
| browser_size_disabled | 0x00001000 | 关闭浏览器窗口大小伪装 |
| screen_size_disabled | 0x00002000 | 关闭屏幕大小伪装 |
| webrtc_disabled | 0x00004000 | 禁用webrtc，仅在fake_webrtc_disabled标志位未设置(即为0)时生效 |

这些设定，能够定义常量，它们控制了候鸟浏览器指纹伪装功能的开关。指纹伪装是一种技术，可以使候鸟浏览器隐匿一些浏览器属性，以保护用户隐私和防止追踪。

### 常见使用场景

#### 基础浏览场景

::: tip 普通网页浏览
适用场景：新闻网站、博客、资讯网站等日常浏览

推荐开启的伪装：
- Canvas指纹保护
- 字体伪装
- 插件信息保护

优点：
- 对网站兼容性影响最小
- 保持基本的隐私保护
- 浏览性能影响较小
:::

#### 社交媒体场景

::: tip 社交平台浏览
适用场景：Facebook、Twitter等社交网站

推荐开启的伪装：
- Canvas指纹保护
- WebGL指纹保护
- 音频指纹保护
- 字体伪装
- 时区伪装
- 语言伪装

可选开启：
- 地理位置保护（如果不需要基于位置的服务）
- WebRTC保护（如果不需要视频通话功能）

注意事项：
- 开启WebRTC保护会影响视频通话功能
- 地理位置保护可能影响基于位置的服务
:::

#### 电商购物场景

::: warning 网购支付场景
适用场景：网上购物、在线支付等

推荐开启的伪装：
- Canvas指纹保护
- 字体伪装
- 时区伪装
- 屏幕信息保护

特别提示：
- 支付环境建议使用较为保守的配置
- 确保支付功能正常使用
- 可以暂时关闭部分伪装以确保交易顺利
:::

#### 完全匿名场景

::: danger 高度隐私保护
适用场景：需要最高级别隐私保护的情况

建议开启全部伪装：
- 所有图形相关保护（Canvas、WebGL）
- 所有音频相关保护
- 所有系统信息保护（字体、时区、语言）
- 所有硬件信息保护
- 所有网络相关保护
- 所有设备信息保护

注意事项：
- 可能严重影响某些网站功能
- 建议仅在特殊场景使用
- 需要经常检查网站功能是否正常
:::

#### 场景对比表

| 场景 | 推荐配置 | 性能影响 | 兼容性 | 隐私等级 |
|------|----------|----------|---------|----------|
| 基础浏览 | 基础保护 | 低 | 极好 | ★★☆☆☆ |
| 社交媒体 | 中等保护 | 中 | 良好 | ★★★☆☆ |
| 电商购物 | 基础保护 | 低 | 很好 | ★★☆☆☆ |
| 完全匿名 | 全部保护 | 高 | 一般 | ★★★★★ |

#### 使用建议

::: tip 配置调整建议
1. 根据实际需求选择合适的保护级别
2. 可以为不同网站设置不同的保护级别
3. 遇到网站功能异常时，可以逐个关闭保护项进行测试
4. 定期检查配置是否符合当前使用需求
:::

#### 性能优化建议

::: warning 注意事项
1. 只启用必要的保护功能
2. 相同类型的网站使用相同的配置
3. 定期更新配置以适应网站变化
4. 留意保护开启后对页面加载速度的影响
:::

================================================== 英文内容 ==================================================
### Constant Definition Table

| Constant Name | Hexadecimal Value | Description |
|---------------|-------------------|-------------|
| canvas_disabled | 0x00000001 | Disable canvas fingerprint protection |
| webgl_disabled | 0x00000002 | Disable WebGL fingerprint protection |
| fake_webrtc_disabled | 0x00000004 | Disable WebRTC spoofing. To disable WebRTC completely, don't set this flag, use webrtc_disabled instead |
| audio_disabled | 0x00000008 | Disable audio fingerprint protection |
| client_rect_disabled | 0x00000010 | Disable client rect protection |
| font_disabled | 0x00000020 | Disable font protection |
| timezone_disabled | 0x00000040 | Disable timezone protection |
| lang_disabled | 0x00000080 | Disable language protection |
| hardware_info_disabled | 0x00000100 | Disable CPU cores and memory size protection |
| geo_disabled | 0x00000200 | Disable geolocation protection, recommended to disable in complex network situations |
| plugin_disabled | 0x00000400 | Disable plugin protection |
| battery_disabled | 0x00000800 | Disable battery information protection |
| browser_size_disabled | 0x00001000 | Disable browser window size protection |
| screen_size_disabled | 0x00002000 | Disable screen size protection |
| webrtc_disabled | 0x00004000 | Disable WebRTC completely, only effective when fake_webrtc_disabled is not set (equals 0) |

These settings define constants that control the browser's fingerprint protection features. Fingerprint protection is a technology that allows the browser to hide certain browser attributes to protect user privacy and prevent tracking.

### Common Usage Scenarios

#### Basic Browsing Scenario

::: tip Basic Web Browsing
Applicable to: News websites, blogs, information websites, and daily browsing

Recommended protections:
- Canvas fingerprint protection
- Font protection
- Plugin information protection

Advantages:
- Minimal impact on website compatibility
- Maintains basic privacy protection
- Low performance impact
:::

#### Social Media Scenario

::: tip Social Platform Browsing
Applicable to: Facebook, Twitter, and other social websites

Recommended protections:
- Canvas fingerprint protection
- WebGL fingerprint protection
- Audio fingerprint protection
- Font protection
- Timezone protection
- Language protection

Optional protections:
- Geolocation protection (if location-based services are not needed)
- WebRTC protection (if video call functionality is not needed)

Important notes:
- Enabling WebRTC protection will affect video call functionality
- Geolocation protection may affect location-based services
:::

#### E-commerce Shopping Scenario

::: warning Online Shopping and Payment
Applicable to: Online shopping, payment processing

Recommended protections:
- Canvas fingerprint protection
- Font protection
- Timezone protection
- Screen information protection

Special notes:
- Use conservative protection settings in payment environments
- Ensure payment functionality works properly
- Consider temporarily disabling certain protections to ensure smooth transactions
:::

#### Complete Anonymity Scenario

::: danger High Privacy Protection
Applicable to: Situations requiring maximum privacy protection

Recommended to enable all protections:
- All graphics-related protections (Canvas, WebGL)
- All audio-related protections
- All system information protections (font, timezone, language)
- All hardware information protections
- All network-related protections
- All device information protections

Important notes:
- May significantly affect certain website functionalities
- Recommended for special scenarios only
- Regular checking of website functionality is necessary
:::

#### Scenario Comparison Table

| Scenario | Recommended Configuration | Performance Impact | Compatibility | Privacy Level |
|----------|-------------------------|-------------------|---------------|---------------|
| Basic Browsing | Basic Protection | Low | Excellent | ★★☆☆☆ |
| Social Media | Medium Protection | Medium | Good | ★★★☆☆ |
| E-commerce | Basic Protection | Low | Very Good | ★★☆☆☆ |
| Complete Anonymity | Full Protection | High | Fair | ★★★★★ |

#### Usage Recommendations

::: tip Configuration Adjustment Tips
1. Choose appropriate protection levels based on actual needs
2. Set different protection levels for different websites
3. When encountering website issues, try disabling protections one by one for testing
4. Regularly review if configurations meet current usage requirements
:::

#### Performance Optimization Tips

::: warning Important Notes
1. Only enable necessary protection features
2. Use consistent configurations for similar types of websites
3. Regularly update configurations to adapt to website changes
4. Monitor the impact of protections on page loading speed
:::