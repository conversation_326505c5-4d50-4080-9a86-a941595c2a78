#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查是否还有文件需要进一步拆分
如果有超过指定大小的文件，进行进一步拆分
"""

import os
import re
from pathlib import Path
from docx import Document

def check_and_further_split(directory, max_size_kb=50):
    """检查并进一步拆分过大的文件"""
    docx_path = Path(directory)
    docx_files = list(docx_path.glob("*.docx"))
    
    print(f"📁 检查目录: {directory}")
    print(f"📊 找到 {len(docx_files)} 个DOCX文件")
    print(f"🎯 最大允许大小: {max_size_kb} KB")
    print("=" * 80)
    
    large_files = []
    total_size = 0
    
    for file_path in sorted(docx_files):
        try:
            file_size = file_path.stat().st_size / 1024  # KB
            total_size += file_size
            
            if file_size > max_size_kb:
                large_files.append({
                    'path': file_path,
                    'name': file_path.name,
                    'size_kb': file_size
                })
                print(f"🔴 需要进一步拆分: {file_path.name} ({file_size:.1f} KB)")
            else:
                print(f"✅ 大小合适: {file_path.name} ({file_size:.1f} KB)")
                
        except Exception as e:
            print(f"❌ 检查失败: {file_path.name} - {str(e)}")
    
    print("=" * 80)
    print(f"📊 检查结果:")
    print(f"   总文件数: {len(docx_files)}")
    print(f"   总大小: {total_size:.1f} KB")
    print(f"   平均大小: {total_size/len(docx_files):.1f} KB")
    print(f"   需要进一步拆分: {len(large_files)} 个文件")
    print(f"   大小合适: {len(docx_files) - len(large_files)} 个文件")
    
    if not large_files:
        print("\n🎉 所有文件大小都合适，无需进一步拆分！")
        return []
    
    print(f"\n🔄 开始进一步拆分 {len(large_files)} 个过大文件...")
    
    split_files = []
    
    for file_info in large_files:
        print(f"\n📄 处理文件: {file_info['name']}")
        
        try:
            doc = Document(str(file_info['path']))
            para_count = len(doc.paragraphs)
            table_count = len(doc.tables)
            
            print(f"   段落数: {para_count}, 表格数: {table_count}")
            
            # 计算需要拆分的部分数
            target_size_ratio = max_size_kb / file_info['size_kb']
            target_paragraphs_per_part = int(para_count * target_size_ratio * 0.8)  # 留一些余量
            num_parts = max(2, (para_count + target_paragraphs_per_part - 1) // target_paragraphs_per_part)
            
            print(f"   计划拆分为 {num_parts} 个部分")
            
            paragraphs_per_part = para_count // num_parts
            
            # 提取基础文件名（去掉.docx）
            base_name = file_info['name'].replace('.docx', '')
            
            # 删除原文件
            file_info['path'].unlink()
            print(f"   🗑️  删除原文件: {file_info['name']}")
            
            for part_num in range(num_parts):
                # 计算段落范围
                start_para = part_num * paragraphs_per_part
                if part_num == num_parts - 1:
                    end_para = para_count  # 最后一部分包含剩余段落
                else:
                    end_para = (part_num + 1) * paragraphs_per_part
                
                # 创建新文档
                new_doc = Document()
                
                # 添加标题
                title = f"{base_name}({part_num + 1})"
                new_doc.add_heading(title, 0)
                
                # 复制段落
                copied_paras = 0
                for para_idx in range(start_para, min(end_para, len(doc.paragraphs))):
                    try:
                        source_para = doc.paragraphs[para_idx]
                        new_para = new_doc.add_paragraph()
                        
                        # 复制段落样式
                        try:
                            new_para.style = source_para.style
                            new_para.alignment = source_para.alignment
                        except:
                            pass
                        
                        # 复制段落内容
                        for run in source_para.runs:
                            new_run = new_para.add_run(run.text)
                            new_run.bold = run.bold
                            new_run.italic = run.italic
                            new_run.underline = run.underline
                        
                        copied_paras += 1
                        
                    except Exception as e:
                        # 备选方案：只复制文本
                        new_doc.add_paragraph(doc.paragraphs[para_idx].text)
                        copied_paras += 1
                
                # 只在第一部分添加表格
                if part_num == 0 and table_count > 0:
                    for table_idx in range(min(2, table_count)):  # 最多添加2个表格
                        try:
                            source_table = doc.tables[table_idx]
                            rows = len(source_table.rows)
                            cols = len(source_table.columns) if source_table.rows else 0
                            
                            if rows > 0 and cols > 0:
                                new_table = new_doc.add_table(rows=rows, cols=cols)
                                new_table.style = 'Table Grid'
                                
                                for r, row in enumerate(source_table.rows):
                                    for c, cell in enumerate(row.cells):
                                        if r < len(new_table.rows) and c < len(new_table.rows[r].cells):
                                            new_table.cell(r, c).text = cell.text
                        except Exception as e:
                            print(f"     ⚠️  表格复制失败: {str(e)}")
                
                # 保存文件
                new_filename = f"{base_name}({part_num + 1}).docx"
                new_filepath = file_info['path'].parent / new_filename
                new_doc.save(str(new_filepath))
                split_files.append(str(new_filepath))
                
                # 检查生成文件的大小
                new_size = new_filepath.stat().st_size / 1024
                status = "✅" if new_size <= max_size_kb else "🔴"
                print(f"   {status} 生成: {new_filename} ({new_size:.1f} KB, {copied_paras}段落)")
        
        except Exception as e:
            print(f"   ❌ 拆分失败: {str(e)}")
    
    return split_files

def final_check_and_report(directory):
    """最终检查并生成报告"""
    docx_path = Path(directory)
    docx_files = list(docx_path.glob("*.docx"))
    
    print(f"\n📋 最终检查报告:")
    print("=" * 80)
    
    total_size = 0
    size_distribution = {
        'under_30': 0,
        '30_to_50': 0,
        '50_to_80': 0,
        'over_80': 0
    }
    
    for file_path in sorted(docx_files):
        try:
            file_size = file_path.stat().st_size / 1024
            total_size += file_size
            
            if file_size < 30:
                size_distribution['under_30'] += 1
                status = "🟢"
            elif file_size < 50:
                size_distribution['30_to_50'] += 1
                status = "✅"
            elif file_size < 80:
                size_distribution['50_to_80'] += 1
                status = "🟡"
            else:
                size_distribution['over_80'] += 1
                status = "🔴"
            
            print(f"{status} {file_path.name} ({file_size:.1f} KB)")
            
        except Exception as e:
            print(f"❌ {file_path.name} - 检查失败: {str(e)}")
    
    print("=" * 80)
    print(f"📊 最终统计:")
    print(f"   总文件数: {len(docx_files)}")
    print(f"   总大小: {total_size:.1f} KB")
    print(f"   平均大小: {total_size/len(docx_files):.1f} KB")
    print(f"\n📈 大小分布:")
    print(f"   🟢 < 30KB: {size_distribution['under_30']} 个文件")
    print(f"   ✅ 30-50KB: {size_distribution['30_to_50']} 个文件")
    print(f"   🟡 50-80KB: {size_distribution['50_to_80']} 个文件")
    print(f"   🔴 > 80KB: {size_distribution['over_80']} 个文件")
    
    if size_distribution['over_80'] == 0:
        print(f"\n🎉 所有文件都适合RAGFlow处理！")
    else:
        print(f"\n⚠️  还有 {size_distribution['over_80']} 个文件可能需要进一步处理")

if __name__ == "__main__":
    target_directory = r"F:\augment\output\docx_files2_split"
    
    if not Path(target_directory).exists():
        print(f"❌ 目录不存在: {target_directory}")
        exit(1)
    
    # 检查并进一步拆分
    split_files = check_and_further_split(target_directory, max_size_kb=50)
    
    if split_files:
        print(f"\n🎉 进一步拆分完成! 新生成了 {len(split_files)} 个文件")
    
    # 最终检查和报告
    final_check_and_report(target_directory)
