ussSystemFont 	默认值：false  使用系统字体：否

ussSystemFont 	默认值：false  使用系统字体：否

otrWindow

geometry      	REG_BINARY	几何模型（二进制）

sync

mmSyncSessions  	REG_BINARY	同步会话（二进制）

useragents

addedUserAgent  已添加的UserAgent  加UserAgent加码保存到此节点中。

付费用户信息节点（此处记录已登录成功用户信息）：

wangpeng

quick

bookmarks 书签 加码方式保存

lostCookies  丢失的cookies

LostCookiesAlert  保存明文串信息：For some reasons at 2020-7-25 15:16:52 your cookies in this sessions (美国0152) was not saved on server, we saved them localy. Do you want apply this settings and recover cookies ?

Passes 通行证  字符串加码保存

sessionDefaults  默认会话环境 @ByteArray() 字符串加码保存

sessions 当前会话@ByteArray() 字符串加码保存

websettings

anonChecker 异步检查器，此节点记录用户打开的TAB中显示的远程WEB服务器网页检测默认URL。 当前值为：

dateOverrider 默认值为0

disablePasswordAutofill 默认值： false 表示此功能默认打开状态。自动帮用户填写网页密码框中密码。

disableUrlHelper 默认值：false 表示此功能默认打开状态。

isBackgroundTabViewPreload 默认值：false  软件后台网页标签预加载，默认关闭。

searchEngine 默认搜索引擎。默认值：DuckDuckGo

standardFont 默认使用字体。默认值：二进制

第十一章 特殊场景与C/S各数据交互框架分支逻辑（扩展逻辑）

第十一章 特殊场景与C/S各数据交互框架分支逻辑（扩展逻辑）

第一节、服务器端心跳功能与客户端逻辑场景：

场景：

用户A电脑休眠时（客户端跟随电脑一并休眠【客户端未退出】），用户使用B电脑客户端登陆了账号，此时服务器端A客户端token已经失效，即用户如果继续打开A电脑客户端，此时服务器端返回invalid token2状态码，即token错误。此种情况下，A客户端由于操作系统被唤醒，A客户端仍旧显示为已登录状态。 此时客户端再进行任何接口操作将不会成功，由于token过期导致无法再使用服务器端接口。

处理方式说明：

在上述场景下，当服务器端token失效的情况下，用户使用的浏览器还是在正常运行（由于用户业务部份和服务器端没有直接关联，因此在此时，用户的业务还应处在正常运行状态。即保证用户的业务不出问题。）

此时，客户端要继续自动连到服务器上来（登录动作），即在后台自动登录并重新获取到新的token自动继续用户的业务（无须在前端提醒用户，客户端全后端自动化进行。）。

当客户端判断到心跳的token失效后，要自动重新获取有效token，无须用户再手工登录。

重点：自动登录失败后，且仅token失败时，才需要自动跳回到登录界面。

跳回登录界面有多种情况导致：

在其它机器上用户已经登录了。无法在后台自动登录获得    token的。

用户各种情况断网了，无法在后台自动登录获得token的。

用户的登录密码被修改过了，无法在后台自动登录获得token的。

第二节、客服平台体系完善，降低由于信息不对称，带来的与客户交流引发的矛盾。

在用户列表页面来显示下面的信息

客服通过在后台的用户帐号列表上

通过邮箱帐号来查找用户数据并显示在页面上

实现基础功能

- 通过列表查看用户客户端的所有FULL包版本数据、创建时间、版本号。

- 查看用户客户端中FULL包各版本的历史ITEM项。

- 支持查看用户的使用行为（客户端运行时间,关闭时间，使用时长），便于提供后台数据分析。

对于历史旧版本，用户未升级到新版本的情况下：

支持人工增加版本号作为客服应急手段。

历史旧版(1.0.1.2 - 1.0.1.3版)存在用户使用过程中因直接关机导致的客户端数据为空情况。

在用户未升级（或少数个别用户不愿升级到新版本）的情况下：

或在已出现旧版问题，同时又进行升级后问题未解决的，需要人工客服来处理。

客户平台 要提供并显示

1、用户登录的日志列表。

2、用户自行操作强制离线的日志点击列表。

3、LOG上传按钮

其中，LOG上传按钮在用户因各种因素导致软件产品使用异常时作为一项重要客服和技术部运维手段，需在2021. 1月份上旬完成此工作要求。

客户端：客户端对log的大小控制工作，在团协版第二版前要完成。

对于超过3天的log信息要在写入前进行清理。

服务器端：提供发出指令要求指定帐户下的客户端将log打包进行上传。

(接口已在第七版团协接口文档中提供。)

以上工作可改善客服在做服务时，由于客服方基于的用户数据信息缺失，在交流中往往处于非常被动的情况。

二、扩展功能 (第13版新增)

1、提供内部使用的动态密码，通过管理员后台自动显示。可通过动态密码登录任意会员控制台进行数据查看和客服使用。

2、

A、可强制客户端离线。

B、点击环境数量可以查看环境数量列表。

C、增加按钮显示用户的full包列表，在full包列表点击后显示此full包的内部信息（总计环境数量、环境名称、分享环境数量，分享环境信息）

D、支持对FULL环境包进行合法性校验。

E、通过列表支持点击下载指定的FULL环境包。

[2022-06-08 新增]

第十二章

【浏览器自动化脚本管理体系】

客户端候鸟浏览器内核
脚本全自动安装
服务器端客户自动化脚本数据
自动整合并保证唯一
逻辑详细流程及约定。候鸟puppteer/selenium框架，候鸟浏览器自动化开发【章节第五版20220708校订】

第十二章 候鸟自动化工程puppteer/selenium框架【章节第五版20220708校订】

使用此框架的竞争对手产品有：vmlogin, mutilogin

Selenium 是一个用于Web应用程序测试的工具。Selenium测试直接运行在浏览器中，就像真正的用户在操作一样。支持的浏览器包括IE（7, 8, 9, 10, 11），Mozilla Firefox，Safari，Google Chrome，Opera等。这个工具的主要功能包括：测试与浏览器的兼容性——测试你的应用程序看是否能够很好得工作在不同浏览器和操作系统之上。测试系统功能——创建回归测试检验软件功能和用户需求。支持自动录制动作和自动生成 .Net、Java、Perl等不同语言的测试脚本。

框架底层使用JavaScript模拟真实用户对浏览器进行操作。测试脚本执行时，浏览器自动按照脚本代码做出点击，输入，打开，验证等操作，就像真实用户所做的一样，从终端用户的角度测试应用程序。

使浏览器兼容性测试自动化成为可能，尽管在不同的浏览器上依然有细微的差别。

使用简单，可使用Java，Python等多种语言编写用例脚本

据 Selenium 主页所描述，与其他测试工具相比，使用 Selenium 的最大好处是：

Selenium 测试直接在浏览器中运行，就像真实用户所做的一样。Selenium 测试可以在 Windows、Linux 和 Macintosh上的 Internet Explorer、Chrome和 Firefox 中运行。其他测试工具都不能覆盖如此多的平台。使用 Selenium 和在浏览器中运行测试还有很多其他好处。

下面是主要的两大好处：

通过编写模仿用户操作的 Selenium 测试脚本，可以从终端用户的角度来测试应用程序。通过在不同浏览器中运行测试，更容易发现浏览器的不兼容性。Selenium 的核心，也称browser bot，是用 JavaScript 编写的。这使得测试脚本可以在受支持的浏览器中运行。browser bot 负责执行从测试脚本接收到的命令，测试脚本要么是用 HTML 的表布局编写的，要么是使用一种受支持的编程语言编写的。

Selenium 2.0适用于以下浏览器：

Google Chrome

Internet Explorer 7, 8, 9, 10, 11

Firefox

Safari

Opera

HtmlUnit

phantomjs

Android

iOS

【Puppeteer】

什么是Puppeteer?

Puppeteer 是一个 Node 库，它提供了一个高级 API 来通过 DevTools 协议控制 Chromium 或 Chrome。我们来看看Puppeteer单词原本的意思：

Puppete 的原意为木偶，即很多90后小时候听的木偶奇遇记的故事里面讲述的那个说谎后鼻子会变长的“小孩”。根据这个单词的意思，我们就很清楚的明白Puppeteer的执行原理就是像操纵木偶的人一样，通过各种方式(接口)进行操纵浏览器帮你执行各种操作的工具。

Puppeteer 能帮我们具体做什么？

Puppeteer是一个Node库，由Chrome官方团队进行维护，提供接口来控制headless Chrome。Headless Chrome是一种不使用Chrome来运行Chrome浏览器的方式。简单的来说就是一个运行在命令行中的 chrome,我们可以通过代码来实现我们常规的浏览器浏览网页的功能。这样就能保证我们拿到的内容和正常通过浏览器查看的是一模一样的！

简单来说，你能在浏览器里做的大部分事情，Puppeteer都可以做！像打开标签页，打开百度，输入搜索关键词，点击搜索，点击下一页....等等你的日常操作行为都能模拟。

生成页面 PDF抓取 SPA（单页应用）并生成预渲染内容（即“SSR”（服务器端渲染））自动提交表单，进行 UI 测试，键盘输入等创建一个实时更新的自动化测试环境使用最新的 JavaScript 和浏览器功能直接在最新版本的Chrome中执行测试捕获网站的timeline trace，帮助你分析该网站的性能问题测试浏览器的自动化扩展

Puppeteer 为什么可以做到控制浏览器？
Puppeteer之所以可以帮助我们做这么多，首先我们需要分析下Puppeteer的整体架构:

我们可以从图中看到Puppeteer将浏览器分为了几个部分：Browser（浏览器）,Context(上下文)，Page(页面或标签)，Workers(工人)等部分，他们之间的关系是：

Puppeteer 使用 DevTools 协议 与浏览器进行通信。Browser 实例可以拥有浏览器上下文。BrowserContext 实例定义了一个浏览会话并可拥有多个页面。Page 至少有一个框架：主框架。可能还有其他框架由 iframe 或 框架标签 创建。frame 至少有一个执行上下文 - 默认的执行上下文 - 框架的 JavaScript 被执行。一个框架可能有额外的与 扩展 关联的执行上下文。Worker 具有单一执行上下文，并且便于与 WebWorkers 进行交互。

Puppeteer VS Puppeteer-core？
Puppeteer从v1.7.0开始，每个版本都会发布两个包：
PuppeteerPuppeteer-core

Puppeteer 核心功能

利用网页生成PDF、图片
爬取SPA应用，并生成预渲染内容（即“SSR” 服务端渲染）
可以从网站抓取内容
自动化表单提交、UI测试、键盘输入等
帮你创建一个最新的自动化测试环境（chrome），可以直接在此运行测试用例
捕获站点的时间线，以便追踪你的网站，帮助分析网站性能问题

Puppeteer是浏览器自动化的产品。安装后，它会下载一个版本的 Chromium，然后使用Puppeteer-core 驱动工作。作为最终用户产品，Puppeteer支持一堆方便的 PUPPETEER* env 变量来调整行为。Puppeteer-core 是一个核心库来帮助驱动任何支持 DevTools 协议的东西。Puppeteer-core 在安装时不会下载 Chromium。作为一个库，Puppeteer-core 是完全是通过其编程接口驱动的并忽略所有PUPPETEER* env 变量。

总结一下，Puppeteer-core 与 Puppeteer不同的地方：

Puppeteer-core 在安装时不会自动下载 ChromiumPuppeteer-core忽略所有的 PUPPETEER_* env 变量大多数情况下，你可以使用Puppeteer来进行实际的应用开发，而如果是考虑下载及打包速度问题或者正在构建一个DevTools协议顶部的产品库，这时你可以选择使用Puppeteer-core来进行开发。

了解了Puppeteer的背景及功能，很多朋友都会猜到为什么Puppeteer被称之为爬虫界的扫地僧了吧？因为其模拟的是真实用户的操作，从打开标签页，到输入关键词，甚至在输入过程中模拟用户打字速度的这种精细操作都会严重混淆服务端风控服务的判断，那一般的风控系统会从几个方面进行反爬虫？接口调用频次限制同IP的访问频次验证码登录token验证链接随机化Request请求头验证......但这些验证对Puppeteer来说，基本无效。因为对于整个请求方式和行为判断上讲，用户的真实场景模拟会直接宣布以上拦截方案的死刑。

=====================================================


================================================== 表格内容 ==================================================

{"message": "Success","status": 0,"data": null}

参数名称 | 类型 | 必传 | 样例串/默认值 | 说明
Account | string | 否 | <EMAIL> | 返回此account注册相关信息