﻿<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Window size="1116,670" caption="0,0,0,80" mininfo="1116,670" sizebox="4,4,4,4" roundcorner="5,5,5,5" showshadow="true" shadowsize="3" shadowposition="0,0" shadowcolor="#ff6272A4" fademode="true">
	<Include source="Default.xml" />

  <VerticalLayout name="MainFrame" width="953" height="590" bkcolor="#FF282A36">

    <HorizontalLayout height="37">

    	 <Button name="login" bkimage="icon.png" padding="8,6,0,0" width="28" height="28"/>
      <Label name="title" padding="6,4,0,0" text="候鸟指纹浏览器平铺管理器"  textpadding="0,0,6,0" texttooltip="true" endellipsis="true" width="180" textcolor="#FF616161" hottextcolor="#ff000000" align="left" font="8"></Label>
      <HorizontalLayout >
        <Slider name="Slider" padding="18,12,0,0" enabled="true" sendmove="true" showthumb="true" height="20" bkimage="slider\SliderBack.png" foreimage="slider\SliderFore.png" foreimage2="slider\SliderFore2.png" min="0" max="100" value="50" hor="true"  thumbhotimage="file=&apos;slider\SliderBar.png&apos; source=&apos;21,0,41,20&apos;" thumbpushedimage="file=&apos;slider\SliderBar.png&apos; source=&apos;42,0,62,20&apos;" thumbsize="20,20" />
      </HorizontalLayout>
      <Control width="20"/>
      <HorizontalLayout height="32" width="280">
        <Label name="lewidth" padding="0,4,0,0" text="宽度:" width="80" textcolor="#FF000000" hottextcolor="#ff000000" align="right" font="8"></Label>
        <RichEdit name="ewidth" text="450px" wanttab="false" padding="5,6,5,0" height="26" width="60" killfocusimage="" bkimage="file_modify_normal.png" font="0" textpadding="6,5,2,0" tipvaluecolor="ff333333" multiline="false" maxchar="5" textcolor="ff666666" rich="false">
        </RichEdit>
        <Label name="leheight" padding="0,4,0,0" text="高度:" width="80" textcolor="#FF000000" hottextcolor="#ff000000" align="right" font="8"></Label>
        <RichEdit name="eheight" text="300px" wanttab="false" padding="5,6,5,0" height="26" width="60" killfocusimage="" bkimage="file_modify_normal.png" font="0" textpadding="6,5,2,0" tipvaluecolor="ff333333" multiline="false" maxchar="5" textcolor="ff666666" rich="false">
        </RichEdit>
      </HorizontalLayout>
      <Control width="20"/>
      <CheckBox name="wh_switch" selected="false" padding="14,8,0,10" width="52" height="24"  normalimage="switch_off.png" selectedimage="switch_on.png" disabledimage="switch_off.png" />
      <!--Button name="minbtn" width="28" height="26" tooltip="最小化" normalimage="minbtn.png" hotimage="minbtn_hover.png" pushedimage="minbtnpush.png" />
      <Button name="maxbtn" width="28" height="26" tooltip="最大化" normalimage="maxbtn.png" hotimage="maxbtn_hover.png" pushedimage="maxbtnpush.png" />
      <Button name="restorebtn" visible="false" width="28" height="26" tooltip="还原" normalimage="restorebtn.png" hotimage="restorebtn_hover.png" pushedimage="restorebtnpush.png" /-->
      <Button name="closebtn" width="28" height="26"  tooltip="关闭" normalimage="closebtn.png" hotimage="closebtn_hover.png" pushedimage="closebtnpush.png" />

    </HorizontalLayout>

   <HorizontalLayout name="bkground" visible="true">

		<VerticalLayout name="bkgroundv" visible="true">
			<HorizontalLayout bkcolor="#FFffffff">
        <ChromeList name="list_session_manager"  scrollwheel="true" reselect="true" bordersize="1,1,1,1" itembkcolor="#FFffffff" itemselectedbkcolor="#FFffffff" itemhotbkcolor="#FFffffff" bordercolor="#FFD9DADB" vscrollbar="true">
        </ChromeList>
      </HorizontalLayout>
		</VerticalLayout>

     <VerticalLayout name="bkgroundvnone" visible="false">
       <Control />
       <HorizontalLayout height="116">
         <Control />
           <Label width="100" height="116" bkimage="none.png"></Label>
          <Control />
       </HorizontalLayout>
         <HorizontalLayout height="60">
         <Control />
           <Label name="ExplorerTip" height="20" padding="0,30,0,0" text="请运行您的浏览器会话环境" autocalcwidth="true" textcolor="#FFadadad" hottextcolor="#ff014e9d" align="center" font="11"></Label>
          <Control />
       </HorizontalLayout>
       <Control />
     </VerticalLayout>

   </HorizontalLayout>

    <HorizontalLayout height="52" bkcolor="#ffe9e9e9">
      <CheckBox name="opt_checkAll" text="" padding="16,16,0,0" height="24" width="24" normalimage="slider\check.png" hotimage="slider\checkClick.png" selectedimage="slider\checked.png" ></CheckBox>
      <Label name="lcheckall" padding="10,4,0,0" text="全选" width="32" textcolor="#FF000000" hottextcolor="#ff000000" align="right" font="8"></Label>
      <!--<Combo name="freshtime" padding="16,16,0,0" textpadding="10,0,40,0" itemalign="center" bkimage="combo_normal_large.png" hotimage="combo_hot_large.png" pushedimage="combo_pushed_large.png" width="155" height="25"  itemhotbkcolor="fff1f1f1" itemselectedbkcolor="ffffffff">
        <ListLabelElement name="cfgencrypt" text="5秒" selected="true"/>
        <ListLabelElement name="cfgencryptnot" text="10秒" />
      </Combo>
      <Label name="lewidth" padding="0,4,0,0" text="监控浏览器间隔时间" width="136" textcolor="#FF000000" hottextcolor="#ff000000" align="right" font="8"></Label>
      <Label name="lewidth" padding="0,4,0,0" text="秒" width="14" textcolor="#FF000000" hottextcolor="#ff000000" align="right" font="8"></Label>-->
      <VerticalLayout width="200">

       </VerticalLayout>

      <Control />

      <HorizontalLayout width="700" >
        <Label name="expwnds" padding="0,4,0,0" text="" width="460" bkcolorcolor="#ffff0000" textcolor="#FF000000" hottextcolor="#ff000000" align="left" font="8"></Label>
        <Label name="lwndsmsg1" padding="0,4,0,0" text="已检测到" texttooltip="true" endellipsis="true" width="60" textcolor="#FF000000" hottextcolor="#ff000000" align="left" font="8"></Label>
        <Label name="lcount" padding="0,4,0,0" text="0" width="40" textcolor="#FF00ff00" hottextcolor="#ff000000" align="center" font="8"></Label>
        <Label name="lwndsmsg2" padding="0,4,0,0" text="个浏览器正在运行中" texttooltip="true" endellipsis="true" width="160" textcolor="#FF000000" hottextcolor="#ff000000" align="left" font="8"></Label>
      </HorizontalLayout>

      <VerticalLayout width="140">
        <Control />
        <Button text="关闭浏览器窗口" name="closeex" enabled="false" padding="20,2,0,0" textpadding="5,0,5,0" texttooltip="true" endellipsis="true" width="120" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	bkimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
        <Control />
      </VerticalLayout>

      <VerticalLayout width="140">
        <Control />
        <Button text="退出" name="closebtn"  padding="20,2,0,0" width="120" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	bkimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
        <Control />
      </VerticalLayout>

      <Control width="20" />
    </HorizontalLayout>
  </VerticalLayout>
</Window>
