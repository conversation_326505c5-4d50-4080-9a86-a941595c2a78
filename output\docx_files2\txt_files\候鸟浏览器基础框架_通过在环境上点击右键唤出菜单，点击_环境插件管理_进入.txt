通过在环境上点击右键唤出菜单，点击 环境插件管理 进入

通过在环境上点击右键唤出菜单，点击 环境插件管理 进入

方式一和方式二的区别： 方式二进入环境插件管理器时，默认焦点指向到 环境二的插件管理。

浏览器插件管理器界面：

功能区域分布图：

插件管理器界面功能说明

在插件管理器中，您可以为您的环境一键添加、删除、查找、分配各种插件。支持单个插件的安装，也支持批量一次性插件安装。

如图示，A区：

下拉菜单项：

【已安装过的浏览器插件】

列出您已经安装过的所有插件的总集合，即您在历史中曾经安装过哪些插件，当前您的所有环境中已安装过的插件，都会在这个列表里显示出来。如果您删除此列表中的插件，候鸟产品会将会将此插件从您本地硬盘插件目录中删除，但不会删除您的环境中此插件。

【使用官方所有浏览器插件】

列出了候鸟官方提供的所有谷歌市场插件，这些插件来源于GOOGLE市场，优点在于您无需进入GOOGLE市场即可通过此列表一键安装到您的环境中。

如果您在此列表中未找到需要的插件，您仍需要进入谷歌插件市场，在候鸟浏览器页面中下载插件并进行常规安装。

请输入关键字查找插件：

插件管理器支持您动态实时查找需要的插件，无须在列表中逐个人工检索，通过查找插件功能，可快速定位到您需要的插件上并进行安装。

A区 列表：

操作列：勾选框 在勾选后，即可进行后续操作。

插件名称：列出插件的正式名称，正式名称与GOOGLE市场的插件名称完全一致。

插件版本：列出官方可以提供下载安装的所有版本。

描述：商业插件官方描述，由插件作者提供，通过将光标移到描述上可以快速查看描述的完整内容而无须拖动插件管理器窗体大小。

如图示，B区：

B区主要功能是列出您的所有环境，并提供已安装过插件的环境集合，并提供环境搜索功能，方便您定位到需要处理的环境。提供每个环境已安装的插件数量，让您很直观的进行环境的插件分配与管理。

【已安装插件的环境】

如果您曾经在某些环境中安装过插件，则进入插件管理器，此项默认显示。默认列出已安装插件的环境集合。

【其它】

列出未分组的环境和已分组的环境分组。

【请输入关键字查找会话】

输入您的会话环境关键字，可快速列出您需要操作的环境。

B区列表：

操作列：勾选框 在勾选后，即可进行后续操作。

环境名称：列出您此分组下环境的名称。

创建时间：列出您的环境的创建时间。

插件数：显示您的环境中已安装的插件数量。

如图示，C区：

C 区主要功能是列出您的某个环境中已安装的插件详单，并提供此环境下插件的管理。默认在未选择环境时，C区插件列表为空。仅在您勾选或点击某个环境时，或您在勾选多个环境时，列出环境的插件详单。

操作重点：

点击B区环境 和 勾选B区环境之区别：

当您 仅 点击（选中）B区某个环境，C区只会列出此单个环境下的所有插件详单。如果您同时点击了B区多个环境，C区也仅会列出最后点击的那单个环境的所有插件。

当您 勾选（在操作列勾选了小方框）某个环境，C区会列出已勾选

的此环境所有插件详单。 如果您同时勾选了B区多个环境，C区会列出这些多个环境中的所有插件详单。

安装新插件

安装插件的过程，是从A区到B区再到C区，再点击插件管理器右下角按钮的过程。

安装方式一：

通过【候鸟官方插件库】来安装插件到我的环境中：

第一步：在A区 选择 使用官方所有浏览器插件 下拉项。

第二步：在A区列表中，找到自已需要安装的插件，并勾选此插件。

第三步：在B区列表中，勾选要将此插件安装到的那些环境上。

第四步：点击按钮 将选中的插件安装到勾选的环境中。

如上图所示，

此时表示此插件已成功安装到 2个环境中，此时您可以在B区和C区看到 环境三和环境四的插件数由0变为了1。表示已成功安装。同时在C区可以看到环境三和环境四的插件列表中已出现刚刚安装的插件。

点击 已安装过的浏览器插件，您可以看到，刚刚从官方插件库中下载安装的插件已分别保存到了您的本地硬盘插件目录和环境目录中。

分配多个插件到各个环境中。

通过批量分配多个插件到多个环境中，可以实现一键批量将插件安装到您指定的环境中，而且支持您在异地无须再次安装插件使用，真正做到了一次分配（安装），在任何地点随心使用。

通过【已安装过的浏览器插件】列表进行分配：

如图，选择A区 已安装过的浏览器插件 下拉项，您可以直接将本地的插件指派到B区指定的环境中，如果勾选的插件在本地候鸟目录中存在，则这是一个本地全自动安装的过程，如果勾选的插件在您的本地硬盘中不存在，则会自动从官方插件库下载，并再次保存到您的本地硬盘中，之后会自动再进行指定的环境中插件安装。

批量分配（安装）完成后，成功分配 (安装) 的插件会显示在C区列表中。

注：如已安装过相同的插件到环境中，则再次安装不会影响原已安装的插件使用。

通过【使用官方所有浏览器插件】列表进行分配：

如图，选择A区 使用官方所有浏览器插件 下拉项，您可以直接将官方插件库的插件指派到B区指定的环境中，如果勾选的插件在本地候鸟目录中存在，则这是一个本地全自动安装的过程，如果勾选的插件在您的本地硬盘中不存在，则会自动从官方插件库下载，并再次保存到您的本地硬盘中，之后会自动再进行指定的环境中插件安装。

批量分配（安装）完成后，成功分配 (安装) 的插件会显示在C区列表中。

删除指定环境插件

当您发现某些 商业环境不再需要使用某些插件，您可以对指定的环境进行插件删除操作。

注：您无法对官方插件库提供的插件进行删除，但您可以对自有已安装的插件进行删除。

A区 本地插件目录中指定插件进行删除：

通过在A区，选择 已安装过的浏览器插件，勾选要删除的插件，点击删除插件。候鸟插件管理器 仅会删除您的硬盘中此插件的插件目录中的此插件数据副本。 并不会删除您的浏览器环境中的此插件数据存根。因此，您对A区列表的插件进行删除，并不会影响到您环境中的插件和插件数据。您此时删除的仅仅为本地硬盘中的曾经下载过的插件副本。

也就是说，您虽然在A区列表中看不到此插件了，但是在环境的插件列表中（C区列表），仍旧可以看到此插件。

那么，如果我想彻底删除环境下的指定插件，该怎么操作呢？

如图：在B区勾选环境八，可以看到C区已列出此环境下所有插件。 勾选其中单个或多个插件。点击删除插件。将会立即彻底删除此环境下指定的插件。

导致的结果如下：

1、此方式删除插件后此环境将不再拥有此插件带来的所有功能。

2、您在异地使用产品，此环境也不再看到此插件和此插件带来的所有功能。

3、如果需要再次使用此插件，您必须通过在A区进行重新安装此插件过程。

批量删除环境中的指定插件


================================================== 表格内容 ==================================================

{
    "code": 0,
    "data": {
        "Delete_All_Session_Success_cont": 118
       },
    "message": "All Session Delete Finished."
}

{
    "code": -12,
    "message": "All Session Delete Failed."
}