标题: 批量本地帐户导入使用
英文标题: Bulk Local Account Import Using
ID: 113
分类ID: 7
添加时间: **********
更新时间: **********
访问次数: 0
SEO标题: 候鸟浏览器批量本地帐户导入使用
SEO关键词: 候鸟浏览器批量本地帐户导入使用
SEO描述: 候鸟浏览器批量本地帐户导入使用

================================================== 内容 ==================================================
# 候鸟 批量本地帐户导入 使用说明书
候鸟浏览器批量本地帐户导入支持，一键将批量购买的商业帐户，一次性导入到候鸟环境并即时使用。
候鸟支持无限制数量级帐户一键导入生成为商业环境。所有环境的代理帐户管理、登录帐户管理、会话COOKIE管理、环境配置状态一目了然，支持将多个帐户、代理、COOKIE一键批量指派到海量的环境中，并实时生效。海量环境的批量配置仅需要数秒即可完成，彻底摒除以往繁杂的逐个环境配置，逐个帐户添加、维护等业务日常管理。

### 候鸟 浏览器帐户批量本地导入 使用前述
基于Google chrome浏览器研发的候鸟浏览器专有商用内核， 开启全面支持批量帐户导入服务。用户可通过直观的导入引导窗口，将无限数量的帐户直接导入到候鸟浏览器中生成各个商业环境，支持直接在导入到候鸟浏览器之前，进行帐户批量的修改调整，也支持在导入后生成的环境中，对海量环境进行批量配置，扩容，与动态调整。环境数据在本地银行级256位加密模式存储，可100%保证数据不会泄密。候鸟浏览器通过自带的300万条官方商业UA库（商业UA库由候鸟官方定期更新，来保证您的业务安全），提供给您在配置环境时提供最优的操作系统、语言、时区、国家、地区、纯净的动态IP/静态IP来为您的业务保驾护航。避免大幅度修改的高稳定性能、高安全性能CHROMIUM内核保持与GOOGLE的所有底层模块、应用层模块完全一致，来保证外界网站服务器对您的业务运营高度的稳定性。

下面通过图文模式，详细描述候鸟浏览器帐户批量导入的使用方法：

### 一、开启并进入 本地帐户批量导入
启动候鸟客户端，点击主面板左上角的功能菜单，选择 **本地帐户批量导入** 并点击进入 本地帐户批量导入窗口。

<p><img src="6d7b2882624511f09a0d0242ac130006/images/image_9260b2f6426f.png" width="360" /></p>

本地帐户批量导入 界面：

![](6d7b2882624511f09a0d0242ac130006/images/image_597706506c13.png)

说明：
在此界面上，您可以看到左上角的桔黄色小铃档在动态的跳动着，在其右侧有一个按钮：**环境导入模板(xlsx)** 。 
此按钮点击后可自动调用office的excel软件打开此文件。
**将您购买的 帐户数据 粘帖入此文件中。**

如图：

![](6d7b2882624511f09a0d0242ac130006/images/image_b0ed1c18de86.png)

温馨提示：
**这里要注意的是：当您保存此EXCEL文件时，建议使用另存为到桌面的方式，并将XLSX文件重命名。**


确认您的数据粘帖到EXCEL表格里并保存好后，点击此按钮，开始导入：

![](6d7b2882624511f09a0d0242ac130006/images/image_2ea2f8ec9d2e.png)

![](6d7b2882624511f09a0d0242ac130006/images/image_f20fccaf4fe1.png)

找到刚刚保存的EXCEL文件，选择并点击打开。

![](6d7b2882624511f09a0d0242ac130006/images/image_9b29654775b1.png)

此时，您可以看到，候鸟已正确的成功预加载完成所有EXCEL中的数据并显示出来。

开始 检查列表中的各项值是否符合您的要求。

### 本地帐户批时导入窗体说明
1、在导入的数据量庞大的时侯，您可以通过左上角的搜索功能，直接找到重要的导入项并再次检查数据是否正确。
2、如果导入的数据不符合您的预期，需要重新修改EXCEL，重新进行导入，则可以点击 右上角 返回上一层/重新导入 进行二次导入操作，当您回到上一层后，之前窗体里加载的列表数据将自动抛弃，同时等待您重新导入。
3、通过本地帐户批时导入，此窗体，您可以修改单个待导入项的各列数据，也可以批量修改多个待导入项的各项数据。

![](6d7b2882624511f09a0d0242ac130006/images/image_a6f4e5eca1bb.png)

**勾选需要修改的待导入项，此时窗体右上角的下拉列表亮起，点击下拉列表。**

下面就各项进行说明：
A、批量更新已勾选环境名称。

![](6d7b2882624511f09a0d0242ac130006/images/image_f3a99befc9a8.png)

此功能可批量更新待导入项的所有环境名称，按数字序号递增。

![](6d7b2882624511f09a0d0242ac130006/images/image_605966d0d33a.png)

B、批量更新已勾选环境代理配置，此功能可批量替换 已勾选的待导入项的代理项各值。

![](6d7b2882624511f09a0d0242ac130006/images/image_35fff10e622c.png)

C、更换环境登录帐号，此功能可批量替换 已勾选的待导入项的登录帐户项各值。

![](6d7b2882624511f09a0d0242ac130006/images/image_e6f3a447eedf.png)

D、更新环境注释，此功能可批量替换 已勾选的待导入项的 环境注释。

![](6d7b2882624511f09a0d0242ac130006/images/image_3b6b7a5972e6.png)

E、UserAgent批量替换，此功能可批量替换 已勾选的待导入项的各USERAGENT值。**在更换新值的时侯，先添加一条新的UA到列表中，或在列表中直接选取一条UA，并打勾，再点击右下角的选择，即可替换完成。**

回到当前的窗口：

![](6d7b2882624511f09a0d0242ac130006/images/image_ecf7ffbb391b.png)

如果不需要修改待导入项的各值，或已修改完成。

![](6d7b2882624511f09a0d0242ac130006/images/image_31c958fd13d7.png)

开始选择导入的分组：

如果您需要将导入项导入到一个新分组中，可点击：** 环境分组管理器**

![](6d7b2882624511f09a0d0242ac130006/images/image_171bb227161d.png)

在弹出的 环境分组管理器里，点击 **创建新分组**，并填入新分组的名称。
这里演示 创建一个新分组名称为：** 我的第一个新分组**
新分组创建完成后，可在 环境分组管理器里 左上角的下拉列表中看到。

![](6d7b2882624511f09a0d0242ac130006/images/image_89191e5a43d5.png)

然后关闭此 分组管理器窗口，回到 **本地帐户批量导入** 窗口。

分组选择 **我的第一个新分组** 
点击 勾选并导入到指定环境分组 按钮
此时，可以看到所有的数据已成功导入。

![](6d7b2882624511f09a0d0242ac130006/images/image_36ac486cd253.png)

注意：如果已存在的环境名称与待导入项的环境名称相同，则会导致相同环境名称的待导入项在导入过程中失败，此时需将有重名的待导入项的环境名称更换后，再进行导入。

<p><img src="6d7b2882624511f09a0d0242ac130006/images/image_9f0eef0d752d.png" width="360" /></p>

点击分组，选择 **我的第一个新分组**
**即可看到已生成为：正式业务环境的 成功导入的各项即时可用的业务环境。**

================================================== 英文内容 ==================================================
# Mbbrowser Import local accounts in batches
Mbbrowser supports batch local account import. With one click, commercial accounts purchased in bulk can be imported to the migratory bird environment for immediate use.
Mbbrowser supports unlimited order of magnitude account one-click import generated into a business environment. Agent account management, login account management, session COOKIE management, and environment configuration status of all environments are clear. Multiple accounts, agents, and cookies can be assigned to massive environments in batches with one key and take effect in real time. The batch configuration of massive environments only takes a few seconds to complete, completely abandoning the previous complex environment configuration, account addition, maintenance and other business routine management.

### Mbbrowser Use the preceding methods to import browser accounts locally in batches
The dedicated commercial kernel of the Migrating Bird browser developed based on Google chrome enables the service of batch account import. Users can import an unlimited number of accounts into the Migrating Bird Browser through the intuitive import guide window to generate various business environments. You can modify and adjust accounts in batches before importing them to the Migrating Bird Browser. You can also batch configure, expand, and dynamically adjust massive environments in the generated environments after import. Environment data is stored in the local bank-level 256-bit encryption mode, which ensures 100% data leakage. With 3 million official commercial UA libraries (which are regularly updated by Migratory Bird officials to ensure the security of your business), Migratory Bird Browser provides you with the best operating system, language, time zone, country, region, and pure dynamic/static IP to protect your business when configuring the environment. Avoid greatly modified CHROMIUM kernel with high stability performance and high security performance, keep consistent with all the underlying modules and application layer modules of GOOGLE, so as to ensure the high stability of external website servers for your business operation.

The following is a detailed description of the batch import of migratory bird browser accounts in graphic mode:

### Enable and import local accounts in batches
Start the Migratory Bird client, click the function menu at the upper left corner of the main panel, select **Local Account Batch Import** and click to enter the Local Account Batch Import window.

<p><img src="6d7b2882624511f09a0d0242ac130006/images/image_946d89b32fd8.png" width="360" /></p>

Import local accounts in batches:

![](6d7b2882624511f09a0d0242ac130006/images/image_2ba61a4b4fc6.png)

Explanation:
On this screen, you can see the small orange bell in the upper left corner of the dynamic beat, there is a button on the right: **Environment Import Template (xlsx)**.
Click this button to automatically call excel software of office to open this file.
**Paste your purchased account data into this file. **

As shown below:

![](6d7b2882624511f09a0d0242ac130006/images/image_b0ed1c18de86.png)

Warm tips:
**Note here: When you save this EXCEL file, it is recommended to use save as to the desktop and rename the XLSX file. **


After confirming that your data is glued to EXCEL and saved, click this button to start importing:

![](6d7b2882624511f09a0d0242ac130006/images/image_a4ffad343ef1.png)

![](6d7b2882624511f09a0d0242ac130006/images/image_b3a1c7635513.png)

Locate the EXCEL file you just saved, select it, and click Open.

![](6d7b2882624511f09a0d0242ac130006/images/image_9710ae98345d.png)

At this point, you can see that Migratory Bird has successfully preloaded all EXCEL data correctly and is displayed.

Start checking that the values in the list match your requirements.

### Local account batch import form description
1. When the amount of imported data is large, you can directly find important imported items and check whether the data is correct through the search function in the upper left corner.
2. If the imported data does not meet your expectations and you need to modify EXCEL and import again, you can click the upper right corner to return to the previous layer/re-import for the second import operation. When you return to the previous layer, the list data loaded in the previous form will be automatically discarded and waiting for you to import again.
3. Batch import through a local account. In this form, you can modify the column data of a single item to be imported or batch modify the data of multiple items to be imported.

![](6d7b2882624511f09a0d0242ac130006/images/image_748ca6289711.png)

**Select the items you want to modify and the drop-down list in the upper right corner of the window will light up. Click the drop-down list. **

The following is a description of each:
A. Update selected environment names in batches.

![](6d7b2882624511f09a0d0242ac130006/images/image_d893b7285b11.png)

This function updates all environment names of the items to be imported in batches, increasing in number.

![](6d7b2882624511f09a0d0242ac130006/images/image_f41605100aea.png)

B. Batch update the selected environmental agent configurations. This function can replace the selected proxy values in batches.

![](6d7b2882624511f09a0d0242ac130006/images/image_69fa2f4d69dc.png)

C. Change the environment login account. This function replaces the selected login account values in batches.

![](6d7b2882624511f09a0d0242ac130006/images/image_8608f75dfa9b.png)

D. Update environment comments. This function can replace environment comments of selected items to be imported in batches.

![](6d7b2882624511f09a0d0242ac130006/images/image_70352796258e.png)

E. Replace UserAgent in batches. This function replaces USERAGENT values of selected items in batches. **When replacing the new value, first add a new UA to the list, or directly select a UA in the list, and tick, and then click the selection in the lower right corner, you can complete the replacement. **

Back to the current window:

![](6d7b2882624511f09a0d0242ac130006/images/image_9710ae98345d.png)

If the values of the items to be imported do not need to be modified or have been modified.

![](6d7b2882624511f09a0d0242ac130006/images/image_99a5c8d753f6.png)

Start selecting the groups to import:

If you need to import an import into a new group, click: **Group Manager**

![](6d7b2882624511f09a0d0242ac130006/images/image_2ae12c79d434.png)

In the Group Manager that pops up, click **Add Group** and fill in the name of the new group.
Here is a demonstration of creating a new group named **New Group**
Once the new group is created, it can be seen in the dropdown in the upper left corner of the Group Manager.

![](6d7b2882624511f09a0d0242ac130006/images/image_573ebf93bd4e.png)

Then close the group Manager window and go back to the **Local Account Batch Import** window.

Group Select **New Group**
Click the Check and Import to the group button
You can see that all data has been imported successfully.

![](6d7b2882624511f09a0d0242ac130006/images/image_549b768f4844.png)

Note: If the existing environment name is the same as the environment name of the item to be imported, the item with the same environment name will fail to be imported. In this case, change the environment name of the item with the same name before importing the item.

<p><img src="6d7b2882624511f09a0d0242ac130006/images/image_2ba434c865ce.png" width="360" /></p>

Click on Groups and select **New Group**
**You can see as born: The ready-to-use business environment successfully imported from the formal business environment. **