标题: 会话环境管理器使用
英文标题: Session Environment Manager
ID: 112
分类ID: 7
添加时间: 1663752388
更新时间: 1695795984
访问次数: 0
SEO标题: 候鸟浏览器会话环境管理器使用
SEO关键词: 候鸟浏览器会话环境管理器使用
SEO描述: 候鸟浏览器会话环境管理器使用

================================================== 内容 ==================================================
# 候鸟 会话环境管理器 使用说明书
候鸟浏览器会话环境管理器支持，全局审阅所有环境配置细节、一键批量修改候鸟环境并即时使用。
候鸟支持无限制数量级环境一键批量修改调整。所有环境的代理帐户管理、登录帐户管理、会话COOKIE管理、环境配置状态一目了然，支持将多个帐户、代理、COOKIE一键批量指派到海量的环境中，并实时生效。海量环境的批量配置仅需要数秒即可完成，彻底摒除以往繁杂的逐个环境配置，逐个帐户添加、维护等业务日常管理。

### 候鸟 会话环境管理器 使用前述
基于Google chrome浏览器研发的候鸟浏览器专有商用内核， 开启全面支持批量帐户修改配置服务。用户可通过直观的会话环境管理器窗口，将无限数量的环境直接批量调整，通过对海量环境进行批量配置，扩容，与动态调整。环境数据在本地银行级256位加密模式存储，可100%保证数据不会泄密。候鸟浏览器通过自带的300万条官方商业UA库（商业UA库由候鸟官方定期更新，来保证您的业务安全），提供给您在配置环境时提供最优的操作系统、语言、时区、国家、地区、纯净的动态IP/静态IP来为您的业务保驾护航。避免大幅度修改的高稳定性能、高安全性能CHROMIUM内核保持与GOOGLE的所有底层模块、应用层模块完全一致，来保证外界网站服务器对您的业务运营高度的稳定性。

下面通过图文模式，详细描述候鸟浏览器环境批量配置的使用方法：

### 一、开启并进入 会话环境管理器
启动候鸟客户端，点击主面板左上角的功能菜单，选择 会话环境管理 并点击进入，或直接在主面板上点右上方：会话环境管理

<p><img src="6d7b2882624511f09a0d0242ac130006/images/image_c7f210fb523b.png" width="360" /></p>

<p><img src="6d7b2882624511f09a0d0242ac130006/images/image_3a09f78f9ad9.png" width="360" /></p>

**会话环境管理器 界面：**

![](6d7b2882624511f09a0d0242ac130006/images/image_0ed637dc1feb.png)

说明：
在此界面上，您可以看到左上角支持搜索任意环境，输入关键字即时显示符合关键字条件的所有环境，支持跨分组全局搜索。方便您第一时间定位到需要调整修改的环境。

### 批量修改环境说明
1、在环境数量庞大的时侯，您可以通过左上角的搜索功能，直接找到重要的导入项并再次检查数据是否正确。
2、如果环境的配置情况不符合您的预期，您可以单个修改，或批量修改勾选的环境各项配置值。 

![](6d7b2882624511f09a0d0242ac130006/images/image_0f9b0cf0719b.png)

**勾选需要修改的环境项，此时窗体右上角的下拉列表亮起，点击下拉列表。**

下面就各项进行说明：
A、批量更新已勾选环境名称。

![](6d7b2882624511f09a0d0242ac130006/images/image_751ba7009d13.png)

此功能可批量更新已勾选环境项的所有环境名称，按数字序号递增。

![](6d7b2882624511f09a0d0242ac130006/images/image_09b78277fac0.png)

B、批量更新已勾选环境代理配置，此功能可批量替换 已勾选的环境的代理项各值。

![](6d7b2882624511f09a0d0242ac130006/images/image_3737e4a8c1e3.png)

C、更换环境登录帐号，此功能可批量替换 已勾选的环境项的登录帐户项各值。

![](6d7b2882624511f09a0d0242ac130006/images/image_5d0175c1bd6e.png)

D、更新环境注释，此功能可批量替换 已勾选的环境项的 环境注释。

![](6d7b2882624511f09a0d0242ac130006/images/image_0e6f54c132ae.png)

E、UserAgent批量替换，此功能可批量替换 已勾选的环境项的各USERAGENT值。在更换新值的时侯，先添加一条新的UA到列表中，或在列表中直接选取一条UA，并打勾，再点击右下角的选择，即可替换完成。

![](6d7b2882624511f09a0d0242ac130006/images/image_cb44198d5b68.png)

各按钮功能说明：

**版本验证：** 手工同步功能。 点击后将触发已勾选的本地环境与云端加密备份进行HASH数据校验，对于有变化本地环境进行云加密备份的过程。通常不用点击，同步备份在候鸟体系里是全自动化的过程。仅当您所在的网络处于极端不稳定的期间，您需要确认已同步到云端，可手工点击数据校验按钮来确保本地与云端数据一致。

**打开：** 批量打开已勾选的环境。使用场景：当您对在主面板逐个运行多个环境感到繁琐时，可通过此方式，一键批量运行勾选的多个环境。候鸟客户端将全自动依次安全的开始各个环境内核。

**关闭：** 批量关闭已勾选的环境。 使用场景：当您对在主面板逐个关闭多个环境感到繁琐时，可通过此方式，一键批量关闭勾选的多个环境。候鸟客户端将全自动依次安全的关闭各个环境内核。注：如果您的某个环境安装的插件有禁止浏览器关闭的功能时，则此浏览器会自动显示在托盘区域，您需要手动关闭。

**删除：** 批量删除已勾选的环境。使用场景：当您确认不需要这个环境时，您可以批量删除环境。注意：已删除的环境将仍旧存在于控制台的历史环境区。如图：

![](6d7b2882624511f09a0d0242ac130006/images/image_1b1d2159f3d9.png)

便于您日后需要恢复时，通过控制台进行恢复，也可以通过控制台进行彻底删除。

![](6d7b2882624511f09a0d0242ac130006/images/image_8cb1533a860b.png)

导出环境包：将当前环境所有数据（包含内核指纹数据）进行打ZIP包，并存储在本地硬盘指定位置。
使用场景1：除团队协作版本，在线分享给子用户方式外，导出环境包支持您将环境导出并发送给同样使用候鸟产品的任意第三方用户。（前题是在您导出时设置了密码，同时对方也知晓您的密码）。
使用场景2：重要环境本地备份职能。 当您的环境非常重要，您可以导出来进行本地封存。在日后有需要的时侯，随时进行导入并立即使用。

### 导出与导入的密码功能
为避免您在不久的未来存在忘记密码的可能，候鸟产品会自动记忆您的导出时设定的单个密码或多个密码，在导出和导入时，候鸟会自动加载这些历史时期您设定的密码来保证导入时的100%成功率。

**【重要】注：如果您的密码彻底忘记，同时也不在候鸟自动加载的您个人的历史密码集合中。则此环境ZIP包处于完全不可解密使用状态。候鸟官方也无法解密解压缩这类ZIP包。**

![](6d7b2882624511f09a0d0242ac130006/images/image_4ae86b809a1a.png)

**导入环境包：**将之前环境所有数据（包含内核指纹数据）打的ZIP包，导入到候鸟并立即加载。如果不存在于云端，将自动触发同步到云端备份。

================================================== 英文内容 ==================================================
# Mbbrowser Session Manager Instruction Manual
Mbbrowser session environment manager supports global review of all environment configuration details, one-click batch modification of migratory bird environment and instant use.
Mbbrowser supports unlimited order of magnitude environment one key batch modification adjustment. Agent account management, login account management, session COOKIE management, and environment configuration status of all environments are clear. Multiple accounts, agents, and cookies can be assigned to massive environments in batches with one key and take effect in real time. The batch configuration of massive environments only takes a few seconds to complete, completely abandoning the previous complex environment configuration, account addition, maintenance and other business routine management.

### Mbbrowser Session Manager Instruction Use The Foregoing
The dedicated commercial kernel of Migratory Bird browser developed based on Google chrome enables the service of batch account modification. In the intuitive Session environment Manager window, you can batch configure, expand, and dynamically adjust an unlimited number of environments. Environment data is stored in the local bank-level 256-bit encryption mode, which ensures 100% data leakage. With 3 million official commercial UA libraries (which are regularly updated by Migratory Bird officials to ensure the security of your business), Migratory Bird Browser provides you with the best operating system, language, time zone, country, region, and pure dynamic/static IP to protect your business when configuring the environment. Avoid greatly modified CHROMIUM kernel with high stability performance and high security performance, keep consistent with all the underlying modules and application layer modules of GOOGLE, so as to ensure the high stability of external website servers for your business operation.

The following is a detailed description of the use of batch configuration of the mbbrowser browser environment in graphic mode:

### I、Start and enter the session manager
To start the Mbbrowser client, click the function menu in the upper left corner of the main panel, select Session Manager and click Enter, or directly click the upper right corner of the main panel: Session Manager

<p><img src="6d7b2882624511f09a0d0242ac130006/images/image_09a63a9ec0b7.png" width="360" /></p>

<p><img src="6d7b2882624511f09a0d0242ac130006/images/image_8ece559bffc2.png" width="360" /></p>

**Session manager interface:**

![](6d7b2882624511f09a0d0242ac130006/images/image_14227e8d9e5c.png)

Explanation:
On the upper left corner of the page, you can search for any environment. If you enter a keyword, all environments that match the keyword conditions are displayed immediately. You can search across groups globally. It is convenient for you to locate the environment that needs adjustment and modification in the first time.

### Modify environment descriptions in batches
1. When the number of environments is large, you can directly find important import items and check the data again through the search function in the upper left corner.
2. If the environment Settings do not meet your expectations, you can modify the selected environment Settings individually or in batches.

![](6d7b2882624511f09a0d0242ac130006/images/image_98d386f29be1.png)

**Select the environment item that you want to modify. Then the drop-down list in the upper right corner of the window will light up. Click the drop-down list.**

The following is a description of each:
A. Update selected environment names in batches.

![](6d7b2882624511f09a0d0242ac130006/images/image_e690acdc7de8.png)

This function updates all environment names that have been selected in batches, increasing in number.

![](6d7b2882624511f09a0d0242ac130006/images/image_fd112f7a149b.png)

B. Batch update the selected environment agent configurations. This function can replace the selected environment agent values in batches.

![](6d7b2882624511f09a0d0242ac130006/images/image_ce9e2a49b1d6.png)

C. Change the environment login account. This function can replace the login account values of selected environment items in batches.

![](6d7b2882624511f09a0d0242ac130006/images/image_0e3cc2627a82.png)

D. Update environment comments. This function can replace environment comments of selected environment items in batches.

![](6d7b2882624511f09a0d0242ac130006/images/image_d84b828270ac.png)

E. Replace UserAgent in batches. This function replaces USERAGENT values of selected environment items in batches. When replacing the new value, first add a new UA to the list, or directly select a UA in the list, and tick, and then click the selection in the lower right corner, you can complete the replacement.

![](6d7b2882624511f09a0d0242ac130006/images/image_941335825400.png)

Function description of each button:

**Update:** Manual synchronization function. After you click this button, the HASH data verification between the selected local environment and the cloud encrypted backup is triggered, and the cloud encrypted backup process is performed for the changed local environment. Usually without clicking, synchronous backup is a fully automated process in Migratory Bird systems. Only when your network is extremely unstable, you need to confirm that the data is synchronized to the cloud center. You can manually click the data verification button to ensure the data consistency between the local and cloud center.

**Active：** Open selected environments in batches. Application scenario: If you are tired of running multiple environments on the main panel, you can run the selected environments in batches with one click. The Migratory Bird client will automatically start each environment kernel safely in turn.

**Close：** Disable the selected environments in batches. Application scenario: Disable multiple environments in batches with one key if you are frustrated with disabling multiple environments on the main panel. Migratory Bird client will automatically shut down each environment kernel in turn safely. Note: If the plug-in installed in one of your environments disables closing the browser, the browser will be automatically displayed in the tray area, you need to manually close it.

**Delete：** Delete selected environments in batches. Application scenario: After confirming that the environment is not needed, you can delete the environment in batches. Note: The deleted environment will still exist in the historical environment area of the console. As shown below:

![](6d7b2882624511f09a0d0242ac130006/images/image_6637fd557d35.png)

You can use the console to restore or completely delete data if you need to restore data later.

![](6d7b2882624511f09a0d0242ac130006/images/image_7712ec32a421.png)

Export environment package: ZIP all data (including kernel fingerprint data) in the current environment and store it in a specified location on the local hard disk.
Application Scenario 1: In addition to the teamwork version and online sharing to sub-users, the export environment package allows you to export the environment and send it to any third-party users who also use the Migratory Bird product. (The previous question is that you set a password when exporting, and the other party also knows your password).
Application scenario 2: Local backup function for critical environments. When your environment is critical, you can export it for local sequestration. In the future when necessary, at any time to import and use immediately.

### Export and import password functions
In order to avoid the possibility of forgetting your password in the near future, Migratory Bird products will automatically remember the single password or multiple passwords you set when exporting. During the export and import, Migratory Bird will automatically load the passwords you set in these historical periods to ensure the 100% success rate of import.

**【Important】Note: If your password is completely forgotten, it is also not in your personal historical password collection automatically loaded by Migratory Bird. The environment ZIP package is in a completely undecryptible state. Migratory Bird officials are also unable to decrypt and compress such ZIP packages.**

![](6d7b2882624511f09a0d0242ac130006/images/image_e22d1340c681.png)

**Import：**ZIP package of all previous environment data (including kernel fingerprint data), import to migratory Bird and load immediately. If it does not exist in the cloud, the system automatically triggers synchronization to the cloud backup.