<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>/api/example</title>
  <style>
/* 基础重置与排版 */
body {
  margin: 0;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.7;
  color: #333;
  background-color: #fff;
  max-width: 960px;
  margin: 0 auto;
  box-sizing: border-box;
}

/* 段落 */
p {
  margin: 1em 0;
}

/* 标题 */
h1, h2, h3, h4, h5, h6 {
  margin: 1.5em 0 0.8em;
  color: #2c3e50;
  font-weight: 600;
  line-height: 1.3;
}

h1 { font-size: 2em; border-bottom: 1px solid #eee; padding-bottom: 0.5em; }
h2 { font-size: 1.6em; }
h3 { font-size: 1.4em; }
h4 { font-size: 1.2em; }

/* 列表 */
ul, ol {
  margin: 1em 0;
  padding-left: 2em;
}

li {
  margin: 0.4em 0;
}

/* 引用块 */
blockquote {
  margin: 1.5em 0;
  padding: 0.8em 1.5em;
  background-color: #f9f9f9;
  border-left: 4px solid #ddd;
  color: #666;
  font-style: italic;
  border-radius: 0 4px 4px 0;
}

/* 代码行内 */
code {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  background-color: #f3f4f6;
  color: #e9602d;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-size: 0.95em;
  white-space: nowrap;
}

/* 代码块 */
pre {
  margin: 1.5em 0;
  padding: 1.2em;
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  overflow-x: auto;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
}

pre code {
  background: none;
  color: inherit;
  padding: 0;
  font-size: inherit;
  white-space: pre;
  display: block;
}

/* 表格 */
table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5em 0;
  font-size: 14px;
  overflow: hidden;
  box-shadow: 0 0 0 1px #e0e0e0;
  border-radius: 6px;
}

th, td {
  padding: 10px 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

th {
  background-color: #f8f9fa;
  color: #2c3e50;
  font-weight: 600;
  white-space: nowrap;
}

tr:nth-child(even) {
  background-color: #f9f9fb;
}

tr:hover {
  background-color: #f0f5ff;
}

/* 链接 */
a {
  color: #1a73e8;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

/* 分隔线 */
hr {
  border: 0;
  height: 1px;
  background: #ddd;
  margin: 2em 0;
}

/* 图片 */
img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 1em 0;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
  </style>
</head>
<body>
  <h2>多种语言脚本示例</h2> <div class="api-tabs ant-tabs ant-tabs-top ant-tabs-card ant-tabs-no-animation"><div class="ant-tabs-bar ant-tabs-top-bar ant-tabs-card-bar"><div class="ant-tabs-nav-container"><span class="ant-tabs-tab-prev ant-tabs-tab-btn-disabled"><span class="ant-tabs-tab-prev-icon"><i class="anticon anticon-left ant-tabs-tab-prev-icon-target"><svg class=""><path></path></svg></i></span></span><span class="ant-tabs-tab-next ant-tabs-tab-btn-disabled"><span class="ant-tabs-tab-next-icon"><i class="anticon anticon-right ant-tabs-tab-next-icon-target"><svg class=""><path></path></svg></i></span></span><div class="ant-tabs-nav-wrap"><div class="ant-tabs-nav-scroll"><div class="ant-tabs-nav ant-tabs-nav-animated"><div><div class="ant-tabs-tab">C++</div><div class="ant-tabs-tab">C#</div><div class="ant-tabs-tab">Golang</div><div class="ant-tabs-tab">JAVA</div><div class="ant-tabs-tab">PHP</div><div class="ant-tabs-tab">Python</div><div class="ant-tabs-tab">Ruby</div><div class="ant-tabs-tab">VBS</div><div class="ant-tabs-tab">Puppeteer</div><div class="ant-tabs-tab">Selenium</div><div class="ant-tabs-tab-active ant-tabs-tab">易语言</div></div><div class="ant-tabs-ink-bar ant-tabs-ink-bar-animated"></div></div></div></div></div></div><div></div><div class="ant-tabs-content ant-tabs-content-no-animated ant-tabs-top-content ant-tabs-card-content"><div class="ant-tabs-tabpane ant-tabs-tabpane-inactive"><div class="code-view"><pre><code>#include &lt;iostream&gt; #include &lt;curl/curl.h&gt; // 回调函数，用于处理响应数据 int write_callback(char* data, size_t size, size_t nmemb, std::string* buffer) { int result = 0; if (buffer != nullptr) { buffer-&gt;append(data, size * nmemb); result = size * nmemb; } return result; } int main() { // 初始化 libcurl curl_global_init(CURL_GLOBAL_DEFAULT); // 设置请求 URL std::string url = "http://example.com/post"; // 准备 JSON 数据 std::string json = "{\"name\": \"John\", \"age\": 30}"; // 发送 POST 请求 CURL* curl = curl_easy_init(); if (curl) { // 设置请求头 struct curl_slist* headers = NULL; headers = curl_slist_append(headers, "Content-Type: application/json"); // 设置请求选项 curl_easy_setopt(curl, CURLOPT_URL, url.c_str()); curl_easy_setopt(curl, CURLOPT_POSTFIELDS, json.c_str()); curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers); curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, write_callback); // 执行请求 CURLcode res = curl_easy_perform(curl); if (res != CURLE_OK) { std::cout &lt;&lt; "Failed to send POST request: " &lt;&lt; curl_easy_strerror(res) &lt;&lt; std::endl; } // 获取响应结果 long http_code = 0; curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &amp;http_code); std::string response_data; curl_easy_getinfo(curl, CURLINFO_PRIVATE, &amp;response_data); // 输出请求的响应结果 std::cout &lt;&lt; "HTTP Status Code: " &lt;&lt; http_code &lt;&lt; std::endl; std::cout &lt;&lt; "Response Data: " &lt;&lt; response_data &lt;&lt; std::endl; // 释放资源 curl_easy_cleanup(curl); } // 清理 libcurl curl_global_cleanup(); return 0; }</code></pre> </div></div><div class="ant-tabs-tabpane ant-tabs-tabpane-inactive"><div class="code-view"><pre><code>using System; using System.Net.Http; using System.Text; using System.Threading.Tasks; class Program { static async Task Main(string[] args) { // 设置请求 URL string url = "http://example.com/post"; // 准备 JSON 数据 string json = "{\"name\": \"John\", \"age\": 30}"; // 发送 POST 请求 using (var httpClient = new HttpClient()) { var content = new StringContent(json, Encoding.UTF8, "application/json"); var response = await httpClient.PostAsync(url, content); // 输出请求的响应结果 Console.WriteLine(response.StatusCode); Console.WriteLine(await response.Content.ReadAsStringAsync()); } } }</code></pre> </div></div><div class="ant-tabs-tabpane ant-tabs-tabpane-inactive"><div class="code-view"><pre><code>package main import ( "bytes" "fmt" "net/http" ) func main() { jsonStr := []byte(`{"name":"John","age":31,"city":"New York"}`) // 准备 JSON 数据 req, err := http.NewRequest("POST", "https://example.com/api", bytes.NewBuffer(jsonStr)) req.Header.Set("Content-Type", "application/json") client := &amp;http.Client{} resp, err := client.Do(req) // 发送 POST 请求 if err != nil { panic(err) } defer resp.Body.Close() fmt.Println("响应状态码:", resp.Status) var result map[string]interface{} err = json.NewDecoder(resp.Body).Decode(&amp;result) // 解析响应中的 JSON 数据并存入 result 变量中 if err != nil { panic(err) } fmt.Println("响应数据:", result) }</code></pre> </div></div><div class="ant-tabs-tabpane ant-tabs-tabpane-inactive"><div class="code-view"><pre><code>Java 发送 JSON 格式 POST import java.io.IOException; import java.net.URI; import java.net.URISyntaxException; import org.apache.http.HttpEntity; import org.apache.http.client.methods.CloseableHttpResponse; import org.apache.http.client.methods.HttpPost; import org.apache.http.entity.ContentType; import org.apache.http.entity.StringEntity; import org.apache.http.impl.client.CloseableHttpClient; import org.apache.http.impl.client.HttpClients; import org.apache.http.util.EntityUtils; public class Main { public static void main(String[] args) throws URISyntaxException, IOException { // 设置请求 URL URI uri = new URI("http://example.com/post"); // 准备 JSON 数据 String json = "{\"name\": \"John\", \"age\": 30}"; // 发送 POST 请求 CloseableHttpClient httpclient = HttpClients.createDefault(); HttpPost httppost = new HttpPost(uri); httppost.setHeader("Content-Type", "application/json"); httppost.setEntity(new StringEntity(json, ContentType.APPLICATION_JSON)); CloseableHttpResponse response = httpclient.execute(httppost); try { // 输出请求的响应结果 System.out.println(response.getStatusLine()); HttpEntity entity = response.getEntity(); EntityUtils.consume(entity); } finally { response.close(); } } }</code></pre> </div></div><div class="ant-tabs-tabpane ant-tabs-tabpane-inactive"><blockquote><p>CURL请求</p></blockquote> <div class="code-view"><pre><code>&lt;?php $url = 'https://example.com/api'; // 设置请求 URL $data = array('name' =&gt; 'John Doe', 'email' =&gt; '<EMAIL>'); // 准备 JSON 数据 $ch = curl_init(); curl_setopt($ch, CURLOPT_URL, $url); curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1); curl_setopt($ch, CURLOPT_POST, 1); curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data)); curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json')); $response = curl_exec($ch); if (curl_errno($ch)) { // 处理请求失败的情况 echo '请求失败:' . curl_error($ch); } else { // 处理请求成功的情况 $result = json_decode($response); echo '请求成功，返回结果为:' . print_r($result, true); } curl_close($ch);</code></pre> </div> <blockquote><p>简易请求</p></blockquote> <div class="code-view"><pre><code>&lt;?php // 设置要提交的数据 $data = array( 'name' =&gt; 'John Doe', 'email' =&gt; '<EMAIL>', 'phone' =&gt; '1234567890' ); // 转换成JSON格式 $json_data = json_encode($data); // 设置POST请求的URL和数据 $url = 'https://example.com/api'; $options = array( 'http' =&gt; array( 'header' =&gt; "Content-type: application/json\r\n", 'method' =&gt; 'POST', 'content' =&gt; $json_data ) ); // 发送 POST 请求 $context = stream_context_create($options); $result = file_get_contents($url, false, $context); // 处理返回结果 if ($result === false) { // 请求失败 echo "请求失败"; } else { // 请求成功，返回结果为 $response = json_decode($result, true); echo "返回结果:" . print_r($response, true); }</code></pre> </div></div><div class="ant-tabs-tabpane ant-tabs-tabpane-inactive"><div class="code-view"><pre><code>import requests import json url = "https://example.com/api" # 设置请求 URL headers = {"Content-Type": "application/json"} data = {"name": "John", "age": 31, "city": "New York"} # 准备 JSON 数据 json_data = json.dumps(data) response = requests.post(url, headers=headers, data=json_data) # 发送 POST 请求 print("响应状态码:", response.status_code) print("响应数据:", response.text)</code></pre> </div></div><div class="ant-tabs-tabpane ant-tabs-tabpane-inactive"><div class="code-view"><pre><code>require 'net/http' require 'uri' require 'json' uri = URI.parse("https://example.com/api") # 设置请求 URL header = {'Content-Type': 'application/json'} data = {name: 'John', age: 31, city: 'New York'} # 准备 JSON 数据 json_data = JSON.generate(data) http = Net::HTTP.new(uri.host, uri.port) http.use_ssl = true # 如果发送到 HTTPS 端口，则需要启用 SSL request = Net::HTTP::Post.new(uri.request_uri, header) # 发送 POST 请求 request.body = json_data response = http.request(request) puts "响应状态码：#{response.code}" puts "响应数据：#{response.body}"</code></pre> </div></div><div class="ant-tabs-tabpane ant-tabs-tabpane-inactive"><div class="code-view"><pre><code>Option Explicit Dim url, json_data, http_request url = "https://example.com/api" ' 设置请求 URL Set http_request = CreateObject("MSXML2.XMLHTTP") json_data = "{""name"": ""John Doe"", ""email"": ""<EMAIL>""}" ' 准备 JSON 数据 http_request.Open "POST", url, False http_request.SetRequestHeader "Content-Type", "application/json" http_request.Send json_data If http_request.Status = 200 Then WScript.Echo "请求成功，返回结果为:" &amp; http_request.ResponseText Else WScript.Echo "请求失败（code " &amp; http_request.Status &amp; "）" End If</code></pre> </div></div><div class="ant-tabs-tabpane ant-tabs-tabpane-inactive"><div class="code-view"><pre><code>const puppeteer = require('puppeteer'); (async () =&gt; { // 启动一个 Headless Chrome 浏览器，并新建一个页面 const browser = await puppeteer.launch(); const page = await browser.newPage(); // 设置请求头 await page.setExtraHTTPHeaders({ 'Content-Type': 'application/json' }); // 构造 POST 请求的参数，这里使用 JSON 格式 const postData = { name: 'puppeteer', type: 'web scraping' }; // 发送 POST 请求 await page.goto('http://127.0.0.1:8186', { method: 'POST', body: JSON.stringify(postData), headers: { 'Content-Type': 'application/json' } }); // 等待一段时间，确保请求已经完成 await page.waitForTimeout(5000); // 关闭浏览器 await browser.close(); })();</code></pre> </div> <ul><li><p>这个示例代码会启动一个 Headless Chrome 浏览器，并在新建的页面上发送一个 POST 请求，向 http://127.0.0.1:8186 发送一个 JSON 格式的数据。请求中指定了请求方法、请求头和请求体等参数，以及一些其他的选项。在请求发送完成后，代码会等待 5 秒钟，以确保请求已经完成，并且关闭浏览器。</p></li> <li><p>如果你需要修改请求体或者其他参数，可以根据自己的需求进行修改。注意，由于浏览器窗口会被打开，所以建议在代码执行完毕后手动关闭浏览器，或者在代码中添加关闭浏览器的逻辑。</p></li></ul></div><div class="ant-tabs-tabpane ant-tabs-tabpane-inactive"><div class="code-view"><pre><code>from selenium import webdriver import time import json # 构造 POST 请求的参数，这里使用 JSON 格式 postData = { "name": "selenium", "type": "web automation" } body = json.dumps(postData) # 设置请求头 headers = { "Content-Type": "application/json" } # 创建 Chrome 浏览器实例 browser = webdriver.Chrome() # 发送 POST 请求 browser.execute_script(""" var xhr = new XMLHttpRequest(); xhr.open('POST', 'http://127.0.0.1', true); xhr.setRequestHeader('Content-type', 'application/json'); xhr.onreadystatechange = function() { if(xhr.readyState == 4) { console.log(xhr.responseText); } }; xhr.send(arguments[0]); """, body) # 等待一段时间，确保请求已经完成 time.sleep(5) # 关闭浏览器 browser.quit()</code></pre> </div> <ul><li><p>这个示例代码会启动一个 Headless Chrome 浏览器，并在新建的页面上发送一个 POST 请求，向 http://127.0.0.1:8186 发送一个 JSON 格式的数据。请求中指定了请求方法、请求头和请求体等参数，以及一些其他的选项。在请求发送完成后，代码会等待 5 秒钟，以确保请求已经完成，并且关闭浏览器。</p></li> <li><p>如果你需要修改请求体或者其他参数，可以根据自己的需求进行修改。注意，由于浏览器窗口会被打开，所以建议在代码执行完毕后手动关闭浏览器，或者在代码中添加关闭浏览器的逻辑。</p></li></ul></div><div class="ant-tabs-tabpane ant-tabs-tabpane-active"><div></div><div class="code-view"><pre><code>'引入HttpLib库 #Include HttpLib.e '构造 POST 请求的参数，这里使用 JSON 格式 Str body = JsonToStr({ "name": "easy language", "type": "application" }) '设置请求头 Map headersMap = { "Content-Type": "application/json" } '发送 POST 请求 Str respBody = HttpPost("http://127.0.0.1:8186", headersMap, body) '响应数据 Print(respBody)</code></pre> </div> <ul><li><p>这个示例代码使用了 HttpLib 库来发送 POST 请求。首先通过 JsonToStr 函数将请求体转换为 JSON 字符串格式，然后构造一个请求头信息的 Map 对象，将 Content-Type 设置为 application/json。最后调用 HttpPost 方法发送 POST 请求，指定目标 URL、请求头和请求体等参数，并返回响应的内容。</p></li> <li><p>如果你需要修改请求体或者其他参数，可以根据自己的需求进行修改。注意，在易语言中需要先引入 HttpLib 库才能使用其中的函数。</p></li></ul><div></div></div></div><div></div></div>
</body>
</html>