API文档: 插件管理 - 4、环境管理
URL: https://www.mbbrowser.com/api/plugin
抓取时间: 2025-07-28 12:35:14
============================================================

API使用文档-候鸟防关联浏览器•

首页

应用

价格

下载

APInew
使用教程

常见问题

佣金计划

博客中心
登录&注册 简体中文
首页

应用

价格

下载

API

使用教程

佣金计划

博客中心

登录&注册

# API
候鸟浏览器API使用文档

API使用须知简介
• 使用须知
• HTTP模式说明
• 常见问题
• API接口文档1、帐号登录
• 2、获取成员列表
• 3、环境开启/关闭
• 4、环境管理
• 5、分组管理
• 6、脚本管理
• 7、插件管理
• 8、附录（国家码、时区、语言、系统和分辨率）
• 9、错误码对照表
• 候鸟API接口实时调试工具POSTMAN下载及安装
• POSTMAN调试候鸟API接口
• 调试接口JSON数据官方更新、下载
• 多种语言脚本示例
• JSON在线格式化工具

## 插件管理
1、列出当前帐户下所有已安装的插件(插件 ID，插件名称)2、查询、列出指定环境中的所有插件(插件 ID，插件名称)3、安装指定多个插件到指定的环境中4、删除指定环境插件 使用POSTMAN调试此接口

支持邮箱: <EMAIL>
©MBBROWSER @2025

京ICP备 2020047947号

本系统不提供代理IP服务，禁止用户使用本系统进行任何违法犯罪活动，用户使用本系统带来的任何责任由用户自行承担。

MBbrowser.com  All Rights Reserved. 候鸟防关联浏览器对网站内容拥有最终解释权。
工作日客服(微信)
工作日09-18点

夜间/周末客服(微信)

工作日 18-24点，周末全天

商务(微信)

mbbrowser_official

###### 全国咨询服务热线

400-112-6050
在线咨询

微信咨询

电话咨询

售后咨询