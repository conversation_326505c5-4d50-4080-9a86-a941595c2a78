1、在候鸟主面板点击设置 -> 浏览器插件管理器 进入插件管理界面。

1、在候鸟主面板点击设置 -> 浏览器插件管理器 进入插件管理界面。

2、在确定我的自定义插件列表中没有要导入的插件后，请点击 导入自定义插件 按钮。

此时可以看到弹出了窗口：导入自定义插件。 此窗口表示候鸟已经开始为接收您给定的自定义插件做好了程序上的准备。在操作这个过程中，并不会影响您已经运行的业务环境，也就是说，您可以在已经运行的多个业务环境的同时，同时进行这一块的操作，不会出现冲突。

当看到这个窗口后，您可以开始操作您的CRX插件包文件，请将CRX插件包解压缩为目录格式。

举例说明：
    例如：MyProject.crx 文件，您将此文件重命名为MyPorject.rar或MyProject.zip，然后用winrar或winzip进行解压缩。解压缩完成后，您会看到一个Myproject文件夹。此时说明您已经做好了导入到候鸟的准备。

然后在此窗口上点击按钮：选择自定义插件并测试运行

此时您可以看到弹出一个提示窗口，此窗口告诉您，请确认您已经将Crx插件包文件解压缩为文件夹。同时告诉您，下一步将打开候鸟浏览器窗口，供您将插件文件夹导入到浏览器中进行验证，验证过程需要8秒时间，在这8秒过程中，候鸟浏览器会将插件的ID，名称，版本号获取到并进行验证，最后保存在您的硬盘上，同时按GOOGLE规范存储在GOOGLE要求的指定位置。整个过程需要8秒的时长，在此期间您可以在浏览器中使用插件，也可以什么都不做，8秒后关闭浏览器即可。

在您了解整个过程后，请点击：我知道了，请立即开启候鸟浏览器

此时您可以看到候鸟弹出了一个浏览器内核窗口供您添加入您的插件，此时您点击：

然后选择您刚刚解压缩的插件文件夹即可。如下图例：

添加完成后，您会看到您的自定义插件已成功加入到浏览器中，这种方式不需要您的插件是否通过GOOGLE扩展商店审核，也不需要您提交任何审核，保证是您自用的插件就行。

在等待或使用刚刚加入的插件满8秒后，可以关闭此浏览器窗口。

然后您会看到您的自定义插件已经被候鸟检测并验证完成，成功获取到插件ID、插件名称、插件版本号。当您看到这些信息都是有值的时侯，表示候鸟检测并验证成功完成。

此时您为了方便管理和记忆，也可以填入插件描述（会显示自我的自定义插件列表栏的插件描述中），也可以不填写。

然后点击 导入到我的自定义插件库

此时表示您的自定义插件已经成功导入到您的候鸟私人插件库中，为保护您的个人隐私与业务运营安全，所有此类插件只存在于您的本地电脑中，不存在任何其它地方，也不会同步到候鸟官方服务器。因此在您以后异地电脑里使用时，如果自定义插件不存在，仍需要进行一次上述导入操作。

此时，您现在可以按照候鸟的标准通用操作，以A,B,C,D的顺序，通过勾选（打勾），将任意自定义插件一键批量安装（指派）到您需要的多个环境中，避免了逐个人工安装插件的繁琐。

第二十七章

【浏览器插件管理体系】

客户端候鸟浏览器内核

插件全自动安装客户使用操作手册

第二十七章 【浏览器插件管理体系】客户端候鸟浏览器内核插件全自动安装客户使用操作手册

候鸟 浏览器插件管理器 使用说明书

候鸟浏览器插件管理器为首款支持一键批量安装管理浏览器环境插件扩展

候鸟支持无限制数量级浏览器插件一键安装到任意环境中。所有环境的插件数量、状态一目了然，支持环境与插件包的分享，导入与导出。安装多个插件到100个环境中仅需要数秒即可完成，彻底摒除以往繁杂的插件安装，维护等业务日常管理。

候鸟 浏览器插件管理器 使用前述：

基于Google chrome浏览器研发的候鸟浏览器内核， 开启全面支持插件扩展服务。用户可通过GOOGLE市场，下载插件直接安装到候鸟浏览器中，也可以直接在候鸟插件管理器自带的官方插件库（插件库所有插件包原生态来源于GOOGLE市场），支持候鸟客户利用全球数万款强大的浏览器插件开展各类合法合规的商业运营服务。

下面通过图文模式，详细描述候鸟插件管理器的使用方法：

开启并进入 浏览器插件管理器

启动候鸟客户端，点击主面板左上角的功能菜单，选择浏览器插件管理器并点击进入浏览器插件管理器。

方式一：

方式二：


================================================== 表格内容 ==================================================

参数名称 | 类型 | 必传 | 样例串/默认值 | 说明
Session_ID | array | 是 | 373808cb37bd63f5f7d92415e736e85f | 指定删除环境ID
Is_Delete_All | bit | 否 | 1 or 0 | 当值为1时，自动忽略Session_Id,进行全局所有环境
删除.

{
    "code": -12,
    "data": {
        "Delete_Session_Success": "373808cb37bd63f5f7d92415e736e85f",
        "Delete_Session_Failed": "f994d8e641ce7006acfa36c901829ff2"
    },
    "message": "Session Delete Finished."
}