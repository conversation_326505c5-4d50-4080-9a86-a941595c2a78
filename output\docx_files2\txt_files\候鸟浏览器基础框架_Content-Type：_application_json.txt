Content-Type： application/json
接口描述：创建环境新分组，名称不能与已创建的分组名称重复。

Content-Type： application/json
接口描述：创建环境新分组，名称不能与已创建的分组名称重复。

请求参数：(用户脚本)

注1：不提供参数，默认返回所有分组名称。

注2：通过分组名称查询分组下所有环境ID参见接口：/api/v1/session/listid

执行成功返回：(APISERVER CONSOLE)

19、删除环境分组
Path：/api/v1/group/del

Method： POST

Content-Type： application/json
接口描述：批量删除指定环境分组，删除成功返回true。

请求参数：(用户脚本)

注1：删除环境分组前需要先删除此分组下所有环境，或将此分组下所有环境转移到其它分组。否则删除非空分组会失败。

注2：查询分组下所有环境参见接口：/api/v1/session/listid

注3：转移分组下所有环境到其它分组参见接口：/api/v1/group/move_session

执行成功返回：(APISERVER CONSOLE)

20、将指定环境从指定分组转移到另一个分组
Path：/api/v1/group/move_session

Method： POST

Content-Type： application/json
接口描述：批量从指定分组中转移指定环境到另一个分组，转移成功Status返回0。

请求参数：(用户脚本)

执行成功返回：(APISERVER CONSOLE)

21、退出APISERVER并关闭客户端(退出登录)

Path：/api/v1/quit

Method： POST

Content-Type： application/json
接口描述：停止并关闭APISERVER，关闭成功Code返回0。

执行成功返回：(APISERVER CONSOLE)

22、查询、列出指定环境中的所有脚本
Path：/api/v1/session/id_script_list

Method： POST

Content-Type： application/json
接口描述：列出指定环境中所有包含的脚本。（支持单个或多个环境）

请求参数：(用户脚本)

执行成功返回：(APISERVER CONSOLE)

23、切换指定环境已激活脚本
Path：/api/v1/session/id_script_active

Method： POST

Content-Type： application/json
接口描述：通过指定单个环境，将此环境中的指定脚本设定为激活状态。

请求参数：(用户脚本)

执行成功返回：(APISERVER CONSOLE)

24、查询、列出指定环境中的所有插件(插件ID，插件名称)
Path：/api/v1/session/id_plugin_list

Method： POST

Content-Type： application/json
接口描述：列出指定环境中所有包含的插件。（支持单个或多个环境）

请求参数：(用户脚本)

执行成功返回：(APISERVER CONSOLE)

(还缺少插件，帐户，脚本的部份)

25、删除指定环境插件

Path：/api/v1/session/plugin_delete
Method： POST

Content-Type： application/json
接口描述：删除指定环境中已安装的插件。删除成功返回code:0 ,  message: Delete plugin Success。一次性支持删除环境中所有插件。 (每次仅针对单个环境进行处理)

请求参数：(用户脚本)

执行成功、失败返回：(APISERVER CONSOLE)

26、列出当前帐户下所有已安装的插件(插件ID，插件名称)
Path：/api/v1/plugin/list

Method： POST

Content-Type： application/json
接口描述：列出指定环境中所有包含的插件。（支持单个或多个环境）

请求参数：(用户脚本)

执行成功返回：(APISERVER CONSOLE)

27、安装指定多个插件到指定的环境中
Path：/api/v1/session/plugin_install

Method： POST

Content-Type： application/json
接口描述：安装新插件到指定环境中。（支持单个或多个插件）

请求参数：(用户脚本)

Plugin_ver作为可选项，默认安装最新版本。如果填入错误的版本号，默认安装插件最新版本。

执行成功返回：(APISERVER CONSOLE)

28、从我的脚本库中指派脚本到目标环境中
Path：/api/v1/session/id_script_add

Method： POST

Content-Type： application/json
接口描述：从脚本库中将我的单个或多个脚本指派到目标环境中。（支持将单个或多个脚本ID分配到指定的单个环境中）

请求参数：(用户脚本)

请求样例串：(POST)

执行成功返回：(APISERVER CONSOLE)

列出所有支持的API集合

29、查看目标环境运行状态【2023-10-29新增】
Path： /api/v1/browser/status

Method： POST

Content-Type： application/json
接口描述：指定查看单个/多个环境的运行状态。

请求参数：(用户脚本)

返回：

session_ actived:  1  or  0

1：正在运行中  0：未运行

请求样例串：(POST)

执行成功返回：(APISERVER CONSOLE)

七、API类型LOG日志

前缀带有API字样

八、WEB控制台API功能详细说明

1、环境ID提供功能：

用户拥有API凭据后，所有显示环境信息位置自动增加显示环境UNQUE_SESSION_ID并允许用户点击复制。

2、服务器端与客户端交互日志提供：

服务器端收到客户端所有请求与服务器返回数据，通过 LIST控件显示在页面中。包含心跳中指令下发内容【需自动转为中文描述，不提供明文指令】

名称：API控制台日志
支持日志清理功能。

3、记录并提供 用户起始使用API凭据 的日期时间，停止使用API凭据的日期时间，以列表显示在单独页面。
    名称：API凭据使用日志

支持记录删除功能。

4、API控制台 增加按钮复用：强制关闭客户端APISERVER

5、提供所有插件ID列表页，显示用户当前已使用的所有插件，名称，说明和插件ID。【用户在无头模式下无法在客户端查阅插件ID，需在控制台提供，以增加用户控制台使用粘性】

6、提供控制台用户端业务模板服务【首个版本暂不做】

7、团队帐户列表，【子帐户名称，当前状态（在线、离线），是否使用API模式，子帐户最近登录时间，子帐户最近离线时间，子帐户有效环境数，子帐户已使用环境数】 主帐户面板增加提供按钮：暂停此帐户 强制客户端离线。

【团队客户在无头模式下无法控制子帐户使用有限的凭据，需提供此功能支持主帐户查阅、管理子帐户，有利于在脚本里进行逻辑梳理，并在单位时间内控制子帐户的使用。此功能为刚需，可增加用户控制台使用粘性】

8、凭据列表增加实时状态显示，哪个帐户正在使用此凭据，需在此页面显示出来。

9、环境管理脚本网页生成器

职能：减少用户代码编写量，提供用户直接COPY代码的方式供用户直接使用。

例：环境开启 – > 用户在控制台里选择需要开启的环境，然后勾选各项，->生成脚本代码  ->  用户COPY代码到自已的脚本中。

[20230615]


================================================== 表格内容 ==================================================

参数名称 | 类型 | 必传 | 样例串/默认值 | 说明
Group_Name | array | 是 | 分组名称 | 单个或多个分组名称

{
"message "Group Delete Success",
"code": 0,
"data": true
}