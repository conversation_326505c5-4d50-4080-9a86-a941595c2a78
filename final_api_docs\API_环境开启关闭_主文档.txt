# 环境开启关闭

## 描述
控制浏览器环境的启动和停止，用于启动指定的环境，启动成功后可以获取浏览器debug接口用于执行selenium和puppeteer自动化脚本。

## 功能列表

1. 打开环境
2. 关闭环境
3. 强制终止环境

## 1. 打开环境

### 接口信息
- **Path**: /api/v1/browser/start
- **Method**: POST
- **Content-Type**: application/json
- **接口描述**: 用于启动指定的环境，启动成功后可以获取浏览器debug接口用于执行selenium和puppeteer自动化脚本

### 请求参数

| 参数名称 | 类型 | 必传 | 样例串/默认值 | 说明 |
|---------|------|------|--------------|------|
| Session_ID | array | 是 | 373808cb37bd63f5f7d92415e736e85f,705cc4c139e69b729a2fd277f30e1863 | 环境ID |
| isHeadless | boolean | 否 | true | true：默认浏览器无头模式<br>false：浏览器有头模式 |
| args | array | 否 | ["--disable-extensions", "--blink-settings=imagesEnabled=false"] | 启动参数 |

### 常用启动参数说明
- `--disable-extensions`: 禁用插件
- `--blink-settings=imagesEnabled=false`: 禁止加载图片
- `--interval-seconds`: 启动各浏览器间隔时间(秒)

### 单个环境请求示例
```json
{
    "Session_ID": [
        "373808cb37bd63f5f7d92415e736e85f"
    ],
    "args": [
        "--disable-extensions",
        "--enable-logging",
        "--v=1",
        "--blink-settings=imagesEnabled=false"
    ]
}
```

### 多个环境请求示例
```json
{
    "Session_ID": [
        "373808cb37bd63f5f7d92415e736e85f",
        "705cc4c139e69b729a2fd277f30e1863"
    ],
    "args": [
        "--disable-extensions",
        "--blink-settings=imagesEnabled=false",
        "--interval-seconds=3"
    ]
}
```

### 执行成功返回示例
```json
{
    "message": "Success",
    "code": 0,
    "data": {
        "listid": [{
            "Session_Name": "商用业务环境一",
            "Session_ID": "373808cb37bd63f5f7d92415e736e85f",
            "Group_Name": "default",
            "Actived_script_id": "O73808cb37bd63f5f7d92415e736e999",
            "Actiived_script_name": "这是一个脚本例子",
            "Actiived_script_encode": "true",
            "Weblogin_Account_Count": "4",
            "Weblogin_Account_name": "<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>",
            "Plugins_Count": "4",
            "Plugin_Id": "jjbnhpnlakcdgfnnldamfeinfmahhdlm,jjbnhpnlakcdgfnnldamfeinfmahhdlm,jjbnhpnlakcdgfnnldamfeinfmahhdlm,jjbnhpnlakcdgfnnldamfeinfmahhdlm",
            "template_id": "123456",
            "template_name": "抖音国际版",
            "browser_Path": "D:\\mbbrowser\\Chromium_x64\\chromium.exe",
            "browser_CDP_Port": 46973,
            "MBData_Path": "C:\\MBDATA\\xxxxxxxxxx\\xxxxxxxxxx\\xxxxxxxxxxx",
            "Public_ip": "************",
            "Internel_ip": "**************",
            "isDynamicIp": false,
            "StartPage": "about:blank",
            "proxyType": "socks5",
            "proxy_ip": "127.0.0.1",
            "proxy_port": "1080",
            "isHeadless": "true",
            "webdriver": "C:\\Users\\<USER>\\houniao\\Driver\\100\\chromedriver.exe",
            "status": 0
        }],
        "total": 1
    }
}
```

## 返回参数说明

| 参数名称 | 说明 |
|---------|------|
| Session_Name | 环境名称 |
| Session_ID | 环境ID |
| Group_Name | 分组名称 |
| Actived_script_id | 激活的脚本ID |
| browser_CDP_Port | 浏览器CDP端口 |
| webdriver | 对应内核webdriver驱动路径 |
| Public_ip | 公网IP |
| proxy_ip | 代理IP |
| proxy_port | 代理端口 |

## 基础信息

- **API服务器地址**: http://127.0.0.1:8186 或 http://localhost:8186
- **支持的HTTP方法**: POST
- **数据格式**: JSON
- **客户端版本要求**: V3.9.2.114以上

## 使用说明

1. 确保候鸟浏览器客户端已启动并登录
2. 获取要启动的环境Session_ID
3. 根据需要配置启动参数
4. 发送POST请求到指定接口
5. 获取返回的CDP端口用于自动化脚本连接
6. 建议使用[POSTMAN调试工具](/api/postman-example)进行接口测试

## 注意事项

1. 支持同时启动多个环境
2. 可配置无头模式和有头模式
3. 启动参数可用于优化性能（如禁用图片加载）
4. 返回的webdriver路径可直接用于selenium
5. CDP端口用于puppeteer等工具连接

