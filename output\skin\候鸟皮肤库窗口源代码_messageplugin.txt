<?xml version="1.0" encoding="UTF-8"?>
<Window size="500,300" trans="true"  caption="0,0,0,100" roundcorner="9,9,9,9" showshadow="true" shadowsize="3" shadowposition="0,0" shadowcolor="#ff6272A4" fademode="true">
  <Include source="default.xml" />
    <Default name="Button" />
  <VerticalLayout bkcolor="#FF282A36">
    <HorizontalLayout height="26">
    	<Button bkimage="icon21px.png" padding="6,6,0,0" width="27" height="21"/>
    	<Label name="msessagetip" text="" align="left" width="180" padding="6,8,0,0" textcolor="#FFF8F8F2" font="8" />
      <Control />
      <Button name="closebtn" width="28" height="26"  tooltip="关闭" normalimage="closebtn.png" hotimage="closebtn_hover.png" pushedimage="closebtnpush.png" />
    </HorizontalLayout>
		<VerticalLayout>

			 <HorizontalLayout height="20" inset="30,10,20,0" >
      </HorizontalLayout>

      <HorizontalLayout height="140" inset="30,10,20,0" >
        <Label name="msessage" text="" align="left" textpadding="8,0,0,0" textcolor="#FFF8F8F2" font="8" />
      </HorizontalLayout>

      <HorizontalLayout height="50" inset="20,10,20,0" >
        <Label name="msessageext" text="" align="left" textpadding="0,0,0,0" textcolor="#FFF8F8F2" font="4" />
      </HorizontalLayout>

      <HorizontalLayout childpadding="2" inset="0,10,0,0">
           <Control />
				      <Button name="btnok" text="我知道了,请立即开启候鸟浏览器" width="400" bkcolor="#FF282A36" hotbkcolor="#FF44475A"  bordercolor="#FFBD93F9" focusbordercolor="#FFFF79C6" bordersize="1" borderround="2,2"  padding="0,4,0,0" float="left" font="0" height="26" textcolor="#FFF8F8F2"/>
           <Control width="20"/>
			   </HorizontalLayout>
		</VerticalLayout>
  </VerticalLayout>
</Window>