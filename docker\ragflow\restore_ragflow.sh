#!/bin/bash

# RAGFlow完整恢复脚本
# 用途: 在目标服务器上恢复RAGFlow的所有数据
# 作者: RAGFlow迁移助手
# 日期: $(date +%Y-%m-%d)

set -e  # 遇到错误立即退出

# 配置变量
BACKUP_FILE=""
RESTORE_DIR="/mnt/ragflow"
TARGET_IP=""

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示使用说明
show_usage() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -f, --file <backup_file>     指定备份文件路径"
    echo "  -d, --dir <restore_dir>      指定恢复目录 (默认: /mnt/ragflow)"
    echo "  -i, --ip <target_ip>         指定目标服务器IP (用于更新配置)"
    echo "  -h, --help                   显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 -f /tmp/ragflow-complete-backup-20240801_123456.tar.gz -i ************"
    echo ""
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -f|--file)
                BACKUP_FILE="$2"
                shift 2
                ;;
            -d|--dir)
                RESTORE_DIR="$2"
                shift 2
                ;;
            -i|--ip)
                TARGET_IP="$2"
                shift 2
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_usage
                exit 1
                ;;
        esac
    done
}

# 自动查找备份文件
find_backup_file() {
    if [ -z "$BACKUP_FILE" ]; then
        log_info "自动查找备份文件..."
        
        # 在常见位置查找备份文件
        for dir in /tmp /mnt/ragflow-backup /root; do
            FOUND_FILE=$(find "$dir" -name "ragflow-complete-backup-*.tar.gz" -type f 2>/dev/null | head -1)
            if [ -n "$FOUND_FILE" ]; then
                BACKUP_FILE="$FOUND_FILE"
                log_info "找到备份文件: $BACKUP_FILE"
                break
            fi
        done
        
        if [ -z "$BACKUP_FILE" ]; then
            log_error "未找到备份文件，请使用 -f 参数指定"
            exit 1
        fi
    fi
}

# 自动获取目标IP
get_target_ip() {
    if [ -z "$TARGET_IP" ]; then
        TARGET_IP=$(hostname -I | awk '{print $1}')
        log_info "自动获取目标IP: $TARGET_IP"
    fi
}

# 检查恢复前置条件
check_prerequisites() {
    log_info "检查恢复前置条件..."
    
    # 检查是否为root用户
    if [ "$EUID" -ne 0 ]; then
        log_error "请使用root用户运行此脚本"
        exit 1
    fi
    
    # 检查备份文件是否存在
    if [ ! -f "$BACKUP_FILE" ]; then
        log_error "备份文件不存在: $BACKUP_FILE"
        exit 1
    fi
    
    # 检查Docker是否安装
    if ! command -v docker >/dev/null 2>&1; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    # 检查Docker Compose是否安装
    if ! command -v docker-compose >/dev/null 2>&1; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    # 检查Docker服务是否运行
    if ! systemctl is-active --quiet docker; then
        log_info "启动Docker服务..."
        systemctl start docker
    fi
    
    # 检查磁盘空间
    AVAILABLE_SPACE=$(df $(dirname $RESTORE_DIR) | tail -1 | awk '{print $4}')
    BACKUP_SIZE=$(du -k "$BACKUP_FILE" | cut -f1)
    REQUIRED_SPACE=$((BACKUP_SIZE * 3))  # 需要3倍空间用于解压和运行
    
    if [ "$AVAILABLE_SPACE" -lt "$REQUIRED_SPACE" ]; then
        log_error "磁盘空间不足。需要: ${REQUIRED_SPACE}KB, 可用: ${AVAILABLE_SPACE}KB"
        exit 1
    fi
    
    log_success "前置条件检查通过"
}

# 解压备份文件
extract_backup() {
    log_info "解压备份文件..."
    
    TEMP_DIR="/tmp/ragflow-restore-$(date +%s)"
    mkdir -p "$TEMP_DIR"
    
    cd "$TEMP_DIR"
    tar xzf "$BACKUP_FILE"
    
    # 查找解压后的目录
    BACKUP_CONTENT_DIR=$(find . -name "ragflow-backup-*" -type d | head -1)
    if [ -z "$BACKUP_CONTENT_DIR" ]; then
        log_error "备份文件格式不正确"
        exit 1
    fi
    
    BACKUP_CONTENT_DIR="$TEMP_DIR/$BACKUP_CONTENT_DIR"
    log_success "备份文件解压完成: $BACKUP_CONTENT_DIR"
}

# 导入Docker镜像
import_docker_images() {
    log_info "导入Docker镜像..."
    
    if [ -f "$BACKUP_CONTENT_DIR/ragflow-images.tar" ]; then
        docker load -i "$BACKUP_CONTENT_DIR/ragflow-images.tar"
        log_success "Docker镜像导入完成"
        
        # 显示导入的镜像
        log_info "导入的镜像:"
        docker images | grep -E "(ragflow|mysql|minio|elasticsearch|valkey)" | head -10
    else
        log_warning "未找到Docker镜像备份文件"
    fi
}

# 恢复配置文件
restore_config() {
    log_info "恢复RAGFlow配置..."
    
    # 创建恢复目录
    mkdir -p "$RESTORE_DIR"
    
    # 恢复配置文件
    if [ -f "$BACKUP_CONTENT_DIR/ragflow-config.tar.gz" ]; then
        cd "$(dirname $RESTORE_DIR)"
        tar xzf "$BACKUP_CONTENT_DIR/ragflow-config.tar.gz"
        log_success "配置文件恢复完成"
    else
        log_error "未找到配置备份文件"
        exit 1
    fi
}

# 更新配置
update_config() {
    log_info "更新配置文件..."
    
    DOCKER_COMPOSE_FILE="$RESTORE_DIR/ragflow/docker/docker-compose.yml"
    
    if [ -f "$DOCKER_COMPOSE_FILE" ]; then
        # 备份原配置
        cp "$DOCKER_COMPOSE_FILE" "$DOCKER_COMPOSE_FILE.backup"
        
        # 更新IP地址
        if [ -n "$TARGET_IP" ]; then
            sed -i "s/192\.168\.1\.21/$TARGET_IP/g" "$DOCKER_COMPOSE_FILE"
            log_info "已更新IP地址为: $TARGET_IP"
        fi
        
        # 检查端口冲突并修改
        check_and_fix_port_conflicts
        
        log_success "配置文件更新完成"
    else
        log_error "未找到docker-compose.yml文件"
        exit 1
    fi
}

# 检查并修复端口冲突
check_and_fix_port_conflicts() {
    log_info "检查端口冲突..."
    
    DOCKER_COMPOSE_FILE="$RESTORE_DIR/ragflow/docker/docker-compose.yml"
    CONFLICTS_FOUND=false
    
    # 检查常见端口冲突
    for port in 80 443 3306 6379; do
        if netstat -tlnp | grep -q ":$port "; then
            log_warning "端口 $port 已被占用"
            CONFLICTS_FOUND=true
            
            case $port in
                80)
                    sed -i 's/- 80:80/- 8080:80/' "$DOCKER_COMPOSE_FILE"
                    log_info "HTTP端口改为8080"
                    ;;
                443)
                    sed -i 's/- 443:443/- 8443:443/' "$DOCKER_COMPOSE_FILE"
                    log_info "HTTPS端口改为8443"
                    ;;
                6379)
                    sed -i 's/- 6379:6379/- 6380:6379/' "$DOCKER_COMPOSE_FILE"
                    log_info "Redis端口改为6380"
                    ;;
            esac
        fi
    done
    
    if [ "$CONFLICTS_FOUND" = true ]; then
        log_warning "检测到端口冲突，已自动调整端口配置"
    else
        log_success "无端口冲突"
    fi
}

# 创建并恢复数据卷
restore_volumes() {
    log_info "恢复Docker数据卷..."
    
    # 创建数据卷
    docker volume create docker_ragflow-mysql-data
    docker volume create docker_ragflow-minio-data
    docker volume create docker_ragflow-es-data
    docker volume create docker_ragflow-redis-data
    
    # 恢复数据卷内容
    for volume_backup in "$BACKUP_CONTENT_DIR"/docker_ragflow-*-data.tar.gz; do
        if [ -f "$volume_backup" ]; then
            volume_name=$(basename "$volume_backup" .tar.gz)
            
            # 检查备份文件大小
            BACKUP_SIZE=$(stat -c%s "$volume_backup" 2>/dev/null || stat -f%z "$volume_backup" 2>/dev/null)
            
            if [ "$BACKUP_SIZE" -gt 1000 ]; then
                log_info "恢复数据卷: $volume_name"
                docker run --rm \
                    -v $volume_name:/data \
                    -v "$BACKUP_CONTENT_DIR":/backup \
                    alpine tar xzf /backup/$(basename "$volume_backup") -C /data
                log_success "数据卷 $volume_name 恢复完成"
            else
                log_warning "跳过空的数据卷备份: $volume_name"
            fi
        fi
    done
}

# 启动RAGFlow服务
start_ragflow() {
    log_info "启动RAGFlow服务..."
    
    cd "$RESTORE_DIR/ragflow/docker"
    
    # 启动服务
    docker-compose up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 30
    
    # 配置MCP Server自动启动
    log_info "配置MCP Server..."
    if docker exec ragflow-server bash -c "
        cp ./entrypoint.sh ./entrypoint.sh.backup 2>/dev/null || true
        sed -i 's/ENABLE_MCP_SERVER=0/ENABLE_MCP_SERVER=1/g' ./entrypoint.sh
        echo 'MCP Server已启用'
    " 2>/dev/null; then
        log_info "重启RAGFlow应用MCP配置..."
        docker-compose restart ragflow
        sleep 45
    fi
    
    log_success "RAGFlow服务启动完成"
}

# 验证恢复结果
verify_restore() {
    log_info "验证恢复结果..."
    
    # 检查容器状态
    log_info "容器状态:"
    docker-compose ps
    
    # 检查服务访问
    log_info "测试服务访问..."
    
    # 获取实际端口
    HTTP_PORT=$(docker port ragflow-server | grep ":80/tcp" | cut -d: -f2 | head -1)
    API_PORT="9380"
    MCP_PORT="9382"
    
    # 测试服务
    if curl -s http://$TARGET_IP:${HTTP_PORT:-8080} >/dev/null; then
        log_success "RAGFlow Web服务正常"
    else
        log_warning "RAGFlow Web服务可能未就绪"
    fi
    
    if curl -s http://$TARGET_IP:$API_PORT >/dev/null; then
        log_success "RAGFlow API服务正常"
    else
        log_warning "RAGFlow API服务可能未就绪"
    fi
    
    if curl -s http://$TARGET_IP:$MCP_PORT/sse | head -1 | grep -q "event: endpoint"; then
        log_success "MCP Server服务正常"
    else
        log_warning "MCP Server服务可能未就绪"
    fi
}

# 显示恢复结果
show_result() {
    echo "========================================"
    log_success "RAGFlow恢复完成！"
    echo "========================================"
    
    # 获取实际端口
    HTTP_PORT=$(docker port ragflow-server 2>/dev/null | grep ":80/tcp" | cut -d: -f2 | head -1)
    
    echo "访问地址:"
    echo "  RAGFlow Web: http://$TARGET_IP:${HTTP_PORT:-8080}"
    echo "  RAGFlow API: http://$TARGET_IP:9380"
    echo "  MCP Server: http://$TARGET_IP:9382"
    echo "  MinIO: http://$TARGET_IP:9000"
    echo ""
    echo "重要信息:"
    echo "  API密钥: ragflow-UzYTlhMjIwNjI0YzExZjA4NzUyMDI0Mm"
    echo "  安装路径: $RESTORE_DIR"
    echo "  配置文件: $RESTORE_DIR/ragflow/docker/docker-compose.yml"
    echo ""
    echo "后续操作:"
    echo "  1. 验证所有功能正常"
    echo "  2. 配置防火墙开放端口"
    echo "  3. 设置域名解析（如需要）"
    echo "  4. 配置SSL证书（如需要）"
    echo "========================================"
}

# 清理临时文件
cleanup() {
    if [ -n "$TEMP_DIR" ] && [ -d "$TEMP_DIR" ]; then
        log_info "清理临时文件..."
        rm -rf "$TEMP_DIR"
        log_success "清理完成"
    fi
}

# 主函数
main() {
    echo "========================================"
    echo "       RAGFlow完整恢复脚本"
    echo "========================================"
    echo "恢复时间: $(date)"
    echo "========================================"
    
    # 解析参数
    parse_args "$@"
    
    # 查找备份文件和获取IP
    find_backup_file
    get_target_ip
    
    echo "备份文件: $BACKUP_FILE"
    echo "恢复目录: $RESTORE_DIR"
    echo "目标IP: $TARGET_IP"
    echo "========================================"
    
    # 执行恢复步骤
    check_prerequisites
    extract_backup
    import_docker_images
    restore_config
    update_config
    restore_volumes
    start_ragflow
    verify_restore
    show_result
    cleanup
}

# 错误处理
trap cleanup EXIT

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
