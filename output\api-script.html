<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>/api/script</title>
  <style>
/* 基础重置与排版 */
body {
  margin: 0;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.7;
  color: #333;
  background-color: #fff;
  max-width: 960px;
  margin: 0 auto;
  box-sizing: border-box;
}

/* 段落 */
p {
  margin: 1em 0;
}

/* 标题 */
h1, h2, h3, h4, h5, h6 {
  margin: 1.5em 0 0.8em;
  color: #2c3e50;
  font-weight: 600;
  line-height: 1.3;
}

h1 { font-size: 2em; border-bottom: 1px solid #eee; padding-bottom: 0.5em; }
h2 { font-size: 1.6em; }
h3 { font-size: 1.4em; }
h4 { font-size: 1.2em; }

/* 列表 */
ul, ol {
  margin: 1em 0;
  padding-left: 2em;
}

li {
  margin: 0.4em 0;
}

/* 引用块 */
blockquote {
  margin: 1.5em 0;
  padding: 0.8em 1.5em;
  background-color: #f9f9f9;
  border-left: 4px solid #ddd;
  color: #666;
  font-style: italic;
  border-radius: 0 4px 4px 0;
}

/* 代码行内 */
code {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  background-color: #f3f4f6;
  color: #e9602d;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-size: 0.95em;
  white-space: nowrap;
}

/* 代码块 */
pre {
  margin: 1.5em 0;
  padding: 1.2em;
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  overflow-x: auto;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
}

pre code {
  background: none;
  color: inherit;
  padding: 0;
  font-size: inherit;
  white-space: pre;
  display: block;
}

/* 表格 */
table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5em 0;
  font-size: 14px;
  overflow: hidden;
  box-shadow: 0 0 0 1px #e0e0e0;
  border-radius: 6px;
}

th, td {
  padding: 10px 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

th {
  background-color: #f8f9fa;
  color: #2c3e50;
  font-weight: 600;
  white-space: nowrap;
}

tr:nth-child(even) {
  background-color: #f9f9fb;
}

tr:hover {
  background-color: #f0f5ff;
}

/* 链接 */
a {
  color: #1a73e8;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

/* 分隔线 */
hr {
  border: 0;
  height: 1px;
  background: #ddd;
  margin: 2em 0;
}

/* 图片 */
img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 1em 0;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
  </style>
</head>
<body>
  <h2>脚本管理</h2> <div class="api-tabs ant-tabs ant-tabs-top ant-tabs-card ant-tabs-no-animation"><div class="ant-tabs-bar ant-tabs-top-bar ant-tabs-card-bar"><div class="ant-tabs-nav-container"><span class="ant-tabs-tab-prev ant-tabs-tab-btn-disabled"><span class="ant-tabs-tab-prev-icon"><i class="anticon anticon-left ant-tabs-tab-prev-icon-target"><svg class=""><path></path></svg></i></span></span><span class="ant-tabs-tab-next ant-tabs-tab-btn-disabled"><span class="ant-tabs-tab-next-icon"><i class="anticon anticon-right ant-tabs-tab-next-icon-target"><svg class=""><path></path></svg></i></span></span><div class="ant-tabs-nav-wrap"><div class="ant-tabs-nav-scroll"><div class="ant-tabs-nav ant-tabs-nav-animated"><div><div class="ant-tabs-tab">1、查询、列出指定环境中的所有脚本</div><div class="ant-tabs-tab">2、切换指定环境已激活脚本</div><div class="ant-tabs-tab">3、从我的脚本库中指派脚本到目标环境中</div><div class="ant-tabs-tab">4、指定环境中的指定脚本设定为非激活状态</div><div class="ant-tabs-tab-active ant-tabs-tab">5、将未激活脚本从指定环境中移除</div></div><div class="ant-tabs-ink-bar ant-tabs-ink-bar-animated"></div></div></div></div></div></div><div></div><div class="ant-tabs-content ant-tabs-content-no-animated ant-tabs-top-content ant-tabs-card-content"><div class="ant-tabs-tabpane ant-tabs-tabpane-inactive"><ul><li><p>Path：/api/v1/session/id_script_list</p></li> <li><p>Method：POST</p></li> <li><p>Content-Type：application/json</p></li> <li><p>接口描述：列出指定环境中所有包含的脚本。（支持单个或多个环境）</p></li></ul> <blockquote><p>请求参数</p></blockquote> <div class="table"><table><thead><tr><td>参数名称</td> <td>类型</td> <td>必传</td> <td>样例串/默认值</td> <td>说明</td></tr></thead> <tbody><tr><td>Session_ID</td> <td>array</td> <td>是</td> <td>373808cb37bd63f5f7d92415e736e85f</td> <td>指定环境ID查询环境</td></tr></tbody></table></div> <blockquote><p>请求示例</p></blockquote> <div class="code-view"><pre><code>{ "Session_ID": ["373808cb37bd63f5f7d92415e736e85f","705cc4c139e69b729a2fd277f30e1863"] }</code></pre> </div> <blockquote><p>执行成功返回</p></blockquote> <div class="code-view"><pre><code>{ "message": "Session Script List Success", "code": 0, "data": { "listcontainer": [{ "Session_Name": "商用业务环境一", "Session_ID": "373808cb37bd63f5f7d92415e736e85f", "Group_Name": "default", "Actived_script_id": "O73808cb37bd63f5f7d92415e736e999", "Actiived_script_name": "这是一个脚本例子", "Actiived_script_encode": "true", "Script_Count": "4", "UnActived_script_list": [{ "UnActived_script_encode": "false", "UnActived_script_Name": "AAA", "UnActived_script_ID": "17c70e014d61b1fa43d3638ca5a1bc21" },{ "UnActived_script_encode": "false", "UnActived_script_Name": "BBB", "UnActived_script_ID": "17c70e014d61b1fa43d3638ca5a1bc22" }], "status": 0 } } }</code></pre> </div></div><div class="ant-tabs-tabpane ant-tabs-tabpane-inactive"><ul><li><p>Path：/api/v1/session/id_script_active</p></li> <li><p>Method：POST</p></li> <li><p>Content-Type：application/json</p></li> <li><p>接口描述：通过指定单个环境，将此环境中的指定脚本设定为激活状态。</p></li></ul> <blockquote><p>请求参数</p></blockquote> <div class="table"><table><thead><tr><td>参数名称</td> <td>类型</td> <td>必传</td> <td>样例串/默认值</td> <td>说明</td></tr></thead> <tbody><tr><td>Session_ID</td> <td>string</td> <td>是</td> <td>373808cb37bd63f5f7d92415e736e85f</td> <td>单个或多个环境ID</td></tr> <tr><td>Active_Script_ID</td> <td>string</td> <td>是</td> <td>O73808cb37bd63f5f7d92415e736e999</td> <td>环境当前已存在的脚本ID</td></tr></tbody></table></div> <blockquote><p>请求示例</p></blockquote> <div class="code-view"><pre><code>{ "Session_ID": "373808cb37bd63f5f7d92415e736e85f", "Active_Script_ID": "O73808cb37bd63f5f7d92415e736e999" }</code></pre> </div> <blockquote><p>执行成功返回</p></blockquote> <div class="code-view"><pre><code>{ "message": "Session Script Active Success", "code": 0, "data": { "listcontainer": [{ "Session_Name": "商用业务环境一", "Session_ID": "373808cb37bd63f5f7d92415e736e85f", "Group_Name": "default", "Actived_script_id": "O73808cb37bd63f5f7d92415e736e999", "Actiived_script_name": "这是一个脚本例子", "Actiived_script_encode": "true", "Script_Count": "4", "UnActived_script_list": [{ "UnActived_script_encode": "false", "UnActived_script_Name": "AAA", "UnActived_script_ID": "17c70e014d61b1fa43d3638ca5a1bc21" },{ "UnActived_script_encode": "false", "UnActived_script_Name": "BBB", "UnActived_script_ID": "17c70e014d61b1fa43d3638ca5a1bc22" }], "status": 0 } } }</code></pre> </div></div><div class="ant-tabs-tabpane ant-tabs-tabpane-inactive"><ul><li><p>Path：/api/v1/session/id_script_add</p></li> <li><p>Method：POST</p></li> <li><p>Content-Type：application/json</p></li> <li><p>API版本支持：候鸟客户端版本**********及以上</p></li> <li><p>接口描述：从脚本库中将我的单个或多个脚本指派到目标环境中。（支持将单个或多个脚本 ID 分配到指定的单个环境中）</p></li></ul> <blockquote><p>请求参数</p></blockquote> <div class="table"><table><thead><tr><td>参数名称</td> <td>类型</td> <td>必传</td> <td>样例串/默认值</td> <td>说明</td></tr></thead> <tbody><tr><td>Script_ID</td> <td>array</td> <td>是</td> <td>373808cb37bd63f5f7d92415e736e85f,705cc4c139e69b729a2fd277f30e1863</td> <td>指派的所有脚本ID（支持一次最多 100 个）</td></tr> <tr><td>Session_ID</td> <td>string</td> <td>是</td> <td>********************************</td> <td>指定环境(支持一次最多 1 个环境)</td></tr></tbody></table></div> <blockquote><p>请求示例</p></blockquote> <div class="code-view"><pre><code>{ "Script_ID": ["7579a099e6fcee76fd1272ccdc30c1cc","c1f3f1b3d5072985581fe54343f1e524"], "Session_ID": "914a9b97c6787a231ed2ab25e02ad5c9" }</code></pre> </div> <blockquote><p>执行成功返回</p></blockquote> <div class="code-view"><pre><code>{ "message": "Session Script Added Success", "code": 0, "data": { "listcontainer": [{ "Session_Name": "商用业务环境一", "Session_ID": "914a9b97c6787a231ed2ab25e02ad5c9", "Group_Name": "default", "Actived_script_id": "O73808cb37bd63f5f7d92415e736e999", "Actiived_script_name": "这是一个脚本例子", "Actiived_script_encode": "true", "Script_Count": "3", "UnActived_script_list": [{ "UnActived_script_encode": "false", "UnActived_script_Name": "AAA", "UnActived_script_ID": "7579a099e6fcee76fd1272ccdc30c1cc" },{ "UnActived_script_encode": "false", "UnActived_script_Name": "BBB", "UnActived_script_ID": "c1f3f1b3d5072985581fe54343f1e524" }], "status": 0 } } }</code></pre> </div></div><div class="ant-tabs-tabpane ant-tabs-tabpane-inactive"><ul><li><p>Path：/api/v1/session/id_script_Deactivate</p></li> <li><p>Method：POST</p></li> <li><p>Content-Type：application/json</p></li> <li><p>API版本支持：候鸟客户端版本*********及以上</p></li> <li><p>接口描述：通过指定单个环境，将此环境中的指定脚本设定为非激活状态。</p></li></ul> <blockquote><p>请求参数</p></blockquote> <div class="table"><table><thead><tr><td>参数名称</td> <td>类型</td> <td>必传</td> <td>样例串/默认值</td> <td>说明</td></tr></thead> <tbody><tr><td>Session_ID</td> <td>string</td> <td>是</td> <td>373808cb37bd63f5f7d92415e736e85f</td> <td>单个或多个环境ID</td></tr> <tr><td>Script_ID</td> <td>string</td> <td>是</td> <td>O73808cb37bd63f5f7d92415e736e999</td> <td>环境当前已存在的脚本ID</td></tr></tbody></table></div> <blockquote><p>请求示例</p></blockquote> <div class="code-view"><pre><code>{ "Session_ID": "373808cb37bd63f5f7d92415e736e85f", "Script_ID": "O73808cb37bd63f5f7d92415e736e999" }</code></pre> </div> <blockquote><p>执行成功返回</p></blockquote> <div class="code-view"><pre><code>{ "message": "Session Script Deactivate Success", "code": 0, "data": { "listcontainer": [{ "Session_Name": "商用业务环境一", "Session_ID": "914a9b97c6787a231ed2ab25e02ad5c9", "Group_Name": "default", "Actived_script_id": "O73808cb37bd63f5f7d92415e736e999", "Actiived_script_name": "这是一个脚本例子", "Actiived_script_encode": "true", "Script_Count": "3", "UnActived_script_list": [{ "UnActived_script_encode": "false", "UnActived_script_Name": "AAA", "UnActived_script_ID": "7579a099e6fcee76fd1272ccdc30c1cc" },{ "UnActived_script_encode": "false", "UnActived_script_Name": "BBB", "UnActived_script_ID": "c1f3f1b3d5072985581fe54343f1e524" }], "status": 0 } } }</code></pre> </div></div><div class="ant-tabs-tabpane ant-tabs-tabpane-active"><div></div><ul><li><p>Path：/api/v1/session/id_script_Delete</p></li> <li><p>Method：POST</p></li> <li><p>Content-Type：application/json</p></li> <li><p>API版本支持：候鸟客户端版本*********及以上</p></li> <li><p>接口描述：通过指定单个环境，将此环境中的指定未激活脚本(或未激活脚本集合)移除。</p></li></ul> <blockquote><p>请求参数</p></blockquote> <div class="table"><table><thead><tr><td>参数名称</td> <td>类型</td> <td>必传</td> <td>样例串/默认值</td> <td>说明</td></tr></thead> <tbody><tr><td>Session_ID</td> <td>string</td> <td>是</td> <td>914a9b97c6787a231ed2ab25e02ad5c9</td> <td>单个或多个环境ID</td></tr> <tr><td>Script_ID</td> <td>array</td> <td>是</td> <td>373808cb37bd63f5f7d92415e736e85f,705cc4c139e69b729a2fd277f30e1863</td> <td>环境当前已存在的未激活脚本ID</td></tr></tbody></table></div> <blockquote><p>请求示例</p></blockquote> <div class="code-view"><pre><code>{ "Session_ID": "914a9b97c6787a231ed2ab25e02ad5c9", "Script_ID": ["373808cb37bd63f5f7d92415e736e85f","705cc4c139e69b729a2fd277f30e1863"] }</code></pre> </div> <blockquote><p>执行成功返回</p></blockquote> <div class="code-view"><pre><code>{ "message": "Session Script Delete Success", "code": 0, "data": { "listcontainer": [{ "Session_Name": "商用业务环境一", "Session_ID": "914a9b97c6787a231ed2ab25e02ad5c9", "Group_Name": "default", "Actived_script_id": "O73808cb37bd63f5f7d92415e736e999", "Actiived_script_name": "这是一个脚本例子", "Actiived_script_encode": "true", "Script_Count": "3", "UnActived_script_list": [{ "UnActived_script_encode": "false", "UnActived_script_Name": "AAA", "UnActived_script_ID": "7579a099e6fcee76fd1272ccdc30c1cc" },{ "UnActived_script_encode": "false", "UnActived_script_Name": "BBB", "UnActived_script_ID": "c1f3f1b3d5072985581fe54343f1e524" }], "status": 0 } } }</code></pre> </div><div></div></div></div><div></div></div> <p><a class="ant-btn ant-btn-primary">使用POSTMAN调试此接口</a></p>
</body>
</html>