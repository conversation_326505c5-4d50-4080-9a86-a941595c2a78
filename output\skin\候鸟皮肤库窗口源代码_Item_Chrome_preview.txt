<?xml version="1.0" encoding="UTF-8"?>
<Window>
	<PreviewItem name="PreviewItem" width="320" >
		<Control width="2"/>
		 <VerticalLayout >
              <HorizontalLayout name="cap" height="32" bkcolor="#FF44475A">
              	<Control name="webIcon" bkimage="chromium.png"  padding="4,10,0,0" width="16" height="16"/>
                <Label name="title" padding="0,1,0,0" text="淘宝网 - 淘！我喜欢" endellipsis="true" width="180"  textpadding="6,0,0,0" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" align="left" font="8"></Label>
                <Control />
                <Button name="closethumb" padding="0,4,6,0" width="24" height="24"  tooltip="关闭" normalimage="slider\close.png" hotimage="slider\closeHover.png" pushedimage="slider\closeclick.png" />
              </HorizontalLayout>
               <WndChrome name="wndChrome"  bkimage="thumb\1.png"/>
              <!--Button name="actbtn"  padding="0,0,0,0" width="300" height="200"  tooltip="查看" normalimage="thumb\1.png" hotimage="thumb\1.png" pushedimage="thumb\1.png" /-->
            <HorizontalLayout height="32" bkcolor="#ffe9e9e9">
              <CheckBox name="opt_check" text="" padding="6,4,0,0" height="24" width="24" normalimage="slider\check.png" hotimage="slider\checkClick.png" selectedimage="slider\checked.png" ></CheckBox>
              <Label name="nodename" padding="4,2,0,0" text="会话环境名称" endellipsis="true" width="242" textcolor="#FF616161" hottextcolor="#ff000000" align="left" font="8"></Label>
              <!--Button name="freshbtn"  padding="0,4,6,0" width="24" height="24"  tooltip="刷新" normalimage="slider\refresh.png" hotimage="slider\refreshHover.png" pushedimage="slider\refreshclick.png" /-->
            </HorizontalLayout>
     </VerticalLayout>
     <Control width="2"/>
	</PreviewItem>
</Window>
