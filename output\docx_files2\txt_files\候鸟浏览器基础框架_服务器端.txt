服务器端

第二十四章 候鸟浏览器

服务器端

客户数据自动分析

逻辑流程及约定。

第二十四章 候鸟浏览器 服务器端客户数据自动分析逻辑流程及约定。[2022-02-04 新增]

第一步：

域名：analyse.mbbrowser.com 指向到 原阿里云备份服务器（备份中转服务器），原此服务器备份逻辑，同步到8T服务器逻辑照常运转不变。

第二步：搭建nginx,mysql,php环境。

第三步：安装webmin,置入phpmyadmin

第四步：lnmp start

第五步：nginx.conf里加入analyse.mbbrowser.com域名, lnmp restart

第六步：在analyse.mbbrowser.com 服务器中 mysql里建表。

主表一 analyse_main

id, 唯一标识(主键)

uid,正式服主USER表用户ID

user_account, 登录名

item_path, item包相对路径

item_filename, item包文件名

item_filesize, item包文件大小

item_creat_time,item文件创建时间

template_id, 如有使用模板，显示模板ID

is_vip, 用户是否已付过费(bit)

is_team,是否为团队协作帐户 (bit)

is_team_owner,是否为团队协作主帐户 (bit)

last_login,用户最近一次使用客户端时间

client_version, item包所属客户端版本

create_time, 记录创建时间 (默认值：timestamp)

is_valid, 此记录是否有效 (默认值：1) (bit)

说明：此表以item文件为单位，每个item文件占一行记录。 例：一个用户下有1000个ITEM文件，则在表一中，有1000条记录。

子表二 analyse_fav

fav_id,唯一标识(主键)

item_id, item_id = 表一的id

favurl, 收藏夹url(一条记录一个url，如单个item下，有多个favurl，则有多个记录)

favurl_title, 收藏url的title

fav_domain_name, favurl的主域名，例：fav_url = www.baidu.com/index.html 则domain_name = baidu.com

create_time,,记录创建时间 (默认值：timestamp)

is_valid, 此记录是否有效 (默认值：1) (bit)

子表三 analyse_history

history_id,唯一标识(主键)

item_id, item_id = 表一的id

history_url  网站历史访问URL (一条记录一个url，如单个item下，有多个history url，则有多个记录)

history_url_title 网站访问url的标题

history_url_last_visit_time url最后一次访问时间

history_domain_name, history的主域名，例：history_url = www.baidu.com/index.html 则history_domain_name = baidu.com

create_time,,记录创建时间 (默认值：timestamp)

is_valid, 此记录是否有效 (默认值：1) (bit)

子表四 analyse_cookie

cookie_id,唯一标识(主键)

item_id, item_id = 表一的id

cookie_content, item中cookies明文内容

create_time,,记录创建时间 (默认值：timestamp)

is_valid, 此记录是否有效 (默认值：1) (bit)

第七步：将正式服对zip文件解包，xml内容解析的代码放置到  analyse.mbbrowser.com服务器中。

第八步：将正式服数据库导入到analyse.mbbrowser.com服务器mysql中。

第九步：修改原有php代码，增加遍历item表，将temp zip进行解压缩，并解析xml内容，读取主表内容，并入库到上述四个表中。

第十步：制作analyse.mbbrowser.com分析页面（先让生提需求，大家进行补充，然后开始做客户数据分析页面。）

[2022-02-11 新增]

第二十五章 【运维】

候鸟浏览器

RSYNC服务器端

客户数据自动备份

逻辑详细流程及约定。

第二十五章 【运维】候鸟浏览器 RSYNC服务器端客户数据自动备份逻辑详细流程及约定。[2022-02-11 新增]

A：【近期：正式服数据完整备份到8T服阶段（短期）】

1、在8T服上 增加一个计划任务，临晨2:00-8:00 这个时间段，进行6个小时通过公网IP同步正式服剩余数据到8T服。

B：【日常：正式服数据同步到8T服(长期)】

1、备份服由于空间成本问题，不再和正式服进行同步关系。

2、8T服通过RSYNC自带的SSH-PROXY通过备份服内网IP带宽，连入正式服，每天在临晨2:00-8:00 这个时间段定时触发进行日常数据同步。

C： 【正式服垃圾数据清理和用户冗余数据存储优化】

1、当A项工作完成后，着手进行正式服的垃圾数据人工清理，清理不要用命令行方式临时清理，需要做到SH脚本中，方便以后复用。

SH脚本逻辑：

(1) userdata下 递规每个帐户文件夹中full.zip的文件列表，对于超过3个月的full包文件，每天只取最大文件字节的在原文件夹中仅保留一份。其它的移出userdata文件夹，转移到History_userdata_rubish_full/account/文件夹中.

(2) userdata下 递规每个帐户文件夹中full.zip的文件列表，对于当前时间大于1个月，小于3个月的full包文件，对于相同时间的full包文件，只保留一份。其它的移出userdata文件夹，转移到History_userdata_rubish_full/account/文件夹中.

(3) userdata下 递规每个帐户文件夹中full.zip的文件列表，对于当前时间小于1个月文件范围，不作任何处理。

(4) 8T服针对正式服的 History_userdata_rubish_full/account/文件夹 增加rsync配置，同步到8T服本地History_userdata_rubish_full/account/文件夹里。

2、（重点）：full包同步逻辑二次完善：

原FULL包同步接口：正式服优化用户数据存储逻辑，在用户上传full包时，服务器端在存储full包的环节中，原流程不变，增加判定是否此用户的userdata/account/文件夹中10分钟内有保存过full包，如果有保存过full包，先将保存过的full包转移到History_userdata_rubish_full/account/文件夹中.然后继续保存当前用户上传的full包。

D: 备份服公网IP带宽利用

1、备份服的内网IP带宽被8T服的rsync接管利用proxy方式进行B项的日常同步。

2、备份服上的rsync进程不再和正式服打交道。

3、备份服上的rsync进程利用公网IP带宽资源获取8T服上的最近三个月数据同步｛使用rsync shell-command find方式｝。

4、备份服上提供item数据分析原约定不变。

同步进行的工作顺序是：

1、A项工作和 D项工作的第4项同步开展进行。【当前】

2、完成之后，进行B项的第2步和C项的第1步。

3、之后进行D项的第3步和C项的第2步同时进行。

function move {

dir=/root/old/$1

# 移动空目录

[ "` ls $1 `" ] || { mv $1 $dir; return; }

mkdir -p $dir

# 移动当前目录下字节数为空的文件

find $1 -mindepth 1 -maxdepth 1 -type f -size 0 -exec mv -t $dir {} \+

# 移动当前目录下时间超过一年的文件

find $1 -mindepth 1 -maxdepth 1 -type f -mtime +365 -exec mv -t $dir {} \+

if [[ $1 =~ /sharedata/ ]]; then

# 移动日期非最新的Itm.zip文件

ls -vr ${1:-.}/*Item.zip 2> /dev/null | tail -n +3 | xargs mv -t $dir 2> /dev/null

else

# 移动三个月前日期非最新的full.zip文件

find $1 -maxdepth 1 -name '*full.zip' -mtime +90 -exec ls -glot --time-style=+%-d {} \+ | awk '{ if($4==date) print $5; else date=$4 }' | xargs -r mv -vt $dir >> /home/<USER>

# 移动三个月内full.zip文件 # 移动三个月内full.zip文件未超过三个月的，保留每天最近的版本5个 + 每天full文件size最大的五条

find $1 -maxdepth 1 -name '*full.zip' -mtime -90 -exec ls -glot --time-style=+%-d {} \+ | awk 'func process() { "ls -S "all" | head -5 | xargs" | getline size; for(i in time) sub(i," ",all); split(size, s); for(i in s) sub(s[i]," ",all); print all } { if($4==date) { all=$5" "all; if(count<5) { count++; time[$5] } } else { if(NR>1) { process(); all=size=""; delete time } date=$4; count=1; time[$5]; all=$5" "all } } END{ process() }' | xargs -r mv -vt $dir >> /home/<USER>

fi

# 递归子目录

find $1 -mindepth 1 -maxdepth 1 -type d | while read dir; do

move $dir

done

}

move $1

function move {

dir=/www/old/$1

# 移动空目录

[ "` ls $1 `" ] || { mv $1 $dir; return; }

mkdir -p $dir

# 移动当前目录下字节数为空的文件

find $1 -mindepth 1 -maxdepth 1 -type f -size 0 -exec mv -t $dir {} \+

# 移动当前目录下时间超过一年的文件

find $1 -mindepth 1 -maxdepth 1 -type f -mtime +365 -exec mv -t $dir {} \+

if [[ $1 =~ /sharedata/ ]]; then

# 移动日期非最新的Itm.zip文件

#ls -lvr ${1:-.}/*Item.zip 2> /dev/null | tail -n +3 | xargs mv -t $dir 2> /dev/null

else

# 移动日期非最新的full.zip文件

ls -glot --time-style=+%-d ${1:-.}/*full.zip 2> /dev/null | awk '{ if($4==date) print $5; else date=$4 }' | xargs mv -t $dir 2> /dev/null

fi

# 递归子目录

find $1 -mindepth 1 -maxdepth 1 -type d | while read dir; do

move $dir

done

}

move $1

function move {

dir=/www/old/$1

# 移动空目录

[ "` ls $1 `" ] || { mv $1 $dir; return; }

mkdir -p $dir

# 移动当前目录下字节数为空的文件

find $1 -mindepth 1 -maxdepth 1 -type f -size 0 -exec mv -t $dir {} \+

# 移动日期非最新的Itm.zip文件

ls -vr ${1:-.}/*Item.zip 2> /dev/null | tail -n +3 | xargs mv -t $dir 2> /dev/null

# 递归子目录

find $1 -mindepth 1 -maxdepth 1 -type d | while read dir; do

move $dir

done

}

move $1

function move {

dir=/www/old/$1

# 移动空目录

[ "` ls $1 `" ] || { mv $1 $dir; return; }

mkdir -p $dir

# 移动当前目录下字节数为空的文件

find $1 -mindepth 1 -maxdepth 1 -type f -size 0 -exec mv -t $dir {} \+

# 移动当前目录下时间超过一年的文件

find $1 -mindepth 1 -maxdepth 1 -type f -mtime +365 -exec mv -t $dir {} \+

if [[ $1 =~ /sharedata/ ]]; then

# 移动日期非最新的Itm.zip文件

ls -lvr ${1:-.}/*Item.zip 2> /dev/null | tail -n +3 | xargs mv -t $dir 2> /dev/null

else

# 移动日期非最新的full.zip文件

ls -glot --time-style=+%-d ${1:-.}/*full.zip 2> /dev/null | awk '{ if($4==date) print $5; else date=$4 }' | xargs mv -t $dir 2> /dev/null

fi

# 递归子目录

find $1 -mindepth 1 -maxdepth 1 -type d | while read dir; do

move $dir

done

}

move $1

rsync -rtopg --password-file=/etc/rsyncd.bak2bak.pass backup2@*************::bakto8T /www/wwwroot/admin.mbbrowser.com

[2022-03-18 新增] [2023-07-21 新增]


================================================== 表格内容 ==================================================

参数名称 | 类型 | 必传 | 样例串/默认值 | 说明
Session_ID | string | 是 | 373808cb37bd63f5f7d92415e736e85f | 环境ID
Session_Name | string | 否 | 商业环境业务一 | 环境名称，最大长度60字
Session_Desc | string | 否 | 环境描述 | 最大长度150字
Session_Group | string | 否 | 环境所属分组 | 最大长度30字，如未填写值则为默认分组
Cookie | string | 否 | Cookie内容 | Json格式cookie文本
Disable_video | bit | 否 | 0 | 视频限流 0关闭 1开启 默认关闭
Disable_img | bit | 否 | 0 | 图片限流 0关闭 1开启 默认关闭
HomePage_url | string | 否 | https://www.baidu.com | 设置环境开启起始页，未设置时默认值:https://www.yalala.com/?wd=mb

{
"message": "Update Session Success",
"code": 0,
"data": {
            “Session_Id” : 373808cb37bd63f5f7d92415e736e85f 	//环境ID
       }
}