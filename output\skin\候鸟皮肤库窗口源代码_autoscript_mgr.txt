﻿<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Window size="1578,1169" sizebox="4,4,4,4" caption="0,0,0,50" mininfo="1140,680" roundcorner="5,5,5,5" showshadow="true" shadowsize="3" shadowposition="0,0" shadowcolor="#FF44475A" fademode="true">
  <Include source="Default.xml" />

  <VerticalLayout width="953" height="590" bkcolor="#FF282A36">
    <HorizontalLayout height="37">

    	 <Button name="login" bkimage="icon.png" padding="8,6,0,0" width="28" height="28"/>
      <Label name="autoscriptswndtitle" padding="6,4,0,0" text="浏览器插件管理器" width="260" textcolor="#FFF8F8F2" hottextcolor="#FFBD93F9" align="left" font="8"></Label>

      <Control />
      <Button name="minbtn" width="28" height="26" tooltip="最小化" normalimage="minbtn.png" hotimage="minbtn_hover.png" pushedimage="minbtnpush.png" />
      <Button name="maxbtn" width="28" height="26" tooltip="最大化" normalimage="maxbtn.png" hotimage="maxbtn_hover.png" pushedimage="maxbtnpush.png" />
      <Button name="restorebtn" visible="false" width="28" height="26" tooltip="还原" normalimage="restorebtn.png" hotimage="restorebtn_hover.png" pushedimage="restorebtnpush.png" />
      <Button name="closebtn" width="28" height="26"  tooltip="关闭" normalimage="closebtn.png" hotimage="closebtn_hover.png" pushedimage="closebtnpush.png" />

    </HorizontalLayout>
  <HorizontalLayout name="bkground">


 <HorizontalLayout name="loading_data" bkcolor="#FF21222C" visible="true">

	    <VerticalLayout >

        <HorizontalLayout></HorizontalLayout>
					     <HorizontalLayout name="loading_data" height="240">
					    	 <Control />
					    		<GifAnim name="data_loading" bkimage="dataloading.gif" height="200" width="200" padding="0,40,0,0" auto="true"/>
                 <Control name="success" visible="false" padding="0,100,0,0"  bkimage="success.png" width="120" height="120" align="center" />
					    	 <Control />
					     </HorizontalLayout>


					     <HorizontalLayout height="30" >
					    	 <Control />
					    		  <Label name="data_percent" text="0%" width="300" textcolor="#FFF8F8F2" hottextcolor="#FFBD93F9" align="center" font="10"></Label>
					    	 <Control />
					     </HorizontalLayout>

					     <HorizontalLayout height="60" >
					    	 <Control />
					    		  <Label name="process_description" text="脚本正在版本验证中，请稍侯.. " width="953" textcolor="#FFF8F8F2" hottextcolor="#FFBD93F9" align="center" font="8"></Label>
					    	 <Control />
					     </HorizontalLayout>

              <HorizontalLayout name="backarea" width="953" height="60" visible="false">
                <Control />
                <Button text="返回" name="back" width="120" height="30" textcolor="#FFF8F8F2" disabledtextcolor="#FF6272A4" align="center" font="14" bkcolor="#FF44475A" hotbkcolor="#FF6272A4" pushedbkcolor="#FF6272A4" borderround="5,5" />
                <Control width="100" />
                <Button text="退出" name="closewnd1" width="120" height="30" textcolor="#FFF8F8F2" disabledtextcolor="#FF6272A4" align="center" font="14" bkcolor="#FF44475A" hotbkcolor="#FF6272A4" pushedbkcolor="#FF6272A4" borderround="5,5" />
                <Control />
              </HorizontalLayout>

        <HorizontalLayout></HorizontalLayout>
      </VerticalLayout>

 </HorizontalLayout>

		<VerticalLayout name="data" visible="false">


			<HorizontalLayout height="56" >
        <Control width="20"/>
        <VerticalLayout width="260">
          <Combo name="autoscripts" bordersize="0" padding="0,10,0,10" width="250" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="8" bkcolor="#ffdce1e7"
              normalimage="file='Profile\Proxy_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\proxy_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\proxy_Combox_Click.png' corner='5,5,25,10'"
              combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
              itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0" textpadding="0,0,20,0" endellipsis="true">
          </Combo>
        </VerticalLayout>
        <VerticalLayout name="combotypearea" visible="false" width="210">
          <Combo name="combotype" bordersize="0" padding="0,10,0,3" width="200" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="8" bkcolor="#ffdce1e7"
          normalimage="file='Profile\Proxy_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\proxy_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\proxy_Combox_Click.png' corner='5,5,25,10'"
          combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
          itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0" textpadding="0,0,4,0" endellipsis="true" font="8">
          </Combo>
        </VerticalLayout>
        <VerticalLayout name="searchlistarea" width="162">
          <Combo name="searchlist" reselect="true" dropboxsize="0,450" bordersize="0" padding="1,10,0,10" width="162" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="4,3,0,0" bkcolor="#ffdce1e7"
														normalimage="file='Profile\Setting_Combox_Normal_big_large.png' corner='5,5,30,5'" hotimage="file='Profile\Setting_Combox_Hover_large.png' corner='5,5,30,5'" pushedimage="file='Profile\Setting_Combox_Click_large.png' corner='5,5,30,5'"
												combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,30,5'"
												itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0">
          </Combo>
				         <RichEdit name="plugin_search" pos="0,10,0,4" height="36" width="122" tipvaluecolor="#FF333333" borderround="7,7" bkcolor="#ffdce1e7" font="8" textpadding="10,8,10,0" maxchar="300" tipvalue="请输入关键字.." multiline="false" textcolor="#ff333333" rich="false" transparent="false" float="true">
				      </RichEdit>
				</VerticalLayout>
        <Control />
        <VerticalLayout width="250">
          <Combo name="group" bordersize="0" textpadding="0,0,30,0" padding="0,10,0,16" width="250" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="8" bkcolor="#ffdce1e7"
              normalimage="file='Profile\Proxy_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\proxy_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\proxy_Combox_Click.png' corner='5,5,25,10'"
              combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
              itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0" endellipsis="true">
          </Combo>
        </VerticalLayout>
        <VerticalLayout width="196">
          <RichEdit name="session_search" padding="6,10,0,10" height="36" width="188" tipvaluecolor="#FF333333" borderround="7,7" bkcolor="#ffdce1e7" font="8" textpadding="10,8,20,0" maxchar="300" tipvalue="请输入关键字查找会话.." multiline="false" textcolor="#ff333333" rich="false" transparent="false">
          </RichEdit>
        </VerticalLayout>
        <Control width="20"/>
        <!--<VerticalLayout width="230">
              <Combo name="agent" bordersize="0" padding="21,0,0,10" width="200" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="4,2,0,0" bkcolor="#ffdce1e7"
                   normalimage="file='Profile\Setting_Combox_Normal_big_large.png' corner='5,5,25,10'" hotimage="file='Profile\Setting_Combox_Hover_large.png' corner='5,5,25,10'" pushedimage="file='Profile\Setting_Combox_Click_large.png' corner='5,5,25,10'"
               combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
               itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0">
              </Combo>
            </VerticalLayout>-->
      </HorizontalLayout>

			<HorizontalLayout height="4" bkcolor="#FF282A36">
	</HorizontalLayout>

      <HorizontalLayout bkcolor="#FF282A36">
        <Control width="20"/>
        <VerticalLayout>
          <List name="list_autoscripts_manager" inset="0,0,1,1" mouseunselect="true" scrollwheel="true" bordersize="1" itembkcolor="#FF282A36" itemselectedbkcolor="#FF44475A" itemhotbkcolor="#FF21222C" bordercolor="#FF44475A" vscrollbar="true" floatscrollbar="true" scrollfbarFade="128">
						<ListHeader height="36" bordersize="1,1,0,1" bordercolor="#FF44475A" bkcolor="#FF21222C">
							<ListHeaderItem text="操作" name="header_device_choice" width="50" align="left" textpadding="10,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="插件名称" name="header_name" width="150" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="创建时间" name="header_date" width="150" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="加密" name="ListheadernameAec" width="50" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <!--<ListHeaderItem text="版本号" name="header_system" width="146" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>-->
              <ListHeaderItem text="描述" name="header_proxy" width="170" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="" name="header_id" width="260" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
            </ListHeader>
          </List>
          <List name="list_autoscripts_manager2" visible="false" inset="0,0,1,1" mouseunselect="true" scrollwheel="true" bordersize="1" itembkcolor="#FF282A36" itemselectedbkcolor="#FF44475A" itemhotbkcolor="#FF21222C" bordercolor="#FF44475A" vscrollbar="true" floatscrollbar="true" scrollfbarFade="128">
            <ListHeader height="36" bordersize="1,1,0,1" bordercolor="#FF44475A" bkcolor="#FF21222C">
              <ListHeaderItem text="操作" name="header_device_choice" width="50" align="left" textpadding="10,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="插件名称" name="header_name" width="150" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="" name="header_type" width="116" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="描述" name="header_proxy" width="320" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="" name="header_size" width="60" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="创建时间" name="header_date" width="150" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <!--<ListHeaderItem text="" name="header_id" width="260" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>-->
            </ListHeader>
          </List>
          <HorizontalLayout height="30" >
            <Button name="selallautoscripts" padding="4,4,0,0" align="center" height="20" autocalcwidth="true" text="全选" font="5" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" />
            <Control width="10"/>
            <Button name="unselallautoscripts" padding="4,4,0,0" align="center" height="20" autocalcwidth="true" text="反选" font="5" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" />
            <Control width="10"/>
            <Button name="delautoscripts" enabled="false" padding="4,4,0,0" align="center" height="20" autocalcwidth="true" text="删除" font="5" textcolor="#FFF8F8F2" hottextcolor="#FFFF5555" />
            <Control width="10"/>
            <Button name="import" padding="4,4,0,0" align="center" height="20" autocalcwidth="true" text="导入" font="5" textcolor="#FFF8F8F2" hottextcolor="#FFFF5555" />
            <Control width="10"/>
            <Button name="export" enabled="false" padding="4,4,0,0" align="center" height="20" autocalcwidth="true" text="导出" font="5" textcolor="#FFF8F8F2" hottextcolor="#FFFF5555" />
            <Control width="10"/>
            <Button name="edit" enabled="false" padding="4,4,0,0" align="center" height="20" autocalcwidth="true" text="编辑" font="5" textcolor="#FFF8F8F2" hottextcolor="#FFFF5555" />
            <Control width="10"/>
            <Button name="refreshplugin" padding="4,4,0,0" align="center" height="20" autocalcwidth="true" text="刷新" font="5" textcolor="#FFF8F8F2" hottextcolor="#FFFF5555" />
            <Control width="10"/>
            <Button name="openfolder" padding="4,4,0,0" align="center" height="20" autocalcwidth="true" text="打开文件夹" font="5" textcolor="#FFF8F8F2" hottextcolor="#FFFF5555" />
          </HorizontalLayout>
        </VerticalLayout>
        <Control width="20"/>
        <VerticalLayout width="446">
          <List name="list_session_manager" inset="0,0,1,1" scrollwheel="true" bordersize="1" itembkcolor="#FF282A36" itemselectedbkcolor="#FF44475A" itemhotbkcolor="#FF21222C" bordercolor="#FF6272A4" vscrollbar="true" floatscrollbar="true" scrollfbarFade="128">
            <ListHeader height="36" bordersize="1,1,0,1" bordercolor="#FF44475A" bkcolor="#FF21222C">
              <ListHeaderItem text="操作" name="header_device_choice" width="50" align="left" textpadding="10,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="环境名称" name="header_name" width="192" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="创建时间" name="header_cdate" width="150" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="脚本数" name="header_proxy" width="56" align="center" textpadding="0,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
            </ListHeader>
          </List>

          <HorizontalLayout height="30" >

            <Label name="lb_autoscriptsinst" text="已选中环境包含的脚本列表" padding="0,4,0,0" width="300" texttooltip="true" endellipsis="true" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" align="left" font="8"></Label>
            <Control />
            <Button name="selallsession" padding="4,4,0,0" align="left" height="20" width="26" text="全选" font="5" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" />
            <Button name="unselallsession" padding="4,4,0,0" align="left" height="20" width="26" text="反选" font="5" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" />
          </HorizontalLayout>

          <List name="list_autoscripts_items" inset="0,0,1,1" scrollwheel="true" bordersize="1" itembkcolor="#FF282A36" itemselectedbkcolor="#FF44475A" itemhotbkcolor="#FF21222C" bordercolor="#FF6272A4" vscrollbar="true" floatscrollbar="true" scrollfbarFade="128">
            <ListHeader height="36" bordersize="1,1,0,1" bordercolor="#FF44475A" bkcolor="#FF21222C">
              <ListHeaderItem text="操作" name="header_device_choice" width="50" align="left" textpadding="10,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="已安装插件名称" name="header_name" width="202" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="最近使用" name="header_utime" width="128" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="状态" name="header_status" width="60" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
            </ListHeader>
          </List>
          <HorizontalLayout height="30" >
            <!--<Button name="selallautoscriptsinst" padding="4,4,0,0" align="left" height="20" width="26" text="全选" font="5" hottextcolor="#FF005ed3" />
            <Button name="unselallautoscriptsinst" padding="4,4,0,0" align="left" height="20" width="26" text="反选" font="5" hottextcolor="#FF005ed3" />-->
            <Button name="editautoscriptsinst" enabled="false" padding="4,4,0,0" align="left" height="20" autocalcwidth="true" text="编辑脚本" font="5" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" />
            <Control width="10"/>
            <Button name="runautoscriptsinst" enabled="false" padding="4,3,0,0" align="center" height="20" autocalcwidth="true" text="调试运行" font="5" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" />
            <Control width="10"/>
            <Button name="actautoscriptsinst" enabled="false" padding="4,4,0,0" align="left" height="20" autocalcwidth="true" text="激活脚本" font="5" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" />
            <Control />
            <Button name="delautoscriptsinst" enabled="false" padding="4,4,0,0" align="left" height="20" autocalcwidth="true" text="删除选中脚本" font="5" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" />
            <Control width="10"/>
            <Button name="delallautoscriptsinst" enabled="false" padding="4,4,0,0" align="left" height="20" autocalcwidth="true" text="删除所有脚本" font="5" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" />
          </HorizontalLayout>
        </VerticalLayout>
        <Control width="20"/>
				</HorizontalLayout>
		</VerticalLayout>

	</HorizontalLayout>
    <HorizontalLayout name="btnarea" visible="false" height="52" bkcolor="#ffe9e9e9" inset="10,0,0,0">
         <!--<CheckBox name="opt_checkAll" text="全选" textpadding="57,1,0,0" selected="false"  visible="true" padding="3,16,0,0" height="18" width="16" normalimage="file='list_check_normal.png'" hotimage="list_check_hover.png" selectedimage="file='list_checked_actvice_normal.png'" ></CheckBox>-->
        <Label name="status" text="" padding="10,0,0,0" textcolor="#FF0006ff" hottextcolor="#ff000000" align="left" font="8"></Label>
      <HorizontalLayout name="statusareainst" visible="false">
        <Label name="statusareainst1" text="已成功将" width="60" padding="10,0,0,0" textcolor="#FF373737" hottextcolor="#ff000000" align="left" font="8"></Label>
        <Label name="statusareanameinst1" text=""  minwidth="20" autocalcwidth="true" maxwidth="300" textcolor="#FF0006ff" hottextcolor="#ff000000" font="8"></Label>
        <Label name="statusareainst2" text="" autocalcwidth="true" width="200" minwidth="20" maxwidth="300" padding="6,0,0,0" textcolor="#FF373737" hottextcolor="#ff000000" align="left" font="8"></Label>
      </HorizontalLayout>
      <HorizontalLayout name="statusarea" visible="false">
        <Label name="statusarea1" text="您已成功在环境" width="106" padding="10,0,0,0" textcolor="#FF373737" hottextcolor="#ff000000" align="left" font="8"></Label>
        <Label name="statusareaname1" text="17" autocalcwidth="true" minwidth="20" maxwidth="300" padding="0,0,0,0" textcolor="#FF0006ff" hottextcolor="#ff000000" font="8"></Label>
        <Label name="statusarea2" text="中激活脚本：" width="82" padding="6,0,0,0" textcolor="#FF373737" hottextcolor="#ff000000" align="left" font="8"></Label>
        <Label name="statusarea22" text="中取消激活脚本：" visible="false" width="112" padding="6,0,0,0" textcolor="#FF373737" hottextcolor="#ff000000" align="left" font="8"></Label>
        <Label name="statusareaname2" text="这是一个puteer测试脚本" autocalcwidth="true" minwidth="20" maxwidth="300" padding="4,0,0,0" textcolor="#FF0006ff" hottextcolor="#ff000000" align="left" font="8"></Label>
        <!--<Label name="statusarea3" text="脚本。" width="60" padding="10,0,0,0" textcolor="#FF373737" hottextcolor="#ff000000" align="left" font="8"></Label>-->
      </HorizontalLayout>
         <Control width="10"/>
      <Button name="help" padding="4,20,0,0" align="right" height="20" width="126" text="在线帮助" texttooltip="true" endellipsis="true" font="5" textcolor="#FF519cff" hottextcolor="#FF005ed3" />
      <Control width="10"/>
      <VerticalLayout width="164">
        <Control />
        <Button text="新建自动化脚本" name="newautoscript" tooltip="新建自动化脚本" padding="2,2,0,0"  textpadding="32,0,16,0" texttooltip="true" endellipsis="true" width="160" height="30" textcolor="#FF4f4f4f" disabledtextcolor="#FFB3B3B3" align="center" font="14"	bkimage="file=&apos;cbg_btn.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;cbg_btn_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;cbg_btn_Click.png&apos; corner=&apos;5,10,5,10&apos;" bkcolor="#FFfffae0" hotbkcolor="#fffff6c8"/>
        <Control />
      </VerticalLayout>
      <Control width="16"/>
      <VerticalLayout width="260">
      		<Control />
          <Button text="将选中的脚本安装到勾选的环境中" enabled="false" name="instautoscripts" tooltip="将选中的脚本安装到勾选的环境中" endellipsis="true" padding="2,2,0,0" textpadding="10,0,10,0" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	bkcolor="#FFffffff" bordersize="1" bordercolor="#ffb3b3b3" borderround="7,7" hotbkcolor="#fff1f2f1" />
          <Control />
      </VerticalLayout>
      <Control  width="20"/>
    </HorizontalLayout>
    <Control name="dragicon" float="true" width="14" height="16" bkimage="dragicon.png"/>
  </VerticalLayout>
</Window>
