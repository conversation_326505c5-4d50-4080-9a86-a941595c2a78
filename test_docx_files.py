#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试DOCX文件是否损坏
"""

from pathlib import Path
from docx import Document

def test_docx_files(directory):
    """测试目录中的DOCX文件"""
    docx_path = Path(directory)
    framework_files = list(docx_path.glob("候鸟浏览器基础框架(*.docx"))
    
    print(f"📁 找到 {len(framework_files)} 个框架文件")
    print("=" * 60)
    
    for file_path in sorted(framework_files):
        try:
            doc = Document(str(file_path))
            para_count = len(doc.paragraphs)
            table_count = len(doc.tables)
            file_size = file_path.stat().st_size / 1024
            
            # 检查内容
            text_content = ""
            for para in doc.paragraphs[:5]:
                if para.text.strip():
                    text_content += para.text.strip()[:50] + "... "
            
            print(f"✅ {file_path.name}")
            print(f"   大小: {file_size:.1f} KB")
            print(f"   段落: {para_count}, 表格: {table_count}")
            print(f"   内容预览: {text_content}")
            print()
            
        except Exception as e:
            print(f"❌ {file_path.name}")
            print(f"   错误: {str(e)}")
            print()

if __name__ == "__main__":
    test_directory = r"F:\augment\output\docx_files"
    test_docx_files(test_directory)
