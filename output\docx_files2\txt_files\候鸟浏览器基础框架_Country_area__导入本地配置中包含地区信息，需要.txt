Country_area:  导入本地配置中包含地区信息，需要加入

Country_area:  导入本地配置中包含地区信息，需要加入

pxyMode: 必须的和type相对应

用户帐户加入到 XML结构 / LUMI代理扩展参数 说明：

<?xml version="1.0" encoding="gb2312" ?>

<VERSION version="1139" xmlversion="2">

<VER ID="9" VERSION="35" SESSION_UNIQUE_ID="5e2181bf29fac52948e105047238566f" FROMUID="0" SHARETIME="" FROM_ACCOUNT="" GROUP_ID="0" GROUP_NAME="" IR32="1">

<SESSION NAME="商业会话环境三" TOP="1" COLOR="#FFffffffff" SYSTEM="Win32" TEMPLATEID="0" TEMPLATEFMASK="0" TEMPLATENAME="" PLUGINCOUNT="0"/>

<IS_ANONYMITY ANONYMITY = "0"/>

<COMMENT COMMENT_STRING="23?????????"/>

<SESSION_DEFINE_CODE SPHEREGL="0" ENABLE_LOCALHTML5_STORAGE="0" SAVE_LOCALHTML5_STORAGE="0" SAVE_INDEXDB_STORAGE="0"/>

<NETWORK TYPE="noproxy" PMODE="0" IP="N/A" PORT="0" USER="" PASSWORD="" PUBLIC_IP="***************" FAKEIP="**************"

COUNTRY =””  TIMEZONE=””

/>

<NETWORK_CTR NA="0" FAKE_WRTC="0" SAME_IP="0" IPV6="0" WRTCOFF="0" DNS="" />

<USERAGENT_BROWSER CHROME="0" SAFARI="0" MSIE="0" OTHER="0" REGEN_CONFIG_USERAGENT="0" MAINURL="" />

<USERAGENT_STRING UA="Mozilla/5.0 (Windows NT 6.3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.67 Safari/537.36 Edg/87.0.664.47" UA_LNG="zh-CN" UA_LNG_STRING="[@en-US@,@US@]" />

<USERAGENT_CTR DISPOPUPS="0" ENABLE_SERVICE="0" BLOCKLNGINPUT="0"  />

<RESOLUTION WIGHT="1024" HEIGHT="640" />

<RESOLUTION_CTR EMU_SCREEN="0" EMU_TOUCH="0" />

<POSITION LONGITUDE="0.000000" LATITUDE="0.000000" COUNTRY="CN" />

<TIMEZONE TIMEZONE_NAME="Asia/Shanghai (-480)" ADD_VALUE=""/>

<FINGERPRINT_CODE AUDIO="1" CANVAS="1" FONTS="1" RETCS="1" DNS="1" AUTOIPCHECK="1" fpver="2" AllowRisize="0" />

<OTHER_SETTING PLUGINS_MIMETYPE="0" SAVE_ENCRYPT_COOKIES="0" ENABLE_FLASH="0" DYNAMIC_FINGERPRINTS="0" BLOCK_CANVAS_OUTPUT="0" />

<DYNAMIC_FINGERPRINTS_CTR D_AUDIO="0" D_CANVAS="0" D_FONTS="0" D_RETCS="0" D_MEDIA="0" D_WEBGL="0" D_MIME="0" D_PLUGINS="0" />

<ACCOUNT_LIST NUM="1">

<ACCOUNT DOMAIN="iuse.com.cn" PLATFORM="自定义类型" DOMAINURL="https://www.iuse.com.cn" LOGIN="111" ORGIN_PASSWORD="1111" ENCRYPT_PASSWORD="" PASSWORD2FA="" IS_VALID="1" action_url="" signon_realm="https://www.iuse.com.cn" BOOKMARK_URL="https://www.iuse.com.cn" CREATE_TIME="" UPDATE_TIME=""/>

</ACCOUNT _LIST>

<PLUGIN_LIST NUM="0">

</PLUGIN_LIST>

<AUTOSCRIPTS_LIST NUM="0">

</AUTOSCRIPTS_LIST>

<IS_VALUED VALUED="0" />

<UPDATETIME VALUE="2022-08-11 16:52:32"/>

<CREATETIME VALUE="2021-12-28 16:38:54"/>

</VER>

</VERSION>

1、*ACCOUNT_LIST 表示 这个环境下有多少个网站登录帐户可用，和内核 的login_data.db是对应的关系。

其中：

NUM="1" 表示一共有多少个帐户

<DOMAIN TITLE=””>  帐户名称

<PLATFORM NAME=””>     归属于哪个平台（帐户销售平台：例：stripe）

<DOMAIN URL=””> 用户名/密码自动填入的URL网址。

<BOOKMARK URL=””> 书签使用URL网址。

<LOGIN NAME=”“>         登录用户名

<ORGIN_PASSWORD VALUE=””>     登录密码(明文)

<ENCRYPT_PASSWORD VALUE=””> 登录密码(密文)

<IS_VALID VALUE=”1”>	     是否有效

<ACCOUNT_DATE="2022-09-01 16:41:05">  此记录的写入时间。

2、* COUNTRY =””  TIMEZONE=””

PROXY节点增加二个参数值，专用于供新的PROXY代理如LUMI等使用。

七、登录帐户批量分配到环境图文流程说明：

原 旧的密码管理器窗体：

变更为：

[2022-09-07]

第三十章

候鸟浏览器

网页登录帐户类型关联网页登录URL-C/S端更新逻辑详细流程及约定。

前述：

在内核的LOGIN DATA SQLITE DB中，CHROME存在2个字段记录着用户登录网页的URL和登录网页URL、帐号、帐户密码，提供用户在访问登录页后，自动将帐户信息填入对方网站的登录表单中。

由于此2个字段属于CHROME内核的必要项，因此，此章节叙述：为自动填入帐户密码的核心逻辑。

由于目标网址的多变性，随时间变化的不确定性 ，例如亚马逊等多个大型商业网站，SHOPEE网站，EBAY网站等，其互联网特性决定了，绝大多数网站登录入口页面的URL地址，会存在各种变化，因此将上图中的网站类型和网页登录URL不宜 仅写死并存放于客户端安装包中，会导致登录URL变化导致整个自动填入URL到SQLITE DB的流程失效，需要服务器端提供此数据更新支持。

数据存储：

服务器端： 将EXECL文档存放在指定公共文件夹下，非AES用户文件夹，此EXCEL为全局数据列。

流程如下：

在用户打开上图此窗口时，客户端用线程请求服务器端接口：

服务器端读取存储位置上，此EXCEL文件的修改时间，并进行返回此EXCEL文件的修改时间。

客户端判断服务器端返回的时间，与本地安装目录下，EXCEL文件时间，判定哪个文件最新，将最新的EXCEL文件下载回来并覆盖保存。

如果客户端安装包下此EXCEL文件不存在，无条件下载服务器端此EXCEL文件。

注：上述逻辑体现为，在用户首次打开此窗口时，一定读取的是客户端本地的EXCEL文件。第二次打开此窗口，则一定加载的为最新EXCEL文件。

注2：EXCEL文件在客户端安装包里文件名为： sitelist.dat

EXCEL文件在服务器端的文件名为： sitelist.dat

注3：DATALIST.DAT文件里增加第三列存储BOOKMARK URL，供浏览器显示书签用途。（在ITEM的XML里 ACCOUNT_LIST里有帐户的情况下，默认显示书签栏，也就是书签栏显示出来的时侯，里面一定有用户加入的帐户书签）

[2022-10-18]

第三十一章

候鸟浏览器

自动化脚本加密、解密及带有

加解密脚本的 ITME 包分享流

程详细说明


================================================== 表格内容 ==================================================

参数名称 | 类型 | 必传 | 样例串/默认值 | 说明
SYSTEM | String | 否 | Windows | 操作系统参数：windows、android、ios（默认windows）
MobileModel | String | 否 | iphone 6 | SYSTEM 选择android和ios时，机型必填。机型参数包括：“google Pixel 4、红米8、红米7、google Pixel 5a、三星Galaxy Note8、小米10、三星Galaxy S9+、小米9、iPhone 6 Plus、iPhone 8 Plus、iPhone SE 2、iPhone 7 Plus、iPhone X、iPhone13 Pro、iPhone XS、iPhone 13 Pro Max、iPhone 12 mini、iPhone 8、iPhone 13 mini、iPhone 6、iPhone 12 Pro Max、iPhone 7、iPhone 12 、iPhone 12 Pro、iPhone 11 Pro、iPhone 13”
Version | String | 否 | 87 | 浏览器版本：支持数组，不传参默认随机。范围包括95、96、97、98、99、100、101、102、103、104、105、106

{
"message": "Get random user-agent Success",
"code": 0,
"data": {
            “user-agent” : "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/104.0.5112.80 Safari/537.36" 	
       }
}