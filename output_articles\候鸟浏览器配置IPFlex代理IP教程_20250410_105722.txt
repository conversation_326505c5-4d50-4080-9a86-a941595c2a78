标题: 候鸟浏览器配置IPFlex代理IP教程
英文标题: IPFlex configuration proxy IP
ID: 133
分类ID: 7
添加时间: 1744253842
更新时间: 1744255404
访问次数: 0
SEO标题: 候鸟浏览器配置IPFlex代理IP教程
SEO关键词: 候鸟浏览器配置IPFlex代理IP教程
SEO描述: 候鸟浏览器配置IPFlex代理IP教程

================================================== 内容 ==================================================
1、打开候鸟防关联浏览器首页，点击“下载候鸟防关联浏览器”；

![](********************************/images/image_6175e1be2011.png)


2、或者进入“[https://www.mbbrowser.com/download](https://www.mbbrowser.com/download "https://www.mbbrowser.com/download")”下载页面，点击下载按钮下载；

![](********************************/images/image_683faee816eb.png)


3、通过浏览器下载，下方会显示下载进度；

![](********************************/images/image_d955955a744a.png)


4、找到下载后的安装文件，双击安装；

![](********************************/images/image_73063f9fefc6.png)


5、双击后点击按钮按钮；

![](********************************/images/image_607f9b7563bf.png)


6、等待安装进度，大概需要30秒钟的时间；

![](********************************/images/image_95c86943176b.png)

7、点击安装完成。

![](********************************/images/image_2c7698dbfd91.png)


注意：首次安装可能会遇到360等软件误报提示，可以将软件加入到360的白名单里面，或者关闭360等安全软件。

8、安装完成后，双击桌面上的“候鸟浏览器”图标打开；

![](********************************/images/image_d986c20d6b6f.png)


9、在弹出登录框中输入您注册好的账户名称、密码信息，点击登录；

![](********************************/images/image_3d67ed6f9987.png)


10、登录后弹出候鸟浏览器的主界面，点击下面“新建环境配置”；

![](********************************/images/image_d4cf0b75a795.png)

11、注册登录IPFlex，登录至使用界面。

![](********************************/images/image_f6b58f40ebac.png)

12、点击充值界面，选择充值金额并进行支付。

![](********************************/images/image_4005be27056e.png)

13、浏览导航栏，选择所需代理服务类型（住宅代理、数据中心代理）。

![](********************************/images/image_272991e375cf.png)

14、以静态住宅代理为例，可按需选择扣费方式（按流量扣费、按IP扣费），按照需求选择IP的区域（国家-州-城市），网络协议（HTTP/SOCKS5）以及IP数量，点击提交即可。

![](********************************/images/image_adc1fce09efa.png)

15、在代理列表可查看生成的代理ID（`username:password@host:port`）。

![](********************************/images/image_217b7071e3c4.png)

16、点击复制生成的IP。

![](********************************/images/image_e9494b6385b7.png)

17、将复制的IP粘贴至候鸟浏览器配置界面，选择对应的代理协议。

================================================== 英文内容 ==================================================
1、Open the home page of Mbbrowser and click "Download Mbbrowser";

![](********************************/images/image_13bfc3cde964.png)


2、Or enter“[https://www.mbbrowser.com/download](https://www.mbbrowser.com/download "https://www.mbbrowser.com/download")”Download page, click the download button to download;

![](********************************/images/image_d35aff1264e8.png)


3、Download from the browser. The download progress is displayed at the bottom;

![](********************************/images/image_e70486e7f1a3.png)


4、Find the downloaded installation file, double-click to install;

![](********************************/images/image_73063f9fefc6.png)


5、Double-click and click the button;

![](********************************/images/image_43c878241b89.png)


6、Wait for the installation progress, which takes about 30 seconds;

![](********************************/images/image_416dc8944c8b.png)

7、Click Install finish.

![](********************************/images/image_0d90b2b504ec.png)


Note: Software such as 360 May encounter false positives when you install it for the first time. You can add the software to the whitelist of 360 or close security software such as 360.

8、After installation, double-click the "Bird Browser" icon on the desktop to open it;

![](********************************/images/image_b5e9d4123ce4.png)


9、Enter your registered account name and password information in the pop-up login box, and click login;

![](********************************/images/image_c80b6f9f9f4c.png)


10、After logging in, the main interface of the migratory bird browser pops up, click on "New Environment Configuration" below;

![](********************************/images/image_d4cf0b75a795.png)

11、Register and log in to IPFlex, and log in to the user interface.

![](********************************/images/image_f6b58f40ebac.png)

12、Click on the recharge interface, select the recharge amount and make the payment.

![](********************************/images/image_4005be27056e.png)

13、Browse the navigation bar and select the desired type of proxy service (residential proxy, data center proxy).

![](********************************/images/image_272991e375cf.png)

14、Taking static residential agents as an example, you can choose the charging method according to your needs (charging by traffic or IP), select the IP region (country state city), network protocol (HTTP/SOCKS5), and number of IPs according to your needs, and click submit.

![](********************************/images/image_adc1fce09efa.png)

15、The generated proxy ID (username:) can be viewed in the proxy list password@host : port`）。

![](********************************/images/image_217b7071e3c4.png)

16、Click to copy the generated IP.

![](********************************/images/image_e9494b6385b7.png)

17、Paste the copied IP into the configuration interface of the Bird Browser and select the corresponding proxy protocol.