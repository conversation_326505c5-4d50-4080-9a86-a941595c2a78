﻿<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Window size="648,760" caption="0,0,0,60" mininfo="648,760" roundcorner="9,9,9,9" showshadow="true" shadowsize="5" shadowposition="0,0" shadowcorner="3,3,3,3" shadowdarkness="50" shadowcolor="#FF44475A" fademode="true" autosize="true">
	<Include source="Default.xml" />
  <VerticalLayout name="main1" width="648" height="750" bkcolor="#FF282A36" bordersize="1" bordercolor="#FF44475A" borderround="2,2">
    <HorizontalLayout height="38">
      <Control bkimage="icon.png" padding="8,6,0,0" width="28" height="28"/>      <Button name="title" padding="6,10,0,10" text="修改环境" autocalcwidth="true" textcolor="#FFF8F8F2" hottextcolor="#FFBD93F9" pushedtextcolor="#FF6272A4" align="left" font="8"></Button>
      <Label name="bl" width="1" padding="8,4,0,0" bkimage="file='split.png'"></Label>
      <Button name="webrtcbtn" padding="8,10,0,0" text="高级配置" autocalcwidth="true" maxwidth="200" textcolor="#FFF8F8F2" hottextcolor="#FFBD93F9" pushedtextcolor="#FF6272A4" align="left" font="8"></Button>
      <Label name="bl2" width="1" padding="8,4,0,0" bkimage="file='split.png'"></Label>
      <Button name="hisverbtn" padding="8,10,0,0" text="更多" autocalcwidth="true" textcolor="#FFF8F8F2" hottextcolor="#FFBD93F9" pushedtextcolor="#FF6272A4" align="left" font="8"></Button>
      <Label name="bl3" visible="false" width="1" padding="6,4,0,0" bkimage="file='split.png'"></Label>
      <Button name="btn1" visible="false" padding="8,10,0,0" text="高级配置" autocalcwidth="true" textcolor="#FFF8F8F2" hottextcolor="#FFBD93F9" pushedtextcolor="#FF6272A4" align="left" font="8"></Button>
      <Control />
      <Button name="share_icon" visible="false" height="26" width="26" normalimage="share_pad_icon.png" />
      <Label name="share_account" visible="false" height="26" width="80" padding="0,0,0,0" text="" endellipsis="true" textcolor="#FFF8F8F2" font="26"></Label>
      <Button name="setupbtn" width="28" height="26" tooltip="configpad_setup" normalimage="configpad_cfg.png" hotimage="configpad_cfg_hover.png"/>
      <Button name="minbtn" width="28" height="26" tooltip="最小化" normalimage="minbtn.png" hotimage="minbtn_hover.png" pushedimage="minbtnpush.png" />
      <Button name="closewnd" width="28" height="26"  tooltip="关闭" normalimage="closebtn.png" hotimage="closebtn_hover.png" pushedimage="closebtnpush.png" />
    </HorizontalLayout>
  <HorizontalLayout name="bkground">
  <TabLayout name="default_bk" selectedid="0">

    <VerticalLayout height="730" sepheight="1" scrollwheel="true" vscrollbar="true" floatscrollbar="true"  scrollfbarFade="128">
       <HorizontalLayout height="14">
         </HorizontalLayout>

	      <HorizontalLayout height="40" inset="22,0,0,0">

	        	    <Label name="SelectItemGroupM" autocalcwidth="true" maxwidth="60" texttooltip="true" endellipsis="true" text="环境分组" font="8" height="36" textcolor="#FFF8F8F2"/>

	        	  <Combo name="group" visible="true" bordersize="0" padding="14,0,0,0" width="205" height="36" borderround="7,7" textcolor="#FFF8F8F2" disabledtextcolor="#FF6272A4" itemalign="left" itemfont="0" bkcolor="#FF21222C"
		          normalimage="file='Profile\Proxy_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\proxy_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\proxy_Combox_Click.png' corner='5,5,25,10'"
		          combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
		          itemhotbkcolor="#FF44475A" itemselectedbkcolor="#FF44475A" itemtextpadding="10,0,0,0" textpadding="0,0,20,0" endellipsis="true">
		          </Combo>
		          <Control width="21" />
		          <Label name="SelectItemMsg" visible="false" text="环境模板"  font="8" height="36" autocalcwidth="true" maxwidth="60" texttooltip="true" endellipsis="true" textcolor="#FFF8F8F2"/>

		           <Combo name="templates" visible="false" bordersize="0" padding="14,0,0,0" textpadding="8,2,60,0" width="221" height="36" borderround="7,7" textcolor="#FFF8F8F2" disabledtextcolor="#FF6272A4" itemalign="left" itemfont="0" bkcolor="#FF21222C"
	                normalimage="file='Profile\Proxy_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\proxy_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\proxy_Combox_Click.png' corner='5,5,25,10'"
	                combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
	                itemhotbkcolor="#FF44475A" itemselectedbkcolor="#FF44475A" itemtextpadding="10,0,0,0">
			            <!--<ListLabelElement height="36" pos="66,0,0,0" textpadding="10,0,0,0" text="点击并选择模板..." font="0" selected="true">
			              <Label name="textLab" pos="66,0,0,0" textpadding="10,0,0,0" text="点击并选择模板..."  height="36" width="260" textcolor="#FFC5C8C6"/>
			            </ListLabelElement>-->
	            </Combo>


	      </HorizontalLayout>

			<HorizontalLayout height="10">
         </HorizontalLayout>

      <HorizontalLayout inset="20,0,26,0" height="2">
        <Control height="2" bkcolor="#FF44475A"/>
      </HorizontalLayout>




			<HorizontalLayout height="34">

				<VerticalLayout width="320">
					<HorizontalLayout>
			        <Label name="lname" padding="22,2,0,0" textpadding="0,0,0,0" text="配置名称" autocalcwidth="true" texttooltip="true" endellipsis="true" textcolor="#FFF8F8F2" hottextcolor="#FFFF5555" font="8"></Label>
			        <Button name="config_tip" padding="6,11,0,0" height="15" width="15"  normalimage="file='helpbtn.png'" hotimage="file='helpbtn_hover.png'" pushedimage="file='helpbtn_push.png'"/>
              <Label name="tstr"  padding="4,2,0,0" textpadding="0,0,0,0" text="" autocalcwidth="true" texttooltip="true" endellipsis="true" textcolor="#FFF8F8F2" hottextcolor="#FFFF5555" font="8"></Label>
          </HorizontalLayout>
			  </VerticalLayout>



			   <VerticalLayout width="100" height="40"  visible="false">
			      <Button name="advance_option" padding="20,16,0,0" visible="false" textpadding="0,6,0,0" text="高级设置"  align="right" width="125" height="32" font="0"  textcolor="#FFC5C8C6" hottextcolor="#ff000000" />
			  </VerticalLayout>


        <VerticalLayout width="125" height="34">
          <Label name="system_pad" padding="1,6,0,0" text="系统" width="120"   textcolor="#FFF8F8F2" hottextcolor="#FFFF5555" font="8"></Label>
        </VerticalLayout>
        <VerticalLayout width="190" height="34">
          <Label name="reso_pad"  textpadding="45,6,10,0" text="分辨率" texttooltip="true" endellipsis="true" width="190"   textcolor="#FFF8F8F2" hottextcolor="#FFFF5555" font="8"></Label>
        </VerticalLayout>

			</HorizontalLayout>

			<HorizontalLayout height="36" >
			    	 <VerticalLayout width="300">
				     		<RichEdit name="configname" wanttab="false" padding="21,0,0,10" height="36" width="278" tipvaluecolor="#FF6272A4" borderround="7,7" bkcolor="#FF21222C" font="8" textpadding="10,8,20,0" maxchar="36" tipvalue="请输入环境名称" multiline="false" textcolor="#FFF8F8F2" rich="false" transparent="false">
				      		</RichEdit>
				     </VerticalLayout>
				     <VerticalLayout width="20">
			        </VerticalLayout>
				     <VerticalLayout width="150">
				     		<Combo name="system" dropboxsize="0,600" bordersize="0" padding="0,0,0,10" width="150" height="36" borderround="7,7" textcolor="#FFF8F8F2" disabledtextcolor="#FF6272A4" itemalign="left" itemfont="0" textpadding="0,0,0,0" bkcolor="#FF21222C"
										normalimage="file='Profile\Proxy_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\proxy_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\proxy_Combox_Click.png' corner='5,5,25,10'"
										combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
										itemhotbkcolor="#FF44475A" itemselectedbkcolor="#FF44475A" itemtextpadding="10,0,0,0" >

                 </Combo>
				     </VerticalLayout>
				     <VerticalLayout width="20"/>
				     <VerticalLayout width="140">
				     				<Combo name="reso" dropboxsize="0,600" bordersize="0" padding="0,0,0,10" width="130" height="36" borderround="7,7" textcolor="#FFF8F8F2" disabledtextcolor="#FF6272A4" itemalign="left" itemfont="0" textpadding="0,0,0,0" bkcolor="#FF21222C"
										normalimage="file='Profile\Proxy_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\proxy_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\proxy_Combox_Click.png' corner='5,5,25,10'"
										combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
										itemhotbkcolor="#FF44475A" itemselectedbkcolor="#FF44475A" itemtextpadding="10,0,0,0">
										</Combo>
                    <Edit name="resotext" pos="0,0,0,10" height="36" width="98" tipvaluecolor="#FF6272A4" nativebkcolor="#FF21222C" borderround="7,7" bkcolor="#FF21222C" font="0" textpadding="10,0,0,0" maxchar="6000" multiline="false" textcolor="#FFF8F8F2" rich="false" transparent="false" float="true"/>
				     </VerticalLayout>
			</HorizontalLayout>

			<HorizontalLayout width="500" height="22">
        <Label name="lmsg" padding="21,3,0,0" visible="false" texttooltip="true" endellipsis="true" text="会话环境已存在此实例名,请更换当前环境名称." width="500" textcolor="#FFFF5555"></Label>
      </HorizontalLayout>



			<HorizontalLayout height="30">

				<VerticalLayout width="620">
					<HorizontalLayout>
			      <Label padding="22,0,0,0" text="UserAgent" width="80" textcolor="#FFF8F8F2" hottextcolor="#FFFF5555" font="8"></Label>
			      <Button name="random_useragent" padding="0,0,0,0" textpadding="2,8,0,0" align="left" height="26" width="60" text="随机" font="5" textcolor="#FFBD93F9" hottextcolor="#FF8BE9FD" />
			      <Control width="342"/>
			      <Button name="UA_manager" padding="0,10,0,0" align="right" height="26" width="110" text="UA管理" font="5" textcolor="#FFBD93F9" hottextcolor="#FF8BE9FD" />
			    </HorizontalLayout>
			  </VerticalLayout>

			</HorizontalLayout>

			<HorizontalLayout height="36" >
							<VerticalLayout width="630">
											 <Combo name="agent" reselect="true" dropboxsize="0,450" bordersize="0" padding="21,0,0,10" width="600" height="36" borderround="7,7" textcolor="#FFF8F8F2" disabledtextcolor="#FF6272A4" itemalign="left" itemfont="0" textpadding="4,2,0,0" bkcolor="#FF21222C"
														normalimage="file='Profile\Setting_Combox_Normal_big_large.png' corner='15,5,25,10'" hotimage="file='Profile\Setting_Combox_Hover_large.png' corner='15,5,25,10'" pushedimage="file='Profile\Setting_Combox_Click_large.png' corner='15,5,25,10'"
												combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='15,5,25,10'"
												itemhotbkcolor="#FF44475A" itemselectedbkcolor="#FF44475A" itemtextpadding="10,0,0,0">
											</Combo>
                      <Edit name="agenttext" pos="21,0,0,10" padding="21,0,0,10" height="36" width="554" tipvaluecolor="#FF6272A4" nativebkcolor="#FF21222C" borderround="7,7" bkcolor="#FF21222C" font="8" textpadding="10,0,10,0" maxchar="6000" multiline="false" textcolor="#FFF8F8F2" rich="false" transparent="false" float="true"/>
              </VerticalLayout>
			</HorizontalLayout>

	    <HorizontalLayout height="20">
        <Label name="RandomAgentTs" visible="false" padding="22,0,0,0" text="" textcolor="#FFC5C8C6" hottextcolor="ffFF0000" font="8"></Label>
      </HorizontalLayout>

			<HorizontalLayout height="36">
           <VerticalLayout width="400">
           	<HorizontalLayout>
           		<Label name="lproxy" padding="22,4,0,0" text="代理Porxy" autocalcwidth="true" textcolor="#FFF8F8F2" hottextcolor="#FFFF5555" font="8"></Label>
              <Button name="define_proxy" autocalcwidth="true" maxwidth="100" padding="9,13,0,0" textpadding="0,0,10,0" align="left" height="20" text="自定义代理" texttooltip="true" endellipsis="true" font="5" textcolor="#FFBD93F9" hottextcolor="#FF8BE9FD" />
              <Combo name="proxys"  reselect="true" width="116" height="21" padding="0,11,0,0" dropboxsize="210,280" endellipsis="true" textpadding="10,0,30,20" itemtextpadding="10,0,0,0" itemalign="left" bkimage="file='configpad_list_normal.png' corner='5,3,25,10'" hotimage="file='configpad_list_hot.png' corner='5,3,25,10'" pushedimage="file='configpad_list_push.png' corner='5,3,25,10'" itemhotbkcolor="#FF44475A" itemselectedbkcolor="#FF6272A4">
              </Combo>
              <Button name="apiproxyip" visible="false" texttooltip="true" endellipsis="true" autocalcwidth="true" maxwidth="72" padding="6,13,0,0" textpadding="0,0,0,0" align="left" height="20" text="API链接提取" font="5" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" />
            </HorizontalLayout>
           </VerticalLayout>


          <VerticalLayout width="150" >
            <Control />
            <HorizontalLayout height="19">
            <Button name="proxy_tip" visible="false" padding="40,2,0,0" text="代理测试失败" height="15" width="110" textpadding="10,-1,0,0" textcolor="#FFFF5555" hottextcolor="#FFFF79C6"  font="8" normalimage="file='clickbtn.png' dest='0,0,12,15'" hotimage="file='clickbtn_hover.png' dest='0,0,12,15'" pushedimage="file='clickbtn_push.png' dest='0,0,12,15'"/>
            <Label  name="checkproxyInfo" padding="0,0,8,0" align="right" text="" width="150" textcolor="#FFF8F8F2"  font="8"></Label>
            </HorizontalLayout>
 			      <Button name="NetBridge" padding="0,0,0,0" visible="false" align="right"  width="150"  text="启动网桥模式"  texttooltip="true" endellipsis="true" maxwidth="184" textcolor="#FFBD93F9" hottextcolor="#FF8BE9FD" font="0"></Button>
            <Control />
          </VerticalLayout>
           <VerticalLayout width="98">
				     	   <Button name="checkproxy"  textcolor="#FFF8F8F2" hottextcolor="#FFFFFFFF"  texttooltip="true" endellipsis="true" bkcolor="#FFBD93F9" padding="2,4,0,0" textpadding="5,1,5,0"  width="68" height="28" text="检查代理" borderround="7,7" hotbkcolor="#FF8BE9FD" />
				     </VerticalLayout>
			</HorizontalLayout>

			<HorizontalLayout height="36" >
			    	 <VerticalLayout width="208">
							 <Combo name="proxy_type" scrollbarwidth="7" bordersize="1" padding="21,0,0,10" width="180" height="36" borderround="7,7" textcolor="#FFF8F8F2" disabledtextcolor="#FF6272A4" itemalign="left" itemfont="0" textpadding="0,0,10,0" bkcolor="#FF21222C"
										normalimage="file='Profile\Proxy_Combox_Normal.png' corner='5,2,30,2'" hotimage="file='Profile\proxy_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\proxy_Combox_Click.png' corner='5,5,25,10'"
										combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
										itemhotbkcolor="#FF44475A" itemselectedbkcolor="#FF44475A" itemtextpadding="10,0,0,0" endellipsis="true">
										</Combo>

				     </VerticalLayout>
				     <VerticalLayout width="150">
							<Edit name="proxyipe" padding="0,0,0,10" height="36" width="146" tipvaluecolor="#FF6272A4" nativebkcolor="#FF21222C" borderround="7,7" bkcolor="#FF21222C" font="8" textpadding="10,0,20,0" tipvalue="      .      .      .      " maxchar="6000" multiline="false" textcolor="#FFF8F8F2" rich="false" transparent="false" />

				     </VerticalLayout>
				     <VerticalLayout width="16">
				     	  <Label padding="4,4,0,8" text=":" width="20" textcolor="#FF6272A4" font="9"></Label>
				     </VerticalLayout>
				     <VerticalLayout width="70">
				     	   <RichEdit name="proxy_port" disabledtextcolor="#FF6272A4" wanttab="false" padding="0,0,0,10" height="36" width="64" tipvaluecolor="#FF6272A4" borderround="7,7" bkcolor="#FF21222C" font="20" textpadding="10,11,0,0" maxchar="5" tipvalue="Port" multiline="false" textcolor="#FFF8F8F2" rich="false" transparent="false"></RichEdit>
				     </VerticalLayout>
				     <VerticalLayout width="176">
				     	  <HorizontalLayout height="19">
				         <Edit name="proxynameinput" padding="0,0,0,10" height="17" width="176" texttooltip="true" endellipsis="true" tipvaluecolor="#FF6272A4" nativebkcolor="#FF21222C" borderround="7,7" bkcolor="#FF21222C" font="20" textpadding="10,0,6,0" tipvalue="Account" maxchar="6000" multiline="false" textcolor="#FFF8F8F2" rich="false" transparent="false" />
				        </HorizontalLayout>
				        <HorizontalLayout height="20">
				         <Edit name="proxypassinput" padding="0,0,0,10" height="17" width="176" texttooltip="true" endellipsis="true" tipvaluecolor="#FF6272A4" nativebkcolor="#FF21222C" borderround="7,7" bkcolor="#FF21222C" font="20" textpadding="10,0,6,0" tipvalue="Password" maxchar="6000" multiline="false" textcolor="#FFF8F8F2" rich="false" transparent="false" />
				         </HorizontalLayout>
				     </VerticalLayout>



			</HorizontalLayout>

			   <HorizontalLayout height="16">
			</HorizontalLayout>

			 <HorizontalLayout inset="20,0,26,0" height="2">
        <Control height="2" bkcolor="#FF44475A"/>
      </HorizontalLayout>
      <HorizontalLayout inset="20,0,26,0" height="10">
      </HorizontalLayout>

      <HorizontalLayout height="32">

      	<VerticalLayout width="620">
      		<HorizontalLayout height="32">
      	    <CheckBox name="WEBRTC" width="18" height="18"  padding="22,4,0,1" normalimage="checkbox_dark.png" selectedimage="checkbox_dark_selected.png" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="true" />
			      <Label padding="8,-4,0,0" textpadding="0,0,0,0" name="LBrtcFinger" text="WebRTC指纹" texttooltip="true" endellipsis="true" autocalcwidth="true" maxwidth="150" textcolor="#FFF8F8F2" hottextcolor="#FFFF5555" font="8"></Label>
			      <Label padding="10,-1,10,0" textpadding="0,0,20,0" name="LBrtcFingerInfo" text="设定WebRTC指纹环境" texttooltip="true" endellipsis="true" autocalcwidth="true" maxwidth="400" textcolor="#FF6272A4" hottextcolor="#FFFF5555" font="0"></Label>
			     </HorizontalLayout>
			  </VerticalLayout>

			</HorizontalLayout>

      <HorizontalLayout height="32">

				<VerticalLayout width="310">
					<HorizontalLayout height="32">
			      <Label padding="22,-1,0,0" name="PublicIP" text="公网IP" texttooltip="true" endellipsis="true" autocalcwidth="true" maxwidth="140" textcolor="#FFF8F8F2" hottextcolor="#FFFF5555" font="8"></Label>
			      <CheckBox name="public_ip_check" width="18" height="18"  padding="20,6,0,1" normalimage="checkbox_dark.png" selectedimage="checkbox_dark_selected.png" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="false" />
			      <Label name="WanIpInfo" padding="0,0,0,0" textpadding="6,0,0,0" text="自动识别代理IP" texttooltip="true" endellipsis="true"  maxwidth="112" align="left" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" font="5"></Label>
			     </HorizontalLayout>
			     </VerticalLayout>
			  <VerticalLayout width="350">
			     <HorizontalLayout height="32" width="350">
			      <Label padding="22,-1,0,0" name="PrivateIP" text="内网IP" texttooltip="true" endellipsis="true" autocalcwidth="true" maxwidth="140" textcolor="#FFF8F8F2" hottextcolor="#FFFF5555" font="8"></Label>
					  <Button name="RandLocalIP" padding="0,8,0,0" textpadding="10,1,0,0" text="随机" width="120" align="left" textcolor="#FFBD93F9" hottextcolor="#FF8BE9FD" font="5"></Button>
					 </HorizontalLayout>
			  </VerticalLayout>

			</HorizontalLayout>



			<HorizontalLayout height="36" >

	           <RichEdit name="public_ip" wanttab="false" padding="21,0,0,10" height="36" width="290" tipvaluecolor="#FF6272A4" borderround="7,7" bkcolor="#FF21222C" font="8" textpadding="10,8,20,0" maxchar="300" tipvalue="Enter the public IP address.." multiline="false" textcolor="#FFF8F8F2" rich="false" transparent="false">

				      		</RichEdit>

				      			<RichEdit name="privite_ip" wanttab="false" padding="21,0,0,10" height="36" width="290" tipvaluecolor="#FF6272A4" borderround="7,7" bkcolor="#FF21222C" font="8" textpadding="10,8,20,0" maxchar="300" tipvalue="Enter or random Private IP address.." multiline="false" textcolor="#FFF8F8F2" rich="false" transparent="false">

				      		</RichEdit>

			</HorizontalLayout>

      <HorizontalLayout inset="20,0,26,0" height="18">
      </HorizontalLayout>
      <HorizontalLayout inset="20,0,26,0" height="2">
        <Control height="2" bkcolor="#FF44475A"/>
      </HorizontalLayout>


			   <HorizontalLayout height="8">
			</HorizontalLayout>

			<HorizontalLayout height="28">
				<VerticalLayout width="620">
					<HorizontalLayout>
			      <Label name="txt_description" padding="22,0,0,0" textpadding="0,0,10,0" text="指纹识别设置" texttooltip="true" endellipsis="true" autocalcwidth="true" maxwidth="120" textcolor="#FFF8F8F2" hottextcolor="#FFFF5555" font="8"></Label>
			      <Button name="GetIPInfo" padding="0,0,0,0" height="28" textpadding="0,4,20,0" text="自动匹配IP时区信息"  texttooltip="true" endellipsis="true" autocalcwidth="true" maxwidth="144" textcolor="#FFBD93F9" hottextcolor="#FF8BE9FD" font="0"></Button>
			      <Label name="txt_descriptionmsg" padding="0,0,0,0" height="28" textpadding="0,4,20,0" texttooltip="true" endellipsis="true" text="候鸟会根据您的代理自动设置识别设置，如非必要不需修改。" autocalcwidth="true" maxwidth="360" textcolor="#FF6272A4" hottextcolor="#FF6272A4" font="0"></Label>
			     </HorizontalLayout>
			  </VerticalLayout>
			</HorizontalLayout>





			<HorizontalLayout height="24" >
				<VerticalLayout width="233">
			      <Label name="ltimezone" padding="22,0,0,0" height="20" textpadding="0,6,0,0" text="时区" width="160" textcolor="#FFF8F8F2" hottextcolor="#FFFF5555" font="0"></Label>
			  </VerticalLayout>
			  <VerticalLayout width="190">
			      <Label name="lcountry" padding="22,0,0,0" height="20" textpadding="0,6,0,0" text="国家" width="160" textcolor="#FFF8F8F2" hottextcolor="#FFFF5555" font="0"></Label>
			  </VerticalLayout>
			<VerticalLayout width="150">
			      <Label name="llangvage" padding="22,0,0,0" height="20" textpadding="0,6,0,0" text="语言" width="160" textcolor="#FFF8F8F2" hottextcolor="#FFFF5555" font="0"></Label>
			  </VerticalLayout>

			</HorizontalLayout>

			<HorizontalLayout height="36" >
			    	 <VerticalLayout width="250">
						       <Combo name="timezone" dropboxsize="0,450" padding="21,0,0,10" bordersize="1" width="226" height="36" borderround="7,7" textcolor="#FFF8F8F2" disabledtextcolor="#FF6272A4" itemalign="left" itemfont="0" textpadding="10,0,45,0" bkcolor="#FF21222C"
										normalimage="file='Profile\timezone_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\timezone_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\timezone_Combox_Click.png' corner='5,5,25,10'"
										combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
										itemhotbkcolor="#FF44475A" itemselectedbkcolor="#FF44475A" itemtextpadding="10,0,0,0" endellipsis="true">
										</Combo>
                    <Edit name="timezoneedit" textpadding="12,0,0,0" pos="21,0,0,10" width="192" height="36" nativebkcolor="#FF21222C" bkcolor="#FF21222C" float="true" />
				     </VerticalLayout>

 			    	 <VerticalLayout width="190">
						       <Combo name="country" dropboxsize="0,450" bordersize="0" padding="4,0,0,10" width="182" height="36" borderround="7,7" textcolor="#FFF8F8F2" disabledtextcolor="#FF6272A4" itemalign="left" itemfont="0" textpadding="10,0,45,0" bkcolor="#FF21222C"
										normalimage="file='Profile\short_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\short_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\short_Combox_Click.png' corner='5,5,25,10'"
										combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
										itemhotbkcolor="#FF44475A" itemselectedbkcolor="#FF44475A" itemtextpadding="10,0,0,0" endellipsis="true">
										</Combo>
                    <Edit name="countryedit" textpadding="12,0,0,0" pos="4,0,0,10" width="148" height="36" nativebkcolor="#FF21222C" bkcolor="#FF21222C" float="true" />
				     </VerticalLayout>



				      <VerticalLayout width="190">
						       <Combo name="lnginput" dropboxsize="0,450" bordersize="0" padding="4,0,0,10" width="178" height="36" borderround="7,7" textcolor="#FFF8F8F2" disabledtextcolor="#FF6272A4" itemalign="left" itemfont="0" textpadding="10,0,45,0" bkcolor="#FF21222C"
										normalimage="file='Profile\timezone_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\timezone_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\timezone_Combox_Click.png' corner='5,5,25,10'"
										combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
										itemhotbkcolor="#FF44475A" itemselectedbkcolor="#FF44475A" itemtextpadding="10,0,0,0">
											<!--<ListLabelElement height="36" pos="66,0,0,0" textpadding="10,0,0,0" text="en-US,en;q=0.5" font="0" selected="true">
												<Label name="telnet" pos="66,0,0,0" textpadding="10,0,0,0" text="en-US,en;q=0.5"  height="36" width="320" textcolor="#FFF8F8F2"/>