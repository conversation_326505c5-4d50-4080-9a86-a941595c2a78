UserAgent管理器 流程与接口

UserAgent管理器 流程与接口

流程：

1）窗口加载数据流程

当用户打开此窗体，窗体显示LOADING动画。

【线程模式】

PC端进行 –>

（一）版本请求：

服务器端：

Json格式：

{"filename":"x_xxx.zip","hash":"xxxxxxxx","version":"xx"}

zip文件为zip加密的xml数据。

(二) PC端本地进行版本对比

1、如果本地版本与服务器版本相同，不下载不更新本地。

2、如果本地版本小于服务器版本，下载更新本地并加载。

（三）PC端下载请求 – 服务器响应：

1、当PC端请求过来，请求串格式：

http://domain/xxx?aes(token=xxxxxxx&type= useragent_list &act=download&msg=request)

2、服务器端返回动态url（JSON格式）

服务器进行AES解码，判定token是否合法。

如合法：

http://domain/download?aes(filename=1_xxxxxxxx.dat&hash=xxxx&size=xxxxxx&type= useragent_list &version=xx)

3、PC端下载更新到本地并加载。

面板LOADING动画消失，显示加载的数据列表。

2）用户点击“保存”按钮流程

窗体显示LOADING动画。

【线程模式】

PC端进行 –> 本地USERAGENT.XML更新，版本号+1

（一）PC端上传请求 – 服务器响应：

1、当PC端请求上传，请求串格式：

http://domain/xxx?aes(token=xxxxxxx&type=useragent_list&uid=session_unique_id&act=upload&msg=request)

上传交互过程见第三章标准上传流程。

2、服务器端返回动态url（JSON格式）

服务器进行AES解码，判定token是否合法。

如合法：

PC端上传到服务器成功。

5、面板LOADING动画消失，显示 带SESSION NAME的useragentlist数据上传成功信息。

3）服务器端针对此数据存储说明及约定

根据用户名的加码规则+类型名称进行数据保存。

例：b45357536beddd0fb618e1e8485afe2f_USERAGENT_LIST.zip

服务器端以用户名的加码规则作为唯一判定标识。

此数据不作15天备份机制。仅保留一份最新副本。

7、   用户提交的此数据必须由服务器端异步非压缩打包流程，在PC端请求FULL的环节点，加入到服务器最新的FULL包中下发。（关键）（详情见第六章）

密码管理器

列表数据源：password_list.xml

搜索类：

列表关键字查找 【已完成测试通过】

右键菜单：

修改，删除。【已完成已测试通过】

描述：

修改： 允许用户修改指定的password。

删除： 允许用户删除指定的password。

保存

描述：将列表数据更新到本地XML格式的 password_list.xml中，然后同步（上传）到服务器。

密码管理器 流程与接口

流程：

1）窗口加载数据流程

当用户打开此窗体，窗体显示LOADING动画。

【线程模式】

PC端进行 –>

（一）版本请求：

服务器端：

Json格式：

{"filename":"x_xxx.zip","hash":"xxxxxxxx","version":"xx"}

zip文件为zip加密的xml数据。

(二) PC端本地进行版本对比

1、如果本地版本与服务器版本相同，不下载不更新本地。

2、如果本地版本小于服务器版本，下载更新本地并加载。

（三）PC端下载请求 – 服务器响应：

1、当PC端请求过来，请求串格式：

http://domain/xxx?aes(token=xxxxxxx&type=password_list &act=download&msg=request)

2、服务器端返回动态url（JSON格式）

服务器进行AES解码，判定token是否合法。

如合法：

http://domain/download?aes(filename=1_xxxxxxxx.dat&hash=xxxx&size=xxxxxx&type=password_list&version=xx)

3、PC端下载更新到本地并加载。

面板LOADING动画消失，显示加载的数据列表。

2）用户点击“保存”按钮流程

窗体显示LOADING动画。

【线程模式】

PC端进行 –> 本地PROXY.XML更新，版本号+1

（一）PC端上传请求 – 服务器响应：

1、当PC端请求上传，请求串格式：

http://domain/xxx?aes(token=xxxxxxx&type=password_list&uid=session_unique_id&act=upload&msg=request)

上传交互过程见第三章标准上传流程。

2、服务器端返回动态url（JSON格式）

服务器进行AES解码，判定token是否合法。

如合法：

3、PC端上传到服务器成功。

4、面板LOADING动画消失，显示 带SESSION NAME的数据上传成功信息。

3）服务器端针对此数据存储说明及约定

根据用户名的加码规则+类型名称进行数据保存。

例：b45357536beddd0fb618e1e8485afe2f_PASSWORD_LIST.zip

服务器端以用户名的加码规则作为唯一判定标识。

此数据不作15天备份机制。仅保留一份最新副本。

13、   用户提交的此数据必须由服务器端异步非压缩打包流程，在PC端请求FULL的环节点，加入到服务器最新的FULL包中下发。（关键）（详情见第六章）

数据备份管理器

列表数据源：backup.xml

前述：

数据备份管理是以FULL包为单位的本地数据存储和BACKUP.XML的列表加载。

每个FULL ZIP包，必须包含以下数据集合：

A、此用户下所有各子ITEM包(ZIP包)【和configdata.xml一一对应。】。

B、用户全局ConfigData.xml文档。

另：C、类别数据包（每个不同的类别包均包括：加码XML列表和ZIP数据）不在FULL ZIP包中，根据第四章的约定，类别包同步到本地，由用户在开启类别面板时自动加载并完成。

按钮类：

从备份中恢复：

描述：用户选定其中一项，点击“从备份中恢复”按钮。

流程：

1、PC端弹出恢复进度对话框显示恢复进度。

2、PC端不请求版本号，请求TOKEN、TYPE、SESSION_UNIQUE_ID到服务器，服务器根据参数找到对应的文件（FULL包），PC端获取FULL包 (full包数据)下载到本地，解压缩FULL包，并覆盖本地所有FULL包数据对应的文件，覆盖完成，重新加载所有数据，在弹出的对话框提示用户恢复完成。

注：第一版允许提示用户重启软件，如果能做到不重启软件直接加载所有数据则更好。


================================================== 表格内容 ==================================================

<?xml version="1.0" encoding="gb2312" ?>
<VERSION version="11">
<VER ID="1" LOGIN_ACCOUNT="" SESSION_UNIQUE_ID="">
	<SESSION NAME="<EMAIL>" />
	<BACKUP STRING="Backup was created at 2020-4-2 11:59:45"/>
	<IS_VALID VALUE="1" />
	<CREATETIME VALUE="2021-01-12 12:22:34"/>
	<UPDATETIME VALUE="2021-01-12 12:22:34"/>
</VER>
</VERSION>

<?xml version="1.0" encoding="gb2312" ?>
<VERSION version="11">
<VER ID="1" TEMPLATE_ID="模板ID">
	< TEMPLATE NAME="模板名称" />
	< TEMPLATE Data="模板数据"/>
	< OWNER="模板所有者"/>
	<CREATETIME VALUE="2021-01-12 12:22:34"/>
	<UPDATETIME VALUE="2021-01-12 12:22:34"/>
</VER>
</VERSION>