标题: 一键批量创建环境
英文标题: Batch Create Environments
ID: 109
分类ID: 7
添加时间: 1641279757
更新时间: 1689908203
访问次数: 0
SEO标题: 候鸟浏览器一键批量创建环境
SEO关键词: 候鸟浏览器一键批量创建环境
SEO描述: 候鸟浏览器一键批量创建环境

================================================== 内容 ==================================================
为了提高创建浏览器环境的效率，您可以使用一键批量创建环境功能。

1、登录候鸟客户端，在主面板上点击设置-一键批量创建环境按钮；

![](6d7b2882624511f09a0d0242ac130006/images/image_04309d607d75.png)

2、在弹出的“一键批量创建环境”窗口中进行设置；

![](6d7b2882624511f09a0d0242ac130006/images/image_51292c2b2f58.png)

   ⑴选择您已经创建的环境分组；
   ⑵输入一个环境名称（必选）；
   ⑶系统选项锁（开锁状态就是在现有选项中随机）；
   ⑷分辨率选项锁（开锁状态就是在现有选项中随机）；
   ⑸UA选项锁（开锁状态就是在现有选项中随机）；
   ⑹设置一个批量生成环境的数量（默认为20）；

3、设置完成后点击右下角“创建环境”按钮进行批量创建浏览器环境；

4、回到主面板就可以看到刚刚批量创建的环境列表。

![](6d7b2882624511f09a0d0242ac130006/images/image_9961140f193e.png)

注：批量创建环境后，环境中其他选项自定义设置可以通过手动修改环境来实现。

================================================== 英文内容 ==================================================
To improve the efficiency of creating browser environments, you can use the one-click batch environment creation function.

1. Log in to the Mbbrowser client and click "List" - "Session batch creation" button on the main panel;

<p><img src="6d7b2882624511f09a0d0242ac130006/images/image_b33030240f36.png" width="360" /></p>

2. In the Session batch creation window that is displayed, set the parameters.

![](6d7b2882624511f09a0d0242ac130006/images/image_a1aa2db7d613.png)

   ⑴Select the environment group you have created;
   ⑵Enter an environment name (mandatory).
   ⑶System option lock (unlocking state is random in existing options);
   ⑷Resolution option lock (unlocking state is random in existing options);
   ⑸UA option lock (unlocking state is random in the existing options);
   ⑹Sets the number of batch build environments (default is 20);

3. Click the "Create Session" button at the lower right corner to create browser environments in batches after setting;

4. Back on the main panel, you can see the list of environments you just created in batches.

<p><img src="6d7b2882624511f09a0d0242ac130006/images/image_e953785600eb.png" width="360" /></p>

Note: After environments are created in batches, you can customize other environment options by manually modifying the environment.