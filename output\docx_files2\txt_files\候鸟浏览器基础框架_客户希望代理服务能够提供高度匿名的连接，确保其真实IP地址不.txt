客户希望代理服务能够提供高度匿名的连接，确保其真实IP地址不被泄露。

1.动态IP更换：

客户可能需要频繁更换IP地址以避免被目标网站封禁或限制访问。

2.高匿名性：

客户希望代理服务能够提供高度匿名的连接，确保其真实IP地址不被泄露。

3.全球IP覆盖：

客户可能需要访问特定地区的内容或服务，因此需要代理服务提供全球范围内的IP地址。

4.稳定性和可靠性：

客户期望代理服务能够提供稳定且可靠的连接，减少因网络问题导致的中断。

5.自动化管理：

客户希望通过API自动提取和管理代理IP，以提高效率和减少人工操作。

6.安全性：

客户关注数据传输的安全性，希望代理服务能够提供加密连接（如HTTPS）。

7.批量操作支持：

对于需要处理大量请求的客户，他们可能需要API支持批量获取和使用代理IP。

8.实时监控和报告：

客户可能需要实时监控代理IP的使用情况和性能，并生成相关报告。

9.易于集成：

客户希望代理API能够轻松集成到他们现有的系统和应用程序中。

10.合规性和法律支持：

在某些情况下，客户可能需要确保代理服务符合当地法律法规的要求。

流程与数据结构：

（1）服务器端流程与步骤

1、服务器端建表

建一个表：IP_PROVIDER
字段：ID,PROVIDER_NAME,WEBSITE,CREATE_TIME,IS_VALID

CREATE TABLE IP_PROVIDER (

ID INT AUTO_INCREMENT PRIMARY KEY COMMENT '唯一标识符，自增主键',

PROVIDER_NAME VARCHAR(255) NOT NULL COMMENT '代理IP提供商的名称',

WEBSITE VARCHAR(255) COMMENT '代理IP提供商的官方网站',

CREATE_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',

IS_VALID TINYINT(1) DEFAULT 1 COMMENT '是否有效（1表示有效，0表示无效）',

UNIQUE KEY (PROVIDER_NAME) COMMENT '确保提供商名称唯一'

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='代理IP提供商信息表';

2、服务器SHOPXO

新增一个栏目，放在 系统设置 栏目下。

名称： IP服务商管理

列表支持 ->  增删改查

3、在云笔记中提供API接口，接口名： xxxxxxxxxxxxx/IP_fetch

接口返回XML内容。

<?xml version="1.0" encoding="UTF-8"?>

<IP_PROVIDER>

<Version>10</Version>

<Record>

<ID>1</ID>

<PROVIDER_NAME>Example Provider</PROVIDER_NAME>

<WEBSITE>https://example.com</WEBSITE>

<CREATE_TIME>2025-01-20T03:40:35</CREATE_TIME>

<IS_VALID>1</IS_VALID>

</Record>

</IP_PROVIDER>

API接口功能主要提供

提供客户端服务商列表和服务商官网地址

接口记录在云笔记中。

4、服务器端每修改一次表数据（包括新增），则生成的XML版本号加1。

5、此API同时提供单一的XML版本号验证接口。

（二）客户端流程与步骤：

在此窗口的TAB点击后，判断内存中是否已加载了服务器端API此数据，如果未加载(本地无IP_PROVIDER.XML或IP_PROVIDER.XML为空)，则无条件请求服务器端数据并进行加载（不判断版本号）。

如果请求服务器端获得到数据，同一时间将此XML数据保存到本地的
    C:\MBData\LocalStorage\3SEkSAN9OiYJrYfNNtLHh1qXsIOPk+tvM8EczODSwzY=\MainData
下，文件名： IP_PROVIDER.XML 用明文保存。

上述流程完成后，再线程进行判断：如本地存在IP_PROVIDER.XML，则直接读取本地IP_PROVIDER.XML，读取完本地IP_PROVIDER.XML后，再线程方式请求远程API接口，进行判断本地版本号是否小于服务器端，如果小于则线程方式下载XML数据覆盖本地。（此时界面上显示的数据是旧的，这个没问题）。

4、

[20250612]

第四十七章
海鸟/候鸟客户端

免费SSL隧道代理服务

流程与数据结构设计

完整说明

依据传统，具体工作步骤、顺序安排，人员分配在工作群里具体阐述。

一、前述：

根据上级部门要求，海鸟客户端以全免费的形态，对外提供防关联跨境基础服务，在积累一定程度用户后，上级部门将根据用户群体的不同，针对性提供相应增值收费服务的思想理念与未来政策。

原海鸟架构的基础服务中，新增SSL免费代理IP服务，此服务因国家政策因素，仅允许在海鸟的服务器端/客户端中提供，解决海外跨境用户使用客户端环境时没有代理IP或 需要购买第三方代理IP时，极高的学习门槛的刚性需求。

二、基础约定与C/S详细流程：

2.1 现有架构框架：

SSL代理层

SSL代理服务器 (约定称KCP服务器)：提供请求串的固定格式返回，提供SSL.exe的代理连接服务。

SSL.EXE (约定称SSL隧道代理客户端)：通过建立KCP隧道，提供本地客户端(如海鸟客户端)所在的操作系统与海外网络稳定联接。

海鸟客户端（Hainiao Client）

基于候鸟客户端完整产品移植客户端，除去候鸟客户端的用户注册体系与同步体系外，与候鸟客户端功能完全一致。

海鸟服务器端 （Hainiao WebServer）

基于候鸟服务器端完整产品移植而来海鸟服务器端，除去候鸟服务器端涉及到的用户注册相关的WEB控制台与同步体系外，与候鸟服务器端功能完全一致。

2.2 免费SSL代理服务 架构中各成员参数，关系，流程简要说明与约定

2.2.1 业务流程(需求)简要说明：

用户进入海鸟客户端 -> 点击 获取免费SSL代理IP入口按钮 ->  选择需要的地域代理IP -> 客户端访问KCP服务器 远程URL并提供必要参数索取SSL.EXE的运行串 -> KCP服务器 验证本次请求是否合法并下发SSL.EXE运行KCP隧道串，同时将本次运行隧道串，先行进行隧道生效处理（15分钟内如未使用此隧道则将此隧道自动失效）-> 海鸟客户端根据KCP服务器 返回串进行SSL隧道代理客户端(SSL.EXE) 的调用、隧道代理检测、业务环境 应用运行。

2.2.2 参数说明：

KCP服务器 (搭建者,服务器内部运行环境,服务器运维：因涉及保密与安全，法律风险因素等，均无须考虑和参与), 提供代理IP资源，SSL.EXE命令执行返回。

KCP服务器 URL使用方法与参数说明：

例：访问URL：http://***************/start.asp?uuid=123456&addr=hk&port=1124

请求/访问 此URL即表示告知KCP服务器，我需要创建一条可用KCP代理隧道，请帮我创建并生效。

固定前缀：   (此前缀有可能在后期会动态变化，因此不能在客户端中写死，应由服务器端来告知客户端使用此前缀，即shopxo 后台需要提供此URL的配置页面。)
 		参数：

（图1）

uuid=123456 	表示 用户的登录ID(取邮箱帐号的数字部份，见图1)

注：KCP服务器 自有管理后台会对uuid的用户群体自行进行分析和数据管理，不在我们本次章节与工作范围内

addr=hk 		表示 用户需要全球哪个地区的代理IP，即区域参数。

port=1124 	表示 这个隧道创建在哪个端口上 （根据本地多人联合测试[测试时长已达16小时连续测试]，确认多台机器可以共用相同的port并相互间不受影响，SSL隧道未出现异常情况，因此，判定同port多个不同用户使用为：可商用模式）

URL返回参数说明：

访问URL：

返回说明：

返回值：ssl.exe -r "***************:1124" -l "127.0.0.1:1124" -key "f9d5c16a7f42203f8c195432354a3271" -crypt aes -mode fast3 -sockbuf 16777217 -sndwnd 1024 -rcvwnd 1024

返回的此串是服务器端告诉客户端，应使用什么样的方式来使用，如果直接运行此串，则会弹出两个colsole窗口来显示隧道代理执行与运行的状态数据。（注：重点，此console窗口在商用时应不显示出来，仅允许在客户WINDOWS电脑的后台运行）。

SSL KCP服务客户端程序，参数含义如下：

-r "***************:1124": 目标地址，转发到本地的1124端口

-key " f9d5c16a7f42203f8c195432354a3271": 加密密钥

-listen "127.0.0.1:1124": 监听本地1124端口

-crypt aes: 使用AES加密

-mode fast3: 使用fast3传输模式（高速但可能消耗更多带宽）

-sockbuf 16777217: 套接字缓冲区大小约16MB

-sndwnd 1024: 发送窗口大小

-rcvwnd 1024: 接收窗口大小

2.3 海鸟客户端界面入口说明，各界面元素说明及实现功能流程与约定。

2.3.1 海鸟客户端界面(ownerUI)入口详细说明：

1、用户点击此按钮，选择需要的地域代理IP


================================================== 表格内容 ==================================================

{
"Script_ID":["7579a099e6fcee76fd1272ccdc30c1cc","c1f3f1b3d5072985581fe54343f1e524"],
"Session_ID":"914a9b97c6787a231ed2ab25e02ad5c9"
}

{
"message "Session Script Added Success",
"code": 0,
"data": {
"listcontainer": [
        {
        "Session_Name": “商用业务环境一”
        "Session_ID": "914a9b97c6787a231ed2ab25e02ad5c9",
"Group_Name": “default”
“Actived_script_id”:” O73808cb37bd63f5f7d92415e736e999”,
“Actiived_script_name”:”这是一个脚本例子”,
“Actiived_script_encode”:”true”,
"Script_Count": "3",
"UnActived_script_list": 
                    [{
                     "UnActived_script_encode" : "false",
                     "UnActived_script_Name" : "AAA",
                     "UnActived_script_ID" : "7579a099e6fcee76fd1272ccdc30c1cc"
                         },{
                     "UnActived_script_encode" : "false",
                     "UnActived_script_Name" : "BBB",
                     "UnActived_script_ID" : " c1f3f1b3d5072985581fe54343f1e524"
                         }],
"status": 0
}

}
}