<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>/api/session</title>
  <style>
/* 基础重置与排版 */
body {
  margin: 0;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.7;
  color: #333;
  background-color: #fff;
  max-width: 960px;
  margin: 0 auto;
  box-sizing: border-box;
}

/* 段落 */
p {
  margin: 1em 0;
}

/* 标题 */
h1, h2, h3, h4, h5, h6 {
  margin: 1.5em 0 0.8em;
  color: #2c3e50;
  font-weight: 600;
  line-height: 1.3;
}

h1 { font-size: 2em; border-bottom: 1px solid #eee; padding-bottom: 0.5em; }
h2 { font-size: 1.6em; }
h3 { font-size: 1.4em; }
h4 { font-size: 1.2em; }

/* 列表 */
ul, ol {
  margin: 1em 0;
  padding-left: 2em;
}

li {
  margin: 0.4em 0;
}

/* 引用块 */
blockquote {
  margin: 1.5em 0;
  padding: 0.8em 1.5em;
  background-color: #f9f9f9;
  border-left: 4px solid #ddd;
  color: #666;
  font-style: italic;
  border-radius: 0 4px 4px 0;
}

/* 代码行内 */
code {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  background-color: #f3f4f6;
  color: #e9602d;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-size: 0.95em;
  white-space: nowrap;
}

/* 代码块 */
pre {
  margin: 1.5em 0;
  padding: 1.2em;
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  overflow-x: auto;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
}

pre code {
  background: none;
  color: inherit;
  padding: 0;
  font-size: inherit;
  white-space: pre;
  display: block;
}

/* 表格 */
table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5em 0;
  font-size: 14px;
  overflow: hidden;
  box-shadow: 0 0 0 1px #e0e0e0;
  border-radius: 6px;
}

th, td {
  padding: 10px 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

th {
  background-color: #f8f9fa;
  color: #2c3e50;
  font-weight: 600;
  white-space: nowrap;
}

tr:nth-child(even) {
  background-color: #f9f9fb;
}

tr:hover {
  background-color: #f0f5ff;
}

/* 链接 */
a {
  color: #1a73e8;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

/* 分隔线 */
hr {
  border: 0;
  height: 1px;
  background: #ddd;
  margin: 2em 0;
}

/* 图片 */
img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 1em 0;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
  </style>
</head>
<body>
  <h2>环境管理</h2> <div class="api-tabs ant-tabs ant-tabs-top ant-tabs-card ant-tabs-no-animation"><div class="ant-tabs-bar ant-tabs-top-bar ant-tabs-card-bar"><div class="ant-tabs-nav-container"><span class="ant-tabs-tab-prev ant-tabs-tab-btn-disabled"><span class="ant-tabs-tab-prev-icon"><i class="anticon anticon-left ant-tabs-tab-prev-icon-target"><svg class=""><path></path></svg></i></span></span><span class="ant-tabs-tab-next ant-tabs-tab-btn-disabled"><span class="ant-tabs-tab-next-icon"><i class="anticon anticon-right ant-tabs-tab-next-icon-target"><svg class=""><path></path></svg></i></span></span><div class="ant-tabs-nav-wrap"><div class="ant-tabs-nav-scroll"><div class="ant-tabs-nav ant-tabs-nav-animated"><div><div class="ant-tabs-tab">1、获取环境列表</div><div class="ant-tabs-tab">2、查询指定环境ID的配置数据</div><div class="ant-tabs-tab">3、创建环境</div><div class="ant-tabs-tab">4、更新环境高级指纹参数</div><div class="ant-tabs-tab">5、更新环境</div><div class="ant-tabs-tab">6、更新环境代理</div><div class="ant-tabs-tab">7、删除环境</div><div class="ant-tabs-tab">8、导入Cookie</div><div class="ant-tabs-tab">9、导出Cookie</div><div class="ant-tabs-tab">10、获取随机UA</div><div class="ant-tabs-tab">11、清除环境本地缓存</div><div class="ant-tabs-tab">12、查看环境运行状态</div><div class="ant-tabs-tab">13、查看环境网页自动运行信息</div><div class="ant-tabs-tab">14、添加环境自动运行网页地址</div><div class="ant-tabs-tab">15、更新环境某个自动运行网页地址</div><div class="ant-tabs-tab-active ant-tabs-tab">16、删除环境某个自动运行网页地址</div></div><div class="ant-tabs-ink-bar ant-tabs-ink-bar-animated"></div></div></div></div></div></div><div></div><div class="ant-tabs-content ant-tabs-content-no-animated ant-tabs-top-content ant-tabs-card-content"><div class="ant-tabs-tabpane ant-tabs-tabpane-inactive"><ul><li><p>Path：/api/v1/session/listid</p></li> <li><p>Method：POST</p></li> <li><p>Content-Type：application/json</p></li> <li><p>接口描述：查询所有符合条件的环境唯一ID，用户仅能查询自有环境及包含收到其它帐户分享过来的环境。</p></li></ul> <blockquote><p>请求参数</p></blockquote> <div class="table"><table><thead><tr><td>参数名称</td> <td>类型</td> <td>必传</td> <td>样例串/默认值</td> <td>说明</td></tr></thead> <tbody><tr><td>Session_Name</td> <td>string</td> <td>否</td> <td>商业环境一</td> <td>指定环境名称查询环境ID</td></tr> <tr><td>Session_GroupName</td> <td>string</td> <td>否</td> <td>环境分组名称</td> <td>指定环境分组名称查询环境ID</td></tr> <tr><td>Session_CreateTime_Start</td> <td>string</td> <td>否</td> <td>2022-12-18 13:23:09</td> <td>指定创建时间查询环境ID (start)</td></tr> <tr><td>Session_CreateTime_End</td> <td>string</td> <td>否</td> <td>2022-12-18 13:23:09</td> <td>指定创建时间查询环境ID (end)</td></tr> <tr><td>Session_UpdateTime_Start</td> <td>string</td> <td>否</td> <td>2022-12-18 13:23:09</td> <td>指定最近使用时间查询环境ID (start)</td></tr> <tr><td>Session_UpdateTime_End</td> <td>string</td> <td>否</td> <td>2022-12-18 13:23:09</td> <td>指定最近使用时间查询环境ID (end)</td></tr> <tr><td>Session_Recv</td> <td>bit</td> <td>否</td> <td>1</td> <td>指定返回我收到的分享环境ID</td></tr> <tr><td>Session_Recv_Account</td> <td>string</td> <td>否</td> <td><EMAIL></td> <td><p>返回我收到指定帐户分享的环境ID</p><p>[生效前题: Session_Recv 必须为 1]</p></td></tr> <tr><td>Session_Sent</td> <td>bit</td> <td>否</td> <td>1</td> <td>指定返回我分享的环境ID</td></tr> <tr><td>Session_Sent_Account</td> <td>string</td> <td>否</td> <td><EMAIL></td> <td><p>返回我分享给指定帐户的环境ID</p><p>[生效前题: Session_Send 必须为 1]</p></td></tr> <tr><td>Session_User_Agent</td> <td>string</td> <td>否</td> <td>Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36</td> <td>返回符合指定 UA 值的所有环境ID [2023/07/03 新增]</td></tr> <tr><td>Proxy_Ip</td> <td>string</td> <td>否</td> <td>*************</td> <td>指定代理IP返回环境ID</td></tr> <tr><td>Proxy_Type</td> <td>array</td> <td>否</td> <td>HTTP,HTTPS,SSH,SOCKS4,SOCKS4A,SOCKS5,Oxylabsauto,Lumauto,Luminati_HTTP,Luminati_HTTPS,smartproxy,noproxy</td> <td><p>指定代理类型返回环境ID</p><p>[无代理:noproxy]</p></td></tr> <tr><td>Comment</td> <td>string</td> <td>否</td> <td>环境备注信息</td> <td>指定返回符合备注关键字环境ID</td></tr> <tr><td>Session_ISWebAccount</td> <td>bit</td> <td>否</td> <td>1</td> <td>指定返回所有存在网页登录帐户环境ID</td></tr> <tr><td>Session_WebAccounts</td> <td>array</td> <td>否</td> <td><EMAIL></td> <td><p>返回存在指定网页登录帐户环境ID</p><p>[生效前题: Session_ISWebAccount 必须为 1]</p></td></tr> <tr><td>Session_ISPlugins</td> <td>bit</td> <td>否</td> <td>1</td> <td>返回所有存在浏览器插件的环境ID</td></tr> <tr><td>Session_Plugins_id</td> <td>array</td> <td>否</td> <td>Aknhicdgfejabmkpjlmnkmakhlmedfga,agedeoibceidbaeajbehgiejlekicbfd</td> <td><p>指定插件ID返回所有环境ID</p><p>[生效前题: Session_ISPlugins 必须为 1]</p></td></tr> <tr><td>Session_ISAutoScript</td> <td>bit</td> <td>否</td> <td>1</td> <td>指定返回所有存在脚本帐户环境ID</td></tr> <tr><td>Session_AutoScript_id</td> <td>array</td> <td>否</td> <td>17c70e014d61b1fa43d3638ca5a1bc25,962a35b816655312db7ddf4d0807829a</td> <td><p>返回存在指定脚本ID的环境ID</p><p>[生效前题: Session_ISAutoScript 必须为 1]</p></td></tr> <tr><td>CurrentPage</td> <td>int</td> <td>是</td> <td>1</td> <td>分页/页数值</td></tr> <tr><td>ListNum</td> <td>int</td> <td>是</td> <td>50</td> <td>分页条数，一次最多返回500个环境ID</td></tr> <tr><td>注：如未给任何参数，则系统默认按 UpdateTime 最近使用时间，倒序返回最近使用不超过 50 条环境 ID。</td></tr></tbody></table></div> <blockquote><p>请求示例【通过环境分组名称查询所有分组下的环境ID】</p></blockquote> <div class="code-view"><pre><code>{ "Session_GroupName": "环境分组名称" }</code></pre> </div> <blockquote><p>请求示例【通过环境的创建时间查询】</p></blockquote> <div class="code-view"><pre><code>{ "Session_CreateTime_Start": "2022-09-01 13:14:15", "Session_CreateTime_End": "2022-09-20 13:14:15" }</code></pre> </div> <blockquote><p>请求示例【通过环境的修改时间查询】</p></blockquote> <div class="code-view"><pre><code>{ "Session_UpdateTime_Start": "2022-09-01 13:14:15", "Session_UpdateTime_End": "2022-09-20 13:14:15" }</code></pre> </div> <blockquote><p>请求示例【返回我收到的分享环境ID集合】</p></blockquote> <div class="code-view"><pre><code>{ "Session_Recv": 1 }</code></pre> </div> <blockquote><p>请求示例【返回我收到指定帐户分享的环境ID】</p></blockquote> <div class="code-view"><pre><code>{ "Session_Recv": 1, "Session_Recv_Account": "<EMAIL>" }</code></pre> </div> <blockquote><p>请求示例【指定返回我分享的环境ID】</p></blockquote> <div class="code-view"><pre><code>{ "Session_Sent": 1 }</code></pre> </div> <blockquote><p>请求示例【返回我分享给指定帐户的环境ID】</p></blockquote> <div class="code-view"><pre><code>{ "Session_Sent": 1, "Session_Sent_Account": "<EMAIL>" }</code></pre> </div> <blockquote><p>请求示例【指定代理IP返回环境ID】</p></blockquote> <div class="code-view"><pre><code>{ "Proxy_Ip": "127.0.0.1" }</code></pre> </div> <blockquote><p>请求示例【指定代理类型返回环境ID】</p></blockquote> <div class="code-view"><pre><code>{ "Proxy_Type": ["smartproxy"] }</code></pre> </div> <blockquote><p>请求示例【指定返回符合备注关键字环境ID】</p></blockquote> <div class="code-view"><pre><code>{ "Comment": "在" }</code></pre> </div> <blockquote><p>请求示例【指定返回所有存在网页登录帐户环境ID】</p></blockquote> <div class="code-view"><pre><code>{ "Session_ISWebAccount": 1 }</code></pre> </div> <blockquote><p>请求示例【返回存在指定网页登录帐户环境ID】</p></blockquote> <div class="code-view"><pre><code>{ "Session_ISWebAccount": 1, "Session_WebAccounts": ["hiamazon3"] }</code></pre> </div> <blockquote><p>请求示例【返回所有存在浏览器插件的环境ID】</p></blockquote> <div class="code-view"><pre><code>{ "Session_ISPlugins": 1 }</code></pre> </div> <blockquote><p>请求示例【指定插件ID返回所有环境ID】</p></blockquote> <div class="code-view"><pre><code>{ "Session_ISPlugins": 1, "Session_Plugins_id": ["ncennffkjdiamlpmcbajkmaiiiddgioo"] }</code></pre> </div> <blockquote><p>请求示例【指定返回所有存在脚本帐户环境ID】</p></blockquote> <div class="code-view"><pre><code>{ "Session_ISAutoScript": 1 }</code></pre> </div> <blockquote><p>请求示例【返回存在指定脚本ID的环境ID】</p></blockquote> <div class="code-view"><pre><code>{ "Session_ISAutoScript": 1, "Session_AutoScript_id": ["7e147176e1d756eb03c0e18e7b640c23"] }</code></pre> </div> <blockquote><p>执行成功返回</p></blockquote> <div class="code-view"><pre><code>{ "message": "Success", "code": 0, "data": { "listid": [{ "373808cb37bd63f5f7d92415e736e85f", //符合条件的环境ID "705cc4c139e69b729a2fd277f30e1863" //符合条件的环境ID }], "total": 2 } }</code></pre> </div></div><div class="ant-tabs-tabpane ant-tabs-tabpane-inactive"><ul><li><p>Path：/api/v1/session/id_container</p></li> <li><p>Method：POST</p></li> <li><p>Content-Type：application/json</p></li> <li><p>接口描述：查询并返回指定环境ID集合的环境内部信息。</p></li></ul> <blockquote><p>请求参数</p></blockquote> <div class="table"><table><thead><tr><td>参数名称</td> <td>类型</td> <td>必传</td> <td>样例串/默认值</td> <td>说明</td></tr></thead> <tbody><tr><td>Session_ID</td> <td>array</td> <td>是</td> <td>373808cb37bd63f5f7d92415e736e85f,705cc4c139e69b729a2fd277f30e1863</td> <td>指定环境ID查询环境</td></tr> <tr><td>Session_container_type</td> <td>int</td> <td>否</td> <td>1</td> <td><p>=1:返回环境完整内容</p><p>=2:返回环境精简内容</p><p>=3:返回环境包含 plugin/script 精简内容</p></td></tr></tbody></table></div> <blockquote><p>请求示例</p></blockquote> <div class="code-view"><pre><code>{ "Session_ID": ["373808cb37bd63f5f7d92415e736e85f"], "Session_container_type": 1 }</code></pre> </div> <blockquote><p><code>Session_container_type = 1</code> 执行成功返回</p></blockquote> <div class="code-view"><pre><code>{ "message": "Success", "code": 0, "data": { "listcontainer": [{ "Session_Name": "商用业务环境一", "Session_ID": "373808cb37bd63f5f7d92415e736e85f", "Group_Name": "Default", "Actived_script_id": "O73808cb37bd63f5f7d92415e736e999", "Actiived_script_name": "这是一个脚本例子", "Actiived_script_encode": "true", "Weblogin_Account_Count": "4", "Weblogin_Account_name": "<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>", "Plugins_Count": "4", "Plugin_Id": "jjbnhpnlakcdgfnnldamfeinfmahhdlm,jjbnhpnlakcdgfnnldamfeinfmahhdlm,jjbnhpnlakcdgfnnldamfeinfmahhdlm,jjbnhpnlakcdgfnnldamfeinfmahhdlm", "template_id": "123456", "template_name": "抖音国际版", "browser_Path": "D:\\mbbrowser\\Chromium_x64\\chromium.exe", "browser_CDP_Port": 46973, "MBData_Path": "C:\\MBDATA\xxxxxxxxxx\xxxxxxxxxx\xxxxxxxxxxx", "Public_ip": "************", "Internel_ip": "**************", "isDynamicIp": false, "StartPage": "about:blank", "System": "windows", "Resolution: "1024x768", "UserAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36", "proxyType": "socks5", "proxy_ip": "127.0.0.1", "proxy_port": "1080", "webdriver": "C:\\Users\\<USER>\\houniao\\Driver\\100\\chromedriver.exe", //根据当前打开环境的内核返回对应内核 webdriver 驱动路径 "updatetime":"2022-12-13 13:23:09", "createtime":"2022-09-23 08:47:36", "item_version":"3030", "UnActived_script_list": [{ "UnActived_script_encode": "false", "UnActived_script_Name": "AAA", "UnActived_script_ID": "17c70e014d61b1fa43d3638ca5a1bc21" },{ "UnActived_script_encode": "false", "UnActived_script_Name": "BBB", "UnActived_script_ID": "17c70e014d61b1fa43d3638ca5a1bc22" }], "status": 0 }], "total": 1 } }</code></pre> </div> <blockquote><p><code>Session_container_type = 2</code> 执行成功返回</p></blockquote> <div class="code-view"><pre><code>{ "message": "Success", "code": 0, "data": { "listcontainer": [{ "Session_Name": "商用业务环境一", "Session_ID": "373808cb37bd63f5f7d92415e736e85f", "Group_Name": "Default", "StartPage": "about:blank", "System": "windows", "Resolution: "1024x768", "UserAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36", "proxyType": "socks5", "proxy_ip": "127.0.0.1", "proxy_port": "1080", "webdriver": "C:\\Users\\<USER>\\houniao\\Driver\\100\\chromedriver.exe", //根据当前打开环境的内核返回对应内核 webdriver 驱动路径 "updatetime": "2022-12-13 13:23:09", "createtime": "2022-09-23 08:47:36", "item_version": "3030", "status": 0 }], "total": 1 } }</code></pre> </div> <blockquote><p><code>Session_container_type = 3</code> 执行成功返回</p></blockquote> <div class="code-view"><pre><code>{ "message": "Success", "code": 0, "data": { "listcontainer": [{ "Session_Name": "商用业务环境一", "Session_ID": "373808cb37bd63f5f7d92415e736e85f", "Group_Name": "Default", "Actived_script_id": "O73808cb37bd63f5f7d92415e736e999", "Actiived_script_name": "这是一个脚本例子", "Actiived_script_encode": "true", "Weblogin_Account_Count": "4", "Weblogin_Account_name": "<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>", "Plugins_Count": "4", "Plugin_Id": "jjbnhpnlakcdgfnnldamfeinfmahhdlm,jjbnhpnlakcdgfnnldamfeinfmahhdlm,jjbnhpnlakcdgfnnldamfeinfmahhdlm,jjbnhpnlakcdgfnnldamfeinfmahhdlm", "proxyType": "socks5", "proxy_ip": "127.0.0.1", "proxy_port": "1080", "webdriver": "C:\\Users\\<USER>\\houniao\\Driver\\100\\chromedriver.exe", //根据当前打开环境的内核返回对应内核 webdriver 驱动路径 "updatetime": "2022-12-13 13:23:09", "createtime": "2022-09-23 08:47:36", "item_version": "3030", "status": 0 }], "total": 1 } }</code></pre> </div></div><div class="ant-tabs-tabpane ant-tabs-tabpane-inactive"><ul><li><p><strong class="pcolor">【最大请求频率：40次/分钟】</strong></p></li> <li><p>Path：/api/v1/session/create</p></li> <li><p>Method：POST</p></li> <li><p>Content-Type：application/json</p></li> <li><p>接口描述：创建环境，支持配置环境的名称、备注、分组和代理信息。创建成功后返回环境ID。</p></li></ul> <blockquote><p>请求参数</p></blockquote> <div class="table"><table><thead><tr><td>参数名称</td> <td>类型</td> <td>必传</td> <td>样例串/默认值</td> <td>说明</td></tr></thead> <tbody><tr><td>Session_Name</td> <td>string</td> <td>是</td> <td>商业环境一</td> <td>环境名称，最大长度60字</td></tr> <tr><td>Session_Desc</td> <td>string</td> <td>否</td> <td>环境描述</td> <td>环境描述，最大长度150字</td></tr> <tr><td>Session_Group</td> <td>string</td> <td>否</td> <td>环境所属分组</td> <td>环境所属分组，最大长度30字，如未填写值则为默认分组</td></tr> <tr><td>Session_System</td> <td>string</td> <td>否</td> <td>Windows</td> <td>设定环境的操作系统</td></tr> <tr><td>Session_Resolution</td> <td>string</td> <td>否</td> <td>1024x768</td> <td>设定环境的分辨率</td></tr> <tr><td>Session_User_Agent</td> <td>string</td> <td>否</td> <td>Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36</td> <td>设定环境指定 UA 值 [2023/07/03 新增]</td></tr> <tr><td>Proxy_Type</td> <td>string</td> <td>否</td> <td>HTTP,HTTPS,SSH,SOCKS4,SOCKS4A,SOCKS5,Oxylabsauto,Lumauto,Luminati_HTTP,Luminati_HTTPS,smartproxy,noproxy</td> <td><p>指定代理类型</p><p>[无代理:noproxy]</p></td></tr> <tr><td>Proxy_Ip</td> <td>string</td> <td>否</td> <td>*************</td> <td>指定代理IP</td></tr> <tr><td>Proxy_Port</td> <td>string</td> <td>否</td> <td>8080</td> <td>指定代理端口</td></tr> <tr><td>Proxy_Username</td> <td>string</td> <td>否</td> <td>admin</td> <td>指定代理帐户</td></tr> <tr><td>Proxy_Password</td> <td>string</td> <td>否</td> <td>Password</td> <td>最大请求频率：40次/分钟</td></tr> <tr><td>TimeZone</td> <td>string</td> <td>否</td> <td>Asia/Shanghai +08:00</td> <td>时区</td></tr> <tr><td>CountryCode</td> <td>string</td> <td>否</td> <td>CN</td> <td>国家CODE</td></tr> <tr><td>CityCode</td> <td>string</td> <td>否</td> <td>Shanghai</td> <td>城市CODE</td></tr> <tr><td>RegionCode</td> <td>string</td> <td>否</td> <td>SH</td> <td>省份CODE</td></tr> <tr><td>LanguageCode</td> <td>string</td> <td>否</td> <td>En-US;en;q=0.9</td> <td>环境默认语言</td></tr> <tr><td>Cookie</td> <td>string</td> <td>否</td> <td>Cookie</td> <td>Json格式cookie文本</td></tr> <tr><td>Automatic_Configure</td> <td>bit</td> <td>是</td> <td>0 or 1 (建议填0)</td> <td><p>自动配置环境高级指纹参数</p><p>0:快速创建环境。依据候鸟商用库自动匹配所有信息。【当此值为0，环境创建时，<a class=""><strong>高级指纹参数</strong></a> 请人工填入参数值】</p><p>1:包含代理IP验证模式创建环境。依据候鸟商用库自动通过给定的代理匹配所有其它配置信息。【环境创建速度包含代理IP验证速度】</p></td></tr> <tr><td>Disable_video</td> <td>bit</td> <td>否</td> <td>0</td> <td>视频限流，0 关闭，1 开启，默认关闭</td></tr> <tr><td>Disable_img</td> <td>bit</td> <td>否</td> <td>0</td> <td>图片限流，0 关闭，1 开启，默认关闭</td></tr> <tr><td>HomePage_url</td> <td>string</td> <td>否</td> <td>https://www.baidu.com</td> <td>设置环境开启起始页，未设置时默认值：https://www.yalala.com/?wd=mb</td></tr></tbody></table></div> <p><strong>说明：</strong></p> <p>1、如 Automatic_Configure 的值为 1，创建环境过程中，会依据候鸟商用库通过给定的代理自动匹配所有其它配置信息。<strong>其中，自动验证代理合法有效的过程会导致每个环境的创建时间较长，</strong>请将您的代码POST请求检测超时设定在68秒以上。如果您希望快速批量创建环境【零耗时等待】，您需要使用：Automatic_Configure 为 0。</p> <p>2、如 Automatic_Configure 的值为 0，不再验证代理模式，您需要设定环境高级指纹参数，详见第4项<a class=""><code><strong>4、更新环境高级指纹参数。</strong></code></a></p> <blockquote><p>请求示例</p></blockquote> <div class="code-view"><pre><code>{ "Session_Name": "20230223", "Session_Desc": "THIS_IS_A_TEST", "Session_Group": "NEWGROUP", "Proxy_Type": "HTTP", "Proxy_Ip": "127.0.0.1", "Proxy_Port": "1080", "Proxy_Username": "TEST", "Proxy_Password": "TEST", "TimeZone": "US/Alaska -09:000", "CountryCode": "", "CityCode": "", "RegionCode": "", "LanguageCode": "", "Cookie": "thisisaCOOKIE", "Automatic_Configure": 1, "Disable_video": 0, "Disable_img": 0 }</code></pre> </div> <blockquote><p>执行成功返回</p></blockquote> <div class="code-view"><pre><code>{ "requestId": "8b558e5c5d1c437183c34aa03a09a368", "message": "Added Success", "code": 0, "data": { "Session_Id": "373808cb37bd63f5f7d92415e736e85f" //环境ID } }</code></pre> </div></div><div class="ant-tabs-tabpane ant-tabs-tabpane-inactive"><ul><li><p><strong class="pcolor">【最大请求频率：40次/分钟】</strong></p></li> <li><p>Path：/api/v1/session/adv_setting</p></li> <li><p>Method：POST</p></li> <li><p>Content-Type：application/json</p></li> <li><p>接口描述：手工设定环境高级指纹等参数，设定成功后返回环境ID。</p></li></ul> <blockquote><p>请求参数</p></blockquote> <div class="table"><table><thead><tr><td>参数名称</td> <td>类型</td> <td>必传</td> <td>样例串/默认值</td> <td>说明</td></tr></thead> <tbody><tr><td>Session_ID</td> <td>string</td> <td>是</td> <td>环境ID</td> <td>环境ID</td></tr> <tr><td>IS_WEBRTC</td> <td>bit</td> <td>否</td> <td>1 or 0</td> <td>1: WEBRTC FINGERPRINT 设定有效</td></tr> <tr><td>SystemOS</td> <td>string</td> <td>否</td> <td>Windows</td> <td>环境所持操作系统</td></tr> <tr><td>Session_Resolution</td> <td>string</td> <td>否</td> <td>2560x1440</td> <td>环境分辨率</td></tr> <tr><td>Session_UserAgent</td> <td>string</td> <td>否</td> <td>Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.111 Safari/537.36</td> <td>环境USER-AGENT</td></tr> <tr><td>StaticIP_Type</td> <td>bit</td> <td>否</td> <td>1 or 0</td> <td><p>1：静态IP</p><p>0：动态IP</p></td></tr> <tr><td>LanguageCode</td> <td>string</td> <td>否</td> <td>En-US;en;q=0.9</td> <td>环境默认语言</td></tr> <tr><td>FingerPrint_Setting</td> <td>string</td> <td>否</td> <td>1,1,1,1</td> <td><p>环境指纹设定：</p><p>Lwav=1,FontCode=1,Dns=1,Canvas=1</p><p>1 表示有效，0 表示无效</p></td></tr> <tr><td>Lock_Browser_windows_size</td> <td>bit</td> <td>否</td> <td>1</td> <td>默认值：1，保持浏览器窗口大小锁定</td></tr> <tr><td>Disable_video</td> <td>bit</td> <td>否</td> <td>0</td> <td>视频限流，0 关闭，1 开启，默认关闭</td></tr> <tr><td>Disable_img</td> <td>bit</td> <td>否</td> <td>0</td> <td>图片限流，0 关闭，1 开启，默认关闭</td></tr> <tr><td>Static_PublicIP</td> <td>string</td> <td>否</td> <td>xxx.xxx.xxx.xxx</td> <td>手动指定公网ip</td></tr> <tr><td>Static_PrivateIP</td> <td>string</td> <td>否</td> <td>xxx.xxx.xxx.xxx</td> <td>手动指定局域网ip</td></tr> <tr><td>TimeZone</td> <td>string</td> <td>否</td> <td>Asia/Shanghai +08:00</td> <td>时区</td></tr> <tr><td>CountryCode</td> <td>string</td> <td>否</td> <td>CN</td> <td>国家CODE</td></tr> <tr><td>CityCode</td> <td>string</td> <td>否</td> <td>Shanghai</td> <td>城市CODE</td></tr> <tr><td>RegionCode</td> <td>string</td> <td>否</td> <td>SH</td> <td>省份CODE</td></tr> <tr><td>ImageInvisible</td> <td>bit</td> <td>否</td> <td>1 or 0</td> <td>禁止加载网页图片资源</td></tr> <tr><td>AUTOPLAY_SETTING</td> <td>bit</td> <td>否</td> <td>1 or 0</td> <td>禁止网页视频自动播放、预加载</td></tr> <tr><td>SOUND_SETTING</td> <td>bit</td> <td>否</td> <td>1 or 0</td> <td>禁止网页播放声音</td></tr> <tr><td>GoogleTranslate</td> <td>bit</td> <td>否</td> <td>1 or 0</td> <td>禁止网页自动弹出谷歌翻译浮窗</td></tr> <tr><td>SavePasswordPop</td> <td>bit</td> <td>否</td> <td>1 or 0</td> <td>禁止网页自动弹出保存密码弹窗提示</td></tr> <tr><td>Notification</td> <td>bit</td> <td>否</td> <td>1 or 0</td> <td>禁止网页自动弹出消息通知窗口</td></tr> <tr><td>CLIPBOARD_READ_WRITE</td> <td>bit</td> <td>否</td> <td>1 or 0</td> <td>禁止网页自动读取系统剪贴板内容</td></tr> <tr><td>NetError_StopChrome</td> <td>bit</td> <td>否</td> <td>1 or 0</td> <td>代理或其它原因导致网络不通畅，自动停止打开浏览器</td></tr> <tr><td>fakedevice_forCamera</td> <td>bit</td> <td>否</td> <td>1 or 0</td> <td>开启环境中网页摄像头调用</td></tr> <tr><td>ExitClearChrome</td> <td>bit</td> <td>否</td> <td>1 or 0</td> <td>退出时自动清理环境缓存</td></tr> <tr><td>restore_on_startup</td> <td>bit</td> <td>否</td> <td>1 or 0</td> <td>自动打开上次浏览的网页</td></tr> <tr><td>ExitSaveCookie</td> <td>bit</td> <td>否</td> <td>1 or 0</td> <td>自动备份当前环境Cookies</td></tr> <tr><td>forcembpoint</td> <td>bit</td> <td>否</td> <td>1 or 0</td> <td>模拟手机页面的小圆点切换为光标箭头</td></tr> <tr><td>StartClearCache</td> <td>bit</td> <td>否</td> <td>1 or 0</td> <td>启动环境时，自动删除所有本地缓存文件</td></tr> <tr><td>StartClearCookies</td> <td>bit</td> <td>否</td> <td>1 or 0</td> <td>启动环境时，清空当前环境内全部Cookies</td></tr> <tr><td>StartClearHistory</td> <td>bit</td> <td>否</td> <td>1 or 0</td> <td>启动环境时，清空当前环境内全部历史记录</td></tr></tbody></table></div> <blockquote><p>请求示例</p></blockquote> <div class="code-view"><pre><code>{ "Session_ID": "cf77e3cb4a2bd3b7b69b48809eb5475b", "SystemOS": "linux", "FingerPrint_Setting": "1,0,1,1" }</code></pre> </div> <blockquote><p>执行成功返回</p></blockquote> <div class="code-view"><pre><code>{ "message": "Update Session adv_setting Success", "code": 0, "data": { "Session_Id": "373808cb37bd63f5f7d92415e736e85f" //环境ID } }</code></pre> </div></div><div class="ant-tabs-tabpane ant-tabs-tabpane-inactive"><ul><li><p><strong class="pcolor">【最大请求频率：40次/分钟】</strong></p></li> <li><p>Path：/api/v1/session/update</p></li> <li><p>Method：POST</p></li> <li><p>Content-Type：application/json</p></li> <li><p>接口描述：更新环境配置，更新成功后返回环境ID。</p></li></ul> <blockquote><p>请求参数</p></blockquote> <div class="table"><table><thead><tr><td>参数名称</td> <td>类型</td> <td>必传</td> <td>样例串/默认值</td> <td>说明</td></tr></thead> <tbody><tr><td>Session_ID</td> <td>string</td> <td>是</td> <td>373808cb37bd63f5f7d92415e736e85f</td> <td>环境ID</td></tr> <tr><td>Session_Name</td> <td>string</td> <td>否</td> <td>商业环境一</td> <td>环境名称，最大长度60字</td></tr> <tr><td>Session_Desc</td> <td>string</td> <td>否</td> <td>环境描述</td> <td>环境描述，最大长度150字</td></tr> <tr><td>Session_Group</td> <td>string</td> <td>否</td> <td>环境所属分组</td> <td>环境所属分组，最大长度30字，如未填写值则为默认分组</td></tr> <tr><td>Cookie</td> <td>string</td> <td>否</td> <td>Cookie</td> <td>Json格式cookie文本</td></tr> <tr><td>Disable_video</td> <td>bit</td> <td>否</td> <td>0</td> <td>视频限流，0 关闭，1 开启，默认关闭</td></tr> <tr><td>Disable_img</td> <td>bit</td> <td>否</td> <td>0</td> <td>图片限流，0 关闭，1 开启，默认关闭</td></tr> <tr><td>HomePage_url</td> <td>string</td> <td>否</td> <td>https://www.baidu.com</td> <td>设置环境开启起始页，未设置时默认值：https://www.yalala.com/?wd=mb</td></tr></tbody></table></div> <p><strong>说明：</strong></p> <p><strong>其中，自动验证代理合法有效的过程会导致每个环境的创建时间较长，</strong></p> <blockquote><p>请求示例</p></blockquote> <div class="code-view"><pre><code>{ "Session_ID": "373808cb37bd63f5f7d92415e736e85f", "Session_Name": "商业环境一", "Session_Desc": "环境描述", "Session_Group": "环境所属分组", "Cookie": "thisisaCOOKIE", "Disable_video": 0, "Disable_img": 0 }</code></pre> </div> <blockquote><p>执行成功返回</p></blockquote> <div class="code-view"><pre><code>{ "message": "Update Session Success", "code": 0, "data": { "Session_Id": "373808cb37bd63f5f7d92415e736e85f" //环境ID } }</code></pre> </div></div><div class="ant-tabs-tabpane ant-tabs-tabpane-inactive"><ul><li><p><strong class="pcolor">【最大请求频率：50次/分钟】</strong></p></li> <li><p>Path：/api/v1/session/proxy/update</p></li> <li><p>Method：POST</p></li> <li><p>Content-Type：application/json</p></li> <li><p>接口描述：更新环境代理配置（将多个代理附加到指定环境集合），更新成功后返回环境 ID。</p></li></ul> <blockquote><p>请求参数</p></blockquote> <div class="table"><table><thead><tr><td>参数名称</td> <td>类型</td> <td>必传</td> <td>样例串/默认值</td> <td>说明</td></tr></thead> <tbody><tr><td>Session_ID</td> <td>array</td> <td>是</td> <td>373808cb37bd63f5f7d92415e736e85f</td> <td>环境ID</td></tr> <tr><td>Proxy_Type</td> <td>string</td> <td>是</td> <td>HTTP,HTTPS,SSH,SOCKS4,SOCKS4A,SOCKS5,Oxylabsauto,Lumauto,Luminati_HTTP,Luminati_HTTPS,smartproxy,noproxy</td> <td><p>自动配置环境高级指纹参数</p><p>[无代理:noproxy]</p></td></tr> <tr><td>Proxy_Ip</td> <td>string</td> <td>否</td> <td>*************</td> <td>指定代理IP</td></tr> <tr><td>Proxy_Port</td> <td>string</td> <td>否</td> <td>8080</td> <td>指定代理端口</td></tr> <tr><td>Proxy_Username</td> <td>string</td> <td>否</td> <td>admin</td> <td>指定代理帐户</td></tr> <tr><td>Proxy_Password</td> <td>string</td> <td>否</td> <td>Password</td> <td>指定代理密码</td></tr> <tr><td>Is_CheckProxy</td> <td>bit</td> <td>否</td> <td>1</td> <td>是否自动检测代理【默认:1】</td></tr> <tr><td>TimeZone</td> <td>string</td> <td>否</td> <td>Asia/Shanghai +08:00</td> <td>时区</td></tr> <tr><td>CountryCode</td> <td>string</td> <td>否</td> <td>CN</td> <td>国家CODE</td></tr> <tr><td>CityCode</td> <td>string</td> <td>否</td> <td>Shanghai</td> <td>城市CODE</td></tr> <tr><td>RegionCode</td> <td>string</td> <td>否</td> <td>SH</td> <td>省份CODE</td></tr> <tr><td>LanguageCode</td> <td>string</td> <td>否</td> <td>En-US;en;q=0.9</td> <td>环境默认语言</td></tr> <tr><td>StaticIP_Type</td> <td>bit</td> <td>否</td> <td>1 or 0</td> <td><p>1：静态IP</p><p>0：动态IP</p></td></tr> <tr><td>Static_PublicIP</td> <td>string</td> <td>否</td> <td>************</td> <td>环境公有IP</td></tr> <tr><td>Static_PrivateIP</td> <td>string</td> <td>否</td> <td>**************</td> <td>环境私有IP</td></tr></tbody></table></div> <blockquote><p>请求示例</p></blockquote> <div class="code-view"><pre><code>{ "Session_ID": "373808cb37bd63f5f7d92415e736e85f", "Proxy_Type": "HTTP", "Proxy_Ip": "*********", "Proxy_Port": 1234, "Proxy_Username": "abcdefg", "Proxy_Password": "123456", "StaticIP_Type": 1, "Is_CheckProxy": 0, "Static_PublicIP": "********", "LanguageCode": "En-US;en;q=0.9" }</code></pre> </div> <blockquote><p>执行成功返回</p></blockquote> <div class="code-view"><pre><code>{ "message": "Update Session Proxy Success", "code": 0, "data": { "Session_Id": "373808cb37bd63f5f7d92415e736e85f" //环境ID } }</code></pre> </div></div><div class="ant-tabs-tabpane ant-tabs-tabpane-inactive"><ul><li><p>Path：/api/v1/session/delete</p></li> <li><p>Method：POST</p></li> <li><p>Content-Type：application/json</p></li> <li><p>接口描述：删除指定环境。删除成功返回code:0 , message:Delete Session Success。一次性支持删除环境 2000 个，删除的环境在本地客户端里将完全彻底删除，支持在 WEB 控制台的 "历史环境" 里找回已删除的环境。</p></li></ul> <blockquote><p>请求参数</p></blockquote> <div class="table"><table><thead><tr><td>参数名称</td> <td>类型</td> <td>必传</td> <td>样例串/默认值</td> <td>说明</td></tr></thead> <tbody><tr><td>Session_ID</td> <td>array</td> <td>是</td> <td>373808cb37bd63f5f7d92415e736e85f</td> <td>支持传多个环境ID</td></tr> <tr><td>Is_Delete_All</td> <td>bit</td> <td>否</td> <td>1 or 0</td> <td>当值为 1 时，自动忽略 Session_Id，进行全局所有环境删除</td></tr></tbody></table></div> <blockquote><p>请求示例</p></blockquote> <div class="code-view"><pre><code>{ "Session_ID": ["373808cb37bd63f5f7d92415e736e85f","7579a099e6fcee76fd1272ccdc30c1cc","c1f3f1b3d5072985581fe54343f1e524"] }</code></pre> </div> <blockquote><p>指定 Session_ID 执行成功、失败返回</p></blockquote> <div class="code-view"><pre><code>{ "msg": "Session Delete Finished.", "code": -12, "data": { "Delete_Session_Success": "373808cb37bd63f5f7d92415e736e85f,c1f3f1b3d5072985581fe54343f1e524", "Delete_Session_Failed": "7579a099e6fcee76fd1272ccdc30c1cc" } }</code></pre> </div> <blockquote><p>Is_Delete_All 执行成功、失败返回</p></blockquote> <div class="code-view"><pre><code>{ "msg": "All Session Delete Finished.", "code": 0, "data": { "Delete_All_Session_Success_cont": 118 } }</code></pre> </div> <div class="code-view"><pre><code>{ "msg": "All Session Delete Failed.", "code": -12 }</code></pre> </div></div><div class="ant-tabs-tabpane ant-tabs-tabpane-inactive"><ul><li><p>Path：/api/v1/session/import-cookie</p></li> <li><p>Method：POST</p></li> <li><p>Content-Type：application/json</p></li> <li><p>接口描述：向指定环境导入Cookie。导入成功返回Code:0, Message:Import Session Success。</p></li></ul> <blockquote><p>请求参数</p></blockquote> <div class="table"><table><thead><tr><td>参数名称</td> <td>类型</td> <td>必传</td> <td>样例串/默认值</td> <td>说明</td></tr></thead> <tbody><tr><td>Session_ID</td> <td>string</td> <td>是</td> <td>373808cb37bd63f5f7d92415e736e85f</td> <td>指定导入的环境ID</td></tr> <tr><td>Cookie_File</td> <td>string</td> <td>是</td> <td>C:\cookie.txt</td> <td>填入Cookie文件路径，支持text,json格式</td></tr></tbody></table></div> <blockquote><p>请求示例</p></blockquote> <div class="code-view"><pre><code>{ "Session_ID": "373808cb37bd63f5f7d92415e736e85f" "Cookie_File": "c:\0221cookie.txt" }</code></pre> </div> <blockquote><p>执行成功返回</p></blockquote> <div class="code-view"><pre><code>{ "msg": "Import Cookie Success", "code": 0, "data": { "Session_Id": 373808cb37bd63f5f7d92415e736e85f, //环境ID "cookie": "cookie content" } }</code></pre> </div> <blockquote><p>执行失败返回</p></blockquote> <div class="code-view"><pre><code>{ "msg": "Import Cookie failed", "code": -13, "data": { "Session_Id": 373808cb37bd63f5f7d92415e736e85f //环境ID } }</code></pre> </div></div><div class="ant-tabs-tabpane ant-tabs-tabpane-inactive"><ul><li><p>Path：/api/v1/session/export-cookie</p></li> <li><p>Method：POST</p></li> <li><p>Content-Type：application/json</p></li> <li><p>接口描述：导出指定环境的Cookie。导出成功返回Code:0, message:Export Session Success。</p></li></ul> <blockquote><p>请求参数</p></blockquote> <div class="table"><table><thead><tr><td>参数名称</td> <td>类型</td> <td>必传</td> <td>样例串/默认值</td> <td>说明</td></tr></thead> <tbody><tr><td>Session_ID</td> <td>string</td> <td>是</td> <td>373808cb37bd63f5f7d92415e736e85f</td> <td>指定导出的环境ID</td></tr> <tr><td>Export_Cookie_File</td> <td>string</td> <td>是</td> <td>C:\cookie.txt</td> <td>指定导出环境的COOKIE到指定路径</td></tr></tbody></table></div> <p><strong>注：如Export_Cookie_File的参数值，例：C:\cookie.json，则导出为json格式cookie。</strong></p> <blockquote><p>请求示例</p></blockquote> <div class="code-view"><pre><code>{ "Session_ID": "373808cb37bd63f5f7d92415e736e85f" "Export_Cookie_File": "c:\0221cookie.txt" }</code></pre> </div> <blockquote><p>执行成功返回</p></blockquote> <div class="code-view"><pre><code>{ "msg": "Export Cookie Success", "code": 0, "data": { "Session_Id": 373808cb37bd63f5f7d92415e736e85f, //环境ID "cookie": "cookie content" } }</code></pre> </div> <blockquote><p>执行失败返回</p></blockquote> <div class="code-view"><pre><code>{ "msg": "Export Cookie failed", "code": -14, "data": { "Session_Id": 373808cb37bd63f5f7d92415e736e85f, //环境ID "cookie": "" } }</code></pre> </div></div><div class="ant-tabs-tabpane ant-tabs-tabpane-inactive"><ul><li><p>Path：/api/v1/session/random-user-agent</p></li> <li><p>Method：POST</p></li> <li><p>Content-Type：application/json</p></li> <li><p>接口描述：从候鸟UA商业库和用户自定义UA库中，获取随机UA，获取成功返回UA。</p></li></ul> <blockquote><p>请求参数</p></blockquote> <div class="table"><table><thead><tr><td>参数名称</td> <td>类型</td> <td>必传</td> <td>样例串/默认值</td> <td>说明</td></tr></thead> <tbody><tr><td>SYSTEM</td> <td>string</td> <td>否</td> <td>Windows</td> <td>操作系统参数：Windows、Android、iPhone<a class="">（点击查看参数表）</a></td></tr> <tr><td>MobileModel</td> <td>string</td> <td>否</td> <td>iphone 6</td> <td>SYSTEM 选择 android 和 ios 时，机型必填。机型参数包括："google Pixel 4、红米 8、红米7、google Pixel 5a、三星 Galaxy Note8、小米10、三星 Galaxy S9+、小米 9、iPhone 6 Plus、iPhone 8 Plus、iPhone SE 2、iPhone 7 Plus、iPhone X、iPhone13 Pro、iPhone XS、iPhone 13 Pro Max、iPhone 12 mini、iPhone 8、iPhone 13 mini、iPhone 6、iPhone 12 Pro Max、iPhone 7、iPhone 12 、iPhone 12 Pro、iPhone 11 Pro、iPhone 13"</td></tr> <tr><td>Version</td> <td>string</td> <td>否</td> <td>97</td> <td>浏览器版本：支持数组，不传参默认随机。范围包括 95、96、97、98、99、100、101、102、103、104、105、106</td></tr></tbody></table></div> <blockquote><p>请求示例</p></blockquote> <div class="code-view"><pre><code>{ "SYSTEM": "Windows" }</code></pre> </div> <blockquote><p>执行成功返回</p></blockquote> <div class="code-view"><pre><code>{ "msg": "Get random user-agent Success", "code": 0, "data": { "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/104.0.5112.80 Safari/537.36" } }</code></pre> </div></div><div class="ant-tabs-tabpane ant-tabs-tabpane-inactive"><ul><li><p>Path：/api/v1/session/BrowserCache-clean</p></li> <li><p>Method：POST</p></li> <li><p>Content-Type：application/json</p></li> <li><p>接口描述：清除环境本地缓存。</p></li></ul> <blockquote><p>请求参数</p></blockquote> <div class="table"><table><thead><tr><td>参数名称</td> <td>类型</td> <td>必传</td> <td>样例串/默认值</td> <td>说明</td></tr></thead> <tbody><tr><td>Session_ID</td> <td>array</td> <td>是</td> <td>373808cb37bd63f5f7d92415e736e85f</td> <td>清除指定环境ID的浏览器缓存</td></tr> <tr><td>Is_Clean_Cookies</td> <td>bit</td> <td>否</td> <td>1</td> <td>清除指定环境ID的COOKIE/仅清除cookie，其它不清除</td></tr></tbody></table></div> <blockquote><p>请求示例</p></blockquote> <div class="code-view"><pre><code>{ "Session_ID": ["373808cb37bd63f5f7d92415e736e85f","7579a099e6fcee76fd1272ccdc30c1cc","c1f3f1b3d5072985581fe54343f1e524"], "Is_Clean_Cookies": 1 }</code></pre> </div> <blockquote><p>执行成功返回</p></blockquote> <div class="code-view"><pre><code>{ "msg": "BrowserCache clean Success", "code": 0, "data": { "Clean_SessionID_Success": "373808cb37bd63f5f7d92415e736e85f", "Clean_SessionID_Failed": "f994d8e641ce7006acfa36c901829ff2" } }</code></pre> </div></div><div class="ant-tabs-tabpane ant-tabs-tabpane-inactive"><ul><li><p>Path：/api/v1/browser/status</p></li> <li><p>Method：POST</p></li> <li><p>Content-Type：application/json</p></li> <li><p>接口描述：指定查看单个/多个环境的运行状态。</p></li></ul> <blockquote><p>请求参数</p></blockquote> <div class="table"><table><thead><tr><td>参数名称</td> <td>类型</td> <td>必传</td> <td>样例串/默认值</td> <td>说明</td></tr></thead> <tbody><tr><td>Session_ID</td> <td>array</td> <td>否</td> <td>********************************,********************************</td> <td>指定环境(支持一次最多100个环境)</td></tr> <tr><td>Actived_Type</td> <td>int</td> <td>否</td> <td>0,1,2</td> <td>0:返回列表中所有环境运行状态<br>1:返回列表中已运行环境集合<br>2:返回列表中未运行环境集合</td></tr></tbody></table></div> <p><strong>如不带任何参数则默认列出所有正在运行的环境ID列表</strong></p> <p><strong>返回：Session_Actived: 1 or 0，1：正在运行中 0：未运行</strong></p> <blockquote><p>请求示例</p></blockquote> <div class="code-view"><pre><code>{ "Session_ID":["4e6d0dca26ef42be9bc4472779d2550f"] }</code></pre> </div> <blockquote><p>执行成功返回</p></blockquote> <div class="code-view"><pre><code>{ "code": 0, "data": { "listcontainer": [ { "Group_Name": "Default", "Session_ID": "4e6d0dca26ef42be9bc4472779d2550f", "Session_Name": "forTest", "Session_ProcessID": 4668, "Session_StartTime": "2024-11-21 16:52:09", "Session_Actived": 1, "Session_Args": "--ImagesEnabled=0 --interval-seconds=3 --disable-extensions--enable-logging --v=1", "Session_Headless": 0 } ], "total": 1 }, "message": "Session Status Return Success" }</code></pre> </div> <blockquote><p>不带任何参数时返回</p></blockquote> <div class="code-view"><pre><code>{ "code": 0, "data": { "listcontainer": [ { "Group_Name": "2323", "Session_Actived": 1, "Session_Args": "", "Session_Headless": 0, "Session_ID": "6f684e78ae2b17a1caeae3a7dae1072b", "Session_Name": "test_1", "Session_ProcessID": 17552, "Session_StartTime": "2025-05-05 22:34:50" }, { "Group_Name": "2323", "Session_Actived": 1, "Session_Args": "", "Session_Headless": 0, "Session_ID": "ac03df49c562ff3b0b4441d57f5407a7", "Session_Name": "test_2", "Session_ProcessID": 43412, "Session_StartTime": "2025-05-05 22:34:51" }, { "Group_Name": "2323", "Session_Actived": 1, "Session_Args": "", "Session_Headless": 0, "Session_ID": "621776722aa7efabcd7aaf7af0dbecd7", "Session_Name": "test_3", "Session_ProcessID": 18908, "Session_StartTime": "2025-05-05 22:34:52" } ], "total": 3 }, "message": "Session Status Return Success" }</code></pre> </div></div><div class="ant-tabs-tabpane ant-tabs-tabpane-inactive"><ul><li><p>Path：/api/v1/session/auto_open/list</p></li> <li><p>Method：POST</p></li> <li><p>Content-Type：application/json</p></li> <li><p>接口描述：查看环境网页自动运行信息，成功后返回环境网页自动运行详细信息。</p></li></ul> <blockquote><p>请求参数</p></blockquote> <div class="table"><table><thead><tr><td>参数名称</td> <td>类型</td> <td>必传</td> <td>样例串/默认值</td> <td>说明</td></tr></thead> <tbody><tr><td>Session_ID</td> <td>array</td> <td>是</td> <td>06b2449cc7c5827e1b02db16811c6c05,e938999fdba8df4a4530cdff6acbeaa6</td> <td>指定环境ID</td></tr></tbody></table></div> <blockquote><p>请求示例</p></blockquote> <div class="code-view"><pre><code>{ "Session_ID": ["06b2449cc7c5827e1b02db16811c6c05", "e938999fdba8df4a4530cdff6acbeaa6"] }</code></pre> </div> <blockquote><p>执行成功返回</p></blockquote> <div class="code-view"><pre><code>{ "code": 0, "data": { "listcontainer": [ { "Group_Name": "Default", "OpenUrl_Count": 1, "Session_ID": "e938999fdba8df4a4530cdff6acbeaa6", "Session_Name": "t3", "StartPage1": "1-https://www.yalala.com/?wd=mb", "status": 0 }, { "Group_Name": "Default", "OpenUrl_Count": 2, "Session_ID": "06b2449cc7c5827e1b02db16811c6c05", "Session_Name": "t7", "StartPage1": "1-https://www.baidu1.com", "StartPage2": "1-https://www.yalala.com/?wd=mb", "status": 0 } ], "total": 2 }, "message": "AutoOpen List Success" }</code></pre> </div></div><div class="ant-tabs-tabpane ant-tabs-tabpane-inactive"><ul><li><p>Path：/api/v1/session/auto_open/add</p></li> <li><p>Method：POST</p></li> <li><p>Content-Type：application/json</p></li> <li><p>接口描述：添加环境自动运行网页地址，成功后返回环境网页自动运行详细信息。</p></li></ul> <blockquote><p>请求参数</p></blockquote> <div class="table"><table><thead><tr><td>参数名称</td> <td>类型</td> <td>必传</td> <td>样例串/默认值</td> <td>说明</td></tr></thead> <tbody><tr><td>Session_ID</td> <td>array</td> <td>是</td> <td>06b2449cc7c5827e1b02db16811c6c05,e938999fdba8df4a4530cdff6acbeaa6</td> <td>指定环境ID</td></tr> <tr><td>StartPage</td> <td>string</td> <td>是</td> <td>https://www.baidu.com</td> <td>环境自动启动页面URL</td></tr></tbody></table></div> <blockquote><p>请求示例</p></blockquote> <div class="code-view"><pre><code>{ "Session_ID": ["06b2449cc7c5827e1b02db16811c6c05", "e938999fdba8df4a4530cdff6acbeaa6"], "StartPage": "https://www.baidu.com" }</code></pre> </div> <blockquote><p>执行成功返回</p></blockquote> <div class="code-view"><pre><code>{ "code": 0, "data": { "listcontainer": [ { "Group_Name": "Default", "OpenUrl_Count": 2, "Session_ID": "e938999fdba8df4a4530cdff6acbeaa6", "Session_Name": "t3", "StartPage1": "1-https://www.yalala.com/?wd=mb", "StartPage2": "1-https://www.baidu.com", "status": 0 }, { "Group_Name": "Default", "OpenUrl_Count": 3, "Session_ID": "06b2449cc7c5827e1b02db16811c6c05", "Session_Name": "t7", "StartPage1": "1-https://www.baidu1.com", "StartPage2": "1-https://www.yalala.com/?wd=mb", "StartPage3": "1-https://www.baidu.com", "status": 0 } ], "total": 2 }, "message": "AutoOpen Add Success" }</code></pre> </div></div><div class="ant-tabs-tabpane ant-tabs-tabpane-inactive"><ul><li><p>Path：/api/v1/session/auto_open/update</p></li> <li><p>Method：POST</p></li> <li><p>Content-Type：application/json</p></li> <li><p>接口描述：更新环境某个自动运行网页地址，成功后返回环境网页自动运行详细信息。</p></li></ul> <blockquote><p>请求参数</p></blockquote> <div class="table"><table><thead><tr><td>参数名称</td> <td>类型</td> <td>必传</td> <td>样例串/默认值</td> <td>说明</td></tr></thead> <tbody><tr><td>Session_ID</td> <td>array</td> <td>是</td> <td>06b2449cc7c5827e1b02db16811c6c05,e938999fdba8df4a4530cdff6acbeaa6</td> <td>指定环境ID</td></tr> <tr><td>OldStartPage</td> <td>string</td> <td>是</td> <td>https://www.baidu.com</td> <td>旧网页地址</td></tr> <tr><td>NewStartPage</td> <td>string</td> <td>是</td> <td>https://www.baidu1.com</td> <td>新网页地址</td></tr></tbody></table></div> <blockquote><p>请求示例</p></blockquote> <div class="code-view"><pre><code>{ "Session_ID": ["06b2449cc7c5827e1b02db16811c6c05", "e938999fdba8df4a4530cdff6acbeaa6"], "OldStartPage": "https://www.baidu.com", "NewStartPage": "https://www.baidu1.com" }</code></pre> </div></div><div class="ant-tabs-tabpane ant-tabs-tabpane-active"><div></div><ul><li><p>Path：/api/v1/session/auto_open/delete</p></li> <li><p>Method：POST</p></li> <li><p>Content-Type：application/json</p></li> <li><p>接口描述：删除环境某个自动运行网页地址，成功后返回环境网页自动运行详细信息。</p></li></ul> <blockquote><p>请求参数</p></blockquote> <div class="table"><table><thead><tr><td>参数名称</td> <td>类型</td> <td>必传</td> <td>样例串/默认值</td> <td>说明</td></tr></thead> <tbody><tr><td>Session_ID</td> <td>array</td> <td>是</td> <td>06b2449cc7c5827e1b02db16811c6c05,e938999fdba8df4a4530cdff6acbeaa6</td> <td>指定环境ID</td></tr> <tr><td>StartPage</td> <td>string</td> <td>是</td> <td>https://www.baidu.com</td> <td>要删除的网页地址</td></tr></tbody></table></div> <blockquote><p>请求示例</p></blockquote> <div class="code-view"><pre><code>{ "Session_ID": ["06b2449cc7c5827e1b02db16811c6c05", "e938999fdba8df4a4530cdff6acbeaa6"], "StartPage": "https://www.baidu.com" }</code></pre> </div><div></div></div></div><div></div></div> <p><a class="ant-btn ant-btn-primary">使用POSTMAN调试此接口</a></p>
</body>
</html>