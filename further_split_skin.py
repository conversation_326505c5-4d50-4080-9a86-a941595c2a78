#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
进一步处理超过30KB的皮肤文件
"""

import os
from pathlib import Path

def find_large_files(directory, max_size_kb=30):
    """查找超过指定大小的文件"""
    dir_path = Path(directory)
    large_files = []
    
    for file_path in dir_path.iterdir():
        if file_path.is_file():
            file_size = file_path.stat().st_size / 1024
            if file_size > max_size_kb:
                large_files.append({
                    'path': file_path,
                    'size': file_size
                })
    
    return large_files

def split_file_further(file_path, max_size_kb=25):
    """进一步切分文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except UnicodeDecodeError:
        try:
            with open(file_path, 'r', encoding='gbk') as f:
                content = f.read()
        except:
            with open(file_path, 'r', encoding='latin-1') as f:
                content = f.read()
    
    lines = content.split('\n')
    total_lines = len(lines)
    
    # 计算需要的部分数
    file_size_kb = file_path.stat().st_size / 1024
    target_lines_per_part = int(total_lines * max_size_kb / file_size_kb)
    num_parts = max(2, (total_lines + target_lines_per_part - 1) // target_lines_per_part)
    
    lines_per_part = total_lines // num_parts
    
    # 提取基础文件名（去掉前缀和扩展名）
    filename = file_path.name
    if filename.startswith("候鸟皮肤库窗口源代码_"):
        base_name = filename[len("候鸟皮肤库窗口源代码_"):]
    else:
        base_name = filename
    
    # 去掉扩展名
    base_name = Path(base_name).stem
    extension = file_path.suffix
    parent_dir = file_path.parent
    
    split_files = []
    
    for part_num in range(num_parts):
        start_line = part_num * lines_per_part
        if part_num == num_parts - 1:
            end_line = total_lines
        else:
            end_line = (part_num + 1) * lines_per_part
        
        part_content = '\n'.join(lines[start_line:end_line])
        
        # 生成新文件名（保持前缀）
        new_filename = f"候鸟皮肤库窗口源代码_{base_name}({part_num + 1}){extension}"
        new_filepath = parent_dir / new_filename
        
        # 保存文件
        with open(new_filepath, 'w', encoding='utf-8') as f:
            f.write(part_content)
        
        split_files.append(new_filepath)
        
        new_size = new_filepath.stat().st_size / 1024
        print(f"   ✅ 生成: {new_filename} ({new_size:.1f} KB, {end_line-start_line}行)")
    
    return split_files

def process_remaining_large_files(directory):
    """处理剩余的大文件"""
    print(f"📁 检查目录: {directory}")
    
    # 查找大文件
    large_files = find_large_files(directory, max_size_kb=30)
    
    if not large_files:
        print("✅ 没有发现超过30KB的文件")
        return
    
    print(f"🔴 发现 {len(large_files)} 个超过30KB的文件:")
    for file_info in large_files:
        print(f"   - {file_info['path'].name} ({file_info['size']:.1f} KB)")
    
    print(f"\n🔄 开始进一步切分...")
    
    for file_info in large_files:
        print(f"\n📄 处理文件: {file_info['path'].name} ({file_info['size']:.1f} KB)")
        
        try:
            # 切分文件
            split_files = split_file_further(file_info['path'], max_size_kb=25)
            
            # 删除原文件
            file_info['path'].unlink()
            print(f"   🗑️  删除原文件: {file_info['path'].name}")
            
            print(f"   ✅ 成功切分为 {len(split_files)} 个文件")
            
        except Exception as e:
            print(f"   ❌ 切分失败: {str(e)}")

def final_check(directory):
    """最终检查"""
    dir_path = Path(directory)
    all_files = [f for f in dir_path.iterdir() if f.is_file()]
    
    print(f"\n📋 最终检查结果:")
    print("=" * 80)
    
    total_size = 0
    large_count = 0
    prefix_count = 0
    
    for file_path in sorted(all_files):
        file_size = file_path.stat().st_size / 1024
        total_size += file_size
        
        # 检查前缀
        if file_path.name.startswith("候鸟皮肤库窗口源代码_"):
            prefix_count += 1
        
        # 检查大小
        if file_size > 30:
            large_count += 1
            status = "🔴"
        elif file_size > 20:
            status = "🟡"
        elif file_size > 10:
            status = "✅"
        else:
            status = "🟢"
        
        print(f"{status} {file_path.name} ({file_size:.1f} KB)")
    
    print("=" * 80)
    print(f"📊 最终统计:")
    print(f"   总文件数: {len(all_files)}")
    print(f"   总大小: {total_size:.1f} KB")
    print(f"   平均大小: {total_size/len(all_files):.1f} KB")
    print(f"   有前缀文件: {prefix_count} 个 ({prefix_count/len(all_files)*100:.1f}%)")
    print(f"   超过30KB文件: {large_count} 个")
    
    if large_count == 0:
        print(f"\n🎉 所有文件都符合大小要求！")
    else:
        print(f"\n⚠️  还有 {large_count} 个文件超过30KB")

if __name__ == "__main__":
    source_directory = r"F:\augment\output\skin"
    
    print("📄 皮肤文件进一步处理工具")
    print("=" * 80)
    
    # 处理剩余大文件
    process_remaining_large_files(source_directory)
    
    # 最终检查
    final_check(source_directory)
    
    print(f"\n📁 处理完成的文件位置: {source_directory}")
    print("🎯 所有文件处理完成！")
