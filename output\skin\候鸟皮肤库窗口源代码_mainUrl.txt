<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Window size="600,280" caption="0,0,0,50" roundcorner="5,5,5,5" showshadow="true" shadowsize="3" shadowposition="0,0" shadowcolor="#ff6272A4" fademode="true">
	<Include source="Default.xml" />

  <VerticalLayout width="600" height="310" bkcolor="#FF282A36">

    <HorizontalLayout height="37">

    	 <Button name="login" bkimage="icon.png" padding="8,6,0,0" width="28" height="28"/>

      <Label name="SetMainUrl" padding="6,4,0,0" text="设定环境起始页" width="380" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" align="left" font="8"></Label>

      <Control />
      <Button name="minbtn" width="28" height="26" tooltip="最小化" normalimage="minbtn.png" hotimage="minbtn_hover.png" pushedimage="minbtnpush.png" />
      <Button name="closebtn" width="28" height="26"  tooltip="关闭" normalimage="closebtn.png" hotimage="closebtn_hover.png" pushedimage="closebtnpush.png" />

    </HorizontalLayout>

  	<HorizontalLayout name="bkground" visible="true">

			<VerticalLayout width="600" height="230" bkcolor="#FF282A36">
        <HorizontalLayout height="36" >
        <Label name="SelectItemGroupM" width="236" textpadding="22,6,0,0" text="请选择会话环境分组"  texttooltip="true"  endellipsis="true" font="8" height="32" textcolor="#FFF8F8F2"/>
        <Label name="SelectItemMainUrlMsg" textpadding="24,6,0,0" text="请选择起始页的会话环境："  texttooltip="true"  endellipsis="true" font="8" height="32" width="320" textcolor="#FFF8F8F2"/>
        </HorizontalLayout>
					<HorizontalLayout height="42" >
            <VerticalLayout width="236">
              <Combo name="group" bordersize="0" padding="21,0,0,0" width="200" height="36" borderround="7,7" textcolor="#FFF8F8F2" disabledtextcolor="#FF6272A4" itemalign="left" itemfont="0" bkcolor="#FF44475A"
              normalimage="file='Profile\Proxy_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\proxy_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\proxy_Combox_Click.png' corner='5,5,25,10'"
              combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
              itemhotbkcolor="#FF44475A" itemselectedbkcolor="#FF6272A4" itemtextpadding="10,0,0,0" textpadding="0,0,20,0" endellipsis="true">
              </Combo>
            </VerticalLayout>
            <VerticalLayout>
					      <Combo name="system" bordersize="0" padding="21,0,0,10" width="320" height="36" borderround="7,7" textcolor="#FFF8F8F2" disabledtextcolor="#FF6272A4" itemalign="left" itemfont="0" textpadding="10,-2,0,0" bkcolor="#FF44475A"
											normalimage="file='Profile\Setting_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\Setting_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\Setting_Combox_Click.png' corner='5,5,25,10'"
											combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
											itemhotbkcolor="#FF44475A" itemselectedbkcolor="#FF6272A4" itemtextpadding="10,0,0,0">

											</Combo>
					     </VerticalLayout>
				</HorizontalLayout>
				<HorizontalLayout height="2">
				</HorizontalLayout>
				<HorizontalLayout height="130" padding="10,6,0,0" >
	            <Label name="mainpageurll" width="86"  padding="15,4,0,0" height="20" text="环境起始页：" texttooltip="true"  endellipsis="true" textcolor="#FFF8F8F2" />
	         <RichEdit name="mainpageurledit" padding="0,0,20,10" height="86" bordercolor="#FF6272A4" bordersize="1" borderround="5,5" killfocusimage="" bkimage="file='file_url_location_normal.png' dest='0,0,276,26'" font="0" textpadding="34,5,20,0" text="" tipvaluecolor="#FFF8F8F2" multiline="true" textcolor="#FFF8F8F2" rich="false" vscrollbar="true">
            </RichEdit>
	       </HorizontalLayout>
			</VerticalLayout>
	  </HorizontalLayout>
    <HorizontalLayout height="52" bkcolor="#FF44475A">
         <Control />
      <VerticalLayout width="320">
       		<Control />
			        <Label name="homepageInfo" padding="20,0,0,0" text="" width="460" align="left" textcolor="#FFF8F8F2" hottextcolor="#FFFF5555" font="8"></Label>
          <Control />
       </VerticalLayout>

      <VerticalLayout width="140">
        <Button text="设定首页" name="btnSetMainUrl"  padding="0,12,0,20" width="120" height="30" texttooltip="true"  endellipsis="true" textpadding="10,0,10,0" textcolor="#FFF8F8F2" disabledtextcolor="#FF6272A4" align="center" font="14"	bkimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
        <Control width="20"/>
      </VerticalLayout>

        <VerticalLayout width="140">
          <Button text="关闭此窗口" name="btnCloseWnd"  padding="0,12,0,20" width="120" height="30" texttooltip="true"  endellipsis="true" textpadding="10,0,10,0" textcolor="#FFF8F8F2" disabledtextcolor="#FF6272A4" align="center" font="14"	bkimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
          <Control width="20"/>
       </VerticalLayout>

			<Control />
    </HorizontalLayout>
  </VerticalLayout>
</Window>
