#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析大型DOCX文件的详细结构
"""

import os
from pathlib import Path
from docx import Document
import re

def analyze_large_docx(docx_path):
    """详细分析大型DOCX文件"""
    try:
        doc = Document(docx_path)
        
        print(f"📄 分析文件: {Path(docx_path).name}")
        file_size = Path(docx_path).stat().st_size / 1024
        print(f"📊 文件大小: {file_size:.1f} KB")
        print(f"📊 总段落数: {len(doc.paragraphs)}")
        print(f"📊 总表格数: {len(doc.tables)}")
        print()
        
        # 分析段落内容
        print("📋 段落内容分析:")
        total_chars = 0
        non_empty_paragraphs = 0
        heading_count = 0
        
        # 统计前100个段落的信息
        print("前100个段落预览:")
        for i, paragraph in enumerate(doc.paragraphs[:100]):
            text = paragraph.text.strip()
            if text:
                non_empty_paragraphs += 1
                total_chars += len(text)
                
                if paragraph.style.name.startswith('Heading') or len(text) < 100:
                    heading_count += 1
                    print(f"  {i:3d}: [{paragraph.style.name}] {text[:80]}...")
                elif i < 20:  # 只显示前20个普通段落
                    print(f"  {i:3d}: {text[:80]}...")
        
        print(f"\n📊 内容统计:")
        print(f"   非空段落: {non_empty_paragraphs}")
        print(f"   总字符数: {total_chars}")
        print(f"   疑似标题: {heading_count}")
        print(f"   平均段落长度: {total_chars/max(1, non_empty_paragraphs):.1f} 字符")
        
        # 分析表格内容
        print(f"\n📊 表格分析:")
        total_table_cells = 0
        table_chars = 0
        
        for i, table in enumerate(doc.tables[:10]):  # 分析前10个表格
            rows = len(table.rows)
            cols = len(table.columns) if table.rows else 0
            cells = rows * cols
            total_table_cells += cells
            
            print(f"  表格 {i+1}: {rows}行 x {cols}列 = {cells}个单元格")
            
            # 统计表格内容
            for row in table.rows:
                for cell in row.cells:
                    table_chars += len(cell.text)
        
        print(f"   前10个表格总单元格: {total_table_cells}")
        print(f"   前10个表格总字符: {table_chars}")
        
        # 估算总内容量
        if len(doc.tables) > 10:
            estimated_table_chars = table_chars * (len(doc.tables) / 10)
        else:
            estimated_table_chars = table_chars
        
        total_estimated_chars = total_chars + estimated_table_chars
        print(f"\n📊 估算总内容:")
        print(f"   段落字符: {total_chars}")
        print(f"   表格字符: {estimated_table_chars:.0f}")
        print(f"   总字符数: {total_estimated_chars:.0f}")
        print(f"   预期文件大小: {total_estimated_chars/1024:.1f} KB (纯文本)")
        
        # 寻找主要章节
        print(f"\n📋 主要章节识别:")
        chapters = []
        
        for i, paragraph in enumerate(doc.paragraphs):
            text = paragraph.text.strip()
            if not text:
                continue
            
            # 更宽松的章节识别
            is_chapter = False
            
            # 模式1: 明确的章节标记
            if re.match(r'^(第[一二三四五六七八九十\d]+章|第[一二三四五六七八九十\d]+节|\d+\.|一、|二、|三、|四、|五、|六、|七、|八、|九、|十、)', text):
                is_chapter = True
            
            # 模式2: 标题样式
            elif paragraph.style.name.startswith('Heading'):
                is_chapter = True
            
            # 模式3: 短文本且包含关键词
            elif len(text) < 50 and any(keyword in text for keyword in 
                ['基础', '架构', '系统', '管理', '配置', '接口', '服务', '客户端', '服务器', '网络', '安全', '优化']):
                is_chapter = True
            
            # 模式4: 全大写标题
            elif text.isupper() and len(text) < 30:
                is_chapter = True
            
            if is_chapter:
                chapters.append({
                    'index': i,
                    'text': text,
                    'style': paragraph.style.name
                })
        
        print(f"发现 {len(chapters)} 个潜在章节:")
        for i, chapter in enumerate(chapters[:20]):  # 显示前20个
            print(f"  {i+1:2d}. [{chapter['index']:4d}] {chapter['text'][:60]}...")
        
        if len(chapters) > 20:
            print(f"  ... 还有 {len(chapters)-20} 个章节")
        
        return doc, chapters
        
    except Exception as e:
        print(f"❌ 分析失败: {str(e)}")
        return None, None

def estimate_content_distribution(doc, chapters):
    """估算内容分布"""
    if not chapters:
        return
    
    print(f"\n📊 内容分布估算:")
    
    # 计算章节间的段落数量
    for i, chapter in enumerate(chapters[:10]):  # 分析前10个章节
        start_idx = chapter['index']
        end_idx = chapters[i+1]['index'] if i+1 < len(chapters) else len(doc.paragraphs)
        
        paragraph_count = end_idx - start_idx
        
        # 统计这个章节的字符数
        char_count = 0
        for j in range(start_idx, min(end_idx, len(doc.paragraphs))):
            char_count += len(doc.paragraphs[j].text)
        
        print(f"  章节 {i+1}: {chapter['text'][:40]}...")
        print(f"    段落范围: {start_idx}-{end_idx} ({paragraph_count}个段落)")
        print(f"    字符数: {char_count}")
        print(f"    预估大小: {char_count/1024:.1f} KB")
        print()

if __name__ == "__main__":
    source_file = r"F:\augment\output\docx_files\候鸟浏览器基础框架第七十七版.docx"
    
    print("🔍 大型DOCX文件详细分析工具")
    print(f"📂 文件: {source_file}")
    print("=" * 60)
    
    if not Path(source_file).exists():
        print(f"❌ 文件不存在: {source_file}")
        exit(1)
    
    doc, chapters = analyze_large_docx(source_file)
    
    if doc and chapters:
        estimate_content_distribution(doc, chapters)
        
        print("=" * 60)
        print("🎯 分析完成！")
        print(f"📋 建议切分策略:")
        print(f"   1. 按主要章节切分 ({len(chapters)} 个章节)")
        print(f"   2. 每个文件包含 3-5 个相关章节")
        print(f"   3. 控制每个文件在 1-3MB 之间")
    else:
        print("❌ 分析失败")
