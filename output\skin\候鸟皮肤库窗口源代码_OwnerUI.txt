﻿<?xml version="1.0" encoding="utf-8" standalone="yes" ?>
<Window size="340,692" sizebox="4,4,4,4" caption="0,0,0,80" mininfo="340,692" showshadow="true" roundcorner="9,9,9,9"  shadowsize="7" shadowposition="0,0" shadowcorner="3,3,3,3" shadowcolor="#ff6272A4" shadowdarkness="50" fademode="true">
	<Include source="Default.xml" />
  <VerticalLayout height="692" bkcolor="#FF282A36" bordersize="1" bordercolor="#FF6272A4" borderround="1,1">


    <HorizontalLayout height="37">
      <VerticalLayout name="TitleLeft" width="180" inset="6,4,0,0">

         <HorizontalLayout height="37">
		      <Button name="login" bkimage="icon_menu.png" hotimage="icon_menu_hover.png" pushedimage="icon_menu_click.png" padding="0,2,0,0" width="28" height="28"/>
		      <!--[软件产品名] -->
		      <Label name="username" padding="6,0,0,0" text=""  autocalcwidth="true" textcolor="#FF616161" hottextcolor="ffFF0000" align="left" font="8"></Label>

		     </HorizontalLayout>

		  </VerticalLayout>

		  <VerticalLayout name="Main_Title_Right">
		  	   <control />
		     <HorizontalLayout height="37" >
		      <!--[用户登录名] -->
		      <Label name="phoneuser" visible="false" padding="0,3,0,0" width="18" height="16" bkimage="file='phoneuser.png' dest='0,0,16,16'"></Label>
		      <Label name="loginname" height="25" autocalcwidth="true" minwidth="60" endellipsis="true" align="right" textcolor="ff747474" padding="0,0,0,0"/>
		      <Button name="toposi" width="28" height="26"  tooltip="置顶" align="right" usertooltipbkcolor="ffdce1e7" normalimage="toposi.png" hotimage="toposi_hover.png" pushedimage="toposipush.png" />
		      <Button name="minbtn" width="28" height="26"   tooltip="最小化" align="right" normalimage="minbtn.png" hotimage="minbtn_hover.png" pushedimage="minbtnpush.png" />
		      <Button name="Closebtn" width="28" height="26" tooltip="关闭" align="right" normalimage="closebtn.png" hotimage="closebtn_hover.png" pushedimage="closebtnpush.png" />
		     </HorizontalLayout>
		  </VerticalLayout>

    </HorizontalLayout>











  <HorizontalLayout name="bkground">
    <VerticalLayout width="50" bkcolor="ff2f2f2f" visible="false">
			<Button name="face_logo" padding="11,11,0,0" normalimage="face.png" width="26" height="26"/>

			<Option name="chat" padding="11,38,0,0" width="24" height="20" selected="true" bindtablayoutname="tabs" bindtabindex="0" bkimage="chat_normal.png" hotimage="chat_hot.png" selectedimage="chat_pushed.png" group="tab1"/>
			<Button name="Msg_Tip_Main" float="true" pos="26,68,0,0" width="16" height="16" bkimage="message.png" textcolor="ffffffff" text="1" textpadding="0,0,0,2" visible="false"/>
			<Option name="contact" padding="11,39,0,0" width="24" height="19" bindtablayoutname="tabs" bindtabindex="1" bkimage="contact_normal.png" hotimage="contact_hot.png" selectedimage="contact_pushed.png" group="tab1"/>

			<Control />
			<Button name="setting" padding="17,0,20,0" width="15" height="15" normalimage="setting_normal.png" hotimage="setting_hot.png" pushedimage="setting_pushed.png" />
			<Control height="20"/>
		</VerticalLayout>

		<VerticalLayout >

			<!--[7-2号需要程序配合之处] -->
			<HorizontalLayout height="40">

				<!--说明：点击搜索框，搜索框背景切换，同时X按钮显示出来 visible由false变为true -->
				<!-- 此行为原生态 <RichEdit name="search_edit" padding="10,10,0,10" height="25" width="235" borderround="3,3" bkcolor="#ffdce1e7" font="0" textpadding="30,5,20,0" tipvalue="搜索" tipvaluecolor="ff333333" multiline="false" textcolor="ff666666" rich="false">-->
					<RichEdit name="search_edit" padding="10,10,0,10" height="25" borderround="5,5" killfocusimage="" focusimage="file='common\Seach_BG.png' corner='4,10,26,10'" inset="3,1,3,1" bkcolor="#ffdce1e7" font="0" textpadding="27,4,20,0" tipvalue="搜索" tipvaluecolor="ff666666" multiline="false" textcolor="ff666666" rich="false">
					<Control height="10"/>
					<Button name="btnCancelSearch" padding="0,2,2,0" visible="false" width="18" height="18" bkcolor="#FFFFFFFF"	normalimage="file='common\SearchBox_clear.png' dest='5,5,14,14'" hotimage="file='common\SearchBox_clear_hover.png' dest='5,5,14,14'" pushedimage="file='common\SearchBox_clear_active.png' dest='5,5,14,14'"/>
				</RichEdit>
				<Button float="true" pos="18,17,0,0" width="12" height="13" normalimage="search.png" mouse="false"/>
        <Control height="10" width="1"/>
				<Button name="contact" tooltip="排序" padding="2,10,0,0" height="25" width="25"  normalimage="file='sort.png' source='0,0,25,25'" hotimage="file='sort.png' source='25,0,50,25'" pushedimage="file='sort.png' source='25,0,50,25'"/>
				<!--<Button name="refresh" tooltip="刷新" padding="0,10,0,0" height="25" width="25"  normalimage="file='refresh.png' source='0,0,25,25'" hotimage="file='refresh.png' source='25,0,50,25'" pushedimage="file='refresh.png' source='25,0,50,25'"/>-->
        <Button name="order1" tooltip="续费" padding="2,10,0,0" height="25" width="25"  normalimage="file='order1.png' source='0,0,25,25'" hotimage="file='order1.png' source='25,0,50,25'" pushedimage="file='order1.png' source='50,0,75,25'"/>
        <Button name="workorder" tooltip="工单" padding="2,10,0,0" height="25" width="25"  normalimage="file='workorder.png' source='0,0,25,25'" hotimage="file='workorder.png' source='25,0,50,25'" pushedimage="file='workorder.png' source='50,0,75,25'"/>
        <Control height="10" width="6"/>
      </HorizontalLayout>

			<HorizontalLayout height="28">
				<VerticalLayout width="72">
           <Button name="sync_head" padding="12,2,0,0" text="设置" height="20" width="59" textpadding="21,1,0,0" textcolor="#FF747474" hottextcolor="#ff3d3d3d" normalimage="file='sync.png'" hotimage="file='sync_hover.png'" align="left" font="5" />
          </VerticalLayout>
				<VerticalLayout width="72">
           <Button name="group_head" padding="12,2,0,0" text="分组" height="20" width="59" textpadding="21,1,0,0" textcolor="#FF747474" hottextcolor="#ff3d3d3d" normalimage="file='cfgroup.png'" hotimage="file='cfgroup_hover.png'" align="left" font="5" />
          </VerticalLayout>


        <VerticalLayout width="200">
           <Button name="confignum_head" padding="12,2,0,0" text="" height="20" width="248" textpadding="22,1,0,0" textcolor="#FF747474" hottextcolor="#ff3d3d3d" normalimage="file='session_btn.png'" hotimage="file='session_btn_hover.png'" align="left" font="5" />
          </VerticalLayout>
        <VerticalLayout width="60" visible="false">
             <Label name="confignum" padding="0,-2,0,0" text="" width="60" textcolor="#FF505254" hottextcolor="ffFF0000" align="left" font="5"></Label>
        </VerticalLayout>

			</HorizontalLayout>

			<TabLayout name="tabs" selectedid="0" inset="0,0,0,0">
				<HorizontalLayout>
					<FriendList name="friends" scrollwheel="true" header="hidden" itemshowhtml="true" itemhotbkcolor="ffedf1f4" itemselectedbkcolor="ffe4e9ed" itemselectedimage="file='selectedbar.png' dest='0,0,300,78'" vscrollbar="true" floatscrollbar="true" scrollfbarFade="128"/>
				</HorizontalLayout>

				<HorizontalLayout>
          <VerticalLayout>
            <HorizontalLayout name="chkcontactstypearea" height="38" bkcolor="#fff5f7f8">
              <Combo name="contactsdate" padding="10,0,0,0" texttooltip="true" endellipsis="true" itemtextpadding="10,0,0,0"  itemselectedtextcolor="#ff61676c" itemtextcolor="#ff61676c" bkimage="combo_normal_seclist_list.png" hotimage="combo_hot_seclist_list.png" pushedimage="combo_push_seclist_list.png" width="59" height="25"  itemhotbkcolor="#fff1f1f1" itemselectedbkcolor="#ffffffff">
                <!--<ListLabelElement name="sec_datelist1" text="今天"/>
                <ListLabelElement name="sec_datelist2" text="本周" />
                <ListLabelElement name="sec_datelist3" text="本月" />
                <ListLabelElement name="sec_datelist4" text="所有" />-->
              </Combo>
              <Combo name="contactstype" padding="10,0,0,0" itemtextpadding="10,0,0,0" dropboxsize="152,300" itemselectedtextcolor="#ff61676c" maxheight="800" itemtextcolor="#ff61676c" bkimage="combo_normal_seclist_list_large.png" hotimage="combo_hot_seclist_list_large.png" pushedimage="combo_push_seclist_list_large.png" width="152" height="25"  itemhotbkcolor="#fff1f1f1" itemselectedbkcolor="#ffffffff">
                            <!--<ListLabelElement name="sec_typelist1" text=" 活跃的环境.."/>
                            <ListLabelElement name="sec_typelist2" text=" 正在运行的环境.." />
                            <ListLabelElement name="sec_typelist3" text=" 新创建的环境.." />
                            <ListLabelElement name="sec_typelist4" text=" 我接收的环境.." />
                            <ListLabelElement name="sec_typelist5" text=" 我分享的环境.." />
                            <ListLabelElement name="sec_typelist6" text=" 有代理的环境.."/>
                            <ListLabelElement name="sec_typelist7" text=" 有帐户的环境.." />
                            <ListLabelElement name="sec_typelist8" text=" 有插件的环境.." />
                            <ListLabelElement name="sec_typelist9" text=" 有自动化脚本的环境.." />-->
                         </Combo>
              <CheckBox name="chkcontactstype" text="" align="left" padding="10,5,0,0" textpadding="18,1,10,0" texttooltip="true" endellipsis="true" selected="true"  textcolor="#ff61676c" visible="true" height="18" autocalcwidth="true" maxwidth="136" normalimage="file='list_check_normal.png' dest='0,0,16,18'"    hotimage="file='list_check_hover.png' dest='0,0,16,18'" selectedimage="file='list_checked_normal.png' dest='0,0,16,18'" ></CheckBox>
            </HorizontalLayout>
            <ContactList name="contacts" header="hidden" itemshowhtml="true" itemhotbkcolor="ffe4e9ef" itemselectedbkcolor="fff2f3f5" itemselectedimage="file='selectedbar.png' dest='0,0,300,75'" vscrollbar="true" floatscrollbar="true" scrollfbarFade="128"/>
          </VerticalLayout>
				</HorizontalLayout>
			</TabLayout>
		</VerticalLayout>


	</HorizontalLayout>


	  <HorizontalLayout height="25">
    </HorizontalLayout>

    <HorizontalLayout height="21">
	      <VerticalLayout width="180">
	      	<Control />
	          <Button name="configbtn" padding="6,0,0,0" height="21" text="新建环境配置" font="8" texttooltip="true" endellipsis="true" textcolor="#FF3c4043" hottextcolor="#FF323232" width="220" textpadding="26,0,46,0" align="left" normalimage="file='AddSessionbtn.png'" hotimage="file='AddSessionbtn_hover.png'"/>
	          <Control />
	      </VerticalLayout>
	      <Control />
	      <VerticalLayout width="90">
	        <Control />
	        <Button name="order" text="套餐续费" font="0" textpadding="4,0,6,0" tooltip="" align="center" endellipsis="true" textcolor="#FF3c4043" hottextcolor="#FFFFFFFF" width="80" height="18" bkcolor="#ffdddddd"  hotbkcolor="#ff519cff" bordersize="0" borderround="5,5"/>
	        <Control />
	      </VerticalLayout>
	      <VerticalLayout width="28">
	      	<Control />
	       <Button name="config" padding="0,-1,0,0" height="16" width="16" normalimage="file='config.png' source='0,0,16,16'" hotimage="file='config.png' source='16,0,32,16'"/>
	          <Control />
	      </VerticalLayout>
     </HorizontalLayout>

    <HorizontalLayout height="6">
    </HorizontalLayout>


  </VerticalLayout>
</Window>