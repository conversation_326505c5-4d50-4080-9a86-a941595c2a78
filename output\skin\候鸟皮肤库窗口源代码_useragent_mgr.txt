﻿<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Window size="1144,590" sizebox="4,4,4,4" mininfo="1144,590" caption="0,0,0,50" roundcorner="5,5,5,5" showshadow="true" shadowsize="3" shadowposition="0,0" shadowcolor="#ff6272A4" fademode="true">
	<Include source="Default.xml" />

  <VerticalLayout bkcolor="#FF282A36">
    <HorizontalLayout height="40">

    	<Control bkimage="icon.png" padding="8,6,0,0" width="28" height="28"/>
      <Button name="title" padding="6,10,0,10" text="UserAgent 管理器 (候鸟商业UA库)" autocalcwidth="true" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" pushedtextcolor="#FFBD93F9" align="left" font="8"></Button>
      <Label name="bl" width="1" padding="8,3,0,0" bkimage="file='split.png'"></Label>
      <Button name="cust_ua_db_tab" padding="8,11,0,0" text="我的本地UA库" autocalcwidth="true" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" pushedtextcolor="#FFBD93F9" align="left" font="8"></Button>
      <Button name="business_ua_db_tab" text="" width="0" visible="false"></Button>
      <Control />
      <Button name="minbtn" width="28" height="26" tooltip="最小化" normalimage="minbtn.png" hotimage="minbtn_hover.png" pushedimage="minbtnpush.png" />
      <Button name="closewnd" width="28" height="26"  tooltip="关闭" normalimage="closebtn.png" hotimage="closebtn_hover.png" pushedimage="closebtnpush.png" />

    </HorizontalLayout>


    <HorizontalLayout name="loading_data" maxheight="2000" bkcolor="#ffe9e9e9" visible="true">

      <VerticalLayout height="520">

        <HorizontalLayout height="340" >
          <Control />
          <GifAnim name="data_loading" bkimage="dataloading.gif" height="200" width="200" padding="0,40,0,0" auto="true"/>
          <Control />
        </HorizontalLayout>


        <HorizontalLayout height="30" >
          <Control />
          <Label name="data_percent" text="0%" width="300" textcolor="#FF616161" hottextcolor="#ff000000" align="center" font="10"></Label>
          <Control />
        </HorizontalLayout>

        <HorizontalLayout height="60" >
          <Control />
          <Label name="process_description" text="UserAgent数据包 正在更新中..  请稍侯.." width="1144" textcolor="#FF616161" hottextcolor="#ff000000" align="center" font="8"></Label>
          <Control />
        </HorizontalLayout>

        <HorizontalLayout height="40" >
        </HorizontalLayout>

        <HorizontalLayout name="backarea" height="60" visible="false">
          <Control />
          <Button text="返回" name="back" width="120" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	normalimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
          <Control width="100" />
          <Button text="退出" name="closewnd1" width="120" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	normalimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
          <Control />
        </HorizontalLayout>

      </VerticalLayout>

    </HorizontalLayout>

    <HorizontalLayout name="data" visible="false">
    <VerticalLayout>


			<HorizontalLayout height="48" padding="22,0,0,0">
			    	 <VerticalLayout width="1062">
               <Combo name="searchlist" reselect="true" dropboxsize="0,450" bordersize="0" padding="31,10,0,10" width="840" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="4,2,0,0" bkcolor="#ffdce1e7"
														normalimage="file='Profile\Setting_Combox_Normal_big_large.png' corner='5,5,30,5'" hotimage="file='Profile\Setting_Combox_Hover_large.png' corner='5,5,30,5'" pushedimage="file='Profile\Setting_Combox_Click_large.png' corner='5,5,30,5'"
												combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,30,5'"
												itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0">
               </Combo>
				       <RichEdit name="session_search" pos="30,10,0,10" height="36" width="798" tipvaluecolor="#FF6272A4" borderround="10,10" bkcolor="#FF21222C" font="8" textpadding="10,8,40,0" tipvalue="查找 User Agent.." maxchar="600" multiline="false" textcolor="#FFF8F8F2" rich="false" transparent="false" float="true">
				      </RichEdit>
				     </VerticalLayout>

			</HorizontalLayout>

      <TabLayout name="default_bk" selectedid="0">
        <VerticalLayout inset="0,0,0,0">

		<HorizontalLayout height="36" padding="52,10,0,0">

			    	 <VerticalLayout width="120">
				       <Combo name="system" dropboxsize="0,260" textpadding="0,0,0,0" bordersize="1" width="120" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemtextpadding="10,0,0,0" itemalign="left" itemfont="0" bkcolor="#ffdce1e7"
										itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" normalimage="file='Profile\Proxy_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\proxy_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\proxy_Combox_Click.png' corner='5,5,25,10'"
										combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'">
											<ListLabelElement height="36" textpadding="10,0,0,0" text="Windows" font="0">
												<Label name="textLab" textpadding="10,0,0,0" text="Windows"  height="36" width="120" textcolor="#FF000000"/>
											</ListLabelElement>
											<ListLabelElement height="36" text="Iphone" font="0">
                       <Label name="textLab" text="Iphone" pos="0,0,0,0" height="36" width="120" textcolor="#FF000000"/>
                     </ListLabelElement>
                     <ListLabelElement height="36" text="Android" font="0">
                       <Label name="textLab" text="Android" pos="0,0,0,0" height="36" width="120" textcolor="#FF000000"/>
                     </ListLabelElement>
											<ListLabelElement height="36"  text="iPad" font="0">
												<Label name="textLab" text="iPad" height="36" width="120" textcolor="#FF000000"/>
											</ListLabelElement>
											<ListLabelElement height="36"  text="MacOS" font="0">
												<Label name="textLab" text="MacOS" height="36" width="120" textcolor="#FF000000"/>
											</ListLabelElement>
                     <ListLabelElement height="36"  text="Linux" font="0">
                       <Label name="textLab" text="Linux" height="36" width="120" textcolor="#FF000000"/>
                     </ListLabelElement>
               </Combo>
             </VerticalLayout>



            <VerticalLayout name="systemverarea" visible="false" width="110">
				       <Combo name="systemver" vdropboxsize="0,600" bordersize="0" padding="10,0,0,10" width="100" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="0,0,0,0" bkcolor="#ffdce1e7"
										normalimage="file='Profile\Proxy_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\proxy_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\proxy_Combox_Click.png' corner='5,5,25,10'"
										combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
										itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0" >

                 </Combo>
           </VerticalLayout>



            <VerticalLayout width="130">
				       <Combo name="render" dropboxsize="0,600" bordersize="0" padding="10,0,0,10" width="120" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="0,0,0,0" bkcolor="#ffdce1e7"
										normalimage="file='Profile\Proxy_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\proxy_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\proxy_Combox_Click.png' corner='5,5,25,10'"
										combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
										itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0" >

				              <ListLabelElement height="36" pos="66,0,0,0" textpadding="10,0,0,0" text="Chrome" font="0">
												<Label name="textLab" pos="66,0,0,0" textpadding="10,0,0,0" text="Chrome"  height="36" width="120" textcolor="#FF000000"/>
											</ListLabelElement>
											<ListLabelElement height="26" pos="66,0,0,0" text="Safari" font="0">
												<Label name="textLab" float="true" pos="66,0,0,0" text="Safari" pos="0,0,0,0" height="36" width="120" textcolor="#FF000000"/>
											</ListLabelElement>
											<ListLabelElement height="26" pos="66,0,0,0" text="AppleWebKit" font="0">
												<Label name="textLab" float="true" pos="66,0,0,0" text="AppleWebKit" pos="0,0,0,0" height="36" width="120" textcolor="#FF000000"/>
											</ListLabelElement>
                 </Combo>
           </VerticalLayout>



            <VerticalLayout name="renderverarea" visible="false" width="110">
				       <Combo name="renderver" dropboxsize="0,600" bordersize="0" padding="10,0,0,10" width="100" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="0,0,0,0" bkcolor="#ffdce1e7"
										normalimage="file='Profile\Proxy_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\proxy_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\proxy_Combox_Click.png' corner='5,5,25,10'"
										combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
										itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0" >

				              <ListLabelElement height="36" pos="66,0,0,0" textpadding="10,0,0,0" text="97" font="0">
												<Label name="textLab" pos="66,0,0,0" textpadding="10,0,0,0" text="Windows"  height="36" width="100" textcolor="#FF000000"/>
											</ListLabelElement>
											<ListLabelElement height="26" pos="66,0,0,0" text="86" font="0">
												<Label name="textLab" float="true" pos="66,0,0,0" text="86" pos="0,0,0,0" height="36" width="100" textcolor="#FF000000"/>
											</ListLabelElement>
                      <ListLabelElement height="26" pos="66,0,0,0" text="99" font="0">
                       <Label name="textLab" float="true" pos="99" text="Iphone" pos="0,0,0,0" height="36" width="100" textcolor="#FF000000"/>
                     </ListLabelElement>
                 </Combo>
           </VerticalLayout>



           <VerticalLayout name="versionarea" visible="false" width="130">
				       <Combo name="version" dropboxsize="0,600" bordersize="0" padding="10,0,0,10" width="120" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="0,0,0,0" bkcolor="#ffdce1e7"
										normalimage="file='Profile\Proxy_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\proxy_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\proxy_Combox_Click.png' corner='5,5,25,10'"
										combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
										itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0" >

                 </Combo>
           </VerticalLayout>



           <VerticalLayout width="170">
				       <Combo name="kernel" dropboxsize="0,600" bordersize="0" padding="10,0,0,10" width="160" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="0,0,0,0" bkcolor="#ffdce1e7"
										normalimage="file='Profile\Proxy_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\proxy_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\proxy_Combox_Click.png' corner='5,5,25,10'"
										combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
										itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0" >

				              <ListLabelElement height="36" pos="66,0,0,0" textpadding="10,0,0,0" text="Microsoft Edge" font="0">
												<Label name="textLab" pos="66,0,0,0" textpadding="10,0,0,0" text="Microsoft Edge"  height="36" width="160" textcolor="#FF000000"/>
											</ListLabelElement>
											<ListLabelElement height="26" pos="66,0,0,0" text="Safari" font="0">
												<Label name="textLab" float="true" pos="66,0,0,0" text="Safari" pos="0,0,0,0" height="36" width="160" textcolor="#FF000000"/>
											</ListLabelElement>
                      <ListLabelElement height="26" pos="66,0,0,0" text="Lbbrowser" font="0">
                       <Label name="textLab" float="true" pos="66,0,0,0" text="Lbbrowser" pos="0,0,0,0" height="36" width="160" textcolor="#FF000000"/>
                     </ListLabelElement>
                     <ListLabelElement height="26" pos="66,0,0,0" text="XiaoMi_MiuiBrowser" font="0">
                       <Label name="textLab" float="true" pos="66,0,0,0" text="XiaoMi_MiuiBrowser" pos="0,0,0,0" height="36" width="160" textcolor="#FF000000"/>
                     </ListLabelElement>
                     <ListLabelElement height="26" pos="66,0,0,0" text="Quark" font="0">
                       <Label name="textLab" float="true" pos="66,0,0,0" text="Quark" pos="0,0,0,0" height="36" width="160" textcolor="#FF000000"/>
                     </ListLabelElement>
                     <ListLabelElement height="26" pos="66,0,0,0" text="HeytapBrowser" font="0">
                       <Label name="textLab" float="true" pos="66,0,0,0" text="HeytapBrowser" pos="0,0,0,0" height="36" width="160" textcolor="#FF000000"/>
                     </ListLabelElement>
                     <ListLabelElement height="26" pos="66,0,0,0" text="Vivobrowser" font="0">
                       <Label name="textLab" float="true" pos="66,0,0,0" text="Vivobrowser" pos="0,0,0,0" height="36" width="160" textcolor="#FF000000"/>
                     </ListLabelElement>
                     <ListLabelElement height="26" pos="66,0,0,0" text="Slbrowser" font="0">
                       <Label name="textLab" float="true" pos="66,0,0,0" text="Slbrowser" pos="0,0,0,0" height="36" width="160" textcolor="#FF000000"/>
                     </ListLabelElement>
                     <ListLabelElement height="26" pos="66,0,0,0" text="Micromessenger" font="0">
                       <Label name="textLab" float="true" pos="66,0,0,0" text="Micromessenger" pos="0,0,0,0" height="36" width="160" textcolor="#FF000000"/>
                     </ListLabelElement>
                     <ListLabelElement height="26" pos="66,0,0,0" text="Maxthon" font="0">
                       <Label name="textLab" float="true" pos="66,0,0,0" text="Maxthon" pos="0,0,0,0" height="36" width="160" textcolor="#FF000000"/>
                     </ListLabelElement>
                     <ListLabelElement height="26" pos="66,0,0,0" text="Baiduboxapp" font="0">
                       <Label name="textLab" float="true" pos="66,0,0,0" text="Baiduboxapp" pos="0,0,0,0" height="36" width="160" textcolor="#FF000000"/>
                     </ListLabelElement>
                     <ListLabelElement height="26" pos="66,0,0,0" text="UCbrowser" font="0">
                       <Label name="textLab" float="true" pos="66,0,0,0" text="UCbrowser" pos="0,0,0,0" height="36" width="160" textcolor="#FF000000"/>
                     </ListLabelElement>
                     <ListLabelElement height="26" pos="66,0,0,0" text="HuaweiBrowser" font="0">
                       <Label name="textLab" float="true" pos="66,0,0,0" text="HuaweiBrowser" pos="0,0,0,0" height="36" width="160" textcolor="#FF000000"/>
                     </ListLabelElement>
                 </Combo>
           </VerticalLayout>


            <VerticalLayout name="kernelverarea" visible="false" width="130">
				       <Combo name="kernelver" dropboxsize="0,600" bordersize="0" padding="10,0,0,10" width="120" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="0,0,0,0" bkcolor="#ffdce1e7"
										normalimage="file='Profile\Proxy_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\proxy_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\proxy_Combox_Click.png' corner='5,5,25,10'"
										combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
										itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0" >

                 </Combo>
           </VerticalLayout>



           <VerticalLayout width="140">
				       <Combo name="reso" visible="false" dropboxsize="0,600" bordersize="0" padding="10,0,0,10" width="130" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="0,0,0,0" bkcolor="#ffdce1e7"
										normalimage="file='Profile\Proxy_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\proxy_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\proxy_Combox_Click.png' corner='5,5,25,10'"
										combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
										itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0" >

				              <ListLabelElement height="36" pos="66,0,0,0" textpadding="10,0,0,0" text="1024x768" font="0">
												<Label name="textLab" pos="66,0,0,0" textpadding="10,0,0,0" text="1024x768"  height="36" width="130" textcolor="#FF000000"/>
											</ListLabelElement>
											<ListLabelElement height="26" pos="66,0,0,0" text="800x600" font="0">
												<Label name="textLab" float="true" pos="66,0,0,0" text="800x600" pos="0,0,0,0" height="36" width="130" textcolor="#FF000000"/>
											</ListLabelElement>
                      <ListLabelElement height="26" pos="66,0,0,0" text="1000x1200" font="0">
                       <Label name="textLab" float="true" pos="66,0,0,0" text="1000x1200" pos="0,0,0,0" height="36" width="130" textcolor="#FF000000"/>
                     </ListLabelElement>
                 </Combo>
           </VerticalLayout>

			</HorizontalLayout>
		<HorizontalLayout height="12" padding="0,0,0,0">
		</HorizontalLayout>


          <HorizontalLayout>
        	<List name="list_business" vscrollbar="true" minheight="403" bordersize="1,1,1,1" itembkcolor="#FFffffff" itemselectedbkcolor="#FFe0e0e0" itemhotbkcolor="#FFf2f2f2" bordercolor="#FFD9DADB">
						<ListHeader height="36" bordersize="1" bordercolor="#FFD7D7D7" bkcolor="#FFF9F9FA">
              <ListHeaderItem text="ope" name="header_device_choice" width="50" align="left" textpadding="10,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <!--<ListHeaderItem text="Session" name="header_device_choice" width="200" align="left" textpadding="20,0,0,0"></ListHeaderItem>-->
              <ListHeaderItem text="UserAgent" name="UserAgent" align="left" textpadding="12,0,0,0"></ListHeaderItem>

						</ListHeader>

					</List>
          </HorizontalLayout>

          <HorizontalLayout height="52" bkcolor="#ffe9e9e9">
            <CheckBox name="opt_checkAll1" text="" textpadding="67,1,0,0" selected="false"  visible="true" padding="12,16,0,0" height="18" width="16" normalimage="file='list_check_normal.png'" hotimage="list_check_hover.png" selectedimage="file='list_checked_actvice_normal.png'" ></CheckBox>

            <Control />

            <!--<VerticalLayout width="140">
              <Control />
              <Button text="删除" name="delete"  padding="20,2,0,0" width="120" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	bkimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
              <Control />
            </VerticalLayout>

            <VerticalLayout width="160">
              <Control />
              <Button text="批量导入" name="inputfile"  padding="20,2,0,0" width="140" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	bkimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
              <Control />
            </VerticalLayout>-->
          <VerticalLayout width="100">
              <Control />
              <Button text="首页" name="firstpage" padding="10,2,0,0" width="80" height="24" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="4"	bkimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
              <Control />
            </VerticalLayout>
            <VerticalLayout width="100">
              <Control />
              <Button text="<<<" name="prevpage" padding="10,2,0,0" width="80" height="24" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="4"	bkimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
              <Control />
            </VerticalLayout>
            <VerticalLayout width="100">
              <Control />
              <Button text=">>>" name="nextpage" padding="10,2,0,0" width="80" height="24" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="4"	bkimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
              <Control />
            </VerticalLayout>
            <VerticalLayout width="100">
              <Control />
              <Button text="末页" name="lastpage" padding="10,2,0,0" width="80" height="24" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="4"	bkimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
              <Control />
            </VerticalLayout>

            <VerticalLayout width="140">

              <HorizontalLayout>
                <Label name="pageinfo" text="" textcolor="#FF616161" hottextcolor="#ff000000" align="center" font="4"></Label>
              </HorizontalLayout>

            </VerticalLayout>

            <VerticalLayout visible="false" name="inputarea2" width="220">
              <Control />
                <Button text="添加到当前环境" enabled="false" name="input2" textcolor="#FF3E3E3E" hottextcolor="#ff000000" padding="20,0,0,0" width="200" height="30" disabledtextcolor="#FFB3B3B3" bordersize="1" bordercolor="#ffb3b3b3" borderround="7,7" texttooltip="true" endellipsis="true" align="center" font="14"	bkcolor="#FFfffae0" hotbkcolor="#fffff6c8"/>
              <Control />
            </VerticalLayout>

            <VerticalLayout width="240">
              <Control />
              <Button text="添加到我的自有UA库" name="addto" textcolor="#FF3E3E3E" hottextcolor="#ff000000" padding="20,0,0,0" width="220" height="30" disabledtextcolor="#FFB3B3B3" bordersize="1" bordercolor="#ffb3b3b3" borderround="7,7" texttooltip="true" endellipsis="true" align="center" font="14"	bkcolor="#FFffffff" hotbkcolor="#fff1f2f1"/>
              <Control />
            </VerticalLayout>
            <VerticalLayout width="40">

            </VerticalLayout>

          </HorizontalLayout>

				</VerticalLayout>

        <VerticalLayout inset="0,0,0,0">

          <HorizontalLayout height="56" padding="22,0,0,0">
            <VerticalLayout >
              <Edit name="add_new_useragent" padding="30,10,0,10" height="36" tipvaluecolor="#FF333333" nativebkcolor="#FFDCE1E7" borderround="10,10" bkcolor="#ffdce1e7" font="8" textpadding="10,0,20,0" tipvalue="添加一个新的 User-Agent.." maxchar="6000" multiline="false" textcolor="#ff333333" rich="false" transparent="false" />
            </VerticalLayout>
            <VerticalLayout width="160">
              <Button text="添加" name="add_useragent"  float="true" pos="20,13,0,0" width="80" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	bordersize="1" borderround="10,10" bordercolor="#ffbebebe" />
            </VerticalLayout>
          </HorizontalLayout>

          <HorizontalLayout>
            <List name="list_session_manager" vscrollbar="true" minheight="403" bordersize="1,1,1,1" itembkcolor="#FFffffff" itemselectedbkcolor="#FFe0e0e0" itemhotbkcolor="#FFf2f2f2" bordercolor="#FFD9DADB">
            <ListHeader height="36" bordersize="1" bordercolor="#FFD7D7D7" bkcolor="#FFF9F9FA">
              <ListHeaderItem text="ope" name="header_device_choice" width="50" align="left" textpadding="10,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <!--<ListHeaderItem text="Session" name="header_device_choice" width="200" align="left" textpadding="20,0,0,0"></ListHeaderItem>-->
              <ListHeaderItem text="UserAgent" name="UserAgent" normalimage="file='headersort_small.png' src='0,0,40,40' dest='70,0,110,40'" align="left" textpadding="12,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="" name="header_adddate" width="150" align="left" textpadding="10,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>

            </ListHeader>

          </List>
          </HorizontalLayout>

          <HorizontalLayout height="52" bkcolor="#ffe9e9e9">
            <CheckBox name="opt_checkAll" text="" textpadding="67,1,0,0" selected="false"  visible="true" padding="12,16,0,0" height="18" width="16" normalimage="file='list_check_normal.png'" hotimage="list_check_hover.png" selectedimage="file='list_checked_actvice_normal.png'" ></CheckBox>

            <Control />

            <VerticalLayout width="140">
              <Control />
              <Button text="删除" name="delete"  padding="20,2,0,0" width="120" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	bkimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
              <Control />
            </VerticalLayout>

            <VerticalLayout width="160">
              <Control />
              <Button text="批量导入" name="inputfile"  padding="20,2,0,0" width="140" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	bkimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
              <Control />
            </VerticalLayout>

            <VerticalLayout width="180">
              <Control />
              <Button text="批量导出" name="exportfile"  padding="20,2,0,0" width="160" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	bkimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
              <Control />
            </VerticalLayout>

            <VerticalLayout visible="false" name="inputarea" width="220">
              <Control />
              <Button text="添加到当前环境" enabled="false" name="input" textcolor="#FF3E3E3E" hottextcolor="#ff000000" padding="20,0,0,0" width="200" height="30" texttooltip="true" endellipsis="true" disabledtextcolor="#FFB3B3B3" bordersize="1" bordercolor="#ffb3b3b3" borderround="7,7" align="center" font="14"	bkcolor="#FFfffae0" hotbkcolor="#fffff6c8"/>
              <Control />
            </VerticalLayout>
            <VerticalLayout width="140">
              <Control />
              <Button text="保存" name="save"  padding="20,2,0,0" width="120" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	bkimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
              <Control />
            </VerticalLayout>
            <VerticalLayout width="40">

            </VerticalLayout>

          </HorizontalLayout>

        </VerticalLayout>

      </TabLayout>





		</VerticalLayout>


	</HorizontalLayout>

    <Control name="dragicon" float="true" width="14" height="16" bkimage="dragicon.png"/>
  </VerticalLayout>
</Window>
