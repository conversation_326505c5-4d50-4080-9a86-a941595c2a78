package main

import (
    "bytes"
    "encoding/json"
    "fmt"
    "io"
    "net/http"
    "time"
)

// HouniaoRAGFlowClient 候鸟浏览器专用 RAGFlow 客户端
type HouniaoRAGFlowClient struct {
    BaseURL          string
    APIKey           string
    HouniaoDatasetID string
    Client           *http.Client
}

// NewHouniaoRAGFlowClient 创建候鸟浏览器专用客户端
func NewHouniaoRAGFlowClient() *HouniaoRAGFlowClient {
    return &HouniaoRAGFlowClient{
        BaseURL:          "http://58.49.146.17:9380",
        APIKey:           "ragflow-UzYTlhMjIwNjI0YzExZjA4NzUyMDI0Mm",
        HouniaoDatasetID: "6d7b2882624511f09a0d0242ac130006",
        Client: &http.Client{
            Timeout: 30 * time.Second,
        },
    }
}

// RetrievalRequest 检索请求结构
type RetrievalRequest struct {
    Question   string   `json:"question"`
    DatasetIDs []string `json:"dataset_ids"`
    TopK       int      `json:"top_k"`
}

// RetrievalResponse 检索响应结构
type RetrievalResponse struct {
    Code int `json:"code"`
    Data struct {
        Chunks []struct {
            ContentWithWeight string  `json:"content_with_weight"`
            Similarity        float64 `json:"similarity"`
        } `json:"chunks"`
    } `json:"data"`
    Message string `json:"message"`
}

// QueryHouniaoKnowledgeBase 查询候鸟浏览器知识库
func (c *HouniaoRAGFlowClient) QueryHouniaoKnowledgeBase(question string) (string, error) {
    url := fmt.Sprintf("%s/api/v1/retrieval", c.BaseURL)
    
    reqData := RetrievalRequest{
        Question:   question,
        DatasetIDs: []string{c.HouniaoDatasetID}, // 只使用候鸟数据集
        TopK:       5,
    }
    
    jsonData, err := json.Marshal(reqData)
    if err != nil {
        return "", fmt.Errorf("序列化请求失败: %v", err)
    }
    
    req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
    if err != nil {
        return "", fmt.Errorf("创建请求失败: %v", err)
    }
    
    req.Header.Set("Authorization", "Bearer "+c.APIKey)
    req.Header.Set("Content-Type", "application/json")
    req.Header.Set("Accept", "application/json")
    
    resp, err := c.Client.Do(req)
    if err != nil {
        return "", fmt.Errorf("发送请求失败: %v", err)
    }
    defer resp.Body.Close()
    
    body, err := io.ReadAll(resp.Body)
    if err != nil {
        return "", fmt.Errorf("读取响应失败: %v", err)
    }
    
    if resp.StatusCode != http.StatusOK {
        return "", fmt.Errorf("HTTP错误 %d: %s", resp.StatusCode, string(body))
    }
    
    var result RetrievalResponse
    if err := json.Unmarshal(body, &result); err != nil {
        return "", fmt.Errorf("解析响应失败: %v", err)
    }
    
    if result.Code != 0 {
        return "", fmt.Errorf("查询失败: %s", result.Message)
    }
    
    // 提取最相关的答案
    if len(result.Data.Chunks) > 0 {
        bestMatch := result.Data.Chunks[0]
        return fmt.Sprintf("根据候鸟浏览器知识库查询结果：\n\n%s\n\n(相似度: %.3f)", 
            bestMatch.ContentWithWeight, bestMatch.Similarity), nil
    }
    
    return "未找到相关信息", nil
}

// TestConnection 测试连接
func (c *HouniaoRAGFlowClient) TestConnection() error {
    result, err := c.QueryHouniaoKnowledgeBase("测试连接")
    if err != nil {
        return fmt.Errorf("连接测试失败: %v", err)
    }
    
    fmt.Printf("✅ 候鸟浏览器知识库连接成功\n")
    fmt.Printf("📄 测试查询结果: %s\n", result[:100]+"...")
    return nil
}

// 在 Wing 客户端中的使用示例
func main() {
    client := NewHouniaoRAGFlowClient()
    
    // 测试连接
    if err := client.TestConnection(); err != nil {
        fmt.Printf("❌ 连接失败: %v\n", err)
        return
    }
    
    // 查询候鸟浏览器相关问题
    questions := []string{
        "候鸟浏览器如何配置代理？",
        "候鸟浏览器的功能有哪些？",
        "如何使用候鸟浏览器？",
    }
    
    for _, question := range questions {
        fmt.Printf("\n🔍 查询: %s\n", question)
        
        answer, err := client.QueryHouniaoKnowledgeBase(question)
        if err != nil {
            fmt.Printf("❌ 查询失败: %v\n", err)
            continue
        }
        
        fmt.Printf("🤖 答案: %s\n", answer)
        fmt.Println(strings.Repeat("-", 60))
    }
}