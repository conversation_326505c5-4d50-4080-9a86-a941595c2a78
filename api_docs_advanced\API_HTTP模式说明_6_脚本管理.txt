API文档: HTTP模式说明 - 6、脚本管理
URL: https://www.mbbrowser.com/api/http
抓取时间: 2025-07-28 12:32:55
============================================================

API使用文档-候鸟防关联浏览器•

首页

应用

价格

下载

APInew
使用教程

常见问题

佣金计划

博客中心
登录&注册 简体中文
首页

应用

价格

下载

API

使用教程

佣金计划

博客中心

登录&注册

# API
候鸟浏览器API使用文档

API使用须知简介
• 使用须知
• HTTP模式说明
• 常见问题
• API接口文档1、帐号登录
• 2、获取成员列表
• 3、环境开启/关闭
• 4、环境管理
• 5、分组管理
• 6、脚本管理
• 7、插件管理
• 8、附录（国家码、时区、语言、系统和分辨率）
• 9、错误码对照表
• 候鸟API接口实时调试工具POSTMAN下载及安装
• POSTMAN调试候鸟API接口
• 调试接口JSON数据官方更新、下载
• 多种语言脚本示例
• JSON在线格式化工具

## HTTP模式说明
• http模式仅支持V3.9.2.114以上版本，请下载客户端最新版本【下载候鸟浏览器最新版】

• 操作前，请先从控制台购买API凭证并获取帐号验证所需的信息。具体见【获取APP_ID、APP_KEY】

### 命令行唤起候鸟浏览器
1、以管理员身份运行CMD或者PowerShell，并确保终端在候鸟浏览器主目录打开，或已进入候鸟浏览器主路径

• cmd运行以下语句

`ApiServer.exe --port=8186 --account=XXXXX --app_id=XXXXX --app_key=XXXXX –-retrun=on –-logs=on`

• PowerShell运行以下语句

`.\ApiServer.exe --port=8186 --account=XXXXX --app_id=XXXXX --app_key=XXXXX –retrun=on –logs=on`

2、启动成功过后在命令行工具可以看到API地址

• 参数带入account、app_id、app_key，则在右下角的托盘菜单看见软件图标，标明已登入，可以调用接口运行脚本

• 若未带入account、app_id、app_key参数，可在后续通过登录帐号接口登录客户端。见接口文档。

3、CLI命令行参数介绍

`  --port         [可选]
http连接端口 (default 8186)

--account      [可选]
登录账号

--app_id       [可选]
凭证APP_ID

--app_key      [可选]
凭证APP_KEY

--retrun        [on/off，default:on]
APISERVER.EXE 所有需返回给用户的数据[JSON/其它]，所有状态信息，返回到 CONSOLE 界面。

--logs          [on/off，default:on]
APISERVER.EXE 所有需返回给用户的 JSON 数据，所有状态信息，明文写入 API_LOG 目录【非 LOG 目录】，LOG 文件前缀例：test@qq.com_API_log。

--hide          [on/off，default:off]
2023-06-19 新增，支持客户端有头运行` 复制
### 停止并退出APISERVER
• 在操作系统右下角TRAY区的APISERVER图标上点击右键，唤出菜单，点击退出并完成退出APISERVER动作。也可以通过任务管理器等强制退出。

### 接口说明
• 支持以下接口

帐号登录：启动客户端

获取成员列表：获取主帐号与子帐号数据

环境开启/关闭：打开环境、关闭环境、强制终止环境

环境管理：获取环境列表、查询指定环境的配置数据、创建环境、更新环境高级指纹参数、更新环境、更新环境代理、删除环境、导入Cookie、导出Cookie、获取随机UA、清除环境本地缓存

分组管理：获取环境分组列表、新建环境分组、删除环境分组、转移环境分组

支持邮箱: <EMAIL>
©MBBROWSER @2025

京ICP备 2020047947号

本系统不提供代理IP服务，禁止用户使用本系统进行任何违法犯罪活动，用户使用本系统带来的任何责任由用户自行承担。

MBbrowser.com  All Rights Reserved. 候鸟防关联浏览器对网站内容拥有最终解释权。
工作日客服(微信)
工作日09-18点

夜间/周末客服(微信)

工作日 18-24点，周末全天

商务(微信)

mbbrowser_official

###### 全国咨询服务热线

400-112-6050
在线咨询

微信咨询

电话咨询

售后咨询