标题: 自动化脚本管理器使用
英文标题: Automate Script Manager
ID: 115
分类ID: 7
添加时间: 1673598131
更新时间: 1702348918
访问次数: 0
SEO标题: 候鸟浏览器自动化脚本管理器使用
SEO关键词: 候鸟浏览器自动化脚本管理器使用
SEO描述: 候鸟浏览器自动化脚本管理器使用

================================================== 内容 ==================================================
# 候鸟 自动化脚本管理器 使用说明书
候鸟浏览器自动化脚本管理器支持，Selenium和Puppeteer的自动化脚本、自动化导入脚本包、指定其它环境批量支持相应自动化脚本。支持用户自行对自动化脚本进行强加密模式，加密后的脚本可直接在候鸟的环境中加载高效运行，支持分享给团队帐户在加密状态下使用。自有脚本安全存储在候鸟云端，可对脚本使用自行设置的密码进行加解密。支持环境包含自动化脚本完整导出到本地存储，彻底摒除以往繁杂的逐个环境安装，逐个调试自动化脚本、维护等业务日常管理。

### 候鸟 自动化脚本管理器 使用前述
基于Google chrome浏览器研发的候鸟浏览器专有商用内核，开启全面支持Selenium和Puppeteer自动化脚本。用户可通过直观的自动化脚本管理器窗口，将自动化脚本包批量导入脚本，批量给其它环境指定相应的自动化脚本。候鸟客户端中采用银行级128位算法加密，用户可自行对自动化脚本进行强加密模式，加密后的脚本除了密码设定/拥有者，其它任何人仅可以运行此脚本外，均无法查看脚本内容，无法修改脚本内容，无法解密脚本内容。

### 视频教程
|  自动化脚本管理器使用 |
| ------------ |
| <video controls="" preload style="width:100%"><source src="https://help.mbbrowser.com/video/automated_script_manager.mp4" type="video/mp4"></video> |

### 一、开启并进入 自动化脚本管理器
启动候鸟客户端，点击主面板左上角的功能菜单，选择 自动化脚本管理器 并点击进入

**方式一：**

<p><img src="6d7b2882624511f09a0d0242ac130006/images/image_7c25a9dbd76c.png" width="360" /></p>

**方式二：** 通过在环境上点击右键唤出菜单，点击 自动化脚本管理器 进入

<p><img src="6d7b2882624511f09a0d0242ac130006/images/image_fe68b41356fb.png" width="360" /></p>

**方式一和方式二的区别：** 方式二进入自动化脚本管理器时，默认焦点指向到环境二的脚本管理。

#### 自动化脚本管理器 界面：

![](6d7b2882624511f09a0d0242ac130006/images/image_46996b39f729.png)

功能区域分布图：

![](6d7b2882624511f09a0d0242ac130006/images/image_688f6e043bbb.png)

### 二、自动化脚本管理器界面功能说明
在自动化脚本管理器中，您可以为您的环境一键添加、删除、查找、分配自动化脚本。支持导入导出脚本包。

![](6d7b2882624511f09a0d0242ac130006/images/image_8c1a78de7802.png)

#### 如图示，A区：

#### 下拉菜单项：
【我的自动化脚本】 列出您已经添加或导入过的所有脚本的总集合，即当前您账号中所有已添加的自动化脚本。
【我的Puppeteer自动化脚本】 列出您已经添加或导入过的Puppeteer脚本的总集合。
【我的Selenium自动化脚本】 列出您已经添加或导入过的Selenium脚本的总集合。
【候鸟官方自动化脚本库】 列出了候鸟官方提供的所有自动化脚本库。

#### 请输入关键字查找脚本：
自动化脚本管理器支持您动态实时查找需要的脚本，无须在列表中逐个人工检索，通过查找脚本功能，可快速定位到您需要的脚本上并进行分配。

![](6d7b2882624511f09a0d0242ac130006/images/image_a072891e4dcd.png)

A区列表：操作列：勾选框 在勾选后，即可进行后续操作。 脚本名称：列出脚本名称。 加密：列出当前脚本是否有进行加密处理。 描述：脚本描述，**通过将光标移到描述上可以快速查看描述的完整内容而无须拖动自动化脚本管理器窗体大小。**

#### 如图示，B区：
B区主要功能是列出您的所有环境，并提供已分配过脚本的环境集合，并提供环境搜索功能，方便您定位到需要处理的环境。提供每个环境已分配的脚本数量，让您很直观的进行环境的脚本分配与管理。

![](6d7b2882624511f09a0d0242ac130006/images/image_5b123c76a01a.png)

【已分配脚本的环境】 如果您曾经在某些环境中分配过脚本，则进入自动化脚本管理器，此项默认显示。默认列出已分配脚本的环境集合。
【其它】 列出未分组的环境和已分组的环境分组。
【请输入关键字查找环境】 输入您的会话环境关键字，可快速列出您需要操作的环境。

![](6d7b2882624511f09a0d0242ac130006/images/image_145f12d0ed35.png)

B区列表： 操作列：勾选框 在勾选后，即可进行后续操作。 环境名称：列出您此分组下环境的名称。 创建时间：列出您的环境的创建时间。 脚本数：显示您的环境中已分配的脚本数量。

#### 如图示，C区：
C 区主要功能是列出您的某个环境中已分配的脚本详单，并提供此环境下脚本的管理。默认在未选择环境时，C区插件列表为空。仅在您勾选或点击某个环境时，或您在勾选多个环境时，列出环境的脚本详单。
操作重点： 点击B区环境 和 勾选B区环境之区别：

![](6d7b2882624511f09a0d0242ac130006/images/image_0828b1d455d4.png)

当您 仅点击（选中）B区某个环境，C区只会列出此单个环境下的所有脚本详单。如果您同时点击了B区多个环境，C区也仅会列出最后点击的那单个环境的所有脚本。

![](6d7b2882624511f09a0d0242ac130006/images/image_345ef481eefe.png)

当您 勾选（在操作列勾选了小方框）某个环境，C区会列出已勾选 的此环境所有脚本详单。 如果您同时勾选了B区多个环境，C区会列出这些多个环境中的所有脚本详单。

### 三、新建自动化脚本
第一步：在底部右侧按钮点击【**新建自动化脚本**】。

![](6d7b2882624511f09a0d0242ac130006/images/image_a9574bb8cc35.png)

第二步：在新建自动化脚本窗口配置对应信息
【脚本类型】有Puppeteer和Selenium种类型选择
【脚本语言】脚本类型为Puppeteer，脚本语言为JS，脚本类型为Puppeteer，脚本语言为Python
【脚本名称】根据您的需求自定义脚本名称
【脚本描述】脚本功能详细描述

![](6d7b2882624511f09a0d0242ac130006/images/image_e327b80bb897.png)

第三步：配置完毕后点击创建此脚本会自动弹出脚本编辑窗口，默认会提供一个示例模板，这里可以开始编辑您所需要的脚本逻辑代码。

![](6d7b2882624511f09a0d0242ac130006/images/image_75ae6445ddaf.png)

### 四、分配脚本到各个环境中

![](6d7b2882624511f09a0d0242ac130006/images/image_4cfd666a995d.png)

分配脚本的过程，是从A区到B区再到C区，再点击自动化脚本管理器右下角按钮的过程。
第一步：在A区 选择 **我的自动化脚本** 下拉项。

![](6d7b2882624511f09a0d0242ac130006/images/image_9a322cd7421f.png)

第二步：在A区列表中，找到自已需要的脚本，并勾选此脚本。

![](6d7b2882624511f09a0d0242ac130006/images/image_72ff2eba0cf9.png)

第三步：在B区列表中，勾选要将此脚本配置到的那些环境上。

![](6d7b2882624511f09a0d0242ac130006/images/image_a769364ac527.png)

第四步：点击按钮 将选中的脚本添加到勾选的环境中。

![](6d7b2882624511f09a0d0242ac130006/images/image_c4157730feb3.png)

![](6d7b2882624511f09a0d0242ac130006/images/image_8c3e8162fec0.png)

如上图所示，此时表示此脚本已成功分配到 2个环境中，此时您可以在B区和C区看到 环境一和环境二的插件数由2变为了3。表示已成功安装。同时在C区可以看到环境一和环境二的脚本列表中已出现刚刚分配的脚本。

### 五、修改脚本
可以通过A区脚本列表点击选中脚本然后进行修改

![](6d7b2882624511f09a0d0242ac130006/images/image_fe08697878b3.png)

也可以通过C区针对环境中的脚本进行修改

![](6d7b2882624511f09a0d0242ac130006/images/image_2a112ed8598d.png)

### 六、调试脚本
需要先将脚本分配给指定环境，然后在B区选择指定环境后，在C区脚本列表中选中脚本进行调试运行。

![](6d7b2882624511f09a0d0242ac130006/images/image_9dac6191d28a.png)

也可以在**编辑脚本**的编辑器中修改脚本后，在工具菜单点运行（或直接按F5）保存并查看执行效果

### 七、激活脚本
需要先将脚本分配给指定环境，然后在B区选择指定环境后，在C区脚本列表中勾选指定脚本进行脚本激活。

![](6d7b2882624511f09a0d0242ac130006/images/image_13f7b5a23097.png)

![](6d7b2882624511f09a0d0242ac130006/images/image_cc432729f985.png)

如上图所示， 此时表示脚本已成功在指定环境中激活成功。

### 八、加解密指定脚本
支持用户自行对自动化脚本进行强加密模式，加密后的脚本可直接在候鸟的环境中加载高效运行。

#### 加密操作步骤：
第一步：在A区选中指定脚本然后右键选择加密脚本

![](6d7b2882624511f09a0d0242ac130006/images/image_f0164f0f0e77.png)

第二步：在加密脚本窗口输入加密密码，然后点击加密脚本或完成并退出，即可完成脚本加密

![](6d7b2882624511f09a0d0242ac130006/images/image_0429d3efbc8a.png)

加密完成后可以在A区列表查看到脚本加密那行列显示为是

![](6d7b2882624511f09a0d0242ac130006/images/image_47cd88287bef.png)

加密脚本在候鸟客户端中采用银行级128位算法加密，加密脚本的密码由您自行设定并妥善保管 ，加密后的脚本除了密码设定/拥有者，其它任何人仅可以运行此脚本外，均无法查看脚本内容，无法修改脚本内容，无法解密脚本内容。 如您将密码告知对方，对方也可以对此脚本进行解密并获得此脚本的明文内容。

#### 解密操作步骤：
第一步：在A区选中指定脚本然后右键选择解密脚本

![](6d7b2882624511f09a0d0242ac130006/images/image_026f4a6bef42.png)

第二步：在解密脚本窗口输入加密时设定的密码，然后点击解密脚本或完成并退出，即可完成脚本解密

![](6d7b2882624511f09a0d0242ac130006/images/image_01d21924855b.png)

**注意：您设定的密码为脚本加解密的唯一钥匙：请您务必自行妥善保管 ，如遗失/遗忘您自行设定的密码，候鸟官方客服无法帮助您解密脚本，您将彻底无法解密脚本。**

### 九、删除指定脚本
当您发现某些商业环境不再需要使用某些脚本，您可以对指定的环境进行脚本删除操作。 注：您无法对官方脚本库提供的脚本进行删除，但您可以对自有已添加的脚本进行删除。

![](6d7b2882624511f09a0d0242ac130006/images/image_33e6125f0634.png)

A区 本地脚本目录中指定脚本进行删除： 通过在A区，选择 我的自动化脚本，勾选要删除的脚本，点击删除脚本。候鸟自动化脚本管理器 仅会删除您的硬盘中此脚本的脚本目录中的此脚本副本。 并不会删除您的浏览器环境中的此脚本数据存根。因此，您对A区列表的脚本进行删除，并不会影响到您环境中的脚本数据。您此时删除的仅仅为本地硬盘中的曾经添加过的脚本副本。 也就是说，您虽然在A区列表中看不到此脚本了，但是在环境的脚本列表中（C区列表），仍旧可以看到此脚本。

那么，如果我想彻底删除环境下的指定脚本，该怎么操作呢？

![](6d7b2882624511f09a0d0242ac130006/images/image_b08ac4843d60.png)

如图：在B区勾选环境一，可以看到C区已列出此环境下所有脚本。 勾选其中单个或多个脚本。点击删除脚本。将会立即彻底删除此环境下指定的脚本。

### 十、导入和导出脚本包
脚本支持导出到本地，以便进行团队协作，支持分享给您的团队帐户导入进行使用脚本。加密后的脚本支持导出到本地，可以将加密后的脚本更安全的传递给您的同事而无须担心脚本内容泄露。

#### 导出操作步骤：
第一步：在A区脚本列表勾选指定脚本进行导出

![](6d7b2882624511f09a0d0242ac130006/images/image_d3fd7514d0db.png)

第二步：在批量导出候鸟自动化脚本到本地的窗口，选择导出的目录，然后点击导出脚本到本地即可

![](6d7b2882624511f09a0d0242ac130006/images/image_185a1b6fee66.png)

#### 导入操作步骤：
第一步：在A区底部点击【**导入**】。

![](6d7b2882624511f09a0d0242ac130006/images/image_6495e2ee481c.png)

第二步：在批量导入本地自动化脚本窗口中，选择我的本地脚本包文件

![](6d7b2882624511f09a0d0242ac130006/images/image_752005d8ec2d.png)

第三步：点击底部 **导入本地脚本包**

![](6d7b2882624511f09a0d0242ac130006/images/image_f8ce9c96325c.png)

如上图所示， 此时表示脚本已成功导入，此时您可以在A区看到该脚本。

### 十一、创建Python脚本并使用CMD命令行执行脚本
1、在弹出的自动化脚本管理器中点击新建自动化脚本按钮，在新建脚本自动化窗口中选择脚本类型**Selenium**和脚本语言**python**，输入脚本名，并点击创建此脚本

![](6d7b2882624511f09a0d0242ac130006/images/image_535b64431b2a.png)

2、程序会自动建立一个标准python脚本，保存并关闭脚本编辑窗口

![](6d7b2882624511f09a0d0242ac130006/images/image_0be4114e2bca.png)

3、选定新建的脚本和要加载脚本的Item，点击 **将选中的脚本添加到勾选的环境中** 按钮

![](6d7b2882624511f09a0d0242ac130006/images/image_64992e77c716.png)

4、右下窗口中点击新添加入的脚本项，再点击调试运行按钮，就可以查看脚本运行效果

![](6d7b2882624511f09a0d0242ac130006/images/image_8710cfbe4578.png)

5、点击编辑脚本，在编辑器中修改脚本后，可以在工具菜单点运行（或直接按F5）保存并查看执行效果

![](6d7b2882624511f09a0d0242ac130006/images/image_079bdf4de0d9.png)

6、CMD命令行方式执行方法，需要在前面**调试脚本**已打开浏览器并调试运行选中脚本的基础上，转到程序安装目录自带的python目录下，按时间排序后，可以看到最新的一个py文件就是当前正在执行的脚本，dbgrun.bat就是当前调用此py文件的批处理

![](6d7b2882624511f09a0d0242ac130006/images/image_90d85e853f0e.png)

7、可以在此目录打开cmd窗口，配置安装需要的第三方python模块，直接修改py文件（注：py文件中的debug端口是当前打开chrome窗口的实际端口，只对当前打开的chrome有效，每次打的chrome debug端口值可能不一样，用户不能修改此端口值），可以新建个tab页，然后运行dbgrun.bat查看脚本运行情况

![](6d7b2882624511f09a0d0242ac130006/images/image_4b3adc58b03e.png)

================================================== 英文内容 ==================================================
# Mbbrowser Automate Script Manager Instruction Manual
Mbbrowser supports automated script manager. Selenium and Puppeteer automation scripts, automated import script packages, and specified other environments support corresponding automated scripts in batches. It supports users to carry out strong encryption mode for automated scripts by themselves. The encrypted scripts can be directly loaded and run efficiently in the environment of migratory birds, and can be shared to the team account for use in the encrypted state. Its own scripts are securely stored in the Migratory Bird cloud, and can be encrypted and decrypted using its own password. The support environment includes the complete export of automation scripts to local storage, and completely banish the previous complex environment one by one installation, one by one debugging automation scripts, maintenance and other business daily management.

### Mbbrowser Automate Script Manager Use The Foregoing
The dedicated commercial kernel of the Migrating bird browser developed based on Google chrome has started to fully support Selenium and Puppeteer automation scripts. You can use the automatic script Manager window to import automatic script packages in batches and assign corresponding automatic scripts to other environments in batches. The client of Migratory Bird adopts bank-level 128-bit algorithm encryption, and the user can carry out strong encryption mode on the automatic script by himself. After encryption, except for the password setting/owner, anyone else can only run the script, and cannot view the script content, modify the script content and decrypt the script content.

### Video Tutorial
|  Automated Script Manager Usage |
| ------------ |
| <video controls="" preload style="width:100%"><source src="https://help.mbbrowser.com/video/automated_script_manager.mp4" type="video/mp4"></video> |

### I、Start and access the automate script manager
To launch the Migratory Bird client, click the Function menu in the upper left corner of the main panel, select Automate Script Manager and click to enter

**Method 1:**

<p><img src="6d7b2882624511f09a0d0242ac130006/images/image_8da323178f86.png" width="360" /></p>

**Method 2:** Right-click on the environment to bring up the menu, and then click Automate Script Manager

<p><img src="6d7b2882624511f09a0d0242ac130006/images/image_8e9ab81cc4f0.png" width="360" /></p>

**The difference between Method 1 and Method 2:** When Mode 2 enters the automated script Manager, the default focus is on script management in Environment 2.

#### Automate script manager interface:

![](6d7b2882624511f09a0d0242ac130006/images/image_b17676f55fb9.png)

Functional area distribution map:

![](6d7b2882624511f09a0d0242ac130006/images/image_c77fcf6ea4b0.png)

### II、Automate script Manager interface function description
In the Automated Script Manager, you can add, remove, find, and assign automated scripts for your environment with one click. Supports importing and exporting script packages.

![](6d7b2882624511f09a0d0242ac130006/images/image_999fd8d36662.png)

#### As shown below, Area A:

#### Drop-down menu items:
【My automated script】 Lists the total collection of all scripts that you have added or imported, that is, all automation scripts that have been added to your account currently.
【My puppeteer automated script】 Lists the total collection of Puppeteer scripts that you have added or imported.
【My selenium automated script】 Lists the total collection of Selenium scripts that you have added or imported.
【Automation Script Library】 All automated script libraries provided by Migratory Bird are listed.

#### Please enter keyword to find the script:
The automatic script manager allows you to dynamically search for scripts in real time without manually searching them one by one in the list. By using the search script function, you can quickly locate the scripts you need and assign them.

![](6d7b2882624511f09a0d0242ac130006/images/image_cd49d82c552d.png)

Area A list: Operation column: Check box After you select this box, you can perform subsequent operations. Script name: Lists the script name. Encryption: Lists whether the current script is encrypted. Description: Script description, **Move the cursor over the description to quickly see the full content of the description without having to drag the automated script manager form size.**

#### As shown below, Area B:
The main function of Section B is to list all of your environments, provide a collection of environments that have been assigned scripts, and provide an environment search function to help you locate the environment you need to work with. Provides the number of scripts allocated for each environment, so that you can easily assign and manage scripts for your environment.

![](6d7b2882624511f09a0d0242ac130006/images/image_893336efcc2e.png)

【Scripted environment】 If you have previously assigned scripts in some environment, go to the Automated Script Manager, which is displayed by default. The set of environments for assigned scripts is listed by default.
【Other】 Lists the ungrouped and grouped environment groups.
【Please enter the keyword to find the environment】 Enter your session environment keyword to quickly list the environments you need to operate in.

![](6d7b2882624511f09a0d0242ac130006/images/image_1569fa63fa59.png)

Area B list: Operation column: Check box After you select this box, you can perform subsequent operations. Environment Name: Lists the name of the environment under your group. Creation time: Lists the creation time of your environment. Script count: Displays the number of scripts allocated in your environment.

#### As shown below, Area C:
The main function of the C area is to list the scripts that have been assigned in one of your environments and to provide management of the scripts in that environment. By default, when no environment is selected, the plug-in list in area C is empty. Lists the script listing of the environment only when you check or click on an environment, or when you check multiple environments.
Operation focus: click on Zone B environment and check the difference between zone B environment:

![](6d7b2882624511f09a0d0242ac130006/images/image_0b006b28e21a.png)

When you only click (select) an environment in section B, section C will only list all scripts in that single environment. If you click on multiple environments in section B, section C will only list all the scripts for the last environment clicked.

![](6d7b2882624511f09a0d0242ac130006/images/image_1d3ce3139ebe.png)

When you check an environment (the small box is checked in the action column), the C area lists all the scripts for that environment that have been checked. If you check multiple environments in section B, section C will list all scripts in those multiple environments.

### III、New automation script
Step 1: Click 【**Create scripts**】 at the bottom right button.

![](6d7b2882624511f09a0d0242ac130006/images/image_cbbae516d2e6.png)

Step 2: Configure the corresponding information in the new automated script window
【Type】There are Puppeteer and Selenium types available
【Language】The script type is Puppeteer, the script language is JS, the script type is Puppeteer, the script language is Python
【Name】Customize the script name according to your requirements
【Script description】Description of script functions

![](6d7b2882624511f09a0d0242ac130006/images/image_b477c30d107f.png)

Step 3: After completing the configuration, click Create this script, and the script editing window will pop up automatically. By default, a sample template will be provided. Here you can start editing the script logic code you need.

![](6d7b2882624511f09a0d0242ac130006/images/image_a23ddcc25c4b.png)

### IV、Assign scripts to various environments

![](6d7b2882624511f09a0d0242ac130006/images/image_8523def16dea.png)

The process of allocating scripts is from zone A to Zone B to Zone C, and then click the button in the lower right corner of the automated script Manager.
Step 1: Select the **My automated script** drop-down in Area A.

![](6d7b2882624511f09a0d0242ac130006/images/image_c989a08a4fce.png)

Step 2: In the list of area A, find the script you need and select this script.

![](6d7b2882624511f09a0d0242ac130006/images/image_192618ed64d6.png)

Step 3: In the B area list, check the environments to which you want to configure this script.

![](6d7b2882624511f09a0d0242ac130006/images/image_10750183f130.png)

Step 4: Click the button to add the selected script to the selected environment.

![](6d7b2882624511f09a0d0242ac130006/images/image_67ba8add596e.png)

![](6d7b2882624511f09a0d0242ac130006/images/image_94bee818bc03.png)

As shown in the preceding figure, the script has been successfully allocated to two environments. In area B and Area C, you can see that the number of plug-ins in environment 1 and environment 2 changes from 2 to 3. Indicates that the installation is successful. In the C area, you can see that the script that has just been assigned appears in the script list of environment 1 and Environment 2.

### V、Modify script
You can select A script from the script list in Area A and modify it

![](6d7b2882624511f09a0d0242ac130006/images/image_986590fa25e5.png)

You can also modify the script in your environment through the C area

![](6d7b2882624511f09a0d0242ac130006/images/image_0f1939b4fbb0.png)

### Ⅵ、Debug script
You need to assign the script to the specified environment. After selecting the specified environment in area B, select the script from the script list in area C for debugging.

![](6d7b2882624511f09a0d0242ac130006/images/image_6095d3b7bba9.png)

You can also modify the script in the editor of **Edit Script** and run it in the Tools menu point (or directly press F5) to save and view the execution effect

### Ⅶ、Activation script
Assign scripts to the specified environment. Select the specified environment in area B and select the specified script in the script list in area C to activate the script.

![](6d7b2882624511f09a0d0242ac130006/images/image_48df949ca4eb.png)

![](6d7b2882624511f09a0d0242ac130006/images/image_1eea873aeeaf.png)

As shown in the figure above, the script has been successfully activated in the specified environment.

### Ⅷ、Encrypt and decrypt the specified script
Users can encrypt automated scripts by themselves. After encryption, scripts can be loaded and run efficiently in the environment of migratory birds.

#### Encryption procedure:
Step 1: Select the specified script in area A and right-click the encryption script

![](6d7b2882624511f09a0d0242ac130006/images/image_48b4e6bab1a8.png)

Step 2: Enter the encryption password in the encryption script window, and then click Encryption script or finish and exit to complete the script encryption

![](6d7b2882624511f09a0d0242ac130006/images/image_431bfbf5d377.png)

After the encryption is complete, you can view the script encryption column in the A list as Yes

![](6d7b2882624511f09a0d0242ac130006/images/image_48c97421ef9c.png)

The encryption script is encrypted by bank 128-bit algorithm in the Migratory Bird client. The password of the encryption script is set by you and properly kept. The encrypted script cannot be viewed, modified or decrypted by anyone other than the password setting/owner who can only run the script. If you tell the other party the password, the other party can decrypt the script and obtain the plaintext content of the script.

#### Decryption procedure:
Step 1: Select the specified script in area A and right-click the decryption script

![](6d7b2882624511f09a0d0242ac130006/images/image_3d1493810331.png)

Step 2: Enter the password set during encryption in the decryption script window, and then click Decryption script or finish and exit to complete the decryption script

![](6d7b2882624511f09a0d0242ac130006/images/image_628dd4fffb74.png)

**Note: The password you set is the only key for script encryption and decryption: please be sure to keep it properly by yourself. If you lose/forget your password, the official customer service of Migratory Bird cannot help you decrypt the script, and you will not be able to decrypt the script completely.**

### Ⅸ、Delete specified script
When you find that certain scripts are no longer required for certain business environments, you can delete the scripts for the specified environment. Note: You cannot delete scripts provided by the official script library, but you can delete your own scripts that have been added.

![](6d7b2882624511f09a0d0242ac130006/images/image_07571bb0844f.png)

Specify A script to delete from the local script directory in Area A: Select my automation script in Area A, check the script to be deleted, and click Delete Script. Migratory Bird Automated Script Manager will only delete a copy of this script in the script directory of this script on your hard drive. This script data stub in your browser environment is not deleted. Therefore, the script data in your environment will not be affected if you delete the script from the list in Section A. You are deleting only a copy of the script that has been added to the local hard disk. That is, you no longer see the script in the A section list, but you still see it in the environment's script list (the C section list).

So, what if I want to completely delete the specified script in the environment?

![](6d7b2882624511f09a0d0242ac130006/images/image_9a93aea579fd.png)

Figure: Select Environment 1 in area B, and all scripts in this environment are listed in area C. Select one or more of the scripts. Click Delete script. The scripts specified in this environment will be deleted immediately and completely.

### Ⅹ、Import and export script packages
Scripts can be exported locally for team collaboration and imported for use by team accounts shared with you. Encrypted scripts can be exported to the local PC. You can send encrypted scripts to colleagues in a secure manner without worrying about script content leakage.

#### Export procedure:
Step 1: In the script list in area A, select the specified script to export

![](6d7b2882624511f09a0d0242ac130006/images/image_0ec00bc9bc14.png)

Step 2: In the window of exporting Migratory bird automation scripts in batches to the local, select the export directory and click Export scripts to the local

![](6d7b2882624511f09a0d0242ac130006/images/image_7da20e35e054.png)

#### Import procedure:
Step 1: Click 【** Import **】 at the bottom of Section A.

![](6d7b2882624511f09a0d0242ac130006/images/image_fb27028c0c49.png)

Step 2: In the Batch Import Local Automated Scripts window, select my local script package file

![](6d7b2882624511f09a0d0242ac130006/images/image_4f3ea1424cbe.png)

Step 3: Click on the bottom ** Import script**

![](6d7b2882624511f09a0d0242ac130006/images/image_8aba257697ca.png)

As shown in the preceding figure, the script is successfully imported. You can view the script in area A.

### Xl、Create the Python script and execute it using the CMD command line
1. In the pop-up Automation Script Manager, click the New Automation Script button. In the New Script Automation window, select the script type **Selenium** and scripting language **python**, enter the script name, and click Create this script

![](6d7b2882624511f09a0d0242ac130006/images/image_c4101178bbe4.png)

2. The program will automatically create a standard python script, save and close the script editing window

![](6d7b2882624511f09a0d0242ac130006/images/image_e0c094b9c2a4.png)

3. Select the newly created script and the Item to be loaded, click **Install script into the environment** button

![](6d7b2882624511f09a0d0242ac130006/images/image_45c519db815b.png)

4. Click the newly added script item in the lower right window, and then click the debug run button to view the script running effect

![](6d7b2882624511f09a0d0242ac130006/images/image_1189d79e7c2f.png)

5. Click Edit Script. After modifying the script in the editor, you can run it in the tool menu (or directly press F5) to save and view the execution effect

![](6d7b2882624511f09a0d0242ac130006/images/image_4ad70e1bbc56.png)

6. CMD command line execution method, you need to open the browser before **Run/Debug**, on the basis of commissioning and running the selected script, go to the python directory in the program installation directory, sorted by time, you can see that the latest py file is the script currently being executed. dbgrun.bat is the batch that is currently calling this py file

![](6d7b2882624511f09a0d0242ac130006/images/image_90d85e853f0e.png)

7. You can open the cmd window in this directory, configure the third-party python module required for installation, and directly modify the py file (Note: The debug port in the py file is the actual port in the chrome window that is currently open, and is only valid for the chrome that is currently open. The value of the chrome debug port may be different each time you type, so you cannot change the value of the port.) You can create a tab page and run dbgrun

![](6d7b2882624511f09a0d0242ac130006/images/image_4b3adc58b03e.png)