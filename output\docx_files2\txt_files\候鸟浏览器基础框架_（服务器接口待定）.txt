*（服务器接口待定）

*（服务器接口待定）

第五章：MBService 值守服务

第五章：MBService 值守服务

综述：为保证用户在商业运营中，保证产品长时间稳定运行，数据同步准确无误，保证各进程在各种意外情形下，不间断运行，并提供充分的后期可扩展性。采用行业内通行架构，MBService服务来进行后台值守。

MBService 以服务模式在后台运行，在服务加载失败的情况下，自动支持进程方式运行。

MBService 服务主职责为：

自动汇总各ITEM浏览器的PASSWORD\SESSION会话\USERAGENT数据，提供给主面板进行全局管理。

除主进程的用户行为同步之外，事件触发同步逻辑外，MBService服务支持全自动后台同步数据策略，保证服务器端数据为最新用户数据。

主面板程序值守，监控主进程，如出现意外崩溃，要将主面板程序第一时间进行恢复。主面板退出后，针对主面板的相关自动数据整合、校验逻辑。

提供强制升级、强制更新其它功能模块，主进程版本等功能。

自动收集，汇总软件操作与各种事件信息，提交到服务器供数据分析和二次产品迭代时的数据依据。

定义与约定：涉及到的无人工参与的，业务自动化逻辑，功能自动化逻辑，自动化事件响应逻辑，可在后台进行的，使用MBService服务来完成。MBService服务任何时侯不退出。

选择配置方案：Release    会激活定义：__WIN_EXE 输出MBService.exe

MBService 命令行参数说明

-i//插入服务

-k//停止服务

-u//卸载服务

-s//启动服务

-svc//系统加载服务,运行后不退出

-s -runnow 按加载服务的方式运行程序,不退出

不跟参数直接运行MBService相当于-s -runnow的效果

以下就各个功能要求进行详细描述：

自动汇总各ITEM浏览器的PASSWORD\SESSION会话\USERAGENT数据，提供给主面板进行全局管理。

PASSWORD流程详述：

A、 服务启动后，间隔5分钟（调试期使用10秒间隔），读取configdata.xml，获取所有item unique_id并换算出实际item的chrome路径。

读取每个chrome/default下password所有数据。 读取本地password_list.xml判断xml中是否已有对应password数据（site、user、password值同时一致）。如果没有，将记录添加到password_list.xml。

添加完成后，根据第四章约定与流程，将password_list.xml同步到服务器上。

Session会话数据包、Useragent数据同理同流程。

除主进程的用户行为同步之外，事件触发同步逻辑外，MBService服务支持全自动后台同步数据策略，保证服务器端数据为最新用户数据。

详细描述：

每间隔3秒，检测主进程是否已退出。

A、mbbrowser.exe主进程正常退出，使用第三章同步流程约定，将本地数据FULL包，同步到服务器。同步成功后，更新backup.xml。

B、chrome退出（item下浏览器退出）。

使用第三章同步流程约定，将本地数据item数据包，同步到服务器。更新本地item数据包xml，同步本地item数据包xml到服务器。

（3）主面板程序值守，监控主进程，如出现意外崩溃，要将主面板程序第一时间进行恢复。

详细描述：

每间隔3秒，检测主进程是否已退出。

A、主进程非正常退出：

1、（导致异常退出的：程序自动崩溃、意外断电、用户人工KILL进程、用户人工系统重启等），为保证下次使用的浏览器数据不毁损，暂约定不同步任何数据到服务器。

2、非人工KILL进程，非人工关闭进程的，要重启主进程（包括重启Chrome）。

非人工异常退出的数据发送到服务器指定接口。

接口参数：用户：token、崩溃类型：type、崩溃数据包。

主面板退出后，针对主面板的相关自动数据整合、校验逻辑。

详细描述：

对于已存在本地，但用户未使用的item，获得hash值，根据根据unique值，和服务器返回进行对比，如果一致，则不打包到full。

（4） 提供强制升级、强制更新其它功能模块，主进程版本等功能。

描述：

采用原历史商业产品升级模块进行支持。

（5）自动收集，汇总软件操作与各种事件信息，提交到服务器供数据分析和二次产品迭代时的数据依据。

描述：

对于崩溃后dump的数据，支持同步到服务器中。使用同步接口模式进行提交。

第六章：服务器压缩解压缩详细描述：

第六章：服务器压缩解压缩详细描述

服务器端以用户hash ID为存储路径，根据PC端发过来的full包、item包，进行分别存储。 即服务器端即可以自行将item包汇总打包下发（采用存储模式打包），也能支持直接下发full版本包。

区别：汇总打包下发由PC端启动后、请求服务器同步数据到本地的环节，服务器下发汇总包。（汇总包所有数据格式与FULL包完全一致）

用户通过backup面板进行数据恢复： 服务器直接下发full版本包。

用户的Item包在服务器有且仅有一份有效包。

Full包根据版本可以存有多个包。

第七章：C/S数据压缩与解压缩、分片HASH与分片同步详细流程架构说明。

第七章：C/S数据压缩与解压缩、分片HASH与分片同步详细流程架构说明。

1、用户数据存储到文件夹过程的容错处理。

2、服务器FULL包仅在PC客户端请求下载流程时，才将各子ZIP包进行（非二次压缩仅使用存储模式[不占用CPU/HDD资源]）合并成独立ZIP包下发给PC端。

第八章：全局用户习惯设置与软件运行环境全局设置、C/S端同步架构流程说明。IP2LOCATION平台数据业务和PC端、WEB端数据同步流程及接口约定。

第八章：全局用户习惯设置与软件运行环境全局设置、C/S端同步架构流程说明。IP2LOCATION平台数据业务和PC端、WEB端数据同步流程及接口约定。

//********无用描述************

（压力测试在各数据格式写入磁盘的工作完成后进行。）

**********无用描述*************//

第九章、浏览器数据类型与存储结构

第九章、浏览器数据类型与存储结构

第一节、

（1）SESSION会话 数据结构：

（2）ConfigData.xml 代码(带描述)：

第二节：密码管理数据结构

（1）PASSWORD_DATA.XML

数据格式表：

（2）PASSWORD_LIST.XML文件格式【2022-0803二次修订】

旧：

新：

各节点描述说明如下：

LOGIN_ACCOUNT  password_list.xml归属于哪个登录帐户：例 <EMAIL>

DOMAIN TITLE  帐户名称

PLATFORM     归属于哪个平台（帐户销售平台：例：stripe）

DOMAIN NAME 用户名/密码自动填入的URL网址。

LOGIN         登录用户名

ORGIN_PASSWORD     登录密码(明文)

ENCRYPT_PASSWORD 登录密码(密文)

IS_VALID	     是否有效

UPDATETIME    更新时间

CREATETIME    创建时间


================================================== 表格内容 ==================================================

<?xml version="1.0" encoding="gb2312" ?>
<VERSION version="759" xmlversion="2">
<VER ID="201" VERSION="26" SESSION_UNIQUE_ID="095fcf2e3e614d76717901abbfeea2cb" FROMUID="0" SHARETIME="" FROM_ACCOUNT="" GROUP_ID="0" GROUP_NAME="" IR32="1">
	<SESSION NAME="999" TOP="1" COLOR="#FFffffffff" SYSTEM="Win32"/>
	<IS_ANONYMITY ANONYMITY = "0"/>
<Template NAME = "模板名称"/>
<Template ID = "模板ID值"/>
<Template FMASK = "模板FMASK值"/>
	<COMMENT COMMENT_STRING=""/>
	<SESSION_DEFINE_CODE SPHEREGL="0" ENABLE_LOCALHTML5_STORAGE="0" SAVE_LOCALHTML5_STORAGE="0" SAVE_INDEXDB_STORAGE="0"/>
	<NETWORK TYPE="http" PMODE="2" IP="************" PORT="8889" USER="" PASSWORD="" PUBLIC_IP="" FAKEIP="" />
	<NETWORK_CTR NA="0" FAKE_WRTC="0" SAME_IP="0" IPV6="0" WRTCOFF="0" DNS="" />
	<USERAGENT_BROWSER CHROME="0" SAFARI="0" MSIE="0" OTHER="0" REGEN_CONFIG_USERAGENT="0" MAINURL="" />
	<USERAGENT_STRING UA="Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/48.0.2564.116 Safari/537.36 TheWorld 7" UA_LNG="en-US" UA_LNG_STRING="[@en-US@,@US@]" />
	<USERAGENT_CTR DISPOPUPS="0" ENABLE_SERVICE="0" BLOCKLNGINPUT="0"  />
	<RESOLUTION WIGHT="1920" HEIGHT="1200" />
	<RESOLUTION_CTR EMU_SCREEN="0" EMU_TOUCH="0" />
	<POSITION LONGITUDE="0.000000" LATITUDE="0.000000" COUNTRY="US" />
	<TIMEZONE TIMEZONE_NAME="America/New_York (300)" ADD_VALUE=""/>
	<FINGERPRINT_CODE AUDIO="1" CANVAS="1" FONTS="1" RETCS="0" DNS="1" AUTOIPCHECK="0" fpver="2"/>
	<OTHER_SETTING PLUGINS_MIMETYPE="0" SAVE_ENCRYPT_COOKIES="0" ENABLE_FLASH="0" DYNAMIC_FINGERPRINTS="0" BLOCK_CANVAS_OUTPUT="0" />
	<DYNAMIC_FINGERPRINTS_CTR D_AUDIO="0" D_CANVAS="0" D_FONTS="0" D_RETCS="0" D_MEDIA="0" D_WEBGL="0" D_MIME="0" D_PLUGINS="0" />
	<IS_VALUED VALUED="0" />
	<UPDATETIME VALUE="2021-12-06 10:49:44"/>
	<CREATETIME VALUE="2021-11-25 17:18:27"/>
</VER>
</VERSION>

<?xml version="1.0" encoding="utf-8" ?>
<VERSION version="11">
<VER ID="1" LOGIN_ACCOUNT="" >
	<DOMAIN TITLE = "帐户名称"/>
<PLATFORM NAME = "21cn.com"/>
<DOMAINURL NAME = "21cn.com"/>
	<LOGIN NAME="sztengli"/>
	<ORGIN_PASSWORD NAME="sztengli"/>
  <ENCRYPT_PASSWORD NAME="sztengli"/>
	<IS_VALID VALUE="1" />
	<UPDATETIME VALUE ="2021-01-12 12:22:34"/>
	<CREATETIME VALUE ="2021-01-12 12:22:34"/>
</VER>
</VERSION>