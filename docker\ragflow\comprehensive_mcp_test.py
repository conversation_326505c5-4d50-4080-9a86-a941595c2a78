#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RAGFlow MCP 综合测试脚本
参考官方实现，提供完整的测试和诊断功能
"""

import json
import uuid
import requests
import time
import sys
from typing import Dict, Any, Optional, List

class ComprehensiveMCPTest:
    """综合 MCP 测试工具"""
    
    def __init__(self, 
                 base_url: str = "http://************:9382", 
                 api_key: str = "ragflow-UzYTlhMjIwNjI0YzExZjA4NzUyMDI0Mm"):
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.session_id = str(uuid.uuid4())
        self.request_id = 0
        
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}",
            "User-Agent": "RAGFlow-Comprehensive-Test/1.0",
            "Accept": "application/json"
        }
        
        self.test_results = {
            "connectivity": False,
            "initialization": False,
            "tools_list": False,
            "query_test": False,
            "errors": []
        }
        
        print(f"🧪 RAGFlow MCP 综合测试")
        print(f"   服务器: {self.base_url}")
        print(f"   会话ID: {self.session_id}")
    
    def _get_next_request_id(self) -> int:
        """获取下一个请求ID"""
        self.request_id += 1
        return self.request_id
    
    def _send_mcp_request(self, method: str, params: Optional[Dict] = None) -> Dict[str, Any]:
        """发送 MCP 请求"""
        request_data = {
            "jsonrpc": "2.0",
            "id": self._get_next_request_id(),
            "method": method
        }
        
        if params is not None:
            request_data["params"] = params
        
        url = f"{self.base_url}/messages/?session_id={self.session_id}"
        
        try:
            response = requests.post(
                url,
                headers=self.headers,
                json=request_data,
                timeout=30
            )
            
            if response.status_code != 200:
                return {"error": {"code": response.status_code, "message": response.text}}
            
            return response.json()
            
        except Exception as e:
            return {"error": {"code": -1, "message": str(e)}}
    
    def test_basic_connectivity(self) -> bool:
        """测试基础连接"""
        print(f"\n1️⃣ 测试基础连接...")
        
        try:
            # 测试主服务
            ragflow_url = self.base_url.replace(":9382", ":9380")
            response = requests.get(ragflow_url, timeout=10)
            print(f"   RAGFlow 主服务: {response.status_code}")
            
            # 测试 MCP SSE 端点
            sse_url = f"{self.base_url}/sse"
            response = requests.get(sse_url, headers=self.headers, timeout=10)
            print(f"   MCP SSE 端点: {response.status_code}")
            
            if response.status_code in [200, 404]:
                self.test_results["connectivity"] = True
                print("✅ 基础连接正常")
                return True
            else:
                self.test_results["errors"].append(f"SSE 端点响应异常: {response.status_code}")
                print("❌ 基础连接失败")
                return False
                
        except Exception as e:
            self.test_results["errors"].append(f"连接测试异常: {e}")
            print(f"❌ 连接测试失败: {e}")
            return False
    
    def test_mcp_initialization(self) -> bool:
        """测试 MCP 初始化"""
        print(f"\n2️⃣ 测试 MCP 初始化...")
        
        params = {
            "protocolVersion": "2024-11-05",
            "capabilities": {"tools": {}},
            "clientInfo": {
                "name": "RAGFlow-Comprehensive-Test",
                "version": "1.0.0"
            }
        }
        
        response = self._send_mcp_request("initialize", params)
        
        if "error" not in response and "result" in response:
            self.test_results["initialization"] = True
            print("✅ MCP 初始化成功")
            print(f"   服务器能力: {response.get('result', {}).get('capabilities', {})}")
            return True
        else:
            error_msg = "MCP 初始化失败"
            if "error" in response:
                error_msg += f": {response['error'].get('message', 'Unknown error')}"
            self.test_results["errors"].append(error_msg)
            print(f"❌ {error_msg}")
            return False
    
    def test_tools_list(self) -> List[Dict[str, Any]]:
        """测试工具列表获取"""
        print(f"\n3️⃣ 测试工具列表...")
        
        response = self._send_mcp_request("tools/list")
        
        if "error" not in response and "result" in response:
            tools = response["result"].get("tools", [])
            self.test_results["tools_list"] = True
            print(f"✅ 获取到 {len(tools)} 个工具:")
            
            for tool in tools:
                name = tool.get("name", "Unknown")
                description = tool.get("description", "No description")
                print(f"   - {name}: {description}")
            
            return tools
        else:
            error_msg = "获取工具列表失败"
            if "error" in response:
                error_msg += f": {response['error'].get('message', 'Unknown error')}"
            self.test_results["errors"].append(error_msg)
            print(f"❌ {error_msg}")
            return []
    
    def test_query_functionality(self) -> bool:
        """测试查询功能"""
        print(f"\n4️⃣ 测试查询功能...")
        
        test_questions = [
            "候鸟浏览器如何配置代理？",
            "RAGFlow是什么？"
        ]
        
        success_count = 0
        
        for i, question in enumerate(test_questions, 1):
            print(f"\n   测试问题 {i}: {question}")
            
            # 调用 ragflow_retrieval 工具
            params = {
                "name": "ragflow_retrieval",
                "arguments": {"question": question}
            }
            
            response = self._send_mcp_request("tools/call", params)
            
            if "error" not in response and "result" in response:
                result = response["result"]
                print(f"   ✅ 查询成功")
                
                # 尝试解析结果
                content = result.get("content", [])
                if content:
                    print(f"   📄 结果预览: {str(content)[:100]}...")
                
                success_count += 1
            else:
                error_msg = f"查询失败: {question}"
                if "error" in response:
                    error_msg += f" - {response['error'].get('message', 'Unknown error')}"
                self.test_results["errors"].append(error_msg)
                print(f"   ❌ 查询失败")
            
            time.sleep(1)  # 避免请求过快
        
        if success_count > 0:
            self.test_results["query_test"] = True
            print(f"\n✅ 查询功能测试完成，成功 {success_count}/{len(test_questions)} 个")
            return True
        else:
            print(f"\n❌ 查询功能测试失败")
            return False
    
    def test_alternative_endpoints(self) -> Dict[str, Any]:
        """测试其他可能的端点"""
        print(f"\n5️⃣ 测试其他端点...")
        
        endpoints = [
            "/health",
            "/api/v1/status",
            "/api/v1/query"
        ]
        
        results = {}
        
        for endpoint in endpoints:
            try:
                url = f"{self.base_url}{endpoint}"
                response = requests.get(url, headers=self.headers, timeout=5)
                results[endpoint] = {
                    "status_code": response.status_code,
                    "response": response.text[:200] if response.text else "Empty"
                }
                print(f"   {endpoint}: {response.status_code}")
            except Exception as e:
                results[endpoint] = {"error": str(e)}
                print(f"   {endpoint}: 错误 - {e}")
        
        return results
    
    def generate_report(self) -> Dict[str, Any]:
        """生成测试报告"""
        print(f"\n" + "=" * 60)
        print(f"📋 测试报告")
        print(f"=" * 60)
        
        total_tests = 4
        passed_tests = sum([
            self.test_results["connectivity"],
            self.test_results["initialization"],
            self.test_results["tools_list"],
            self.test_results["query_test"]
        ])
        
        print(f"📊 测试结果: {passed_tests}/{total_tests} 通过")
        print(f"   ✅ 基础连接: {'通过' if self.test_results['connectivity'] else '失败'}")
        print(f"   ✅ MCP 初始化: {'通过' if self.test_results['initialization'] else '失败'}")
        print(f"   ✅ 工具列表: {'通过' if self.test_results['tools_list'] else '失败'}")
        print(f"   ✅ 查询功能: {'通过' if self.test_results['query_test'] else '失败'}")
        
        if self.test_results["errors"]:
            print(f"\n❌ 错误列表:")
            for i, error in enumerate(self.test_results["errors"], 1):
                print(f"   {i}. {error}")
        
        # 生成建议
        print(f"\n💡 建议:")
        if not self.test_results["connectivity"]:
            print("   - 检查服务器状态和网络连接")
            print("   - 确认防火墙设置")
        elif not self.test_results["initialization"]:
            print("   - 检查 API 密钥是否正确")
            print("   - 验证 MCP Server 配置")
        elif not self.test_results["tools_list"]:
            print("   - 检查 RAGFlow 知识库配置")
            print("   - 确认 MCP Server 工具注册")
        elif not self.test_results["query_test"]:
            print("   - 检查知识库是否有数据")
            print("   - 验证查询工具配置")
        else:
            print("   - 所有测试通过！可以开始集成 Wing 客户端")
        
        return self.test_results
    
    def run_comprehensive_test(self) -> Dict[str, Any]:
        """运行综合测试"""
        print("🚀 开始 RAGFlow MCP 综合测试")
        print("=" * 60)
        
        try:
            # 1. 基础连接测试
            if not self.test_basic_connectivity():
                return self.generate_report()
            
            # 2. MCP 初始化测试
            if not self.test_mcp_initialization():
                return self.generate_report()
            
            # 3. 工具列表测试
            tools = self.test_tools_list()
            if not tools:
                return self.generate_report()
            
            # 4. 查询功能测试
            self.test_query_functionality()
            
            # 5. 其他端点测试
            self.test_alternative_endpoints()
            
            # 6. 生成报告
            return self.generate_report()
            
        except KeyboardInterrupt:
            print(f"\n\n⏹️ 测试被用户中断")
            return self.test_results
        except Exception as e:
            print(f"\n❌ 测试过程中发生错误: {e}")
            self.test_results["errors"].append(f"测试异常: {e}")
            return self.generate_report()


def main():
    """主函数"""
    if len(sys.argv) > 1:
        server_url = sys.argv[1]
    else:
        server_url = "http://************:9382"
    
    if len(sys.argv) > 2:
        api_key = sys.argv[2]
    else:
        api_key = "ragflow-UzYTlhMjIwNjI0YzExZjA4NzUyMDI0Mm"
    
    print(f"RAGFlow MCP 综合测试工具")
    print(f"使用方法: python {sys.argv[0]} [server_url] [api_key]")
    print()
    
    tester = ComprehensiveMCPTest(server_url, api_key)
    results = tester.run_comprehensive_test()
    
    # 保存结果
    with open("comprehensive_test_results.json", "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 测试结果已保存到: comprehensive_test_results.json")


if __name__ == "__main__":
    main()
