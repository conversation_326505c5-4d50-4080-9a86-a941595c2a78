标题: Cookie的导入、导出、清空
英文标题: Cookie Import, Export and Clean
ID: 60
分类ID: 7
添加时间: 1613547862
更新时间: 1689907992
访问次数: 0
SEO标题: 候鸟浏览器Cookie导入导出清空
SEO关键词: 候鸟浏览器Cookie导入导出清空
SEO描述: 候鸟浏览器Cookie导入导出清空

================================================== 内容 ==================================================
Cookie导入可让您的网站账号保持登录状态。


### 一、 导入Cookie

1、候鸟浏览器任意环境列表处点击鼠标右键，在弹出的菜单中点击“导入cookie”；

![](6d7b2882624511f09a0d0242ac130006/images/image_a02228dfa450.png)

2、弹出“导入cookie”操作面板，导入cookie支持“文件导入”和“剪贴板导入”，选择任意方式即可，文件导入要选择包含cookie代码的txt文件，然后点击下方“导入cookie”按钮；

![](6d7b2882624511f09a0d0242ac130006/images/image_b7a5e83e5947.png)

3、下方提示“您的COOKIE数据以导入成功”即完成导入；

![](6d7b2882624511f09a0d0242ac130006/images/image_6f376c710430.png)

4、如果使用“剪贴板导入”的方式，将cookie代码剪切或粘贴到红框内，点击下方“剪贴板导入Cookie”；

![](6d7b2882624511f09a0d0242ac130006/images/image_db652e77ea11.png)

5、下方提示“您的COOKIE数据以导入成功”即完成导入。

![](6d7b2882624511f09a0d0242ac130006/images/image_add0130af512.png)

### 二、导出Cookie

1、候鸟浏览器任意环境列表处点击鼠标右键，在弹出的菜单中点击“导出cookie”；

![](6d7b2882624511f09a0d0242ac130006/images/image_5dc03645dee0.png)

2、弹出“导出cookie”操作面板，选择一个文件夹，勾选是否将导出数据进行加密，然后点击下方“导出Cookie”按钮。

![](6d7b2882624511f09a0d0242ac130006/images/image_6471ec267f13.png)

3、就会在刚刚选择的文件夹里看到一个导出的“cookie.txt”文件；

![](6d7b2882624511f09a0d0242ac130006/images/image_e14eab85867d.png)

4、下方是加密后的cookie代码。

![](6d7b2882624511f09a0d0242ac130006/images/image_a43253ea8b78.png)

### 三、清空Cookie

1、候鸟浏览器任意环境列表处点击鼠标右键，在弹出的菜单中点击“清空cookie”；

![](6d7b2882624511f09a0d0242ac130006/images/image_a840efd61baf.png)

2、直到屏幕下方出现“Cookie数据已清空”的提示，即清空成功。

![](6d7b2882624511f09a0d0242ac130006/images/image_5fb10d0928f0.png)

================================================== 英文内容 ==================================================
Cookie import allows your website account to stay logged in.


### I、 Import Cookie

1. Click the right mouse button in any environment list of the Mbbrowser, and click "CookieMgr - Import Cookie" in the pop-up menu;

<p><img src="6d7b2882624511f09a0d0242ac130006/images/image_68657bc8e2f2.png" width="360" /></p>

2. Pop up the "Import Cookie" operation panel, import cookie support "file import" and "clipboard import", select any way, file import to select the cookie code contains txt file, and then click the "Import Cookie" button below;

![](6d7b2882624511f09a0d0242ac130006/images/image_00729e3c3d24.png)

3. The message "COOKIE update successfully.." is displayed below to complete the import;

![](6d7b2882624511f09a0d0242ac130006/images/image_9bac76a3c460.png)

4. If you use "Clipboard Import", cut or paste the cookie code into the red box and click " Import from Clipboard" below;

![](6d7b2882624511f09a0d0242ac130006/images/image_d78416428c15.png)

5. The message "COOKIE update successfully.." is displayed below to complete the import;

![](6d7b2882624511f09a0d0242ac130006/images/image_1c06c606419d.png)

### II、Export Cookie

1. Click the right mouse button in any environment list of the Mbbrowser, and click "CookieMgr - Export Cookie" in the pop-up menu;

<p><img src="6d7b2882624511f09a0d0242ac130006/images/image_94e052d65289.png" width="360" /></p>

2. The "Export Cookie" operation panel pops up, select a folder, check whether to encrypt the exported data, and then click the "Export Cookies" button below.

![](6d7b2882624511f09a0d0242ac130006/images/image_06f11acffd59.png)

3. You will see an exported "cookie.txt" file in the folder you just selected.

![](6d7b2882624511f09a0d0242ac130006/images/image_e14eab85867d.png)

4. Below is the encrypted cookie code.

![](6d7b2882624511f09a0d0242ac130006/images/image_a43253ea8b78.png)

### III、Clean Cookie

1. Click the right mouse button in any environment list of the Mbbrowser, and click "CookieMgr - Clean Cookie" in the pop-up menu;

<p><img src="6d7b2882624511f09a0d0242ac130006/images/image_f7e313d9bd9d.png" width="360" /></p>

2. Until the message "Cookie data has been cleared" appears at the bottom of the screen, the clearing is successful.

![](6d7b2882624511f09a0d0242ac130006/images/image_80b0aab98ead.png)