# 帐号登录

## 描述
候鸟浏览器API登录认证，支持本地API功能，帮助用户通过程序化的方式来启动和关闭浏览器等基础API功能，还可以配合Selenium和Puppeteer等自动化框架来实现浏览器操作的自动化。

## 版本要求
- **客户端版本**: V3.9.2.114以上版本
- **下载地址**: [下载候鸟浏览器最新版](https://www.mbbrowser.com/download.php)

## HTTP模式启动

### 启动脚本示例 (apiserver.bat)
```bash
ApiServer.exe --port=8186 --account=<EMAIL> --app_id=7e147176e1d756eb03c0e18e7b640c23 --app_key=ExOTNjMWNWYzZTU5ZjYzMGEzZDU4ZDI3 --return=on --logs=on
```

**说明**: HTTP模式需配合使用CLI命令行启动客户端，可以用上面的bat文件示例修改对应的account、app_id和app_key并放到apiserver.exe的同级目录中。

## API服务器地址
- **地址1**: http://127.0.0.1:8186
- **地址2**: http://localhost:8186

## 1. 实时切换账号并重新登录

### 接口信息
- **Path**: /login
- **Method**: POST
- **Content-Type**: application/json
- **最大请求频率**: 10次/分钟

### 请求参数

| 参数名称 | 类型 | 必传 | 样例串/默认值 | 说明 |
|---------|------|------|--------------|------|
| Account | string | 是 | <EMAIL> | 用户凭证 Account |
| APP_ID | string | 是 | 7e147176e1d756eb03c0e18e7b640c23 | 用户凭证 app_Id |
| APP_KEY | string | 是 | kwMTYxNG_MzlkZjhlZmM2YWNiY2M5MTkw | 用户凭证 app_key |
| return | string | 否 | on (default) | on: 所有返回数据显示到CONSOLE界面<br>off: 不在CONSOLE界面显示 |
| logs | string | 否 | on (default) | on: 所有JSON数据写入API_LOG目录<br>off: 不写入LOG |
| hide | string | 否 | on (default) | on: 全自动化模式，屏蔽主面板<br>off: 半自动化模式，显示主面板<br>[仅在版本**********及以后有效] |

### 请求示例
```json
{
    "APP_ID": "7e147176e1d756eb03c0e18e7b640c23",
    "APP_KEY": "kwMTYxNG_MzlkZjhlZmM2YWNiY2M5MTkw",
    "Account": "<EMAIL>"
}
```

### 响应示例
```json
{
    "msg": "Login Success",
    "status": 0,
    "data": "Login Aaccount: <EMAIL>"
}
```

## 2. 退出APISERVER并关闭客户端

### 接口信息
- **Path**: /api/v1/quit
- **Method**: POST
- **Content-Type**: application/json
- **接口描述**: 停止并关闭APISERVER，关闭成功Code返回0

### 响应示例
```json
{
    "message": "APISERVER shut down.",
    "code": 0,
    "data": true
}
```

## 使用说明

1. **启动准备**: 确保已下载并安装候鸟浏览器客户端V3.9.2.114以上版本
2. **获取凭证**: 前往[使用须知](/api/help)查看如何获取APP_ID和APP_KEY
3. **启动服务**: 使用CLI命令行或bat脚本启动APISERVER
4. **接口调用**: 使用POST方式发送JSON数据到指定地址
5. **调试工具**: 建议使用[POSTMAN调试工具](/api/postman-example)进行接口测试

## 注意事项

1. HTTP模式需配合CLI命令行启动客户端
2. 需要配置正确的APP_ID和APP_KEY
3. 请求频率限制：最大10次/分钟
4. 支持实时切换账号，中间没有任何延迟
5. 可配置日志记录和界面显示选项

