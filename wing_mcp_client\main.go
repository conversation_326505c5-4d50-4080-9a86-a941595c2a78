package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"time"

	"github.com/google/uuid"
)

// MCPClient RAGFlow MCP 客户端
type MCPClient struct {
	BaseURL   string
	APIKey    string
	SessionID string
	RequestID int
	Client    *http.Client
}

// MCPRequest MCP 协议请求结构
type MCPRequest struct {
	JSONRPC string      `json:"jsonrpc"`
	ID      int         `json:"id"`
	Method  string      `json:"method"`
	Params  interface{} `json:"params,omitempty"`
}

// MCPResponse MCP 协议响应结构
type MCPResponse struct {
	JSONRPC string      `json:"jsonrpc"`
	ID      int         `json:"id"`
	Result  interface{} `json:"result,omitempty"`
	Error   *MCPError   `json:"error,omitempty"`
}

// MCPError MCP 错误结构
type MCPError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// InitializeParams 初始化参数
type InitializeParams struct {
	ProtocolVersion string                 `json:"protocolVersion"`
	Capabilities    map[string]interface{} `json:"capabilities"`
	ClientInfo      ClientInfo             `json:"clientInfo"`
}

// ClientInfo 客户端信息
type ClientInfo struct {
	Name    string `json:"name"`
	Version string `json:"version"`
}

// ToolCallParams 工具调用参数
type ToolCallParams struct {
	Name      string                 `json:"name"`
	Arguments map[string]interface{} `json:"arguments"`
}

// NewMCPClient 创建新的 MCP 客户端
func NewMCPClient(baseURL, apiKey string) *MCPClient {
	return &MCPClient{
		BaseURL:   baseURL,
		APIKey:    apiKey,
		SessionID: uuid.New().String(),
		RequestID: 0,
		Client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// getNextRequestID 获取下一个请求ID
func (c *MCPClient) getNextRequestID() int {
	c.RequestID++
	return c.RequestID
}

// sendRequest 发送 MCP 请求
func (c *MCPClient) sendRequest(method string, params interface{}) (*MCPResponse, error) {
	request := MCPRequest{
		JSONRPC: "2.0",
		ID:      c.getNextRequestID(),
		Method:  method,
		Params:  params,
	}

	jsonData, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %v", err)
	}

	url := fmt.Sprintf("%s/messages/?session_id=%s", c.BaseURL, c.SessionID)
	
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+c.APIKey)
	req.Header.Set("User-Agent", "Wing-MCP-Client/1.0")

	resp, err := c.Client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP错误 %d: %s", resp.StatusCode, string(body))
	}

	var mcpResp MCPResponse
	if err := json.Unmarshal(body, &mcpResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	if mcpResp.Error != nil {
		return nil, fmt.Errorf("MCP错误 %d: %s", mcpResp.Error.Code, mcpResp.Error.Message)
	}

	return &mcpResp, nil
}

// Initialize 初始化 MCP 连接
func (c *MCPClient) Initialize() error {
	params := InitializeParams{
		ProtocolVersion: "2024-11-05",
		Capabilities: map[string]interface{}{
			"tools": map[string]interface{}{},
		},
		ClientInfo: ClientInfo{
			Name:    "Wing-MCP-Client",
			Version: "1.0.0",
		},
	}

	resp, err := c.sendRequest("initialize", params)
	if err != nil {
		return fmt.Errorf("初始化失败: %v", err)
	}

	log.Printf("MCP 连接初始化成功: %+v", resp.Result)
	return nil
}

// ListTools 获取可用工具列表
func (c *MCPClient) ListTools() ([]map[string]interface{}, error) {
	resp, err := c.sendRequest("tools/list", nil)
	if err != nil {
		return nil, fmt.Errorf("获取工具列表失败: %v", err)
	}

	result, ok := resp.Result.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("工具列表响应格式错误")
	}

	tools, ok := result["tools"].([]interface{})
	if !ok {
		return nil, fmt.Errorf("工具列表格式错误")
	}

	var toolList []map[string]interface{}
	for _, tool := range tools {
		if toolMap, ok := tool.(map[string]interface{}); ok {
			toolList = append(toolList, toolMap)
		}
	}

	return toolList, nil
}

// CallTool 调用工具
func (c *MCPClient) CallTool(toolName string, arguments map[string]interface{}) (interface{}, error) {
	params := ToolCallParams{
		Name:      toolName,
		Arguments: arguments,
	}

	resp, err := c.sendRequest("tools/call", params)
	if err != nil {
		return nil, fmt.Errorf("调用工具失败: %v", err)
	}

	return resp.Result, nil
}

// QueryRAGFlow 查询 RAGFlow 知识库
func (c *MCPClient) QueryRAGFlow(question string, datasetIDs []string) (string, error) {
	arguments := map[string]interface{}{
		"question": question,
	}
	
	if len(datasetIDs) > 0 {
		arguments["dataset_ids"] = datasetIDs
	}

	result, err := c.CallTool("ragflow_retrieval", arguments)
	if err != nil {
		return "", fmt.Errorf("查询 RAGFlow 失败: %v", err)
	}

	// 解析结果
	if resultMap, ok := result.(map[string]interface{}); ok {
		if content, ok := resultMap["content"].([]interface{}); ok && len(content) > 0 {
			if firstContent, ok := content[0].(map[string]interface{}); ok {
				if text, ok := firstContent["text"].(string); ok {
					return text, nil
				}
			}
		}
	}

	return "", fmt.Errorf("无法解析查询结果")
}

// TestConnection 测试连接
func (c *MCPClient) TestConnection() error {
	url := fmt.Sprintf("%s/sse", c.BaseURL)
	
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return fmt.Errorf("创建测试请求失败: %v", err)
	}

	req.Header.Set("Authorization", "Bearer "+c.APIKey)

	resp, err := c.Client.Do(req)
	if err != nil {
		return fmt.Errorf("连接测试失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusOK || resp.StatusCode == http.StatusNotFound {
		return nil // 200 或 404 都表示服务器可达
	}

	return fmt.Errorf("连接测试失败，状态码: %d", resp.StatusCode)
}

func main() {
	// 配置
	serverURL := "http://58.49.146.17:9382"
	apiKey := "ragflow-UzYTlhMjIwNjI0YzExZjA4NzUyMDI0Mm"

	fmt.Println("🚀 Wing MCP Client for RAGFlow")
	fmt.Println("==============================")

	// 创建客户端
	client := NewMCPClient(serverURL, apiKey)
	fmt.Printf("服务器: %s\n", serverURL)
	fmt.Printf("会话ID: %s\n", client.SessionID)

	// 1. 测试连接
	fmt.Println("\n1️⃣ 测试连接...")
	if err := client.TestConnection(); err != nil {
		log.Fatalf("连接测试失败: %v", err)
	}
	fmt.Println("✅ 连接测试成功")

	// 2. 初始化 MCP 连接
	fmt.Println("\n2️⃣ 初始化 MCP 连接...")
	if err := client.Initialize(); err != nil {
		log.Fatalf("初始化失败: %v", err)
	}
	fmt.Println("✅ MCP 连接初始化成功")

	// 3. 获取工具列表
	fmt.Println("\n3️⃣ 获取工具列表...")
	tools, err := client.ListTools()
	if err != nil {
		log.Fatalf("获取工具列表失败: %v", err)
	}
	fmt.Printf("✅ 获取到 %d 个工具:\n", len(tools))
	for _, tool := range tools {
		name := tool["name"]
		description := tool["description"]
		fmt.Printf("   - %s: %s\n", name, description)
	}

	// 4. 测试查询
	fmt.Println("\n4️⃣ 测试查询 RAGFlow...")
	testQuestions := []string{
		"候鸟浏览器如何配置代理？",
		"RAGFlow是什么？",
		"如何创建知识库？",
	}

	for i, question := range testQuestions {
		fmt.Printf("\n📝 问题 %d: %s\n", i+1, question)
		
		answer, err := client.QueryRAGFlow(question, nil)
		if err != nil {
			fmt.Printf("❌ 查询失败: %v\n", err)
			continue
		}

		fmt.Printf("🤖 回答: %s\n", answer)
		
		// 添加延迟
		if i < len(testQuestions)-1 {
			time.Sleep(2 * time.Second)
		}
	}

	fmt.Println("\n🎉 测试完成！")
}
