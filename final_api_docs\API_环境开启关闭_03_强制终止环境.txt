# API_环境开启关闭_03_强制终止环境

## 功能描述
强制终止指定的浏览器环境，用于处理无法正常关闭的环境

## 所属模块
环境开启关闭

## API信息

- **Path**: /api/v1/browser/kill
- **Method**: POST
- **Content-Type**: application/json
- **服务器地址**: http://127.0.0.1:8186

## 请求参数

| 参数名称       | 类型    | 是否必传 | 示例/默认值                             | 说明       |
|------------|-------|------|-------------------------------------|----------|
| Session_ID | 字符串 | 是    | "373808cb37bd63f5f7d92415e736e85f"  | 环境ID     |

## 请求示例

```json
{
    "Session_ID": "373808cb37bd63f5f7d92415e736e85f"
}
```

## 成功响应

```json
{
    "message": "成功终止",
    "code": 0,
    "data": {
        "action": "停止环境ID",
        "status": 0
    }
}
```

## 使用说明

1. **使用场景**: 当环境无法正常关闭时使用
2. **特别注意**: Headless模式的环境无法用stop关闭，建议使用kill
3. **数据保存**: 强制终止可能导致数据丢失
4. **支持批量**: 可以同时强制终止多个环境
5. 建议使用[POSTMAN调试工具](/api/postman-example)进行接口测试

## 与正常关闭的区别

| 特性 | 正常关闭(stop) | 强制终止(kill) |
|------|---------------|----------------|
| 数据保存 | 会保存当前状态 | 可能丢失未保存数据 |
| 响应时间 | 较慢，等待环境响应 | 较快，立即终止 |
| 适用场景 | 环境正常运行时 | 环境无响应或卡死时 |
| Headless模式 | 可能失败 | 推荐使用 |

## 注意事项

1. **数据风险**: 强制终止可能导致数据丢失，请谨慎使用
2. **优先级**: 建议先尝试正常关闭，失败后再使用强制终止
3. **Headless模式**: 对于无头模式的环境，建议直接使用kill
4. **批量操作**: 支持同时强制终止多个环境

## 常见错误码

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| -20 | Headless 打开项无法用 stop 关闭，建议使用 kill 关闭 |
| -21 | 当前浏览器环境：强制关闭失败 |
| -22 | 未找到指定的环境SessionId |

## 相关链接

- [正常关闭环境](/api/browser/stop)
- [打开环境](/api/browser/start)
- [POSTMAN调试工具](/api/postman-example)
- [错误码对照表](/api/code)
