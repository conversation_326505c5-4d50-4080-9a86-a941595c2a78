﻿<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Window size="660,460" caption="0,0,0,40" roundcorner="5,5,5,5" showshadow="true" shadowsize="3" shadowposition="0,0" shadowcolor="#ff6272A4" fademode="true">
	<Include source="Default.xml" />

  <VerticalLayout bkcolor="#FF282A36">

    <HorizontalLayout height="37">

    	 <Button name="login" bkimage="icon.png" padding="8,6,0,0" width="28" height="28"/>
      <Label name="title" padding="6,4,0,0" text="" width="180" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" align="left" font="8"></Label>

      <Control />
      <Button name="minbtn" width="28" height="26" tooltip="最小化" normalimage="minbtn.png" hotimage="minbtn_hover.png" pushedimage="minbtnpush.png" />
      <Button name="closebtn" width="28" height="26"  tooltip="关闭" normalimage="closebtn.png" hotimage="closebtn_hover.png" pushedimage="closebtnpush.png" />

    </HorizontalLayout>

  <HorizontalLayout name="bkground" visible="true">

		<VerticalLayout >

      <HorizontalLayout height="10">
        <!--<VerticalLayout maxwidth="220">
          <Label name="lname" padding="22,6,0,0" text="请输入环境名称" maxwidth="180"   textcolor="#FF333333" hottextcolor="ffFF0000" font="8"></Label>
        </VerticalLayout>-->
      </HorizontalLayout>

      <VerticalLayout height="360" padding="20,0,0,10">

        <HorizontalLayout height="36" >
          <Button text="选择文件" name="btn_choose_file" padding="0,3,0,0" textcolor="#FF000000" height="32" width="400" font="8" texttooltip="true" endellipsis="true" bkimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;"></Button>

        </HorizontalLayout>
        <HorizontalLayout height="6" >
        </HorizontalLayout>
        <HorizontalLayout height="36" >
        <VerticalLayout >
          <Edit name="pluginfilepath" padding="0,0,20,10" text="" height="36" endellipsis="true" tipvaluecolor="#FF5f5f5f" nativebkcolor="#FFDCE1E7" borderround="3,3" bkcolor="#ffdce1e7" font="4" textpadding="10,0,20,0" maxchar="6000" multiline="false" textcolor="#ff333333" rich="false" transparent="false" />
        </VerticalLayout>
        </HorizontalLayout>

        <HorizontalLayout height="6" >
        </HorizontalLayout>
        <HorizontalLayout height="36" >
            <Label name="localpluginidl" padding="0,4,0,0" text="插件ID：" autocalcwidth="true" textcolor="#FF000000" hottextcolor="#ff000000" font="8"></Label>
            <!--<Label name="localarg1" padding="0,4,0,0" text="(必填)" autocalcwidth="true" textcolor="#FF616161" hottextcolor="#ff000000" font="8"></Label>-->
        </HorizontalLayout>
        <HorizontalLayout height="36" >
          <VerticalLayout >
            <Edit name="pluginid" padding="0,0,20,10" height="36" endellipsis="true" tipvaluecolor="#FF5f5f5f" nativebkcolor="#FFDCE1E7" borderround="3,3" bkcolor="#ffdce1e7" font="8" textpadding="10,0,20,0" maxchar="32" multiline="false" textcolor="#ff333333" rich="false" transparent="false" />
          </VerticalLayout>
        </HorizontalLayout>

        <HorizontalLayout height="6" >
        </HorizontalLayout>
        <HorizontalLayout height="36" >
          <VerticalLayout width="330">
            <HorizontalLayout>
            <Label name="localpluginnamel" padding="0,4,0,0" text="插件名称：" autocalcwidth="true" textcolor="#FF000000" hottextcolor="#ff000000" font="8"></Label>
              <!--<Label name="localarg1" padding="0,4,0,0" text="(必填)" autocalcwidth="true" textcolor="#FF616161" hottextcolor="#ff000000" font="8"></Label>-->
            </HorizontalLayout>
          </VerticalLayout>
          <VerticalLayout width="210">
            <HorizontalLayout>
            <Label name="localpluginversionl" padding="30,4,0,0" text="插件版本：" autocalcwidth="true" textcolor="#FF000000" hottextcolor="#ff000000" font="8"></Label>
              <!--<Label name="localarg1" padding="0,4,0,0" text="(必填)" autocalcwidth="true" textcolor="#FF616161" hottextcolor="#ff000000" font="8"></Label>-->
            </HorizontalLayout>
          </VerticalLayout>
        </HorizontalLayout>

        <HorizontalLayout height="36" >
          <VerticalLayout width="350">
            <Edit name="pluginname" padding="0,0,20,10" height="36" endellipsis="true" tipvaluecolor="#FF5f5f5f" nativebkcolor="#FFDCE1E7" borderround="3,3" bkcolor="#ffdce1e7" font="8" textpadding="10,0,20,0" maxchar="6000" multiline="false" textcolor="#ff333333" rich="false" transparent="false" />
          </VerticalLayout>
          <VerticalLayout width="210">
            <Edit name="pluginversion" padding="10,0,0,10" text="" height="36" endellipsis="true" tipvaluecolor="#FF5f5f5f" nativebkcolor="#FFDCE1E7" borderround="3,3" bkcolor="#ffdce1e7" font="8" textpadding="10,0,20,0" maxchar="6000" multiline="false" textcolor="#ff333333" rich="false" transparent="false" />
          </VerticalLayout>
        </HorizontalLayout>

        <HorizontalLayout height="6" >
        </HorizontalLayout>
        <HorizontalLayout height="36" >
            <Label name="localplugindesl" padding="0,4,0,0" text="插件描述：" autocalcwidth="true" textcolor="#FF000000" hottextcolor="#ff000000" font="8"></Label>
            <Label name="localarg1" padding="0,4,0,0" text="(选填)" autocalcwidth="true" textcolor="#FF616161" hottextcolor="#ff000000" font="8"></Label>
        </HorizontalLayout>
        <HorizontalLayout height="80" >
          <VerticalLayout >
            <!--<Edit name="plugindes" padding="0,0,20,10" height="36" tipvaluecolor="#FF5f5f5f" nativebkcolor="#FFDCE1E7" borderround="3,3" bkcolor="#ffdce1e7" font="8" textpadding="10,0,20,0" maxchar="6000" multiline="false" textcolor="#ff333333" rich="false" transparent="false" />-->
            <RichEdit name="plugindes" padding="0,0,20,10" tipvaluecolor="#FF333333" borderround="3,3" bkcolor="#ffdce1e7" font="0" textpadding="10,8,10,0" tipvalue="" multiline="true" textcolor="#ff333333" rich="true" transparent="false" vscrollbar="true">
            </RichEdit>
          </VerticalLayout>
        </HorizontalLayout>

        <!--<HorizontalLayout height="6" >
        </HorizontalLayout>
        <HorizontalLayout height="36" >
          <VerticalLayout width="110">
            <Label name="localpluginfilepathl" padding="40,4,0,0" text="插件路径：" textcolor="#FF616161" hottextcolor="#ff000000" align="left" font="8"></Label>
          </VerticalLayout>
          <VerticalLayout width="330">
            <Edit name="pluginfilepath" padding="21,0,0,10" height="36" endellipsis="true" width="300" tipvaluecolor="#FF5f5f5f" nativebkcolor="#FFDCE1E7" borderround="3,3" bkcolor="#ffdce1e7" font="8" textpadding="10,0,20,0" maxchar="6000" multiline="false" textcolor="#ff333333" rich="false" transparent="false" />
          </VerticalLayout>
          <Button text="选择文件" name="btn_choose_file" padding="0,3,0,0" textcolor="#FFF8F8F2" height="32" width="120" font="8" texttooltip="true" endellipsis="true" bkcolor="#FF282A36" bordersize="1" bordercolor="#FF6272A4" borderround="5,5" hotbkcolor="#FF44475A"></Button>
        </HorizontalLayout>-->

			</VerticalLayout>

      <HorizontalLayout inset="0,0,0,0" height="373">

				</HorizontalLayout>






		</VerticalLayout>


	</HorizontalLayout>

		  <!--<HorizontalLayout inset="0,0,0,0" height="33">
        	  <Label name="confignamearea_tiptitle1" padding="12,6,0,0" text="提示：网页登录地址请填写网站的登录页面URL地址，网站类型不清楚可不选。" width="480" textcolor="#ff333333" hottextcolor="#ffaaaaaa" font="8"></Label>
				</HorizontalLayout>-->

    <HorizontalLayout height="52" bkcolor="#ffe9e9e9">
         <Control />
      <!--<VerticalLayout width="420">

       </VerticalLayout>-->
      <Control />
        <VerticalLayout width="360">
      		<Control />
          <Button text="确定" name="btnok" enabled="false"  padding="16,2,0,0" width="340" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	bkimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
          <Control />
       </VerticalLayout>
      <Control width="20"/>
    </HorizontalLayout>
  </VerticalLayout>
</Window>
