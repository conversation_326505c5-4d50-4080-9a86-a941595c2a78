<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>/api/group</title>
  <style>
/* 基础重置与排版 */
body {
  margin: 0;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.7;
  color: #333;
  background-color: #fff;
  max-width: 960px;
  margin: 0 auto;
  box-sizing: border-box;
}

/* 段落 */
p {
  margin: 1em 0;
}

/* 标题 */
h1, h2, h3, h4, h5, h6 {
  margin: 1.5em 0 0.8em;
  color: #2c3e50;
  font-weight: 600;
  line-height: 1.3;
}

h1 { font-size: 2em; border-bottom: 1px solid #eee; padding-bottom: 0.5em; }
h2 { font-size: 1.6em; }
h3 { font-size: 1.4em; }
h4 { font-size: 1.2em; }

/* 列表 */
ul, ol {
  margin: 1em 0;
  padding-left: 2em;
}

li {
  margin: 0.4em 0;
}

/* 引用块 */
blockquote {
  margin: 1.5em 0;
  padding: 0.8em 1.5em;
  background-color: #f9f9f9;
  border-left: 4px solid #ddd;
  color: #666;
  font-style: italic;
  border-radius: 0 4px 4px 0;
}

/* 代码行内 */
code {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  background-color: #f3f4f6;
  color: #e9602d;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-size: 0.95em;
  white-space: nowrap;
}

/* 代码块 */
pre {
  margin: 1.5em 0;
  padding: 1.2em;
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  overflow-x: auto;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
}

pre code {
  background: none;
  color: inherit;
  padding: 0;
  font-size: inherit;
  white-space: pre;
  display: block;
}

/* 表格 */
table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5em 0;
  font-size: 14px;
  overflow: hidden;
  box-shadow: 0 0 0 1px #e0e0e0;
  border-radius: 6px;
}

th, td {
  padding: 10px 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

th {
  background-color: #f8f9fa;
  color: #2c3e50;
  font-weight: 600;
  white-space: nowrap;
}

tr:nth-child(even) {
  background-color: #f9f9fb;
}

tr:hover {
  background-color: #f0f5ff;
}

/* 链接 */
a {
  color: #1a73e8;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

/* 分隔线 */
hr {
  border: 0;
  height: 1px;
  background: #ddd;
  margin: 2em 0;
}

/* 图片 */
img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 1em 0;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
  </style>
</head>
<body>
  <h2>分组管理</h2> <div class="api-tabs ant-tabs ant-tabs-top ant-tabs-card ant-tabs-no-animation"><div class="ant-tabs-bar ant-tabs-top-bar ant-tabs-card-bar"><div class="ant-tabs-nav-container"><span class="ant-tabs-tab-prev ant-tabs-tab-btn-disabled"><span class="ant-tabs-tab-prev-icon"><i class="anticon anticon-left ant-tabs-tab-prev-icon-target"><svg class=""><path></path></svg></i></span></span><span class="ant-tabs-tab-next ant-tabs-tab-btn-disabled"><span class="ant-tabs-tab-next-icon"><i class="anticon anticon-right ant-tabs-tab-next-icon-target"><svg class=""><path></path></svg></i></span></span><div class="ant-tabs-nav-wrap"><div class="ant-tabs-nav-scroll"><div class="ant-tabs-nav ant-tabs-nav-animated"><div><div class="ant-tabs-tab">1、获取环境分组列表</div><div class="ant-tabs-tab">2、新建环境分组</div><div class="ant-tabs-tab">3、删除环境分组</div><div class="ant-tabs-tab-active ant-tabs-tab">4、将指定环境从指定分组转移到另一个分组</div></div><div class="ant-tabs-ink-bar ant-tabs-ink-bar-animated"></div></div></div></div></div></div><div></div><div class="ant-tabs-content ant-tabs-content-no-animated ant-tabs-top-content ant-tabs-card-content"><div class="ant-tabs-tabpane ant-tabs-tabpane-inactive"><ul><li><p>Path：/api/v1/group/list</p></li> <li><p>Method：POST</p></li> <li><p>Content-Type：application/json</p></li> <li><p>接口描述：返回当前帐户凭据中环境所有分组列表。</p></li></ul> <blockquote><p>请求参数</p></blockquote> <div class="table"><table><thead><tr><td>参数名称</td> <td>类型</td> <td>必传</td> <td>样例串/默认值</td> <td>说明</td></tr></thead> <tbody><tr><td>Session_ID</td> <td>string</td> <td>否</td> <td>373808cb37bd63f5f7d92415e736e85f</td> <td>返回指定环境ID所属分组</td></tr> <tr><td>Session_Name</td> <td>string</td> <td>否</td> <td>环境名称一</td> <td>返回指定环境名称所属分组</td></tr></tbody></table></div> <p>注 1：不提供参数，默认返回所有分组名称。</p> <p>注 2：通过分组名称查询分组下所有环境ID参见接口：<a class=""><code>Path：/api/v1/session/listid</code></a></p> <blockquote><p>请求示例</p></blockquote> <div class="code-view"><pre><code>{ "Session_ID": "373808cb37bd63f5f7d92415e736e85f", "Session_Name": "环境名称一" }</code></pre> </div> <blockquote><p>返回参数</p></blockquote> <div class="code-view"><pre><code>{ "msg": "Group List Return Success", "code": 0, "data": { "Group_Name_List": "我的分组一, 我的分组二, 我的分组三" //分组名称 } }</code></pre> </div></div><div class="ant-tabs-tabpane ant-tabs-tabpane-inactive"><ul><li><p>Path：/api/v1/group/create</p></li> <li><p>Method：POST</p></li> <li><p>Content-Type：application/json</p></li> <li><p>接口描述：创建环境新分组，名称不能与已创建的分组名称重复。</p></li></ul> <blockquote><p>请求参数</p></blockquote> <div class="table"><table><thead><tr><td>参数名称</td> <td>类型</td> <td>必传</td> <td>样例串/默认值</td> <td>说明</td></tr></thead> <tbody><tr><td>Group_Name</td> <td>string</td> <td>是</td> <td>分组名称</td> <td>新创建的分组名称</td></tr></tbody></table></div> <blockquote><p>请求示例</p></blockquote> <div class="code-view"><pre><code>{ "Group_Name": "分组名称" }</code></pre> </div> <blockquote><p>返回参数</p></blockquote> <div class="code-view"><pre><code>{ "msg": "New Group Create Success", "code": 0, "data": true }</code></pre> </div></div><div class="ant-tabs-tabpane ant-tabs-tabpane-inactive"><ul><li><p>Path：/api/v1/group/del</p></li> <li><p>Method：POST</p></li> <li><p>Content-Type：application/json</p></li> <li><p>接口描述：批量删除指定环境分组，删除成功返回true。</p></li></ul> <blockquote><p>请求参数</p></blockquote> <div class="table"><table><thead><tr><td>参数名称</td> <td>类型</td> <td>必传</td> <td>样例串/默认值</td> <td>说明</td></tr></thead> <tbody><tr><td>Group_Name</td> <td>array</td> <td>是</td> <td>分组名称</td> <td>单个或多个分组名称</td></tr></tbody></table></div> <p>注 1：删除环境分组前需要先删除此分组下所有环境，或将此分组下所有环境转移到其它分组。否则删除非空分组会失败。</p> <p>注 2：查询分组下所有环境参见接口：<a class=""><code>Path：/api/v1/session/listid</code></a></p> <p>注 3：转移分组下所有环境到其它分组参见接口：<a class="nuxt-link-exact-active nuxt-link-active"><code>Path：/api/v1/group/move_session</code></a></p> <blockquote><p>请求示例</p></blockquote> <div class="code-view"><pre><code>{ "Group_Name": "分组名称" }</code></pre> </div> <blockquote><p>返回参数</p></blockquote> <div class="code-view"><pre><code>{ "msg": "Group Delete Success", "code": 0, "data": true }</code></pre> </div></div><div class="ant-tabs-tabpane ant-tabs-tabpane-active"><div></div><ul><li><p>Path：/api/v1/group/move_session</p></li> <li><p>Method：POST</p></li> <li><p>Content-Type：application/json</p></li> <li><p>接口描述：批量从指定分组中转移指定环境到另一个分组，转移成功Status返回0。</p></li></ul> <blockquote><p>请求参数</p></blockquote> <div class="table"><table><thead><tr><td>参数名称</td> <td>类型</td> <td>必传</td> <td>样例串/默认值</td> <td>说明</td></tr></thead> <tbody><tr><td>Session_ID</td> <td>array</td> <td>是</td> <td>["ce4c125179fbe98ba362384115a6f82c", "ba404bdda87bdf5a7c5ecff2a4a260ea"]</td> <td>单个或多个环境ID</td></tr> <tr><td>Orgin_Group_Name</td> <td>string</td> <td>否</td> <td>我的分组一</td> <td>环境当前分组名称</td></tr> <tr><td>Target_Group_Name</td> <td>string</td> <td>是</td> <td>我的分组二</td> <td>环境转移到目标分组名称</td></tr></tbody></table></div> <blockquote><p>请求示例</p></blockquote> <div class="code-view"><pre><code>{ "Session_ID": ["ce4c125179fbe98ba362384115a6f82c", "ba404bdda87bdf5a7c5ecff2a4a260ea"], "Orgin_Group_Name": "我的分组一", "Target_Group_Name": "我的分组二" }</code></pre> </div> <blockquote><p>返回参数</p></blockquote> <div class="code-view"><pre><code>{ "msg": ""Session move Success", "code": 0, "data": [{ "Session_ID": "ce4c125179fbe98ba362384115a6f82c", "Orgin_Group_Name": "我的分组一", "Target_Group_Name": "我的分组二", "status": "0" }, { "Session_ID": "ba404bdda87bdf5a7c5ecff2a4a260ea", "Orgin_Group_Name": "我的分组一", "Target_Group_Name": "我的分组二", "status": "0" }] }</code></pre> </div><div></div></div></div><div></div></div> <p><a class="ant-btn ant-btn-primary">使用POSTMAN调试此接口</a></p>
</body>
</html>