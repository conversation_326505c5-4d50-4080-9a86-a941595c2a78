﻿<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Window size="1240,590" sizebox="4,4,4,4" caption="0,0,0,50" mininfo="1100,640" roundcorner="5,5,5,5" showshadow="true" shadowsize="3" shadowposition="0,0" shadowcolor="#ff6272A4" fademode="true">
  <Include source="Default.xml" />

  <VerticalLayout width="953" height="590" bkcolor="#FF282A36">
    <HorizontalLayout height="37">

    	 <Button name="login" bkimage="icon.png" padding="8,6,0,0" width="28" height="28"/>
      <Label name="session_muti_control_title" padding="6,4,0,0" text="环境群控管理器" width="260" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" align="left" font="8"></Label>

      <Control />
      <Button name="minbtn" width="28" height="26" tooltip="最小化" normalimage="minbtn.png" hotimage="minbtn_hover.png" pushedimage="minbtnpush.png" />
      <Button name="maxbtn" width="28" height="26" tooltip="最大化" normalimage="maxbtn.png" hotimage="maxbtn_hover.png" pushedimage="maxbtnpush.png" />
      <Button name="restorebtn" visible="false" width="28" height="26" tooltip="还原" normalimage="restorebtn.png" hotimage="restorebtn_hover.png" pushedimage="restorebtnpush.png" />
      <Button name="closebtn" width="28" height="26"  tooltip="关闭" normalimage="closebtn.png" hotimage="closebtn_hover.png" pushedimage="closebtnpush.png" />

    </HorizontalLayout>
<!--<HorizontalLayout name="bkground">-->


 <HorizontalLayout name="loading_data" bkcolor="#FF44475A" visible="false">

	    <VerticalLayout >

					     <HorizontalLayout name="loading_data" height="240">
					    	 <Control />
					    		<GifAnim name="data_loading" bkimage="dataloading.gif" height="200" width="200" padding="0,40,0,0" auto="true"/>
                 <Control name="success" visible="false" padding="0,100,0,0"  bkimage="success.png" width="120" height="120" align="center" />
					    	 <Control />
					     </HorizontalLayout>


					     <HorizontalLayout height="30" >
					    	 <Control />
					    		  <Label name="data_percent" text="55%" width="300" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" align="center" font="10"></Label>
					    	 <Control />
					     </HorizontalLayout>

					     <HorizontalLayout height="60" >
					    	 <Control />
					    		  <Label name="process_description" text="会话环境正在版本验证中，请稍侯.. " width="953" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" align="center" font="8"></Label>
					    	 <Control />
					     </HorizontalLayout>

              <HorizontalLayout height="40" >
              </HorizontalLayout>

              <HorizontalLayout name="backarea" height="60" visible="false">
                <Control />
                <Button text="返回" name="back" width="120" height="30" textcolor="#FFF8F8F2" disabledtextcolor="#FF6272A4" align="center" font="14"	bkcolor="#FF6272A4" hotbkcolor="#FF8BE9FD" borderround="5,5" />
                <Control width="100" />
                <Button text="退出" name="closewnd1" width="120" height="30" textcolor="#FFF8F8F2" disabledtextcolor="#FF6272A4" align="center" font="14"	bkcolor="#FF6272A4" hotbkcolor="#FF8BE9FD" borderround="5,5" />
                <Control />
              </HorizontalLayout>
      </VerticalLayout>

 </HorizontalLayout>

    <VerticalLayout name="verify_area" bkcolor="#FF44475A" visible="false">

      <HorizontalLayout bkcolor="#FF282A36">
        <List name="list_verify" scrollwheel="true" reselect="true" bordersize="1,1,1,1" itembkcolor="#FF282A36" itemselectedbkcolor="#FF44475A" itemhotbkcolor="#FF6272A4" bordercolor="#FF6272A4" vscrollbar="true">
          <ListHeader height="36" bordersize="1" bordercolor="#FF6272A4" bkcolor="#FF44475A">
            <ListHeaderItem text="操作" name="header_device_choice" width="60" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
            <ListHeaderItem text="会话名称" name="header_name" width="156" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
            <ListHeaderItem text="注释" name="header_desc" width="380" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
            <ListHeaderItem text="创建时间" name="header_ctime" width="150" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
            <ListHeaderItem text="最近使用" name="header_utime" width="150" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
            <ListHeaderItem text="本地版本号" name="header_lversion" width="157" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
            <ListHeaderItem text="云端版本号" name="header_sversion" width="157" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
            <ListHeaderItem text="状态报告" name="header_status" width="200" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
            <ListHeaderItem text="同步建议" name="header_recommend" width="200" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
          </ListHeader>
        </List>
      </HorizontalLayout>

        <HorizontalLayout height="52">
          <HorizontalLayout padding="20,0,0,0" >
            <CheckBox name="opt_checkAllv" text="" textpadding="57,1,0,0" selected="false"  visible="true" padding="3,16,0,0" height="18" width="16" normalimage="file='list_check_normal.png'" hotimage="list_check_hover.png" selectedimage="file='list_checked_actvice_normal.png'" ></CheckBox>
            <Button name="refreshverify" enabled="false" padding="50,18,0,0" align="left" height="20" width="50" text="刷新" font="5" textcolor="#FF005ed3" hottextcolor="#FFff4444" />
          </HorizontalLayout>
          <Control />
          <Button text="返回" name="backmain" padding="0,10,0,0"  textpadding="10,0,10,0" tooltip="" endellipsis="true" width="160" height="30" textcolor="#FFF8F8F2" disabledtextcolor="#FF6272A4" align="center" font="14"	bkcolor="#FF6272A4" hotbkcolor="#FF8BE9FD" borderround="5,5" />
          <Control width="50" />
          <Button text="上传环境" name="upload" padding="0,10,0,0" textpadding="10,0,10,0"  tooltip="" endellipsis="true" enabled="false" width="160" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	normalimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
          <Control width="50" />
          <Button text="下载环境" name="download" padding="0,10,0,0" textpadding="10,0,10,0" tooltip="" endellipsis="true" enabled="false" width="160" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	normalimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
          <Control width="50" />
          <Button text="退出" name="closewnd2" padding="0,10,0,0" textpadding="10,0,10,0"  tooltip="" endellipsis="true" width="160" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	normalimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
          <Control width="60" />
        </HorizontalLayout>

     </VerticalLayout>



		<VerticalLayout name="data" visible="true">


			<HorizontalLayout height="56" >
				        <VerticalLayout width="271">
          <Combo name="group" bordersize="0" padding="21,10,0,10" width="250" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" bkcolor="#ffdce1e7"
              normalimage="file='Profile\Proxy_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\proxy_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\proxy_Combox_Click.png' corner='5,5,25,10'"
              combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
              itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0" textpadding="0,0,20,0" endellipsis="true">
          </Combo>
        </VerticalLayout>
			    	 <VerticalLayout width="240">
               <Combo name="searchlist" reselect="true" dropboxsize="0,450" bordersize="0" padding="21,10,0,10" width="220" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="4,2,0,0" bkcolor="#ffdce1e7"
														normalimage="file='Profile\Setting_Combox_Normal_big_large.png' corner='5,5,30,5'" hotimage="file='Profile\Setting_Combox_Hover_large.png' corner='5,5,30,5'" pushedimage="file='Profile\Setting_Combox_Click_large.png' corner='5,5,25,10'"
												combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,30,5'"
												itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0">
               </Combo>
				         <RichEdit name="session_search" pos="20,10,0,10" height="36" width="178" tipvaluecolor="#FF333333" borderround="7,7" bkcolor="#ffdce1e7" font="5" textpadding="10,10,20,0" maxchar="300" tipvalue="请输入关键字查找会话.." multiline="false" textcolor="#ff333333" rich="false" transparent="false" float="true">
				      </RichEdit>
				     </VerticalLayout>
				     <VerticalLayout width="30">
				        <CheckBox name="opt_searchl" selected="false"  visible="true" padding="10,26,0,0" height="18" width="16" normalimage="file='list_check_normal.png'" hotimage="list_check_hover.png" selectedimage="file='list_checked_actvice_normal.png'" ></CheckBox>
				     </VerticalLayout>
				     <VerticalLayout width="280">
				        <Label name="searchl" padding="4,16,0,0" text="搜索包含在会话中的关键词" width="280" textcolor="#FF616161" hottextcolor="#ff000000" align="left" font="16"></Label>
				     </VerticalLayout>

        <Control width="10"/>
      </HorizontalLayout>


      <HorizontalLayout>

      	<VerticalLayout width="580" inset="21,0,0,0">
        	<List name="list_session_manager" scrollwheel="true" reselect="true" bordersize="1,1,1,1" itembkcolor="#FFffffff" itemselectedbkcolor="#FFe0e0e0" itemhotbkcolor="#FFf2f2f2" bordercolor="#FFD9DADB" vscrollbar="true">
						<ListHeader height="36" bordersize="1" bordercolor="#FFD7D7D7" bkcolor="#FFF9F9FA">
							<ListHeaderItem text="操作" name="header_device_choice" width="60" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="会话名称" name="header_name" width="156" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="操作平台" name="header_system" width="100" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="代理类型" name="header_proxytype" width="120" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="代理主机" name="header_proxy" width="120" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="注释" name="header_backup_datetime" width="100" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="最近使用" name="header_utime" width="100" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
						</ListHeader>
          </List>
				</VerticalLayout>

				<VerticalLayout inset="20,0,0,0">
        		  <VerticalLayout>
					        <HorizontalLayout height="36">
					            <Label name="muti_control_title" padding="0,0,0,0" autocalcwidth="true" maxwidth="200" textpadding="8,0,8,0" texttooltip="true" endellipsis="true" text="群控操作台" align="center" borderround="7,7" font="11" textcolor="#ff373b40"></Label>
					            <CheckBox name="check_apiprovider" padding="6,6,0,10" width="52" height="24"  normalimage="key_off.png" selectedimage="key_on.png" disabledimage="key_off.png" />
					            <Control />
					            <Button name="env_run_reset_to_default_btn" bkcolor="#ffebeff1" hotbkcolor="#ffdee1e3" padding="0,4,0,0" width="140" height="28" textpadding="8,0,8,0" texttooltip="true" endellipsis="true" text="浮动到桌面" tooltip="在桌面显示浮动窗" align="center" borderround="7,7" font="8" textcolor="#ff949698"></Button>
					            <Control width="10"/>
					        </HorizontalLayout>

					        <HorizontalLayout inset="0,0,22,0" height="10"></HorizontalLayout>

 	                <HorizontalLayout inset="0,0,22,0" height="30">
							      	<Button name="finger_recorder_btn1" padding="8,11,0,0" text="窗口管理" height="22" textpadding="10,0,10,0" texttooltip="true" endellipsis="true" bkcolor="#FF006fdf" hotbkcolor="#ff0055ab"	 borderround="7,7" autocalcwidth="true" maxwidth="200" textcolor="#FFe6ebf2" hottextcolor="#FFFFFFFF" pushedtextcolor="FFe6ebf2" align="center" font="5"></Button>
							        <Button name="his_version_btn1" padding="8,11,0,0" text="文本管理" height="22" textpadding="10,0,10,0" texttooltip="true" endellipsis="true" bkcolor="#FF006fdf" hotbkcolor="#ff0055ab"	 borderround="7,7" autocalcwidth="true" maxwidth="200" textcolor="#FFe6ebf2" hottextcolor="#FFFFFFFF" pushedtextcolor="FFe6ebf2" align="center" font="5"></Button>
							      	<Button name="api_proxyip_btn1" padding="8,11,0,0" text="标签页管理" height="22" textpadding="10,0,10,0" texttooltip="true" endellipsis="true" bkcolor="#FF006fdf" hotbkcolor="#ff0055ab"	 borderround="7,7" autocalcwidth="true" maxwidth="200" textcolor="#FFe6ebf2" hottextcolor="#FFFFFFFF" pushedtextcolor="FFe6ebf2" align="center" font="5"></Button>
							    </HorizontalLayout>
							    <HorizontalLayout inset="0,-1,22,0" height="2">
							        <Control height="2" bkcolor="#FF006fdf"/>
							    </HorizontalLayout>

					        <HorizontalLayout inset="0,0,22,0" height="10"></HorizontalLayout>

					 <TabLayout name="muti_control_tab" selectedid="0">

               <!--窗口管理-->
					       <VerticalLayout inset="0,0,0,0">

					        <HorizontalLayout height="52">
		          				<Button name="btn_WndSizeSame" padding="8,8,0,0" height="31" width="140" text="统一窗口大小" textpadding="2,6,0,6" texttooltip="true" endellipsis="true" align="center" font="5" borderround="7,7" textcolor="#FFe6ebf2" hottextcolor="#FFFFFFFF" pushedtextcolor="FFe6ebf2" disabledtextcolor="#ffcccccc" bkcolor="#FF006fdf" hotbkcolor="#ff0055ab"	/>
		          				<Button name="btn_showWnd" padding="26,8,0,0" height="31" width="140" text="显示环境窗口" textpadding="2,6,0,6" texttooltip="true" endellipsis="true" align="center" font="5" borderround="7,7" textcolor="#FFe6ebf2" hottextcolor="#FFFFFFFF" pushedtextcolor="FFe6ebf2" disabledtextcolor="#ffcccccc" bkcolor="#FF006fdf" hotbkcolor="#ff0055ab"	/>
									</HorizontalLayout>

					        <HorizontalLayout inset="0,0,22,0" height="20"></HorizontalLayout>
					        <HorizontalLayout height="36">
					        	<Label name="muti_control_title" padding="0,0,0,0" autocalcwidth="true" maxwidth="200" textpadding="8,0,8,0" texttooltip="true" endellipsis="true" text="环境窗口排列" align="center" borderround="7,7" font="8" textcolor="#ff373b40"></Label>
					          <Button name="ip_provider_help" padding="8,10,0,0" height="15" width="15" tooltip="Proxy IP Provisioning Platform Provider" normalimage="file='helpbtn.png'" hotimage="file='helpbtn_hover.png'" pushedimage="file='helpbtn_push.png'"/>
					        </HorizontalLayout>

					        <HorizontalLayout height="40">
					        	<Label name="muti_control_title" padding="26,0,0,0" autocalcwidth="true" maxwidth="100" textpadding="8,0,8,0" texttooltip="true" endellipsis="true" text="当前显示器" align="center" borderround="7,7" font="8" textcolor="#ff373b40"></Label>
										<VerticalLayout width="360">
											        <Combo name="ip_provider_list2" reselect="true" dropboxsize="0,320" bordersize="0" padding="10,2,0,10" width="320" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="4,2,0,0" bkcolor="#ffdce1e7"
											            normalimage="file='Profile\Setting_Combox_Normal_big_large.png' corner='25,5,32,7'" hotimage="file='Profile\Setting_Combox_Hover_large.png' corner='25,5,32,7'" pushedimage="file='Profile\Setting_Combox_Click_large.png' corner='25,5,32,7'"
											            combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='25,5,32,7'"
											            itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0">
											        </Combo>
										</VerticalLayout>
									</HorizontalLayout>
					        <HorizontalLayout height="22">
		          				<CheckBox name="Screen_rude_switch_main" width="18" height="18"  padding="124,4,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="true" />
		          				<Label padding="6,3,10,0" textpadding="0,2,20,0" name="Screen_rude_switch_dec" text="根据显示器的分辨率自动排列所选窗口" texttooltip="true" endellipsis="true" autocalcwidth="true" maxwidth="400" textcolor="#FF333333" hottextcolor="ffFF0000" font="3"></Label>
									</HorizontalLayout>

					        <HorizontalLayout height="26">
					        	<Label name="control_rule_title" padding="0,0,0,0" autocalcwidth="true" maxwidth="200" textpadding="8,0,8,0" texttooltip="true" endellipsis="true" text="排列方式" align="center" borderround="7,7" font="8" textcolor="#ff373b40"></Label>
					        </HorizontalLayout>
					        <HorizontalLayout height="26">
					        	<Label name="win_start_posi_title" padding="28,0,0,0" autocalcwidth="true" maxwidth="200" textpadding="8,0,8,0" texttooltip="true" endellipsis="true" text="起始位置" align="center" borderround="7,7" font="3" textcolor="#ff373b40"></Label>
					        	<Label name="win_start_posi_x_title" padding="12,0,0,0" autocalcwidth="true" maxwidth="200" textpadding="8,0,8,0" texttooltip="true" endellipsis="true" text="X" align="center" borderround="7,7" font="3" textcolor="#ff373b40"></Label>
					        	<RichEdit name="win_start_posi_x" padding="5,3,0,10" height="20" width="60" tipvaluecolor="#FF333333" borderround="7,7" bkcolor="#ffdce1e7" font="0" textpadding="6,2,0,0" maxchar="5" tipvalue="0" multiline="false" textcolor="#ff333333" rich="false" transparent="false"></RichEdit>
					        	<Label name="win_start_posi_y_title" padding="12,0,0,0" autocalcwidth="true" maxwidth="200" textpadding="8,0,8,0" texttooltip="true" endellipsis="true" text="Y" align="center" borderround="7,7" font="3" textcolor="#ff373b40"></Label>
					        	<RichEdit name="win_start_posi_y" padding="5,3,0,10" height="20" width="60" tipvaluecolor="#FF333333" borderround="7,7" bkcolor="#ffdce1e7" font="0" textpadding="6,2,0,0" maxchar="5" tipvalue="0" multiline="false" textcolor="#ff333333" rich="false" transparent="false"></RichEdit>
					        </HorizontalLayout>
					        <HorizontalLayout height="26">
					        	<Label name="win_size_title" padding="28,0,0,0" autocalcwidth="true" maxwidth="200" textpadding="8,0,8,0" texttooltip="true" endellipsis="true" text="窗口大小" align="center" borderround="7,7" font="3" textcolor="#ff373b40"></Label>
					        	<Label name="win_size_w_title" padding="10,0,0,0" autocalcwidth="true" maxwidth="200" textpadding="8,0,8,0" texttooltip="true" endellipsis="true" text="宽" align="center" borderround="7,7" font="3" textcolor="#ff373b40"></Label>
					        	<RichEdit name="win_size_w" padding="2,3,0,10" height="20" width="60" tipvaluecolor="#FF333333" borderround="7,7" bkcolor="#ffdce1e7" font="0" textpadding="6,2,0,0" maxchar="5" tipvalue="500" multiline="false" textcolor="#ff333333" rich="false" transparent="false"></RichEdit>
					        	<Label name="win_size_h_title" padding="10,0,0,0" autocalcwidth="true" maxwidth="200" textpadding="8,0,8,0" texttooltip="true" endellipsis="true" text="高" align="center" borderround="7,7" font="3" textcolor="#ff373b40"></Label>
					        	<RichEdit name="win_size_h" padding="2,3,0,10" height="20" width="60" tipvaluecolor="#FF333333" borderround="7,7" bkcolor="#ffdce1e7" font="0" textpadding="6,2,0,0" maxchar="5" tipvalue="200" multiline="false" textcolor="#ff333333" rich="false" transparent="false"></RichEdit>
					        </HorizontalLayout>
					        <HorizontalLayout height="26">
					        	<Label name="win_spacing_size_title" padding="28,0,0,0" autocalcwidth="true" maxwidth="200" textpadding="8,0,8,0" texttooltip="true" endellipsis="true" text="窗口间距" align="center" borderround="7,7" font="3" textcolor="#ff373b40"></Label>
					        	<Label name="win_spacing_w_title" padding="10,0,0,0" autocalcwidth="true" maxwidth="200" textpadding="8,0,8,0" texttooltip="true" endellipsis="true" text="横" align="center" borderround="7,7" font="3" textcolor="#ff373b40"></Label>
					        	<RichEdit name="win_spacing_w" padding="2,3,0,10" height="20" width="60" tipvaluecolor="#FF333333" borderround="7,7" bkcolor="#ffdce1e7" font="0" textpadding="6,2,0,0" maxchar="5" tipvalue="0" multiline="false" textcolor="#ff333333" rich="false" transparent="false"></RichEdit>
					        	<Label name="win_spacing_h_title" padding="10,0,0,0" autocalcwidth="true" maxwidth="200" textpadding="8,0,8,0" texttooltip="true" endellipsis="true" text="纵" align="center" borderround="7,7" font="3" textcolor="#ff373b40"></Label>
					        	<RichEdit name="win_spacing_h" padding="2,3,0,10" height="20" width="60" tipvaluecolor="#FF333333" borderround="7,7" bkcolor="#ffdce1e7" font="0" textpadding="6,2,0,0" maxchar="5" tipvalue="0" multiline="false" textcolor="#ff333333" rich="false" transparent="false"></RichEdit>