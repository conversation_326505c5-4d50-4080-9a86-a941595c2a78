使用须知 - 9、错误码对照表
===============

API文档: 使用须知 - 9、错误码对照表
URL: https://www.mbbrowser.com/api/help
抓取时间: 2025-07-28 12:32:42

• POSTMAN调试候鸟API接口
• 调试接口JSON数据官方更新、下载

## 使用须知
• 候鸟浏览器支持本地API的功能，帮助用户通过程序化的方式来启动和关闭浏览器等基础API功能，还可以配合Selenium和Puppeteer等自动化框架来实现浏览器操作的自动化。

• 后续客户端 API将`采用HTTP模式连接`持续更新。

使用前请根据以下指引完成操作并获取信息，然后参照【http模式说明】启动客户端并开始使用API

### 1、获取API凭证
• API启动候鸟浏览器需要占用1个API凭证，即1个API凭证同一时间只能允许1台设备使用。使用前请确保账号至少有1个API凭证。

• 非团队版用户只能申请1个凭证，团队版仅限主账户申请，可申请的凭证数量等于团队成员总数

### 2、查看API凭证
• 打开候鸟控制台，点击API-查看凭证获取 `APP_ID` 和 `APP_KEY`

### 2、获取环境ID
• API通过 `Session_ID`（环境ID）打开环境，环境ID如图，也可以通过“获取环境列表接口”（`Path：/api/v1/session/listid`）获取：

进入我的控制台

支持邮箱: <EMAIL>
©MBBROWSER @2025

京ICP备 2020047947号

本系统不提供代理IP服务，禁止用户使用本系统进行任何违法犯罪活动，用户使用本系统带来的任何责任由用户自行承担。

MBbrowser.com  All Rights Reserved. 候鸟防关联浏览器对网站内容拥有最终解释权。
工作日客服(微信)
工作日09-18点

夜间/周末客服(微信)

工作日 18-24点，周末全天

商务(微信)

mbbrowser_official

###### 全国咨询服务热线

400-112-6050
在线咨询

微信咨询

电话咨询

售后咨询