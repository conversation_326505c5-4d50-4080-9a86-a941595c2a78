<?xml version="1.0" encoding="utf-8" standalone="yes" ?>
<Window size="850,590" caption="0,0,0,50" showshadow="true" shadowimage="shadow.png" shadowcorner="23,13,23,33" shadowcolor="#ff6272A4" trayiconid="102" traytiptext="UABrowser 1.0">
	<Include source="Default.xml" />
	<HorizontalLayout name="bkground" bkcolor="#FF282A36">
		<VerticalLayout width="50" bkcolor="#FF21222C">
			<Button name="face_logo" padding="11,11,0,0" normalimage="face.png" width="26" height="26"/>

			<Option name="chat" padding="11,38,0,0" width="24" height="20" selected="true" bindtablayoutname="tabs" bindtabindex="0" tooltip="聊天" bkimage="chat_normal.png" hotimage="chat_hot.png" selectedimage="chat_pushed.png" group="tab1"/>

			<Option name="contact" padding="11 39,0,0" width="24" height="19" bindtablayoutname="tabs" bindtabindex="1" tooltip="联系人" bkimage="contact_normal.png" hotimage="contact_hot.png" selectedimage="contact_pushed.png" group="tab1"/>

			<Control />
			<Button name="setting" padding="17,0,20,0" width="15" height="15" normalimage="setting_normal.png" hotimage="setting_hot.png" pushedimage="setting_pushed.png" />
			<Control height="20"/>
		</VerticalLayout>

		<VerticalLayout width="250" bkcolor="ff3a3a3a">
			<HorizontalLayout height="48" bkcolor="ff3a3a3a">
				<RichEdit name="search_edit" padding="10,10,0,10" height="28" width="200" borderround="3,3" bkcolor="ff464545" textpadding="30,5,20,0" tipvalue="搜索" multiline="false" textcolor="fffafafa" rich="false" transparent="false">
					<!-- <Control height="10" width="5"/>
					<Button width="12" height="13" normalimage="search.png" mouse="false"/> -->
				</RichEdit>
				<Button float="true" pos="18,18,0,0" width="12" height="13" normalimage="search.png" mouse="false"/>
				<Button name="add" padding="13,15,0,0" height="16" width="16" normalimage="add_normal.png" hotimage="add_hot.png" />
			</HorizontalLayout>
			<TabLayout name="tabs" selectedid="0">
				<HorizontalLayout>
					<FriendList name="friends" header="hidden" itemshowhtml="true" itemhotbkcolor="ff3e3e3e" itemselectedbkcolor="ff505050" vscrollbar="true"/>
				</HorizontalLayout>

				<HorizontalLayout>
					<FriendList name="contacts" header="hidden" bkcolor="ff3a3a3a" itemshowhtml="true" itemhotbkcolor="ff3e3e3e" itemselectedbkcolor="ff505050" vscrollbar="true"/>
				</HorizontalLayout>
			</TabLayout>
		</VerticalLayout>

		<VerticalLayout bkcolor="fff2f2f2">
			<HorizontalLayout height="37">
				<Control />
				<Button name="minbtn" width="30" height="37" tooltip="最小化" normalimage="min_normal.png" hotimage="min_hot.png" pushedimage="min_pushed.png" />
				<Button name="maxbtn" width="30" height="37" tooltip="最大化" normalimage="max_normal.png" hotimage="max_hot.png" pushedimage="max_pushed.png" />
				<Button name="closebtn" width="35" height="37" tooltip="关闭" normalimage="close_normal.png" hotimage="close_hot.png" pushedimage="close_pushed.png" />
			</HorizontalLayout>

			<TabLayout name="default_bk" selectedid="0">
				<VerticalLayout>  <!-- 默认界面 -->
					<Button padding="227,238,0,0" bkimage="wechat.png" width="96" height="77"/>
				</VerticalLayout>

				<VerticalLayout>   <!-- 好友界面 -->
					<Button name="friend_face" padding="215,70,0,0" bkimage="friend_face.png" height="120" width="120"/>
					<Control height= "15"/>
					<Label name="friend_name" text="陈哲" align="center" font="1" />
					<Control height="10"/>
					<Label name="friend_sign" text="除了知情权，人应当还拥有不知情权。" align="center" font="2" textcolor="ffa6a6a6" />
					<HorizontalLayout height="35" inset="0,15,0,0">
						<Label text="备    注" textcolor="ffa6a6a6" font="2" autocalcwidth="true" padding="210,0,0,0"/>
						<Control width="15"/>
						<RichEdit name="back_name" font="2" text="草哥" rich="false" bordersize="2" />
					</HorizontalLayout>

					<HorizontalLayout height="35" inset="0,15,0,0">
						<Label text="微信号" textcolor="ffa6a6a6" font="2" autocalcwidth="true" padding="210,0,0,0"/>
						<Control width="15"/>
						<RichEdit name="weixin_id" font="2" text="zhe5960"  rich="false" enabled="false"/>
					</HorizontalLayout>

					<HorizontalLayout height="35" inset="0,15,0,0">
						<Label text="地    区" textcolor="ffa6a6a6" font="2" autocalcwidth="true" padding="210,0,0,0"/>
						<Control width="15"/>
						<RichEdit name="zone" font="2" text="浙江 杭州"  rich="false" enabled="false"/>
					</HorizontalLayout>

					<Button padding="191,50,0,0" width="168" height="37" normalimage="sendmsg_normal.png" hotimage="sendmsg_hot.png" pushedimage="sendmsg_hot.png" />
				</VerticalLayout>

				<VerticalLayout bkcolor="ffffffff">  <!-- 群聊界面 -->
					<TileLayout name="group_list" inset="51,15,51,0" columns="6" height="450" vscrollbar="true" vscrollbarstyle="thumbnormalimage=&quot;file='scrollbar_group_normal.png' corner=&apos;2,3,2,3&apos;&quot; thumbhotimage=&quot;file='scrollbar_group_hover.png' corner=&apos;2,3,2,3&apos;&quot; width=&quot;7&quot;">
						<!-- <Button bkimage="file='c1.png' dest='5,10,66,71'" hotimage="file='bk_hover.png' corner='5,10,5,37' hole='true'" pushedimage="file='bk_hover.png' corner='5,10,5,37' hole='true'" width="71" height="108" textpadding="0,73,0,0" text="许敏敏"/> -->
					</TileLayout>
					<Button name="begin_chat" padding="191,38,0,0" height="37" width="168" normalimage="begin_chat_normal.png" hotimage="begin_chat_hot.png" pushedimage="begin_chat_pushed.png"/>
				</VerticalLayout>

				<VerticalLayout>    <!-- 最近联系人界面 -->
					<!-- <RichEdit inset="10,10,10,10" name="conversation" height="400" bkcolor="fff2f2f2" textpadding="54,4,0,0" font="3" vscrollbar="true" vscrollbarstyle="thumbnormalimage=&quot;file='scrollbar_group_normal.png' corner=&apos;2,3,2,3&apos;&quot; thumbhotimage=&quot;file='scrollbar_group_hover.png' corner=&apos;2,3,2,3&apos;&quot; width=&quot;7&quot;">
						<Button name="emotion" normalimage="c2.png" height="34" width="34"/>
						<Button padding="10,0,0,0" bkimage="file='RichEdit_normal.png' corner='10,5,5,5' hole='true'" width="111" height="34"/>
					</RichEdit> -->
					<FriendList name="Bubble_Chat" inset="10,10,3,10" height="400" font="3" header="hidden" itemhotbkcolor="fff2f2f2" itemselectedbkcolor="ffff1258" vscrollbar="true" vscrollbarstyle="thumbnormalimage=&quot;file='scrollbar_group_normal.png' corner=&apos;2,3,2,3&apos;&quot; thumbhotimage=&quot;file='scrollbar_group_hover.png' corner=&apos;2,3,2,3&apos;&quot; width=&quot;7&quot;"/>
					<HorizontalLayout height="40" inset="11,11,0,0" bkcolor="ffffffff">
						<Button name="emotion" normalimage="emtion_normal.png" hotimage="emtion_hot.png" height="18" width="18"/>
						<Button name="cut" padding="13,0,0,0" normalimage="cut_normal.png" hotimage="cut_hot.png" height="16" width="14"/>
						<Button name="open_file" padding="13,2,0,0" normalimage="open_file_normal.png" hotimage="open_file_hot.png" height="14" width="16"/>
					</HorizontalLayout>
					<RichEdit inset="10,0,10,0" font="3" name="input_edit" height="70" bkcolor="ffffffff" vscrollbar="true" vscrollbarstyle="thumbnormalimage=&quot;file='scrollbar_group_normal.png' corner=&apos;2,3,2,3&apos;&quot; thumbhotimage=&quot;file='scrollbar_group_hover.png' corner=&apos;2,3,2,3&apos;&quot; width=&quot;7&quot;"/>
					<HorizontalLayout bkcolor="ffffffff">
						<Control />
						<Button padding="0,8,10,0" name="send" normalimage="send_normal.png" hotimage="send_hot.png" height="26" width="68"/>
					</HorizontalLayout>
				</VerticalLayout>
			</TabLayout>
		</VerticalLayout>
	</HorizontalLayout>
</Window>