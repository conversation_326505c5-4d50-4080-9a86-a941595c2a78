网络代理管理器

网络代理管理器

列表数据源：proxy.xml

<?xml version="1.0" encoding="gb2312" ?>

<VERSION version="2805">

<VER ID="4" LOGIN_ACCOUNT="" SESSION_UNIQUE_ID="">

<SESSION NAME="" />

<TYPE TYPE="http"/>

<PxyMode VALUE="2"/>

<RealPxyMode VALUE="2"/>

<PROXY IP="************"/>

<PORT VALUE="11242"/>

<LOGIN NAME=""/>

<PASSWORD VALUE=""/>

<IS_VALID VALUE="1" />

<CREATETIME VALUE="2022-09-29 18:59:26"/>

<UPDATETIME VALUE="2022-10-02 18:19:46"/>

<COUNTRY VALUE="JP"/>

<TIMEZONE VALUE="Asia/Tokyo (-540)"/>

<LANGVAGE VALUE="ja-JP"/>

<Country_area VALUE=""/>

<SPEED VALUE="177ms"/>

</VER>

</VERSION>

【2022-10-08】增加SPEED节点

说明：此节点为纯文本，保存检测代理的速率值或超时信息。

超时写入：<SPEED VALUE="timeout"/>

搜索类：

列表关键字查找 【已完成未测试通过】

补充功能：需要将多列信息一并纳入查找范围。

右键菜单：

修改，删除。【已完成已测试通过】

描述：

修改： 允许用户修改指定的proxy记录。

删除： 允许用户删除指定的proxy记录。

按钮类：

从文件中导入

描述：代理服务器数据文件格式(每一行)： 代理类型，连接ip，porsy ip，登录，密码，DNS类型；Socks5，http，SSH隧道，动态socks5，Tor+SSH隧道。导入过程无需判断当前各PROXY是否有效。仅在列表中显示，默认不保存到本地正式proxy XML。

列表以XML ID为顺序依次列出所有数据。

添加新代理

描述：弹出添加代理面板，用户手工添加代理。

保存

描述：将列表数据写入到本地XML格式的proxy.xml中，然后同步（上传）到服务器。

网络代理管理器 流程与接口

流程：

1）窗口加载数据流程

当用户打开此窗体，窗体显示LOADING动画。

【线程模式】

PC端进行 –>

（一）版本请求：

服务器端：

Json格式：

{"filename":"x_xxx.zip","hash":"xxxxxxxx","version":"xx"}

zip文件为zip加密的xml数据。

(二) PC端本地进行版本对比

1、如果本地版本与服务器版本相同，不下载不更新本地。

2、如果本地版本小于服务器版本，下载更新本地并加载。

（三）PC端下载请求 – 服务器响应：

1、当PC端请求过来，请求串格式：

http://domain/xxx?aes(token=xxxxxxx&type=proxy_list &act=download&msg=request)

2、服务器端返回动态url（JSON格式）

服务器进行AES解码，判定token是否合法。

如合法：

http://domain/download?aes(filename=1_xxxxxxxx.dat&hash=xxxx&size=xxxxxx&type=proxy_list&version=xx)

3、PC端下载更新到本地并加载。

面板LOADING动画消失，显示加载的数据列表。

2）用户点击“保存”按钮流程

窗体显示LOADING动画。

【线程模式】

PC端进行 –> 本地PROXY.XML更新，版本号+1

（一）PC端上传请求 – 服务器响应：

1、当PC端请求上传，请求串格式：

http://domain/xxx?aes(token=xxxxxxx&type=proxy_list&uid=session_unique_id&act=upload&msg=request)

上传交互过程见第三章标准上传流程。

2、服务器端返回动态url（JSON格式）

服务器进行AES解码，判定token是否合法。

如合法：

PC端上传到服务器成功。

5、面板LOADING动画消失，显示 带SESSION NAME的数据上传成功信息。

3）服务器端针对此数据存储说明及约定

根据用户名的加码规则+类型名称进行数据保存。

例：b45357536beddd0fb618e1e8485afe2f_PROXY_LIST.zip

服务器端以用户名的加码规则作为唯一判定标识。

此数据不作15天备份机制。仅保留一份最新副本。

7、   用户提交的此数据必须由服务器端异步非压缩打包流程，在PC端请求FULL的环节点，加入到服务器最新的FULL包中下发。（关键）（详情见第六章）

【2022-10-08】新增：

4） 代理服务器批量检测流程：

UserAgent管理器

搜索类：

列表关键字查找 【已完成测试通过】

右键菜单：

修改，删除。【已完成已测试通过】

描述：

修改： 允许用户修改指定的useragent。

删除： 允许用户删除指定的useragent

修改技巧：右键菜单点击修改项后，原添加控件 input区自动变为修改控件，添加按钮变为修改按钮，用户点击保存后默认变回添加按钮。

按钮类：

保存

描述：将列表数据写入到本地XML格式的 useragent.xml中，然后同步（上传）到服务器。


================================================== 表格内容 ==================================================

<?xml version="1.0" encoding="gb2312" ?>
<VERSION version="11">
<VER ID="1" LOGIN_ACCOUNT="" SESSION_UNIQUE_ID="">
	<SESSION NAME="<EMAIL>" />
	<USERAGENT STRING="Mozilla/5.0(iPhone;CPU iPhone OS 12_2 like MAC OS X)AppleWebKit/605.1.15(KHTML,like Geoko)Mobile/1434E9"/>
	<IS_VALID VALUE="1" />
	<CREATETIME VALUE="2021-01-12 12:22:34"/>
	<UPDATETIME VALUE="2021-01-12 12:22:34"/>
</VER>
</VERSION>

<?xml version="1.0" encoding="gb2312" ?>
<VERSION version="11">
<VER ID="1" LOGIN_ACCOUNT="" SESSION_UNIQUE_ID="">
	<SESSION NAME="<EMAIL>" />
	<PLATFORM STRING="Win32"/>
	<PROXY STRING="***************"/>
  <COMMENTS STRING="City:DALLAS ZIP:10010"/>
	<IS_VALID VALUE="1" />
	<CREATETIME VALUE="2021-01-12 12:22:34"/>
	<UPDATETIME VALUE="2021-01-12 12:22:34"/>
</VER>
</VERSION>