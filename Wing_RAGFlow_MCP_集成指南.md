# Wing 客户端集成 RAGFlow MCP Server 完整指南

## 📋 概述

本指南将帮助你解决 Wing 客户端连接远程 CentOS 服务器上的 RAGFlow MCP Server 的问题，实现 Wing 客户端的 AI 模型访问 RAGFlow 知识库。

## 🔍 问题分析

根据你提供的 curl 测试结果，主要问题包括：

1. **API 端点不匹配**：测试的端点路径不正确
2. **认证问题**：API 密钥格式或权限问题  
3. **网络连接**：从 Windows 到 CentOS 的网络访问
4. **MCP 协议格式**：请求格式不符合 MCP 标准

## 🛠️ 解决方案

### 第一步：诊断和修复 RAGFlow MCP Server

#### 1. 运行诊断脚本

在你的 CentOS 服务器上运行：

```bash
cd /path/to/ragflow
python docker/ragflow/diagnose_mcp_connection.py
```

这个脚本会：
- 测试基础网络连接
- 检查 MCP 端点状态
- 验证 MCP 协议通信
- 测试工具列表获取

#### 2. 修复配置问题

如果诊断发现问题，运行修复脚本：

```bash
python docker/ragflow/fix_mcp_server.py
```

修复脚本会：
- 更新 Docker Compose 配置
- 确保 MCP 端口正确映射
- 重启 RAGFlow 服务
- 验证服务状态

#### 3. 配置防火墙

确保 CentOS 服务器防火墙开放必要端口：

```bash
# 开放 RAGFlow 主服务端口
sudo firewall-cmd --permanent --add-port=9380/tcp

# 开放 MCP Server 端口  
sudo firewall-cmd --permanent --add-port=9382/tcp

# 重新加载防火墙配置
sudo firewall-cmd --reload

# 验证端口开放
sudo firewall-cmd --list-ports
```

### 第二步：配置 Wing 客户端

#### 1. GO 语言 MCP 客户端

我已经为你创建了完整的 GO 语言 MCP 客户端代码：

**文件位置：** `wing_mcp_client/main.go`

**主要功能：**
- MCP 协议通信
- RAGFlow 知识库查询
- 错误处理和重试
- 连接测试

#### 2. 编译和测试 GO 客户端

```bash
cd wing_mcp_client

# 初始化 GO 模块
go mod init wing-mcp-client
go mod tidy

# 编译
go build -o wing_mcp_client.exe main.go

# 运行测试
./wing_mcp_client.exe
```

#### 3. Wing 客户端配置

使用提供的配置文件 `wing_mcp_client/wing_config.json`：

```json
{
  "mcp_servers": {
    "ragflow": {
      "name": "RAGFlow Knowledge Base",
      "server_url": "http://58.49.146.17:9382",
      "api_key": "ragflow-UzYTlhMjIwNjI0YzExZjA4NzUyMDI0Mm",
      "timeout": 30,
      "enabled": true
    }
  }
}
```

### 第三步：集成到 VSCode + Cline

#### 1. MCP 服务器配置

在 VSCode 的 Cline 扩展中配置 MCP 服务器：

```json
{
  "mcpServers": {
    "ragflow": {
      "command": "wing_mcp_client.exe",
      "args": [],
      "env": {
        "RAGFLOW_SERVER": "http://58.49.146.17:9382",
        "RAGFLOW_API_KEY": "ragflow-UzYTlhMjIwNjI0YzExZjA8NzUyMDI0Mm"
      }
    }
  }
}
```

#### 2. 候鸟浏览器 API 集成

保持现有的候鸟浏览器 API MCP 服务器，添加 RAGFlow 作为额外的知识源：

```json
{
  "mcpServers": {
    "browser_api": {
      "command": "go",
      "args": ["run", "browser_mcp_server.go"],
      "env": {}
    },
    "ragflow": {
      "command": "wing_mcp_client.exe", 
      "args": [],
      "env": {
        "RAGFLOW_SERVER": "http://58.49.146.17:9382",
        "RAGFLOW_API_KEY": "ragflow-UzYTlhMjIwNjI0YzExZjA4NzUyMDI0Mm"
      }
    }
  }
}
```

## 🧪 测试和验证

### 1. 综合测试（推荐）

运行参考官方实现的综合测试：

```bash
cd docker/ragflow
python comprehensive_mcp_test.py
```

这个测试会：
- 检查基础连接
- 验证 MCP 协议初始化
- 测试工具列表获取
- 验证查询功能
- 生成详细报告

### 2. 官方风格客户端测试

运行参考官方实现的客户端：

```bash
cd docker/ragflow
python ragflow_mcp_client_official.py
```

### 3. GO 客户端测试

运行改进的 GO 客户端：

```bash
cd wing_mcp_client
go mod tidy
go run main.go
```

### 4. 分步测试

如果需要分步诊断：

```bash
# 1. 基础诊断
python diagnose_mcp_connection.py

# 2. 修复配置（如果需要）
python fix_mcp_server.py

# 3. 快速测试
python quick_test_mcp.py
```

### 5. 完整集成测试

在 Wing 客户端中测试以下查询：

```
请帮我查询候鸟浏览器如何配置代理？
```

AI 应该能够：
1. 识别这是一个知识查询请求
2. 调用 RAGFlow MCP 工具
3. 从知识库中检索相关信息
4. 返回准确的答案

## 🔧 故障排除

### 常见问题和解决方案

#### 1. 连接超时
```
错误: connection timeout
解决: 检查防火墙设置和网络连接
```

#### 2. 认证失败
```
错误: authentication failed
解决: 验证 API 密钥是否正确
```

#### 3. MCP 协议错误
```
错误: invalid MCP request
解决: 检查请求格式和协议版本
```

#### 4. 工具列表为空
```
错误: no tools available
解决: 检查 RAGFlow 知识库配置
```

### 调试步骤

1. **检查服务状态**
   ```bash
   docker ps | grep ragflow
   docker logs ragflow-server
   ```

2. **测试端口连通性**
   ```bash
   telnet 58.49.146.17 9382
   curl http://58.49.146.17:9382/sse
   ```

3. **查看详细日志**
   ```bash
   tail -f docker/ragflow/ragflow-logs/api.log
   ```

## 📚 相关文件

### 核心文件
- **综合测试脚本**: `docker/ragflow/comprehensive_mcp_test.py` ⭐ **推荐使用**
- **官方风格客户端**: `docker/ragflow/ragflow_mcp_client_official.py` ⭐ **参考官方实现**
- **GO 客户端**: `wing_mcp_client/main.go` ⭐ **Wing 集成用**
- **配置文件**: `wing_mcp_client/wing_config.json`

### 辅助工具
- **诊断脚本**: `docker/ragflow/diagnose_mcp_connection.py`
- **修复脚本**: `docker/ragflow/fix_mcp_server.py`
- **批处理工具**: `docker/ragflow/run_mcp_fix.bat`
- **快速测试**: `docker/ragflow/quick_test_mcp.py`

### 参考实现
基于你提供的 GitHub 链接 `https://github.com/infiniflow/ragflow/blob/main/mcp/client/client.py`，我们的实现参考了 RAGFlow 官方的 MCP 客户端标准，确保最大兼容性。

## 🎯 下一步

1. **运行诊断脚本**确认服务器状态
2. **修复发现的问题**
3. **测试 GO 客户端**连接
4. **集成到 Wing 客户端**
5. **验证完整流程**

完成这些步骤后，你的 Wing 客户端应该能够成功访问 RAGFlow 知识库，实现企业文档的智能检索和问答功能。
