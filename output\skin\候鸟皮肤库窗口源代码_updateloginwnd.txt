﻿<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Window size="600,490" caption="0,0,0,40" roundcorner="5,5,5,5" showshadow="true" shadowsize="3" shadowposition="0,0" shadowcolor="#ff6272A4" fademode="true">
	<Include source="Default.xml" />

  <VerticalLayout bkcolor="#FF282A36">

    <HorizontalLayout height="37">

    	 <Button name="login" bkimage="icon.png" padding="8,6,0,0" width="28" height="28"/>
      <Label name="title" padding="6,4,0,0" text="批量自定义环境名称" width="180" textcolor="#FF616161" hottextcolor="#ff000000" align="left" font="8"></Label>

      <Control />
      <Button name="minbtn" width="28" height="26" tooltip="最小化" normalimage="minbtn.png" hotimage="minbtn_hover.png" pushedimage="minbtnpush.png" />
      <Button name="closebtn" width="28" height="26"  tooltip="关闭" normalimage="closebtn.png" hotimage="closebtn_hover.png" pushedimage="closebtnpush.png" />

    </HorizontalLayout>

  <HorizontalLayout name="bkground" visible="true">

		<VerticalLayout >

      <HorizontalLayout height="20">
        <!--<VerticalLayout maxwidth="220">
          <Label name="lname" padding="22,6,0,0" text="请输入环境名称" maxwidth="180"   textcolor="#FF333333" hottextcolor="ffFF0000" font="8"></Label>
        </VerticalLayout>-->
      </HorizontalLayout>

      <VerticalLayout height="400" padding="20,0,0,10">

        <HorizontalLayout height="36" >
          <VerticalLayout width="110">
            <Label name="confignamel" padding="40,0,0,0" text="账户名称：" textcolor="#FF616161" hottextcolor="#ff000000" align="left" font="8"></Label>
          </VerticalLayout>
          <VerticalLayout width="390">
            <RichEdit name="configname" wanttab="false" maxchar="32" padding="10,0,0,10" width="370" height="36" tipvaluecolor="#FF333333" borderround="5,5" bkcolor="#ffdce1e7" font="5" textpadding="10,10,20,0" tipvalue="请输入账户名称" multiline="false" textcolor="#ff333333" rich="false" transparent="false">
            </RichEdit>
          </VerticalLayout>
        </HorizontalLayout>




        <HorizontalLayout height="48" >
          <VerticalLayout width="110">
            <Label name="platforml" padding="40,0,0,0" text="网站类型：" textcolor="#FF616161" hottextcolor="#ff000000" align="left" font="8"></Label>
          </VerticalLayout>
        <VerticalLayout width="390">
          <Combo name="platform" dropboxsize="0,300" bordersize="0" padding="10,6,0,10" width="370" height="36" borderround="5,5" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" bkcolor="#ffdce1e7"
              normalimage="file='Profile\Proxy_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\proxy_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\proxy_Combox_Click.png' corner='5,5,25,10'"
              combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
              itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0" textpadding="0,0,24,0" endellipsis="true">
          </Combo>
        </VerticalLayout>
        </HorizontalLayout>



        <HorizontalLayout height="126" >
          <VerticalLayout width="110">
            <Label name="domainurll" padding="14,-1,0,0" textpadding="0,0,0,0" text="网页登录URL：" height="36" textcolor="#FF616161" hottextcolor="#ff000000" font="8"></Label>
          </VerticalLayout>
          <VerticalLayout width="390">
            <RichEdit name="domainurl" wanttab="false" maxchar="1024" padding="10,0,0,10" width="370" height="126" tipvaluecolor="#FF333333" borderround="5,5" nativebkcolor="#ffdce1e7" bkcolor="#ffdce1e7" font="5" textpadding="10,10,20,0" tipvalue="请输入网站登录页面URL.." multiline="true" textcolor="#ff333333" rich="false" transparent="false" vscrollbar="true">
            </RichEdit>
          </VerticalLayout>
        </HorizontalLayout>


             <HorizontalLayout height="6" >
        </HorizontalLayout>

        <HorizontalLayout height="36" >
          <VerticalLayout width="110">
            <Label name="loginl" padding="40,0,0,0" text="账号：" textcolor="#FF616161" hottextcolor="#ff000000" align="right" font="8"></Label>
          </VerticalLayout>
          <VerticalLayout width="390">
            <RichEdit name="login" wanttab="false" maxchar="255" padding="10,0,0,10" width="370" height="36" tipvaluecolor="#FF333333" borderround="5,5" bkcolor="#ffdce1e7" font="5" textpadding="10,10,20,0" tipvalue="请输入账号" multiline="false" textcolor="#ff333333" rich="false" transparent="false">
            </RichEdit>
          </VerticalLayout>
        </HorizontalLayout>

        <HorizontalLayout height="6" >
        </HorizontalLayout>

        <HorizontalLayout height="36" >
          <VerticalLayout width="110">
            <Label name="passl" padding="40,0,0,0" text="账号密码：" textcolor="#FF616161" hottextcolor="#ff000000" align="left" font="8"></Label>
          </VerticalLayout>
          <VerticalLayout width="390">
            <RichEdit name="pass" wanttab="false" maxchar="255" padding="10,0,0,10" width="370" height="36" tipvaluecolor="#FF333333" borderround="5,5" bkcolor="#ffdce1e7" font="5" textpadding="10,10,20,0" tipvalue="请输入账号密码" multiline="false" textcolor="#ff333333" rich="false" transparent="false">
            </RichEdit>
            <Button name="viewpass" bkimage="eye_open.png" float="true" pos="350,12,0,0" font="0" width="24" height="12" text=""/>
          </VerticalLayout>
        </HorizontalLayout>

        <HorizontalLayout height="6" >
        </HorizontalLayout>

        <HorizontalLayout height="36" >
          <VerticalLayout width="110">
            <Label name="fa2l" padding="40,0,0,0" text="2FA密匙：" textcolor="#FF616161" hottextcolor="#ff000000" align="left" font="8"></Label>
          </VerticalLayout>
          <VerticalLayout width="390">
            <RichEdit name="fa2" wanttab="false" maxchar="255" padding="10,0,0,10" width="370" height="36" tipvaluecolor="#FF333333" borderround="5,5" bkcolor="#ffdce1e7" font="5" textpadding="10,10,20,0" tipvalue="请输入账号密码" multiline="false" textcolor="#ff333333" rich="false" transparent="false">
            </RichEdit>
            <!--<Button name="viewpass" bkimage="eye_open.png" float="true" pos="350,16,0,0" font="0" width="24" height="12" text=""/>-->
          </VerticalLayout>
        </HorizontalLayout>

			</VerticalLayout>

      <HorizontalLayout inset="0,0,0,0" height="373">

				</HorizontalLayout>






		</VerticalLayout>


	</HorizontalLayout>

		  <HorizontalLayout inset="0,0,0,0" height="33">
        	  <Label name="confignamearea_tiptitle1" padding="12,6,0,0" text="提示：网页登录地址请填写网站的登录页面URL地址，网站类型不清楚可不选。" textcolor="#ff333333" hottextcolor="#ffaaaaaa" font="5"></Label>

				</HorizontalLayout>

    <HorizontalLayout height="52" bkcolor="#ffe9e9e9">
         <Control />
      <!--<VerticalLayout width="420">

       </VerticalLayout>-->
        <VerticalLayout width="140">
      		<Control />
          <Button text="确定" name="btnok"   padding="16,2,0,0" width="120" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	bkimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
          <Control />
       </VerticalLayout>

      <Control name="loginwndarea"/>
      <Button name="loginwnd" visible="false" padding="0,28,6,0" align="center" height="20" width="120" text="进入环境账户管理器" font="5" textcolor="#FF3E3E3E" hottextcolor="#FFff4444" />
    </HorizontalLayout>
  </VerticalLayout>
</Window>
