客户端收到服务器端返回JSON数据后，对JSON进行解析并下载。  （请求服务器、下载成功失败均要写LOG日志）

客户端收到服务器端返回JSON数据后，对JSON进行解析并下载。  （请求服务器、下载成功失败均要写LOG日志）

6、客户端读取JSON格式中的 close_process参数值，对于数据处理前是否要关闭浏览器进行判定： 1 关闭 0 不关闭。

7、客户端读取JSON格式中的 act参数值，对于unzip的数据进行解压缩覆盖到chrome安装目录。 对于execute的数据在后台静默执行。

（覆盖是否成功，执行是否已触发，需要写本地LOG日志）

8、上述流程进行完毕后，客户端在本地保存VERSION.MD 将版本号更新为当前覆盖版本。

（覆盖是否成功，需要写本地LOG日志）

第七节：

LOGIN接口更新：

LOGIN接口增加 chrome_version 参数。

客户端登录时，增加提交chrome_version到服务器。

注：

向下兼容历史旧版用户，允许chrome_version为空

[2021-06-04 新增]

第二十二章 候鸟浏览器

客户端/服务器端 会话环境锁
数据接口与详细图文逻辑流程及约定。

第二十二章 候鸟浏览器 客户端/服务器端 会话环境锁数据接口与详细图文逻辑流程及约定。[2021-06-04 新增]

第一节：

前述：

基于团队协作产品模式下的大客户群体：
针对会话环境的团队中母帐户控制需求，团队协作版付费客户反馈或投诉过来时、其态度是较强硬和激烈的。（已有多个历史客户投诉截图）

2020年中旬至目前，中高阶客户（即大客户）较多以指责的方式认定候鸟产品在这一块功能的缺失给其带来了业务运营隐患，即未来可能发生的其自身客户业务风险。

产品各方面功能的不断完善和不断推进过程中的历史因素：

候鸟整体基础架构的升级、海量环境与服务器实时数据交互、 环境后端数据同步与计费限定、COOKIES批量导入导出、环境本地批量导入导出，环境分组管理等功能，上述各项功能必须全面完善提供后，方可进行 会话环境锁 之功能的历史缘由。

因此，目前实施环境锁对外公开服务的条件，已基本具备。

综述：

针对客户自有业务开展，对于候鸟当前提供的团队协作下的环境控制，部份客户需要将某一批环境锁死为仅指定的少数帐户或唯一帐户可在同时期内进行业务开展、调试、修改、登录和控制，来确保其业务安全和目标网站风控。

团队协作环境下，客户认为不仅仅分享的环境可受子帐户使用、管理，修改。还应提供禁止已分享的环境被其它子帐户管理和修改，需要提供禁止的服务。其次，当分享者创建的环境被创建者修改后，创建者最新修改的环境可以直接覆盖历史中已接受过此环境的子帐户的相同环境。

提供环境锁中涉及到的批量管理的服务器端控制台、PC端对应服务人机界面交互与后台通讯。

对于同一个环境在同一个周期内只能由一个帐户进行使用的功能实现。

第二节：

环境（子帐户）创建者与 母帐户关系说明：

1、环境创建者指母帐户或子帐户，即某个环境由谁创建的，则创建者即是此环境的所有者。

仅环境创建者可对此环境的锁状态管理，且拥有最高权限。

母子帐户对非自有创建的环境仅具备查看权限。

当子帐户分享了此环境给母帐户，或母帐户分享给子帐户指定自有环境时，母帐户仅拥有对自有创建的环境拥有环境锁控制的最高权限。母子帐户对接收的外来分享环境均无权限设置环境锁。（实现方法在第四节中详述）

第三节：

界面说明及功能纲要：

环境创建者帐户可在控制台管理自有帐户中所有环境，并对自有环境的锁控制进行修改。

PC端、服务器端均提供用户进行环境锁控制。服务器端可通过分享的环境列表栏进行环境锁设定。（图一）

PC端、服务器端涉及到环境锁图标，使用相同图标进行标识和环境锁功能入口。

PC端、服务器端批量设置环境锁说明。

PC端：仅对自有环境列表进行设置环境锁，用户可在PC端可对未分享/已分享的环境进行环境锁自由设置，之后用户分享自带环境锁设置信息。

服务器端：子用户可对已分享环境单个/批量设置环境锁。母帐户如需设置子帐户环境锁，必须切换到子帐户控制台后进行环境锁批量设置

（图二）

注：当某个环境已添加环境锁后，其运行按钮图标显示为加锁状态。

（图三）

当单个环境被唤出右键菜单，如图，增加会话环境锁和对应菜单。

当单个/多个环境被勾选，下方按钮亮起，会话环境锁一并亮起，可批量设定环境锁。

设定环境锁操作后要有弹窗提示处理结果。

当子帐户针对接受到的分享环境进行设置时，受权限限制应禁止进行设定，并在弹窗里进行提示。

A、子母帐户仅仅且仅能在自有创建的环境中，设定环境锁。

B、帐户之间的各自有环境相互之间不受对方管制。

第四节：底层数据结构与接口约定

1、数据结构约定：

ConfigData数据结构与增加部份：(增加内容以红色标注)

----configdata.xml ----

<VER ID="8" VERSION="0" SESSION_UNIQUE_ID="9bcc1ce2723465bdcd540e2e3280e4a1" FROMUID="110524" SHARETIME="2021-01-25 15:44:23" FROM_ACCOUNT=”<EMAIL>” GROUP_ID=”0” GROUP_NAME=”default” str_lock=”0|0|0”>

<SESSION NAME="790790" TOP="0" COLOR="#FFffffffff" SYSTEM="Win32"/>

<IS_ANONYMITY ANONYMITY = "0"/>

<COMMENT COMMENT_STRING=""/>

<SESSION_DEFINE_CODE SPHEREGL="0" ENABLE_LOCALHTML5_STORAGE="0" SAVE_LOCALHTML5_STORAGE="0" SAVE_INDEXDB_STORAGE="0"/>

<NETWORK TYPE="noproxy" PMODE="0" IP="N/A" PORT="0" USER="" PASSWORD="" PUBLIC_IP="*************" FAKEIP="**************" />

<NETWORK_CTR NA="0" FAKE_WRTC="0" SAME_IP="0" IPV6="0" WRTCOFF="0" DNS="" />

<USERAGENT_BROWSER CHROME="0" SAFARI="0" MSIE="0" OTHER="0" REGEN_CONFIG_USERAGENT="0" />

<USERAGENT_STRING UA="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36" UA_LNG="en-US" UA_LNG_STRING="[@en-US@,@US@]" />

<USERAGENT_CTR DISPOPUPS="0" ENABLE_SERVICE="0" BLOCKLNGINPUT="0"  />

<RESOLUTION WIGHT="721" HEIGHT="1280" />

<RESOLUTION_CTR EMU_SCREEN="0" EMU_TOUCH="0" />

<POSITION LONGITUDE="0.000000" LATITUDE="0.000000" COUNTRY="AD" />

<TIMEZONE TIMEZONE_NAME="Africa/Abidjan (0)" ADD_VALUE=""/>

<FINGERPRINT_CODE AUDIO="1" CANVAS="1" FONTS="1" RETCS="1" DNS="1" AUTOIPCHECK="1"/>

<OTHER_SETTING PLUGINS_MIMETYPE="0" SAVE_ENCRYPT_COOKIES="0" ENABLE_FLASH="0" DYNAMIC_FINGERPRINTS="0" BLOCK_CANVAS_OUTPUT="0" />

<DYNAMIC_FINGERPRINTS_CTR D_AUDIO="0" D_CANVAS="0" D_FONTS="0" D_RETCS="0" D_MEDIA="0" D_WEBGL="0" D_MIME="0" D_PLUGINS="0" />

<IS_VALUED VALUED="0" />

<UPDATETIME VALUE="2021-01-25 11:39:22"/>

<CREATETIME VALUE="2021-01-25 11:39:22"/>

</VER>

----configdata.xml ----

备注：本地环境导出功能，需要考虑增加一个选项，允许用户将fromuid从item包里剥离掉，剥离掉后此导出的环境不再是一个分享环境，进而变成一个原生的本地环境，避免导入此环境后再次受创建者控制，导致用户间ITEM买卖受到影响。（当前不考虑实施此逻辑）

2、strlock串约定（重点）：

同时段仅允许单个帐户运行：默认值 0

禁止其它帐户修改此环境：默认值 0

实时更新子帐户当前环境：默认值 0

注：对于环境创建帐户想禁止其它用户运行此已分享给他的环境，将分享给对方的环境删除即可达到目的。

默认在configdata.xml中串值为：str_lock  = “0|0|0”


================================================== 表格内容 ==================================================

{
    "message": "Success",
    "code": 0,
"data": {
  "listcontainer": [
        {
        "Session_Name": “商用业务环境一”
        "Session_ID": "373808cb37bd63f5f7d92415e736e85f",
"Group_Name": “default”
        "StartPage": "about:blank",
"System": "windows",
        "Resolution: "1024x768",
        "UserAgent": " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "proxyType": "socks5",
        "proxy_ip": "127.0.0.1",
        "proxy_port": "1080",
        "webdriver":"C:\\Users\\<USER>\\houniao\\Driver\\100\\chromedriver.exe",        //根据当前打开环境的内核返回对应内核webdriver驱动路径
        "updatetime":”2022-12-13 13:23:09”,
        "createtime":”2022-09-23 08:47:36”,
        "item_version":”3030”,
"status": 0
},
{
        "Session_Name": “商用业务环境二”
        "Session_ID": "705cc4c139e69b729a2fd277f30e1863",
"Group_Name": “default”
        "StartPage": "about:blank",
        "proxyType": "socks5",
        "proxy_ip": "127.0.0.1",
        "proxy_port": "1080",
        "webdriver":"C:\\Users\\<USER>\\houniao\\Driver\\100\\chromedriver.exe",        //根据当前打开环境的内核返回对应内核webdriver驱动路径
"updatetime":”2022-12-13 13:23:09”,
        "createtime":”2022-09-23 08:47:36”,
        "item_version":”1030”,
        "status": 0
}],”total”:2
}

{
    "message": "Success",
    "code": 0,
"data": {
  "listcontainer": [
        {
        "Session_Name": “商用业务环境一”
        "Session_ID": "373808cb37bd63f5f7d92415e736e85f",
"Group_Name": “default”
“Actived_script_id”:” O73808cb37bd63f5f7d92415e736e999”,
“Actiived_script_name”:”这是一个脚本例子”,
“Actiived_script_encode”:”true”,
"Weblogin_Account_Count": "4",
        "Weblogin_Account_name":"<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>",
"Plugins_Count": "4",
        "Plugin_Id": "jjbnhpnlakcdgfnnldamfeinfmahhdlm,jjbnhpnlakcdgfnnldamfeinfmahhdlm,jjbnhpnlakcdgfnnldamfeinfmahhdlm, jjbnhpnlakcdgfnnldamfeinfmahhdlm",
        "proxyType": "socks5",
        "proxy_ip": "127.0.0.1",
        "proxy_port": "1080",
        "webdriver":"C:\\Users\\<USER>\\houniao\\Driver\\100\\chromedriver.exe",        //根据当前打开环境的内核返回对应内核webdriver驱动路径
        "updatetime":”2022-12-13 13:23:09”,
        "createtime":”2022-09-23 08:47:36”,
        "item_version":”3030”,
"status": 0
},
{
        "Session_Name": “商用业务环境二”
        "Session_ID": "705cc4c139e69b729a2fd277f30e1863",
"Group_Name": “default”
“Actived_script_id”:” O73808cb37bd63f5f7d92415e736e999”,
“Actiived_script_name”:”这是一个脚本例子”,
“Actiived_script_encode”:”true”,
"Weblogin_Account_Count": "4",
        "Weblogin_Account_name":"<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>",
"Plugins_Count": "4",
        "Plugin_Id": "jjbnhpnlakcdgfnnldamfeinfmahhdlm,jjbnhpnlakcdgfnnldamfeinfmahhdlm,jjbnhpnlakcdgfnnldamfeinfmahhdlm, jjbnhpnlakcdgfnnldamfeinfmahhdlm",
        "proxyType": "socks5",
        "proxy_ip": "127.0.0.1",
        "proxy_port": "1080",
        "webdriver":"C:\\Users\\<USER>\\houniao\\Driver\\100\\chromedriver.exe",        //根据当前打开环境的内核返回对应内核webdriver驱动路径
"updatetime":”2022-12-13 13:23:09”,
        "createtime":”2022-09-23 08:47:36”,
        "item_version":”1030”,
        "status": 0
}],”total”:2
}