#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DOCX文件验证脚本
检查转换后的DOCX文件的基本信息
"""

import os
from pathlib import Path
from docx import Document

def verify_docx_file(docx_path):
    """验证单个DOCX文件"""
    try:
        doc = Document(docx_path)
        
        # 统计信息
        paragraph_count = len(doc.paragraphs)
        table_count = len(doc.tables)
        
        # 获取文档标题
        title = ""
        if doc.paragraphs:
            title = doc.paragraphs[0].text[:50] + "..." if len(doc.paragraphs[0].text) > 50 else doc.paragraphs[0].text
        
        # 计算文件大小
        file_size = os.path.getsize(docx_path)
        file_size_kb = file_size / 1024
        
        return {
            'filename': Path(docx_path).name,
            'title': title,
            'paragraphs': paragraph_count,
            'tables': table_count,
            'size_kb': round(file_size_kb, 2),
            'status': 'OK'
        }
        
    except Exception as e:
        return {
            'filename': Path(docx_path).name,
            'title': 'ERROR',
            'paragraphs': 0,
            'tables': 0,
            'size_kb': 0,
            'status': f'ERROR: {str(e)}'
        }

def verify_all_docx_files(docx_dir):
    """验证目录中的所有DOCX文件"""
    docx_path = Path(docx_dir)
    docx_files = list(docx_path.glob("*.docx"))
    
    if not docx_files:
        print(f"❌ 在目录 {docx_dir} 中没有找到DOCX文件")
        return
    
    print(f"📁 找到 {len(docx_files)} 个DOCX文件")
    print("=" * 80)
    print(f"{'文件名':<25} {'段落数':<8} {'表格数':<8} {'大小(KB)':<10} {'状态':<10}")
    print("-" * 80)
    
    total_size = 0
    success_count = 0
    
    for docx_file in sorted(docx_files):
        result = verify_docx_file(str(docx_file))
        
        print(f"{result['filename']:<25} {result['paragraphs']:<8} {result['tables']:<8} {result['size_kb']:<10} {result['status']:<10}")
        
        if result['status'] == 'OK':
            success_count += 1
            total_size += result['size_kb']
    
    print("-" * 80)
    print(f"✅ 验证完成: {success_count}/{len(docx_files)} 个文件正常")
    print(f"📊 总大小: {round(total_size, 2)} KB")
    print(f"📊 平均大小: {round(total_size/len(docx_files), 2)} KB")

if __name__ == "__main__":
    docx_directory = r"F:\augment\output\docx_files"
    
    print("🔍 开始验证DOCX文件...")
    print(f"📂 目录: {docx_directory}")
    print()
    
    verify_all_docx_files(docx_directory)
