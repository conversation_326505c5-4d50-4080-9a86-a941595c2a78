标题: 环境包导入导出
英文标题: Environment Package Import And Export
ID: 103
分类ID: 7
添加时间: 1616998950
更新时间: 1702021048
访问次数: 0
SEO标题: 候鸟浏览器环境包导入导出
SEO关键词: 候鸟浏览器环境包导入导出教程
SEO描述: 候鸟浏览器环境包导入导出教程

================================================== 内容 ==================================================
为了保障账号导入导出的安全性，候鸟浏览器采用了账号导入的终极方案“环境包”导入。

账号导入的安全性对比：环境包导入>Cookie导入>账号导入

环境包中包含那些信息：
1、浏览器指纹配置
2、代理IP
3、Cookie
4、账号密码

### 视频教程
|  环境包批量导入导出 |
| ------------ |
| <video controls="" preload style="width:100%"><source src="https://help.mbbrowser.com/video/environment_package_import_and_export.mp4" type="video/mp4"></video> |


### 一、导出环境包

1、登录候鸟浏览器后在主面板点击“会话环境管理”，进入“会话环境管理器”；

![](6d7b2882624511f09a0d0242ac130006/images/image_d9c952991bb4.png)

2、进入“会话环境管理器”后，我们在下方可以看到“导入环境包”和“导出环境包”的按钮；

![](6d7b2882624511f09a0d0242ac130006/images/image_6f3eabd223f9.png)

3、或者在主面板任意环境处单击鼠标右键弹开菜单栏，在点击“导入本地环境包”、“导出环境到本地”进入环境包导入导出窗口；

![](6d7b2882624511f09a0d0242ac130006/images/image_1e06c6a9233e.png)

4、我们在“会话环境管理器”中选择您要导出的环境包（可以单选或多选），在点击下方“导出环境包”按钮；

![](6d7b2882624511f09a0d0242ac130006/images/image_f461326522a1.png)

5、在弹出的窗口中输入您要设置的环境包的密码（也可以不用设置），可以自定义选择存储目录，然后点击右下角“导出本地环境包”按钮；

![](6d7b2882624511f09a0d0242ac130006/images/image_87f16eec5cae.png)

6、我们就会在您定义的存储目录中看到已导出的候鸟浏览器环境包；

![](6d7b2882624511f09a0d0242ac130006/images/image_46d9de46c19c.png)



### 二、导入环境包

1、在“会话环境管理器”中，点击右下角“导入环境包”按钮；

![](6d7b2882624511f09a0d0242ac130006/images/image_a0cba014c618.png)

2、在导入环境包窗口中输入环境包密码（如果没有密码可以不用填写），点击“选择我的本地环境包文件”按钮，选择一个我们要导入的环境包，在点击“导入本地环境包”按钮；

![](6d7b2882624511f09a0d0242ac130006/images/image_deaeb69bbc60.png)

3、如果下方提示“已导入成功”，说明导入环境包成功了；

![](6d7b2882624511f09a0d0242ac130006/images/image_287f5a12ea4c.png)

4、我们在主面板或“会话环境管理器”中都会看到刚刚导入的环境列表；

![](6d7b2882624511f09a0d0242ac130006/images/image_a7819be2194d.png)

注意：如果环境包导入后不能正常打开浏览器，大部分情况是因为环境包中的代理IP失效了，建议您重新修改环境，更换可用的代理IP进行操作。

================================================== 英文内容 ==================================================
In order to ensure the security of account import and export, Mbbrowser adopts the ultimate scheme of account import "environment package" import.

Security comparison of account import: environment package import >Cookie Import > Account import

What information is contained in the environment package:
1. Configure the browser fingerprint
2. Proxy IP address
3. Cookies
4. Account password

###  Video Tutorial
|  Batch import and export of environment packages |
| ------------ |
| <video controls="" preload style="width:100%"><source src="https://help.mbbrowser.com/video/environment_package_import_and_export.mp4" type="video/mp4"></video> |

### I、Export Environment Package

1. After logging in to the Mbbrowser, click "Session Manager" in the main panel to enter "Session Manager";

<p><img src="6d7b2882624511f09a0d0242ac130006/images/image_8823b0ec0a0e.png" width="360" /></p>

2. After entering the "Session Manager", we can see the "Import" and "Export" buttons below;

![](6d7b2882624511f09a0d0242ac130006/images/image_8ffd9f012c24.png)

3. Or click the right mouse button in any environment in the main panel to pop up the menu bar, and then click "Import Local Package" and "Export Package to localhost" to enter the environment package import and export window;

<p><img src="6d7b2882624511f09a0d0242ac130006/images/image_e94f5b7ba969.png" width="460" /></p>

4. In "Session Manager", select the environment package you want to export (single or multiple options can be selected) and click the "Export" button below;

![](6d7b2882624511f09a0d0242ac130006/images/image_76adfcc1a379.png)

5. Enter the password of the environment package you want to set in the pop-up window (you can also not set), you can customize the storage directory, and then click the "Export Now" button in the lower right corner;

![](6d7b2882624511f09a0d0242ac130006/images/image_98cb28a69a8b.png)

6. We see the exported Mbbrowser environment package in the storage directory you defined;

![](6d7b2882624511f09a0d0242ac130006/images/image_46d9de46c19c.png)



### II、Import Environment Package

1. In "Session Manager", click the "Import" button in the lower right corner;

![](6d7b2882624511f09a0d0242ac130006/images/image_f4ad9949e14d.png)

2. Enter the environment package password in the import environment package window (you don't need to fill in the password if you don't have one), click "Select Local Package" button, select an environment package we want to import, and then click "Import Now" button;

![](6d7b2882624511f09a0d0242ac130006/images/image_d4588b04c4b1.png)

3. If Import Succeeded is displayed, the environment package is successfully imported.

![](6d7b2882624511f09a0d0242ac130006/images/image_edc935f51a8c.png)

4. In the main panel or Session Manager, you will see the list of environments you have just imported;

![](6d7b2882624511f09a0d0242ac130006/images/image_57b10d93c361.png)

Note: If the browser cannot be opened properly after the environment package is imported, it is mostly because the proxy IP address in the environment package is invalid. You are advised to modify the environment again and replace the available proxy IP address.