【浏览器插件管理体系】

第二十六章

【浏览器插件管理体系】

客户端候鸟浏览器内核

插件全自动安装

服务器端客户插件数据

自动整合并保证唯一

逻辑详细流程及约定。

第二十六章 【浏览器插件管理体系】客户端候鸟浏览器内核插件全自动安装服务器端客户插件数据自动整合并保证唯一逻辑详细流程及约定。[2022-03-18 新增]

一、需求详述：

由于各个环境之间是物理隔离，因此，在业务开展中，候鸟VIP客户抱怨插件在每个浏览器（环境中），需要一个个安装，过于繁琐和麻烦。需要候鸟提供在某个环境中安装一次后，可以通过自行控制，指定其它环境批量支持相应插件（其它环境批量自动生效）。

初始约定：插件数据与管理，以用户为单位，隔离开，即A用户用A插件和B用户用A插件，会在服务器上保存2份 A插件。因此保证了各个用户间版本的不同，并带来其业务稳定性。

二、数据结构说明：

基于帐户为单位，全局插件列表存储： plugins.xml

存储位置：

已安装插件包实际存储位置样图：

A、Full包同步说明：

1、plugins.xml 连同full.zip 包进行打包，并按原约定进行同步处理。

2、plugins.xml在任意时间节点产生变化，需触发full包进行版本校验，同步上传。

B、插件包同步到服务器端说明：

1、用户在客户端安装的插件包支持同步到服务器上。

2、服务器端通过api/plugin_upload 同步接口进行插件包的文件存储和插件名称、文件名及md5值、upload时间，插件版本号，UID值存表（表名plugins）。

3、插件包md5值由客户端生成提交，服务器端进行md5验证，验证通过后本地存储并成功存表。

4、所有插件包放在/www/wwwroot/admin.mbbrowser.com/upload/plugins 目录下，所有用户上传的插件包均放在此目录下。（重点）

5、插件包api/plugin_upload同步接口根据插件包的md5的值进行唯一判定，对于/www/wwwroot/admin.mbbrowser.com/upload/plugins 目录下，仅且只允许存储唯一md5相同的包。

6、客户端在item.zip的configdata.xml中记录此环境下已分配的插件，允许多个插件包，通过子节点记录多个插件包的名称、文件名、MD5。

7、客户端请求获取插件包下载时，通过提交md5值来成功下载需要的包。

8、服务器端根据表plugins通过约定接口 api/plugin_list返回给客户端已存在服务器上的所有插件。

三、插件管理窗口 数据来源于：plugins.xml

需实现的基础功能有：

1、列出用户已安装过的所有插件（不重复）【数据来源：plugins.xml】

2、列出插件的名称，插件的唯一标识（MD5），安装时间。

3、列出插件的版本号。

扩展功能：

支持显示分组列表、环境列表。

支持单选，多选插件，指派给某个分组。（实际操作为：将item.xml文件中的plugins节点进行添加赋值）

支持单选多选插件，指派给某个环境。（实际操作为：将item.xml文件中的plugins节点进行添加赋值）

支持单选多选插件，指派给多个分组。（实际操作为：将item.xml文件中的plugins节点进行添加赋值）

支持单选多选插件，指派给多个环境。（实际操作为：将item.xml文件中的plugins节点进行添加赋值）

支持一键清空所有插件。（实际操作为：遍历所有item，并将item.xml文件中的plugins相关节点删除）

支持一键清空指定分组下所有插件。（实际操作为：将item.xml文件中的plugins相关节点删除）

支持一键清空指定多个环境下所有插件。（实际操作为：将item.xml文件中的plugins相关节点删除）

四、浏览器内核与 插件xml数据源关系说明：【此节点2022.3.29更新】

客户端子进程在用户关闭chrome时获取一次当前chrome窗口已安装插件的列表数据。

同时，客户端加载全局plugins.xml进行插件的ID值和版本号比对。

判定已安装的插件中，是否有 插件项 未存在于全局plugins.xml中，如果已存在，判断此chrome窗口对应的此item环境包的configdata.xml的plugins节点是否存在此插件及ID,VER，如果存在，则跳过。如果不存在，进行第5节点的动作。

判定如果未存在于全局plugins.xml中，将用户新安装的插件的：插件名称、插件文件名、插件文件的唯一标识（ID），MD5, 安装时间，插件版本号 新增模式添加到 plugins.xml中。

触发对此环境的item.zip包中的configdata.xml中的plugins节点进行新增赋值, 如ID相同，VER不同，则进行此节点项更新。

【原有即定流程】触发item包的版本验证与上传同步。

【原有即定流程】触发full包进行一次版本校验后进行full包的上传流程，不将plugins.xml连同full包更新到服务器。

五、插件管理面板，窗体说明

浏览器插件管理器 窗口包含了以下功能：

1、用户自行管理（增加删除）已安装过的所有插件。

2、用户自行添加官方提供的所有插件。

3、支持用户快速查找需要的插件。

4、所见即所得，一键查看不同分组，单个或多个环境下 已安装的插件。

5、所见即所得，一键查找任意关键字环境下已安装的插件。

6、支持一键 勾选多个插件 支持 安装到单个环境或多个环境中。

注：此窗口的长宽和会话管理器的窗口长宽严格一致。

A框列表框，请求服务器API获得用户自有插件列表。

B框列表，列出分组下的各个环境

C框列表，每个环境item.zip中的configdata.xml中的plugin节点列表数据。

A区顶部下拉列表控件显示二个项:

已安装过的浏览器插件（总数）

使用官方所有浏览器插件（总数）

已安装过的浏览器插件  = 用户full包中 account/plugins.xml

使用官方所有浏览器插件 =  请求服务器获得的所有已在服务器存在的所有插件包。

C区列表，显示用户已勾选环境的所有插件，没勾选的环境的插件不用列出来。

查找环境仍旧为全局查找，不是在分组内查找。

左下角的文字显示的是处理过程与处理结果。

XML数据结构附加节点

【2022-3-20新增】

XML数据结构新增说明：

Item.zip 中 configdata.xml

<?xml version="1.0" encoding="gb2312" ?>

<VERSION version="4740" xmlversion="2">

<VER ID="6" VERSION="28" SESSION_UNIQUE_ID="a07db3145ad61566a392d04038671f39" FROMUID="0" SHARETIME="" FROM_ACCOUNT="" GROUP_ID="0" GROUP_NAME="" IR32="1">

<SESSION NAME="27" TOP="0" COLOR="#FFffffffff" SYSTEM="Win32" TEMPLATEID="11602" TEMPLATEFMASK="300" TEMPLATENAME="抖音国际版" PLUGINCOUNT="2"/>

<IS_ANONYMITY ANONYMITY = "0"/>

<COMMENT COMMENT_STRING=""/>

<SESSION_DEFINE_CODE SPHEREGL="0" ENABLE_LOCALHTML5_STORAGE="0" SAVE_LOCALHTML5_STORAGE="0" SAVE_INDEXDB_STORAGE="0"/>

<NETWORK TYPE="http" PMODE="2" IP="************" PORT="20000" USER="liantiao111" PASSWORD="liantiao111" PUBLIC_IP="" FAKEIP="***************" />

<NETWORK_CTR NA="0" FAKE_WRTC="0" SAME_IP="0" IPV6="0" WRTCOFF="0" DNS="" />

<USERAGENT_BROWSER CHROME="0" SAFARI="0" MSIE="0" OTHER="0" REGEN_CONFIG_USERAGENT="0" MAINURL="" />

<USERAGENT_STRING UA="Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/84.0.4147.89 Safari/537.36" UA_LNG="en-US" UA_LNG_STRING="[@en-US@,@US@]" />

<USERAGENT_CTR DISPOPUPS="0" ENABLE_SERVICE="0" BLOCKLNGINPUT="0"  />

<RESOLUTION WIGHT="1920" HEIGHT="1200" />

<RESOLUTION_CTR EMU_SCREEN="0" EMU_TOUCH="0" />

<POSITION LONGITUDE="0.000000" LATITUDE="0.000000" COUNTRY="AD" />

<TIMEZONE TIMEZONE_NAME="Africa/Abidjan (0)" ADD_VALUE=""/>

<FINGERPRINT_CODE AUDIO="1" CANVAS="1" FONTS="1" RETCS="1" DNS="1" AUTOIPCHECK="1" fpver="1"/>

<OTHER_SETTING PLUGINS_MIMETYPE="0" SAVE_ENCRYPT_COOKIES="0" ENABLE_FLASH="0" DYNAMIC_FINGERPRINTS="0" BLOCK_CANVAS_OUTPUT="0" />

<DYNAMIC_FINGERPRINTS_CTR D_AUDIO="0" D_CANVAS="0" D_FONTS="0" D_RETCS="0" D_MEDIA="0" D_WEBGL="0" D_MIME="0" D_PLUGINS="0" />

<PLUGIN_LIST NUM="2">

<PLUGIN ID="13b976d9c3305ebc26e5e0ae338a475d" NAME="setupvpn" VER="*******" MD5="1234567890"/>

<PLUGIN ID="bb32d8993a1fdc9757c4f1e8735baa53" NAME="setupvpn123" VER="*******" MD5="12345678901234567890"/>

</PLUGIN_LIST>

<IS_VALUED VALUED="0" />

<UPDATETIME VALUE="2022-03-21 18:06:58"/>

<CREATETIME VALUE="2021-03-05 18:34:28"/>

</VER>

</VERSION>

1、绿色区域为新增加部份，用来记录此用户所有已安装的插件列表。

2、红色标记记录插件的名称，为保证XML的格式合法，使用hex对此串进行加码。

3、用户在A区进行自有插件增删处理时，此XML绿色区域一并实时进行数据变化。

4、<PLUGIN_LIST NUM="2"> 中的2表示总计已安装了有2个插件

七、在服务器端的数据库结构，表增加。

表一： （候鸟官方插件库，全局库）表名：PLUGINS

字段：

[主键]ID									主键索引

Plugins_name    	(varchar)          	插件名称

Plugins_version   	(varchar)			插件版本

Plugins_size      	(int)					插件包大小使用字节数记录

Plugins_id       	(varchar)			插件ID，GOOGLE市场内插件ID

Plugins_dec			(varchar)			插件说明，默认为空

Plugins_md5     	(varchar)			插件包MD5,由客户端提供

Plugins_filename  	(varchar)			插件存储在服务器上文件名称

Create_Time 		(datetime)			插件插入表中时间，字段属性默认填值

IS_VALID   			(bit)					是否有效，默认为1（有效）


================================================== 表格内容 ==================================================

参数名称 | 类型 | 类型 | 必传 | 必传 | 样例串/默认值 | 样例串/默认值 | 说明 | 说明
Session_ID | array | array | 是 | 是 | 373808cb37bd63f5f7d92415e736e85f | 373808cb37bd63f5f7d92415e736e85f | 环境ID | 环境ID
Proxy_Type | string | string | 是 | 是 | HTTP,HTTPS,SSH,SOCKS4,SOCKS4A,SOCKS5,
Oxylabsauto,Lumauto,Luminati_HTTP,
Luminati_HTTPS,smartproxy,noproxy | HTTP,HTTPS,SSH,SOCKS4,SOCKS4A,SOCKS5,
Oxylabsauto,Lumauto,Luminati_HTTP,
Luminati_HTTPS,smartproxy,noproxy | 指定代理类型
[无代理:noproxy] | 指定代理类型
[无代理:noproxy]
Proxy_Ip | string | string | 否 | 否 | ************* | ************* | 指定代理IP | 指定代理IP
Proxy_Port | string | string | 否 | 否 | 8080 | 8080 | 指定代理端口 | 指定代理端口
Proxy_Username | string | string | 否 | 否 | admin | admin | 指定代理帐户 | 指定代理帐户
Proxy_Password | string | string | 否 | 否 | Password | Password | 指定代理密码 | 指定代理密码
Is_CheckProxy | bit | bit | 否 | 否 | 1 | 1 | 是否自动检测代理【默认:1】 | 是否自动检测代理【默认:1】
TimeZone | string | string | 否 | 否 | US/Alaska -09:000 | US/Alaska -09:000 | 时区 | 时区
CountryCode | string | string | 否 | 否 | US | US | 国家CODE | 国家CODE
CityCode | array | array | 否 | 否 | 城市CODE | 城市CODE
RegionCode | array | array | 否 | 否 | 州CODE | 州CODE
StaticIP_Type | bit | bit | 是 | 是 | 1 or 0 | 1 or 0 | 1:静态IP
0：动态IP | 1:静态IP
0：动态IP
Static_PublicIP | Static_PublicIP | string | string | 否 | 否 | ************ | ************ | 环境公有IP
Static_PrivateIP | Static_PrivateIP | string | string | 否 | 否 | ************** | ************** | 环境私有IP
LanguageCode | array | array | 否 | 否 | En-US;en;q=0.9 | En-US;en;q=0.9 | 环境默认语言 | 环境默认语言

{
"message": "Update Session Proxy Success",
"code": 0,
"data": {
            “Session_Id” : 373808cb37bd63f5f7d92415e736e85f 	//环境ID
       }
}