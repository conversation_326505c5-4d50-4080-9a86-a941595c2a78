﻿<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Window size="1640,590" caption="0,0,0,50" sizebox="4,4,4,4" mininfo="1100,590" roundcorner="9,9,9,9" showshadow="true" shadowsize="3" shadowposition="0,0" shadowcolor="#ff6272A4" fademode="false">
	<Include source="Default.xml" />

  <VerticalLayout height="590" bkcolor="#FF282A36">
    <HorizontalLayout height="37">

    	 <Button name="login" bkimage="icon.png" padding="8,6,0,0" width="28" height="28"/>
      <Label name="session_list_title" padding="6,4,0,0" text="会话数据包管理器" width="260" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" align="left" font="8"></Label>

      <Control />
      <Button name="minbtn" width="28" height="26" tooltip="最小化" normalimage="minbtn.png" hotimage="minbtn_hover.png" pushedimage="minbtnpush.png" />
      <Button name="closebtn" width="28" height="26"  tooltip="关闭" normalimage="closebtn.png" hotimage="closebtn_hover.png" pushedimage="closebtnpush.png" />

    </HorizontalLayout>
  <HorizontalLayout name="bkground">
 <HorizontalLayout name="loading_data" bkcolor="#FF44475A" visible="false">

	    <VerticalLayout height="420">

					     <HorizontalLayout height="240">
					    	 <Control />
					    		<GifAnim name="data_loading" bkimage="dataloading.gif" height="200" width="200" padding="0,40,0,0" auto="true"/>
					    	 <Control />
					     </HorizontalLayout>


					     <HorizontalLayout height="30" >
					    	 <Control />
					    		  <Label name="data_percent" text="55%" width="300" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" align="center" font="10"></Label>
					    	 <Control />
					     </HorizontalLayout>

					     <HorizontalLayout height="60" >
					    	 <Control />
					    		  <Label name="process_description" text="会话环境数据包 正在同步到云端中..  请稍侯.." textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" align="center" font="8"></Label>
					    	 <Control />
					     </HorizontalLayout>

              <HorizontalLayout height="40" >
              </HorizontalLayout>

              <HorizontalLayout name="backarea" height="60" visible="false">
                <Control />
                <Button text="返回" name="back" width="120" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	normalimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
                <Control width="100" />
                <Button text="退出" name="closebtn" width="120" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	normalimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
                <Control />
              </HorizontalLayout>

      </VerticalLayout>

 </HorizontalLayout>

		<VerticalLayout name="data" visible="true">


			<HorizontalLayout height="56" >
			    	 <VerticalLayout width="440">
               <Combo name="searchlist" reselect="true" dropboxsize="0,450" bordersize="0" padding="21,10,0,10" width="420" height="36" borderround="5,5" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="4,2,0,0" bkcolor="#ffdce1e7"
														normalimage="file='Profile\Setting_Combox_Normal_big_large.png' corner='5,5,30,5'" hotimage="file='Profile\Setting_Combox_Hover_large.png' corner='5,5,30,5'" pushedimage="file='Profile\Setting_Combox_Click_large.png' corner='5,5,25,10'"
												combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,30,5'"
												itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0">
               </Combo>
				         <RichEdit name="session_search" pos="20,10,0,10" height="36" width="378" tipvaluecolor="#FF6272A4" borderround="3,3" bkcolor="#FF21222C" font="8" textpadding="10,8,20,0" maxchar="400" tipvalue="请输入关键字查找会话.." multiline="false" textcolor="#FFF8F8F2" rich="false" transparent="false" float="true">
				      </RichEdit>
				     </VerticalLayout>
				     <VerticalLayout width="30">
				        <CheckBox name="opt_ie_cache" selected="false"  visible="true" padding="10,26,0,0" height="18" width="16" normalimage="file='list_check_normal.png'" hotimage="list_check_hover.png" selectedimage="file='list_checked_actvice_normal.png'" ></CheckBox>
				     </VerticalLayout>
				     <VerticalLayout width="360">
				        <Label name="searchl" padding="4,16,0,0" text="搜索包含在会话中的关键词" width="360" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" align="left" font="16"></Label>
				     </VerticalLayout>
			</HorizontalLayout>


      <HorizontalLayout inset="0,0,0,0" bkcolor="#FF282A36">
        	<List name="list_session_manager" vscrollbar="true" minheight="403" bordersize="1,1,1,1" itembkcolor="#FF282A36" itemselectedbkcolor="#FF44475A" itemhotbkcolor="#FF21222C" bordercolor="#FF6272A4">
            <ListHeader height="40" bordersize="1" bordercolor="#FF44475A" bkcolor="#FF21222C">
              <ListHeaderItem text="操作" name="header_device_choice" width="60" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="会话名称" name="header_name" width="156" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="操作平台" name="header_system" width="100" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="代理类型" name="header_proxytype" width="120" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="代理主机" name="header_proxy" width="120" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="代理端口" name="header_proxyport" width="70" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="代理账号" name="header_proxyuser" width="80" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="代理密码" name="header_proxypass" width="80" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="时区" name="header_timezone" width="160" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="国家" name="header_country" width="60" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="语言" name="header_lng" width="80" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="注释" name="header_backup_datetime" width="100" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="创建时间" name="header_ctime" width="100" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="最近使用" name="header_utime" width="100" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="" name="header_sessionid" width="240" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
            </ListHeader>
					</List>
				</HorizontalLayout>






		</VerticalLayout>


	</HorizontalLayout>
    <HorizontalLayout height="52" bkcolor="#FF44475A">
      <Control />
      <VerticalLayout width="160">
      		<Control />
          <Button text="同步" name="tosync" tooltip="将已勾选项的本地会话配置数据包更新到云端" endellipsis="true" enabled="false" padding="20,2,0,0" width="140" height="30" textcolor="#FFF8F8F2" disabledtextcolor="#FF6272A4" align="center" font="14" bkcolor="#FF282A36" bordersize="1" bordercolor="#FF6272A4" borderround="5,5" hotbkcolor="#FF44475A" />
          <Control />
       </VerticalLayout>
      <VerticalLayout width="160">
      		<Control />
          <Button text="打开选定项"  tooltip="用候鸟浏览器调用勾选的会话配置环境数据" name="openselected" endellipsis="true" enabled="false"  padding="20,2,0,0" width="140" height="30" textcolor="#FFF8F8F2" disabledtextcolor="#FF6272A4" align="center" font="14" bkcolor="#FF282A36" bordersize="1" bordercolor="#FF6272A4" borderround="5,5" hotbkcolor="#FF44475A" />
          <Control />
       </VerticalLayout>

      <VerticalLayout width="160">
      		<Control />
          <Button text="删除勾选项" tooltip="删除勾选的会话配置环境数据包"  name="deleteselected" endellipsis="true" enabled="false"  padding="20,2,0,0" width="140" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	bkcolor="#FFffffff" bordersize="1" bordercolor="#ffb3b3b3" borderround="5,5" hotbkcolor="#fff1f2f1" />
          <Control />
       </VerticalLayout>
      <VerticalLayout width="160">
      		<Control />
          <Button text="恢复勾选项" tooltip="从云端同步会话配置环境数据包到本地并覆盖" name="archiveselected" endellipsis="true" enabled="false"  padding="20,2,0,0" width="140" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	bkcolor="#FFffffff" bordersize="1" bordercolor="#ffb3b3b3" borderround="5,5" hotbkcolor="#fff1f2f1" />
          <Control />
       </VerticalLayout>


      <Control />
      <VerticalLayout width="15">
        <Control />
        <Control width="15" height="16" bkimage="file='dragicon.png' src='0,0,14,16' dest='1,0,15,16'"/>
      </VerticalLayout>
    </HorizontalLayout>
  </VerticalLayout>
</Window>
