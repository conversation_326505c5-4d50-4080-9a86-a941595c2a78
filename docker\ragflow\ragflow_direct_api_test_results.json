{"connectivity": true, "endpoints": {"/api/v1/status": {"method": "GET", "status_code": 200, "response": "{\"code\":100,\"data\":null,\"message\":\"<NotFound '404: Not Found'>\"}\n"}, "/api/v1/health": {"method": "GET", "status_code": 200, "response": "{\"code\":100,\"data\":null,\"message\":\"<NotFound '404: Not Found'>\"}\n"}, "/api/v1/datasets": {"method": "GET", "status_code": 200, "response": "{\"code\":0,\"data\":[{\"avatar\":null,\"chunk_count\":6,\"chunk_method\":\"naive\",\"create_date\":\"Mon, 28 Jul 2025 00:48:10 GMT\",\"create_time\":1753634890972,\"created_by\":\"5df1572e624511f0a56b0242ac130006\",\"descr"}, "/api/v1/chat": {"method": "GET", "status_code": 200, "response": "{\"code\":100,\"data\":null,\"message\":\"<NotFound '404: Not Found'>\"}\n"}, "/api/v1/retrieval": {"method": "GET", "status_code": 200, "response": "{\"code\":100,\"data\":null,\"message\":\"<MethodNotAllowed '405: Method Not Allowed'>\"}\n"}, "/api/v1/conversation": {"method": "GET", "status_code": 200, "response": "{\"code\":100,\"data\":null,\"message\":\"<NotFound '404: Not Found'>\"}\n"}, "/v1/chat/completions": {"method": "GET", "status_code": 200, "response": "{\"code\":100,\"data\":null,\"message\":\"<NotFound '404: Not Found'>\"}\n"}, "/health": {"method": "GET", "status_code": 200, "response": "{\"code\":100,\"data\":null,\"message\":\"<NotFound '404: Not Found'>\"}\n"}, "/status": {"method": "GET", "status_code": 200, "response": "{\"code\":100,\"data\":null,\"message\":\"<NotFound '404: Not Found'>\"}\n"}}, "chat_test": {"code": 100, "data": null, "message": "<NotFound '404: Not Found'>"}, "retrieval_test": {"code": 102, "message": "`dataset_ids` is required."}}