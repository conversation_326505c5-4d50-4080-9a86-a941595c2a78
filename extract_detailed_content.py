#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度提取API页面的详细内容
尝试从HTML中提取所有可能的API信息
"""

import requests
import re
import json
import time
from urllib.parse import urljoin
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DetailedContentExtractor:
    def __init__(self, base_url='https://www.mbbrowser.com/api/', output_dir='detailed_api_content'):
        self.base_url = base_url
        self.output_dir = output_dir
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        
        # 创建输出目录
        Path(self.output_dir).mkdir(exist_ok=True)
    
    def fetch_page_with_details(self, url):
        """获取页面并提取所有可能的内容"""
        try:
            full_url = urljoin(self.base_url, url)
            logger.info(f"正在深度分析页面: {full_url}")
            
            response = self.session.get(full_url, timeout=30)
            response.raise_for_status()
            response.encoding = 'utf-8'
            
            return response.text
            
        except Exception as e:
            logger.error(f"获取页面失败: {url}, 错误: {str(e)}")
            return None
    
    def extract_all_text_content(self, html_content):
        """提取页面中所有可能的文本内容"""
        try:
            # 移除script和style标签
            clean_html = re.sub(r'<script[^>]*>.*?</script>', '', html_content, flags=re.DOTALL | re.IGNORECASE)
            clean_html = re.sub(r'<style[^>]*>.*?</style>', '', clean_html, flags=re.DOTALL | re.IGNORECASE)
            
            # 移除HTML注释
            clean_html = re.sub(r'<!--.*?-->', '', clean_html, flags=re.DOTALL)
            
            # 提取所有文本内容，保持基本结构
            # 处理标题
            clean_html = re.sub(r'<h([1-6])[^>]*>(.*?)</h\1>', r'\n\2\n' + '='*20 + '\n', clean_html, flags=re.DOTALL | re.IGNORECASE)
            
            # 处理段落
            clean_html = re.sub(r'<p[^>]*>(.*?)</p>', r'\1\n\n', clean_html, flags=re.DOTALL | re.IGNORECASE)
            
            # 处理列表项
            clean_html = re.sub(r'<li[^>]*>(.*?)</li>', r'• \1\n', clean_html, flags=re.DOTALL | re.IGNORECASE)
            
            # 处理换行
            clean_html = re.sub(r'<br[^>]*/?>', '\n', clean_html, flags=re.IGNORECASE)
            
            # 处理div（可能包含重要内容）
            clean_html = re.sub(r'<div[^>]*>(.*?)</div>', r'\1\n', clean_html, flags=re.DOTALL | re.IGNORECASE)
            
            # 移除所有剩余的HTML标签
            clean_html = re.sub(r'<[^>]+>', '', clean_html)
            
            # 解码HTML实体
            clean_html = clean_html.replace('&nbsp;', ' ')
            clean_html = clean_html.replace('&lt;', '<')
            clean_html = clean_html.replace('&gt;', '>')
            clean_html = clean_html.replace('&amp;', '&')
            clean_html = clean_html.replace('&quot;', '"')
            clean_html = clean_html.replace('&#39;', "'")
            
            # 清理多余的空白
            lines = clean_html.split('\n')
            cleaned_lines = []
            
            for line in lines:
                line = line.strip()
                if line:
                    cleaned_lines.append(line)
                elif cleaned_lines and cleaned_lines[-1] != '':
                    cleaned_lines.append('')
            
            return '\n'.join(cleaned_lines)
            
        except Exception as e:
            logger.error(f"提取文本内容失败: {str(e)}")
            return html_content
    
    def search_for_api_patterns(self, content):
        """搜索API相关的模式"""
        api_patterns = {
            'http_methods': re.findall(r'\b(GET|POST|PUT|DELETE|PATCH)\b', content, re.IGNORECASE),
            'urls': re.findall(r'https?://[^\s<>"\']+', content),
            'json_objects': re.findall(r'\{[^{}]*\}', content),
            'parameters': re.findall(r'参数[:：]\s*([^\n]+)', content),
            'responses': re.findall(r'响应[:：]\s*([^\n]+)', content),
            'examples': re.findall(r'示例[:：]\s*([^\n]+)', content),
            'error_codes': re.findall(r'错误码[:：]\s*([^\n]+)', content),
            'api_endpoints': re.findall(r'/api/[^\s<>"\']+', content),
        }
        
        return api_patterns
    
    def extract_structured_content(self, content, page_title):
        """提取结构化的内容"""
        try:
            # 查找可能的API功能列表
            api_functions = []
            
            # 模式1: 数字编号的功能列表
            numbered_functions = re.findall(r'(\d+)、([^<>\n]+)', content)
            for number, title in numbered_functions:
                if len(title.strip()) > 3:
                    api_functions.append({
                        'number': number,
                        'title': title.strip(),
                        'type': 'numbered_function'
                    })
            
            # 模式2: 查找可能的API描述段落
            api_descriptions = []
            paragraphs = content.split('\n\n')
            
            for para in paragraphs:
                para = para.strip()
                if len(para) > 50:  # 只关注较长的段落
                    # 检查是否包含API相关关键词
                    api_keywords = ['接口', 'API', '请求', '响应', '参数', '返回', 'JSON', 'HTTP']
                    if any(keyword in para for keyword in api_keywords):
                        api_descriptions.append(para)
            
            return {
                'functions': api_functions,
                'descriptions': api_descriptions,
                'patterns': self.search_for_api_patterns(content)
            }
            
        except Exception as e:
            logger.error(f"提取结构化内容失败: {str(e)}")
            return {'functions': [], 'descriptions': [], 'patterns': {}}
    
    def save_detailed_analysis(self, url, content, structured_data):
        """保存详细分析结果"""
        try:
            # 生成文件名
            safe_url = url.replace('/', '_').replace(':', '').strip('_')
            filename = f"detailed_analysis_{safe_url}.txt"
            filepath = Path(self.output_dir) / filename
            
            # 创建详细报告
            report = f"详细内容分析报告\n"
            report += f"URL: {urljoin(self.base_url, url)}\n"
            report += f"分析时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n"
            report += "=" * 60 + "\n\n"
            
            # 添加发现的API功能
            if structured_data['functions']:
                report += "发现的API功能:\n"
                report += "-" * 30 + "\n"
                for func in structured_data['functions']:
                    report += f"{func['number']}. {func['title']}\n"
                report += "\n"
            
            # 添加API相关描述
            if structured_data['descriptions']:
                report += "API相关描述:\n"
                report += "-" * 30 + "\n"
                for i, desc in enumerate(structured_data['descriptions'][:5], 1):  # 只显示前5个
                    report += f"{i}. {desc}\n\n"
            
            # 添加发现的模式
            patterns = structured_data['patterns']
            if any(patterns.values()):
                report += "发现的API模式:\n"
                report += "-" * 30 + "\n"
                for pattern_type, matches in patterns.items():
                    if matches:
                        report += f"{pattern_type}: {len(matches)} 个匹配\n"
                        for match in matches[:3]:  # 只显示前3个
                            report += f"  - {match}\n"
                        if len(matches) > 3:
                            report += f"  ... 还有 {len(matches) - 3} 个\n"
                        report += "\n"
            
            # 添加完整内容
            report += "完整页面内容:\n"
            report += "=" * 60 + "\n"
            report += content
            
            # 保存文件
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(report)
            
            logger.info(f"已保存详细分析: {filename}")
            return True
            
        except Exception as e:
            logger.error(f"保存分析失败: {str(e)}")
            return False
    
    def analyze_all_pages(self):
        """分析所有主要页面"""
        pages_to_analyze = [
            '/api/',
            '/api/session',
            '/api/login',
            '/api/browser',
            '/api/group',
            '/api/script',
            '/api/plugin',
            '/api/members',
            '/api/help',
            '/api/question'
        ]
        
        results = []
        
        for page in pages_to_analyze:
            logger.info(f"正在分析页面: {page}")
            
            # 获取页面内容
            html_content = self.fetch_page_with_details(page)
            if not html_content:
                continue
            
            # 提取文本内容
            text_content = self.extract_all_text_content(html_content)
            
            # 提取结构化数据
            structured_data = self.extract_structured_content(text_content, page)
            
            # 保存分析结果
            success = self.save_detailed_analysis(page, text_content, structured_data)
            
            if success:
                results.append({
                    'page': page,
                    'functions_found': len(structured_data['functions']),
                    'descriptions_found': len(structured_data['descriptions']),
                    'content_length': len(text_content)
                })
            
            time.sleep(2)  # 避免请求过快
        
        return results
    
    def create_summary_report(self, results):
        """创建总结报告"""
        try:
            summary = "API内容提取总结报告\n"
            summary += "=" * 50 + "\n\n"
            
            total_functions = sum(r['functions_found'] for r in results)
            total_descriptions = sum(r['descriptions_found'] for r in results)
            
            summary += f"分析页面数: {len(results)}\n"
            summary += f"发现API功能总数: {total_functions}\n"
            summary += f"发现描述段落总数: {total_descriptions}\n\n"
            
            summary += "各页面详情:\n"
            summary += "-" * 30 + "\n"
            
            for result in results:
                summary += f"页面: {result['page']}\n"
                summary += f"  API功能: {result['functions_found']} 个\n"
                summary += f"  描述段落: {result['descriptions_found']} 个\n"
                summary += f"  内容长度: {result['content_length']} 字符\n\n"
            
            # 保存总结
            summary_path = Path(self.output_dir) / "summary_report.txt"
            with open(summary_path, 'w', encoding='utf-8') as f:
                f.write(summary)
            
            logger.info("已创建总结报告")
            
        except Exception as e:
            logger.error(f"创建总结报告失败: {str(e)}")

def main():
    """主函数"""
    print("API详细内容提取器启动...")
    print("="*50)
    
    extractor = DetailedContentExtractor()
    results = extractor.analyze_all_pages()
    extractor.create_summary_report(results)
    
    print("="*50)
    print("分析完成!")
    print("- 输出目录: detailed_api_content")
    print("- 总结报告: detailed_api_content/summary_report.txt")

if __name__ == "__main__":
    main()
