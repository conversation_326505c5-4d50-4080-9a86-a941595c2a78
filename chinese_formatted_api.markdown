# 格式化的API文档

本文档将 `full_api.txt` 的内容格式化为一个结构化、一致且适合AI阅读的中文格式。每个API都以清晰的标题、路径、方法、内容类型、描述、请求参数（表格形式）、请求示例和成功响应示例的形式呈现。

---

## API 1: 从我的脚本库中指派脚本到目标环境中
- **路径**: `/api/v1/session/id_script_add`
- **方法**: POST
- **内容类型**: application/json
- **API版本支持**: 候鸟客户端版本**********及以上
- **描述**: 从脚本库中将一个或多个脚本指派到指定的目标环境中。（支持一次性将最多100个脚本ID分配到一个环境中。）

### 请求参数
| 参数名称    | 类型   | 是否必传 | 示例/默认值                                              | 说明                                |
|-----------|------|------|-------------------------------------------------------|-----------------------------------|
| Script_ID | 数组   | 是    | ["7579a099e6fcee76fd1272ccdc30c1cc", "c1f3f1b3d5072985581fe54343f1e524"] | 要指派的脚本ID（最多100个）                  |
| Session_ID| 字符串 | 是    | "914a9b97c6787a231ed2ab25e02ad5c9"                     | 目标环境ID（一次最多1个环境）                 |

### 请求示例
```json
{
    "Script_ID": ["7579a099e6fcee76fd1272ccdc30c1cc", "c1f3f1b3d5072985581fe54343f1e524"],
    "Session_ID": "914a9b97c6787a231ed2ab25e02ad5c9"
}
```

### 成功响应
```json
{
    "message": "环境脚本添加成功",
    "code": 0,
    "data": {
        "listcontainer": [{
            "Session_Name": "商用业务环境一",
            "Session_ID": "914a9b97c6787a231ed2ab25e02ad5c9",
            "Group_Name": "默认",
            "Actived_script_id": "O73808cb37bd63f5f7d92415e736e999",
            "Actiived_script_name": "这是一个脚本例子",
            "Actiived_script_encode": "true",
            "Script_Count": "3",
            "UnActived_script_list": [{
                "UnActived_script_encode": "false",
                "UnActived_script_Name": "AAA",
                "UnActived_script_ID": "7579a099e6fcee76fd1272ccdc30c1cc"
            }, {
                "UnActived_script_encode": "false",
                "UnActived_script_Name": "BBB",
                "UnActived_script_ID": "c1f3f1b3d5072985581fe54343f1e524"
            }],
            "status": 0
        }]
    }
}
```

---

## API 2: 关闭环境
- **路径**: `/api/v1/browser/stop`
- **方法**: POST
- **内容类型**: application/json
- **描述**: 关闭指定的环境。

### 请求参数
| 参数名称       | 类型    | 是否必传 | 示例/默认值                             | 说明       |
|------------|-------|------|-------------------------------------|----------|
| Session_ID | 字符串 | 是    | "373808cb37bd63f5f7d92415e736e85f"  | 环境ID     |

### 请求示例
```json
{
    "Session_ID": "373808cb37bd63f5f7d92415e736e85f"
}
```

### 成功响应
```json
{
    "message": "成功",
    "code": 0,
    "data": {
        "action": "停止环境ID",
        "status": 0
    }
}
```

---

## API 3: 切换指定环境已激活脚本
- **路径**: `/api/v1/session/id_script_active`
- **方法**: POST
- **内容类型**: application/json
- **描述**: 将指定环境中的一个脚本设置为激活状态。

### 请求参数
| 参数名称           | 类型    | 是否必传 | 示例/默认值                             | 说明                    |
|----------------|-------|------|-------------------------------------|-----------------------|
| Session_ID     | 字符串 | 是    | "373808cb37bd63f5f7d92415e736e85f"  | 环境ID（单个或多个）          |
| Active_Script_ID | 字符串 | 是    | "O73808cb37bd63f5f7d92415e736e999"  | 环境中已存在的脚本ID          |

### 请求示例
```json
{
    "Session_ID": "373808cb37bd63f5f7d92415e736e85f",
    "Active_Script_ID": "O73808cb37bd63f5f7d92415e736e999"
}
```

### 成功响应
```json
{
    "message": "环境脚本激活成功",
    "code": 0,
    "data": {
        "listcontainer": [{
            "Session_Name": "商用业务环境一",
            "Session_ID": "373808cb37bd63f5f7d92415e736e85f",
            "Group_Name": "默认",
            "Actived_script_id": "O73808cb37bd63f5f7d92415e736e999",
            "Actiived_script_name": "这是一个脚本例子",
            "Actiived_script_encode": "true",
            "Script_Count": "4",
            "UnActived_script_list": [{
                "UnActived_script_encode": "false",
                "UnActived_script_Name": "AAA",
                "UnActived_script_ID": "17c70e014d61b1fa43d3638ca5a1bc21"
            }, {
                "UnActived_script_encode": "false",
                "UnActived_script_Name": "BBB",
                "UnActived_script_ID": "17c70e014d61b1fa43d3638ca5a1bc22"
            }],
            "status": 0
        }]
    }
}
```

---

## API 4: 列出当前帐户下所有已安装的插件
- **路径**: `/api/v1/plugin/list`
- **方法**: POST
- **内容类型**: application/json
- **描述**: 列出当前帐户下所有已安装的插件（包括插件ID和名称）。

### 请求参数
无参数。

### 成功响应
```json
{
    "message": "全局已安装插件列表成功",
    "code": 0,
    "data": {
        "listcontainer": [{
            "Plugins_Count": "4",
            "Plugin_list": [{
                "Plugin_Name": "AAA",
                "Plugin_Id": "jjbnhpnlakcdgfnnldamfeinfmahhdlm",
                "Plugin_Ver": "*******"
            }, {
                "Plugin_Name": "BBB",
                "Plugin_Id": "jjbnhpnlakcdgfnnldamfeinfmahhdlm",
                "Plugin_Ver": "*******"
            }],
            "status": 0
        }]
    }
}
```

---

## API 5: 创建环境
- **路径**: `/api/v1/session/create`
- **方法**: POST
- **内容类型**: application/json
- **描述**: 创建一个环境，支持配置名称、描述、分组和代理设置。成功后返回环境ID。

### 请求参数
| 参数名称               | 类型     | 是否必传 | 示例/默认值                                                                 | 说明                                                                                   |
|--------------------|--------|------|-------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------|
| Session_Name       | 字符串  | 是    | "20230223"                                                                    | 环境名称，最多60个字符                                                          |
| Session_Desc       | 字符串  | 否    | "这是一个测试"                                                              | 环境描述，最多150个字符                                                  |
| Session_Group      | 字符串  | 否    | "新分组"                                                                    | 环境分组，最多30个字符；未提供时默认为"默认"                  |
| Session_System     | 字符串  | 否    | "Windows"                                                                     | 环境操作系统                                                         |
| Session_Resolution | 字符串  | 否    | "1024x768"                                                                    | 环境分辨率                                                                       |
| Session_User_Agent | 字符串  | 否    | "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36" | 环境的UA值 [2023/07/03 新增] |
| Proxy_Type         | 字符串  | 否    | "HTTP"                                                                        | 代理类型 [HTTP, HTTPS, SSH, SOCKS4, SOCKS4A, SOCKS5, Oxylabsauto, Lumauto, Luminati_HTTP, Luminati_HTTPS, smartproxy, noproxy] |
| Proxy_Ip           | 字符串  | 否    | "127.0.0.1"                                                                   | 代理IP                                                                                     |
| Proxy_Port         | 字符串  | 否    | "1080"                                                                        | 代理端口                                                                                   |
| Proxy_Username     | 字符串  | 否    | "TEST"                                                                        | 代理用户名                                                                               |
| Proxy_Password     | 字符串  | 否    | "TEST"                                                                        | 代理密码                                                                               |
| TimeZone           | 字符串  | 否    | "美国/阿拉斯加 -09:000"                                                           | 时区                                                                                    |
| CountryCode        | 字符串  | 否    | ""                                                                            | 国家代码                                                                                 |
| CityCode           | 字符串  | 否    | ""                                                                            | 城市代码                                                                                    |
| RegionCode         | 字符串  | 否    | ""                                                                            | 州/地区代码                                                                            |
| LanguageCode       | 字符串  | 否    | ""                                                                            | 环境默认语言                                                                 |
| Cookie             | 字符串  | 否    | "这是一个COOKIE"                                                               | JSON格式的cookie文本                                                                   |
| Automatic_Configure| 位     | 是    | 1                                                                             | 自动配置高级指纹参数 [0: 快速创建, 1: 包含代理IP验证] |
| Disable_video      | 位     | 否    | 0                                                                             | 视频限流 [0: 关闭, 1: 开启]                                                             |
| Disable_img        | 位     | 否    | 0                                                                             | 图片限流 [0: 关闭, 1: 开启]                                                             |
| HomePage_url       | 字符串  | 否    | "https://www.baidu.com"                                                       | 环境起始页；未设置时默认为"https://www.yalala.com/?wd=mb"              |

### 请求示例
```json
{
    "Session_Name": "20230223",
    "Session_Desc": "这是一个测试",
    "Session_Group": "新分组",
    "Proxy_Type": "HTTP",
    "Proxy_Ip": "127.0.0.1",
    "Proxy_Port": "1080",
    "Proxy_Username": "TEST",
    "Proxy_Password": "TEST",
    "TimeZone": "美国/阿拉斯加 -09:000",
    "CountryCode": "",
    "CityCode": "",
    "RegionCode": "",
    "LanguageCode": "",
    "Cookie": "这是一个COOKIE",
    "Automatic_Configure": 1,
    "Disable_video": 0,
    "Disable_img": 0
}
```

### 成功响应
```json
{
    "requestId": "8b558e5c5d1c437183c34aa03a09a368",
    "message": "添加成功",
    "code": 0,
    "data": {
        "Session_Id": "373808cb37bd63f5f7d92415e736e85f"
    }
}
```

---

## API 6: 删除指定环境中的插件
- **路径**: `/api/v1/session/plugin_delete`
- **方法**: POST
- **内容类型**: application/json
- **描述**: 删除指定环境中的已安装插件。成功时返回`code: 0`和`message: 删除插件成功`。支持一次性删除环境中所有插件（每次仅处理一个环境）。

### 请求参数
| 参数名称    | 类型   | 是否必传 | 示例/默认值                                              | 说明                                |
|-----------|------|------|-------------------------------------------------------|-----------------------------------|
| Session_ID | 字符串 | 是    | "373808cb37bd63f5f7d92415e736e85f"                     | 指定环境ID                  |
| Plugin_ID  | 数组   | 是    | ["ncennffkjdiamlpmcbajkmaiiiddgioo", "f994d8e641ce7006acfa36c901829ff2"] | 要删除的插件ID（支持多个）                  |

### 请求示例
```json
{
    "Session_ID": "373808cb37bd63f5f7d92415e736e85f",
    "Plugin_ID": ["ncennffkjdiamlpmcbajkmaiiiddgioo", "f994d8e641ce7006acfa36c901829ff2"]
}
```

### 成功响应
```json
{
    "message": "插件删除完成",
    "code": 0,
    "data": {
        "Delete_Plugin_Success": "ncennffkjdiamlpmcbajkmaiiiddgioo",
        "Delete_Plugin_Failed": "f994d8e641ce7006acfa36c901829ff2"
    }
}
```

---

## API 7: 删除环境
- **路径**: `/api/v1/session/delete`
- **方法**: POST
- **内容类型**: application/json
- **描述**: 删除指定的环境。成功时返回`code: 0`和`message: 删除环境成功`。支持一次性删除最多2000个环境。删除的环境将在本地客户端中彻底删除，但可以在WEB控制台的“历史环境”中找回。

### 请求参数
| 参数名称       | 类型   | 是否必传 | 示例/默认值                                              | 说明                                |
|------------|------|------|-------------------------------------------------------|-----------------------------------|
| Session_ID | 数组   | 是    | ["373808cb37bd63f5f7d92415e736e85f", "7579a099e6fcee76fd1272ccdc30c1cc", "c1f3f1b3d5072985581fe54343f1e524"] | 支持多个环境ID                  |
| Is_Delete_All | 位     | 否    | 0                                                      | 如果为1，忽略Session_ID，删除所有环境                  |

### 请求示例
```json
{
    "Session_ID": ["373808cb37bd63f5f7d92415e736e85f", "7579a099e6fcee76fd1272ccdc30c1cc", "c1f3f1b3d5072985581fe54343f1e524"]
}
```

### 成功响应
```json
{
    "msg": "环境删除完成",
    "code": -12,
    "data": {
        "Delete_Session_Success": "373808cb37bd63f5f7d92415e736e85f,c1f3f1b3d5072985581fe54343f1e524",
        "Delete_Session_Failed": "7579a099e6fcee76fd1272ccdc30c1cc"
    }
}
```

---

## API 8: 删除环境分组
- **路径**: `/api/v1/group/del`
- **方法**: POST
- **内容类型**: application/json
- **描述**: 批量删除指定的环境分组。成功时返回`true`。

### 请求参数
| 参数名称    | 类型   | 是否必传 | 示例/默认值                                              | 说明                                |
|-----------|------|------|-------------------------------------------------------|-----------------------------------|
| Group_Name | 数组   | 是    | ["分组名称"]                         | 单个或多个分组名称                  |

### 请求示例
```json
{
    "Group_Name": ["分组名称"]
}
```

### 成功响应
```json
{
    "msg": "分组删除成功",
    "code": 0,
    "data": true
}
```

---

## API 9: 安装多个插件到指定环境中
- **路径**: `/api/v1/session/plugin_install`
- **方法**: POST
- **内容类型**: application/json
- **描述**: 将新插件安装到指定的环境中（支持单个或多个插件）。

### 请求参数
| 参数名称    | 类型   | 是否必传 | 示例/默认值                                              | 说明                                |
|-----------|------|------|-------------------------------------------------------|-----------------------------------|
| Session_ID | 字符串 | 是    | "373808cb37bd63f5f7d92415e736e85f"                     | 指定环境ID                  |
| Plugin_ID  | 数组   | 是    | ["ncennffkjdiamlpmcbajkmaiiiddgioo", "f994d8e641ce7006acfa36c901829ff2"] | 要安装的插件ID（支持多个）                  |
| Plugin_ver | 字符串 | 否    | "1.0.0"                                                | 插件版本（可选）                  |

### 请求示例
```json
{
    "Session_ID": "373808cb37bd63f5f7d92415e736e85f",
    "Plugin_ID": ["ncennffkjdiamlpmcbajkmaiiiddgioo", "f994d8e641ce7006acfa36c901829ff2"]
}
```

### 成功响应
```json
{
    "message": "插件安装成功",
    "code": 0,
    "data": {
        "listcontainer": [{
            "Session_Name": "商用业务环境一",
            "Session_ID": "373808cb37bd63f5f7d92415e736e85f",
            "Group_Name": "默认",
            "Plugins_Count": "4",
            "Plugin_list": [{
                "Plugin_Name": "AAA",
                "Plugin_Id": "jjbnhpnlakcdgfnnldamfeinfmahhdlm",
                "Plugin_Ver": "*******"
            }, {
                "Plugin_Name": "BBB",
                "Plugin_Id": "jjbnhpnlakcdgfnnldamfeinfmahhdlm",
                "Plugin_Ver": "*******"
            }],
            "status": 0
        }]
    }
}
```

---

## API 10: 导入Cookie
- **路径**: `/api/v1/session/import-cookie`
- **方法**: POST
- **内容类型**: application/json
- **描述**: 将Cookie导入到指定的环境中。成功时返回`Code: 0`和`Message: 导入环境成功`。

### 请求参数
| 参数名称       | 类型    | 是否必传 | 示例/默认值                             | 说明       |
|------------|-------|------|-------------------------------------|----------|
| Session_ID | 字符串 | 是    | "373808cb37bd63f5f7d92415e736e85f"  | 目标环境ID     |
| Cookie_File| 字符串 | 是    | "c:\\0221cookie"                    | Cookie文件路径（支持text、JSON格式） |

### 请求示例
```json
{
    "Session_ID": "373808cb37bd63f5f7d92415e736e85f",
    "Cookie_File": "c:\\0221cookie"
}
```

### 成功响应
```json
{
    "msg": "导入Cookie成功",
    "code": 0,
    "data": {
        "Session_Id": "373808cb37bd63f5f7d92415e736e85f",
        "cookie": "cookie命令标题"
    }
}
```

---

## API 11: 导出Cookie
- **路径**: `/api/v1/session/export-cookie`
- **方法**: POST
- **内容类型**: application/json
- **描述**: 从指定的环境中导出Cookie。成功时返回`Code: 0`和`message: 导出环境成功`。

### 请求参数
| 参数名称           | 类型    | 是否必传 | 示例/默认值                             | 说明       |
|----------------|-------|------|-------------------------------------|----------|
| Session_ID     | 字符串 | 是    | "373808cb37bd63f5f7d92415e736e85f"  | 源环境ID     |
| Export_Cookie_File | 字符串 | 是    | "c:\\0221cookie"                    | 导出Cookie的路径 |

### 请求示例
```json
{
    "Session_ID": "373808cb37bd63f5f7d92415e736e85f",
    "Export_Cookie_File": "c:\\0221cookie"
}
```

### 成功响应
```json
{
    "msg": "导出Cookie成功",
    "code": 0,
    "data": {
        "Session_Id": "373808cb37bd63f5f7d92415e736e85f",
        "cookie": "cookie命令标题"
    }
}
```

---

## API 12: 将指定环境从一个分组转移到另一个分组
- **路径**: `/api/v1/group/move_session`
- **方法**: POST
- **内容类型**: application/json
- **描述**: 批量将指定的环境从一个分组转移到另一个分组。成功时返回`Status: 0`。

### 请求参数
| 参数名称            | 类型    | 是否必传 | 示例/默认值                                              | 说明       |
|-----------------|-------|------|-------------------------------------------------------|----------|
| Session_ID      | 数组   | 是    | ["ce4c125179fbe98ba362384115a6f82c", "ba404bdda87bdf5a7c5ecff2a4a260ea"] | 单个或多个环境ID |
| Orgin_Group_Name| 字符串 | 否    | "我的分组一"                                         | 当前分组名称   |
| Target_Group_Name| 字符串 | 是    | "我的分组二"                                         | 目标分组名称   |

### 请求示例
```json
{
    "Session_ID": ["ce4c125179fbe98ba362384115a6f82c", "ba404bdda87bdf5a7c5ecff2a4a260ea"],
    "Orgin_Group_Name": "我的分组一",
    "Target_Group_Name": "我的分组二"
}
```

### 成功响应
```json
{
    "msg": "环境转移成功",
    "code": 0,
    "data": [{
        "Session_ID": "ce4c125179fbe98ba362384115a6f82c",
        "Orgin_Group_Name": "我的分组一",
        "Target_Group_Name": "我的分组二",
        "status": "0"
    }, {
        "Session_ID": "ba404bdda87bdf5a7c5ecff2a4a260ea",
        "Orgin_Group_Name": "我的分组一",
        "Target_Group_Name": "我的分组二",
        "status": "0"
    }]
}
```

---

## API 13: 从指定环境中移除未激活脚本
- **路径**: `/api/v1/session/id_script_Delete`
- **方法**: POST
- **内容类型**: application/json
- **API版本支持**: 候鸟客户端版本*********及以上
- **描述**: 从单个环境中移除指定的未激活脚本（或未激活脚本集合）。

### 请求参数
| 参数名称    | 类型   | 是否必传 | 示例/默认值                                              | 说明                                |
|-----------|------|------|-------------------------------------------------------|-----------------------------------|
| Session_ID | 字符串 | 是    | "914a9b97c6787a231ed2ab25e02ad5c9"                     | 单个或多个环境ID                  |
| Script_ID  | 数组   | 是    | ["373808cb37bd63f5f7d92415e736e85f", "705cc4c139e69b729a2fd277f30e1863"] | 环境中已存在的未激活脚本ID                  |

### 请求示例
```json
{
    "Session_ID": "914a9b97c6787a231ed2ab25e02ad5c9",
    "Script_ID": ["373808cb37bd63f5f7d92415e736e85f", "705cc4c139e69b729a2fd277f30e1863"]
}
```

### 成功响应
```json
{
    "message": "环境脚本删除成功",
    "code": 0,
    "data": {
        "listcontainer": [{
            "Session_Name": "商用业务环境一",
            "Session_ID": "914a9b97c6787a231ed2ab25e02ad5c9",
            "Group_Name": "默认",
            "Actived_script_id": "O73808cb37bd63f5f7d92415e736e999",
            "Actiived_script_name": "这是一个脚本例子",
            "Actiived_script_encode": "true",
            "Script_Count": "3",
            "UnActived_script_list": [{
                "UnActived_script_encode": "false",
                "UnActived_script_Name": "AAA",
                "UnActived_script_ID": "7579a099e6fcee76fd1272ccdc30c1cc"
            }, {
                "UnActived_script_encode": "false",
                "UnActived_script_Name": "BBB",
                "UnActived_script_ID": "c1f3f1b3d5072985581fe54343f1e524"
            }],
            "status": 0
        }]
    }
}
```

---

## API 14: 帐号登录
- **路径**: `/login`
- **方法**: POST
- **内容类型**: application/json
- **描述**: 实时切换帐号并登录。

### 请求参数
| 参数名称   | 类型    | 是否必传 | 示例/默认值                             | 说明       |
|--------|-------|------|-------------------------------------|----------|
| Account| 字符串 | 是    | "<EMAIL>"                     | 用户凭证：帐号 |
| APP_ID | 字符串 | 是    | "7e147176e1d756eb03c0e18e7b640c23"  | 用户凭证：应用ID |
| APP_KEY| 字符串 | 是    | "kwMTYxNG_MzlkZjhlZmM2YWNiY2M5MTkw" | 用户凭证：应用密钥 |
| return | 字符串 | 否    | "on"                                | 控制台输出："on"（默认），"off" |
| logs   | 字符串 | 否    | "on"                                | 日志记录："on"（默认），"off" |
| hide   | 字符串 | 否    | "on"                                | UI模式："on"（默认，全自动化），"off"（半自动化） |

### 请求示例
```json
{
    "Account": "<EMAIL>",
    "APP_ID": "7e147176e1d756eb03c0e18e7b640c23",
    "APP_KEY": "kwMTYxNG_MzlkZjhlZmM2YWNiY2M5MTkw",
    "return": "on",
    "logs": "on",
    "hide": "on"
}
```

### 成功响应
```json
{
    "msg": "登录成功",
    "status": 0,
    "data": "登录帐号: <EMAIL>"
}
```

---

## API 15: 强制终止环境
- **路径**: `/api/v1/browser/kill`
- **方法**: POST
- **内容类型**: application/json
- **描述**: 强制终止指定的环境。

### 请求参数
| 参数名称       | 类型    | 是否必传 | 示例/默认值                             | 说明       |
|------------|-------|------|-------------------------------------|----------|
| Session_ID | 字符串 | 是    | "373808cb37bd63f5f7d92415e736e85f"  | 环境ID     |

### 请求示例
```json
{
    "Session_ID": "373808cb37bd63f5f7d92415e736e85f"
}
```

### 成功响应
```json
{
    "message": "成功终止",
    "code": 0,
    "data": {
        "action": "停止环境ID",
        "status": 0
    }
}
```

---

## API 16: 打开环境
- **路径**: `/api/v1/browser/start`
- **方法**: POST
- **内容类型**: application/json
- **描述**: 启动指定的环境，成功后提供浏览器调试接口以供Selenium和Puppeteer自动化脚本使用。

### 请求参数
| 参数名称    | 类型      | 是否必传 | 示例/默认值                                              | 说明       |
|---------|---------|------|-------------------------------------------------------|----------|
| Session_ID | 数组     | 是    | ["373808cb37bd63f5f7d92415e736e85f"]                   | 环境ID     |
| isHeadless | 布尔值   | 否    | true                                                  | True: 无头模式, False: 有头模式 |
| args    | 数组     | 否    | ["--disable-extensions", "--blink-settings=imagesEnabled=false"] | 启动参数   |

### 请求示例
```json
{
    "Session_ID": ["373808cb37bd63f5f7d92415e736e85f"],
    "isHeadless": true,
    "args": ["--disable-extensions", "--blink-settings=imagesEnabled=false"]
}
```

### 成功响应
```json
{
    "message": "成功",
    "code": 0,
    "data": {
        "listid": [{
            "Session_Name": "商用业务环境一",
            "Session_ID": "373808cb37bd63f5f7d92415e736e85f",
            "Group_Name": "默认",
            "Actived_script_id": "O73808cb37bd63f5f7d92415e736e999",
            "Actiived_script_name": "这是一个脚本例子",
            "Actiived_script_encode": "true",
            "Weblogin_Account_Count": "4",
            "Weblogin_Account_name": "<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>",
            "Plugins_Count": "4",
            "Plugin_Id": "jjbnhpnlakcdgfnnldamfeinfmahhdlm,jjbnhpnlakcdgfnnldamfeinfmahhdlm,jjbnhpnlakcdgfnnldamfeinfmahhdlm,jjbnhpnlakcdgfnnldamfeinfmahhdlm",
            "template_id": "123456",
            "template_name": "TikTok国际版",
            "browser_Path": "D:\\mbbrowser\\Chromium_x64\\chromium.exe",
            "browser_CDP_Port": 46973,
            "MBData_Path": "C:\\MBDATA\\xxxxxxxxxx\\xxxxxxxxxx\\xxxxxxxxxxx",
            "Public_ip": "************",
            "Internel_ip": "**************",
            "isDynamicIp": false,
            "StartPage": "about:blank",
            "proxyType": "socks5",
            "proxy_ip": "127.0.0.1",
            "proxy_port": "1080",
            "isHeadless": "true",
            "webdriver": "C:\\Users\\<USER>\\houniao\\Driver\\100\\chromedriver.exe",
            "status": 0
        }],
        "total": 1
    }
}
```

---

## API 17: 将指定环境中的脚本设置为非激活状态
- **路径**: `/api/v1/session/id_script_Deactivate`
- **方法**: POST
- **内容类型**: application/json
- **API版本支持**: 候鸟客户端版本*********及以上
- **描述**: 将单个环境中的指定脚本设置为非激活状态。

### 请求参数
| 参数名称    | 类型    | 是否必传 | 示例/默认值                             | 说明       |
|---------|-------|------|-------------------------------------|----------|
| Session_ID | 字符串 | 是    | "373808cb37bd63f5f7d92415e736e85f"  | 单个或多个环境ID |
| Script_ID  | 字符串 | 是    | "O73808cb37bd63f5f7d92415e736e999"  | 环境中已存在的脚本ID |

### 请求示例
```json
{
    "Session_ID": "373808cb37bd63f5f7d92415e736e85f",
    "Script_ID": "O73808cb37bd63f5f7d92415e736e999"
}
```

### 成功响应
```json
{
    "message": "环境脚本取消激活成功",
    "code": 0,
    "data": {
        "listcontainer": [{
            "Session_Name": "商用业务环境一",
            "Session_ID": "914a9b97c6787a231ed2ab25e02ad5c9",
            "Group_Name": "默认",
            "Actived_script_id": "O73808cb37bd63f5f7d92415e736e999",
            "Actiived_script_name": "这是一个脚本例子",
            "Actiived_script_encode": "true",
            "Script_Count": "3",
            "UnActived_script_list": [{
                "UnActived_script_encode": "false",
                "UnActived_script_Name": "AAA",
                "UnActived_script_ID": "7579a099e6fcee76fd1272ccdc30c1cc"
            }, {
                "UnActived_script_encode": "false",
                "UnActived_script_Name": "BBB",
                "UnActived_script_ID": "c1f3f1b3d5072985581fe54343f1e524"
            }],
            "status": 0
        }]
    }
}
```

---

## API 18: 创建新的环境分组
- **路径**: `/api/v1/group/create`
- **方法**: POST
- **内容类型**: application/json
- **描述**: 创建一个新的环境分组；名称不得与现有分组名称重复。

### 请求参数
| 参数名称    | 类型    | 是否必传 | 示例/默认值                             | 说明       |
|---------|-------|------|-------------------------------------|----------|
| Group_Name | 字符串 | 是    | "分组名称"                        | 新分组的名称 |

### 请求示例
```json
{
    "Group_Name": "分组名称"
}
```

### 成功响应
```json
{
    "msg": "新分组创建成功",
    "code": 0,
    "data": true
}
```

---

## API 19: 更新环境
- **路径**: `/api/v1/session/update`
- **方法**: POST
- **内容类型**: application/json
- **描述**: 更新环境配置，成功后返回环境ID。

### 请求参数
| 参数名称       | 类型    | 是否必传 | 示例/默认值                             | 说明       |
|------------|-------|------|-------------------------------------|----------|
| Session_ID | 字符串 | 是    | "373808cb37bd63f5f7d92415e736e85f"  | 环境ID     |
| Session_Name | 字符串 | 否    | "商用业务环境一"                        | 环境名称，最多60个字符 |
| Session_Desc | 字符串 | 否    | "环境描述"                           | 描述，最多150个字符 |
| Session_Group | 字符串 | 否    | "分组名称"                           | 分组名称，最多30个字符；未设置时默认为"默认" |
| Cookie     | 字符串 | 否    | "这是一个COOKIE"                     | JSON格式的cookie文本 |
| Disable_video | 位     | 否    | 0                                   | 视频限流 [0: 关闭, 1: 开启] |
| Disable_img | 位     | 否    | 0                                   | 图片限流 [0: 关闭, 1: 开启] |
| HomePage_url | 字符串 | 否    | "https://www.baidu.com"             | 起始页；未设置时默认为"https://www.yalala.com/?wd=mb" |

### 请求示例
```json
{
    "Session_ID": "373808cb37bd63f5f7d92415e736e85f",
    "Session_Name": "商用业务环境一",
    "Session_Desc": "环境描述",
    "Session_Group": "分组名称",
    "Cookie": "这是一个COOKIE",
    "Disable_video": 0,
    "Disable_img": 0
}
```

### 成功响应
```json
{
    "message": "更新环境成功",
    "code": 0,
    "data": {
        "Session_Id": "373808cb37bd63f5f7d92415e736e85f"
    }
}
```

---

## API 20: 更新环境代理
- **路径**: `/api/v1/session/proxy/update`
- **方法**: POST
- **内容类型**: application/json
- **描述**: 更新多个环境的代理设置，成功后返回环境ID。

### 请求参数
| 参数名称           | 类型    | 是否必传 | 示例/默认值                             | 说明       |
|----------------|-------|------|-------------------------------------|----------|
| Session_ID     | 数组   | 是    | ["373808cb37bd63f5f7d92415e736e85f"]| 环境ID     |
| Proxy_Type     | 字符串 | 是    | "HTTP"                              | 代理类型 [HTTP, HTTPS, SSH, SOCKS4, SOCKS4A, SOCKS5, Oxylabsauto, Lumauto, Luminati_HTTP, Luminati_HTTPS, smartproxy, noproxy] |
| Proxy_Ip       | 字符串 | 否    | "*********"                         | 代理IP     |
| Proxy_Port     | 字符串 | 否    | "1234"                              | 代理端口   |
| Proxy_Username | 字符串 | 否    | "abcdefg"                           | 代理用户名 |
| Proxy_Password | 字符串 | 否    | "123456"                            | 代理密码   |
| Is_CheckProxy  | 位     | 否    | 0                                   | 自动检测代理 [0: 否, 1: 是] |
| TimeZone       | 字符串 | 否    | "美国/阿拉斯加 -09:000"                 | 时区       |
| CountryCode    | 字符串 | 否    | "US"                                | 国家代码   |
| CityCode       | 字符串 | 否    | ""                                  | 城市代码   |
| RegionCode     | 字符串 | 否    | ""                                  | 州/地区代码|
| LanguageCode   | 字符串 | 否    | "En-US;en;q=0.9"                    | 默认语言   |
| StaticIP_Type  | 位     | 否    | 1                                   | IP类型 [0: 动态, 1: 静态] |
| Static_PublicIP| 字符串 | 否    | "********"                          | 公网IP     |
| Static_PrivateIP| 字符串 | 否    | "**************"                    | 私网IP     |

### 请求示例
```json
{
    "Session_ID": ["373808cb37bd63f5f7d92415e736e85f"],
    "Proxy_Type": "HTTP",
    "Proxy_Ip": "*********",
    "Proxy_Port": "1234",
    "Proxy_Username": "abcdefg",
    "Proxy_Password": "123456",
    "StaticIP_Type": 1,
    "Is_CheckProxy": 0,
    "Static_PublicIP": "********",
    "LanguageCode": "En-US;en;q=0.9"
}
```

### 成功响应
```json
{
    "message": "更新环境代理成功",
    "code": 0,
    "data": {
        "Session_Id": "373808cb37bd63f5f7d92415e736e85f"
    }
}
```

---

## API 21: 更新高级指纹参数
- **路径**: `/api/v1/session/adv_setting`
- **方法**: POST
- **内容类型**: application/json
- **描述**: 手动设置环境的高级指纹参数，成功后返回环境ID。

### 请求参数
| 参数名称               | 类型    | 是否必传 | 示例/默认值                             | 说明       |
|--------------------|-------|------|-------------------------------------|----------|
| Session_ID         | 字符串 | 是    | "cf77e3cb4a2bd3b7b69b48809eb5475b"  | 环境ID     |
| IS_WEBRTC          | 位     | 否    | 1                                   | WebRTC指纹 [0: 禁用, 1: 启用] |
| SystemOS           | 字符串 | 否    | "linux"                             | 操作系统   |
| Session_Resolution | 字符串 | 否    | "2560x1440"                         | 分辨率     |
| Session_UserAgent  | 字符串 | 否    | "Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.111 Safari/537.36" | User-Agent |
| StaticIP_Type      | 位     | 否    | 0                                   | IP类型 [0: 动态, 1: 静态] |
| LanguageCode       | 字符串 | 否    | "En-US;en;q=0.9"                    | 默认语言   |
| FingerPrint_Setting| 字符串 | 否    | "1,0,1,1"                           | 指纹设置：Lwav=1, FontCode=0, Dns=1, Canvas=1 (1: 启用, 0: 禁用) |
| Lock_Browser_windows_size| 位     | 否    | 1                                   | 锁定浏览器窗口大小 [0: 解锁, 1: 锁定] |
| Disable_video      | 位     | 否    | 0                                   | 视频限流 [0: 关闭, 1: 开启] |
| Disable_img        | 位     | 否    | 0                                   | 图片限流 [0: 关闭, 1: 开启] |
| Static_PublicIP    | 字符串 | 否    | "xxx.xxx.xxx.xxx"                   | 手动公网IP |
| Static_PrivateIP   | 字符串 | 否    | "xxx.xxx.xxx.xxx"                   | 手动私网IP |
| TimeZone           | 字符串 | 否    | "美国/阿拉斯加 -09:000"                 | 手动时区   |
| CountryCode        | 字符串 | 否    | "US"                                | 国家代码   |
| CityCode           | 字符串 | 否    | ""                                  | 城市代码   |
| RegionCode         | 字符串 | 否    | ""                                  | 州/地区代码|

### 请求示例
```json
{
    "Session_ID": "cf77e3cb4a2bd3b7b69b48809eb5475b",
    "SystemOS": "linux",
    "FingerPrint_Setting": "1,0,1,1"
}
```

### 成功响应
```json
{
    "message": "更新环境高级设置成功",
    "code": 0,
    "data": {
        "Session_Id": "373808cb37bd63f5f7d92415e736e85f"
    }
}
```

---

## API 22: 检查环境运行状态
- **路径**: `/api/v1/browser/status`
- **方法**: POST
- **内容类型**: application/json
- **描述**: 检查一个或多个指定环境的运行状态。

### 请求参数
| 参数名称    | 类型    | 是否必传 | 示例/默认值                             | 说明       |
|---------|-------|------|-------------------------------------|----------|
| Session_ID | 数组   | 否    | ["4e6d0dca26ef42be9bc4472779d2550f"]| 指定环境（一次最多100个） |
| Actived_Type | 整数   | 否    | 0                                   | 0: 所有状态, 1: 仅运行中, 2: 仅未运行 |

### 请求示例
```json
{
    "Session_ID": ["4e6d0dca26ef42be9bc4472779d2550f"]
}
```

### 成功响应
```json
{
    "code": 0,
    "data": {
        "listcontainer": [{
            "Group_Name": "未分组环境",
            "Session_ID": "4e6d0dca26ef42be9bc4472779d2550f",
            "Session_Name": "测试用",
            "Session_ProcessID": 4668,
            "Session_StartTime": "2024-11-21 16:52:09",
            "Session_Actived": 1,
            "Session_Args": "--ImagesEnabled=0 --interval-seconds=3 --disable-extensions--enable-logging --v=1",
            "Session_Headless": 0
        }],
        "total": 1
    },
    "message": "环境状态返回成功"
}
```

---

## API 23: 列出指定环境中的所有插件
- **路径**: `/api/v1/session/id_plugin_list`
- **方法**: POST
- **内容类型**: application/json
- **描述**: 列出指定环境中包含的所有插件（支持单个或多个环境）。

### 请求参数
| 参数名称    | 类型   | 是否必传 | 示例/默认值                                              | 说明                                |
|-----------|------|------|-------------------------------------------------------|-----------------------------------|
| Session_ID | 数组   | 是    | ["373808cb37bd63f5f7d92415e736e85f", "705cc4c139e69b729a2fd277f30e1863"] | 要查询的环境ID                  |

### 请求示例
```json
{
    "Session_ID": ["373808cb37bd63f5f7d92415e736e85f", "705cc4c139e69b729a2fd277f30e1863"]
}
```

### 成功响应
```json
{
    "message": "环境插件列表成功",
    "code": 0,
    "data": {
        "listcontainer": [{
            "Session_Name": "商用业务环境一",
            "Session_ID": "373808cb37bd63f5f7d92415e736e85f",
            "Group_Name": "默认",
            "Plugins_Count": "4",
            "Plugin_list": [{
                "Plugin_Name": "AAA",
                "Plugin_Id": "jjbnhpnlakcdgfnnldamfeinfmahhdlm",
                "Plugin_Ver": "*******"
            }, {
                "Plugin_Name": "BBB",
                "Plugin_Id": "jjbnhpnlakcdgfnnldamfeinfmahhdlm",
                "Plugin_Ver": "*******"
            }],
            "status": 0
        }]
    }
}
```

---

## API 24: 列出指定环境中的所有脚本
- **路径**: `/api/v1/session/id_script_list`
- **方法**: POST
- **内容类型**: application/json
- **描述**: 列出指定环境中包含的所有脚本（支持单个或多个环境）。

### 请求参数
| 参数名称    | 类型   | 是否必传 | 示例/默认值                                              | 说明                                |
|-----------|------|------|-------------------------------------------------------|-----------------------------------|
| Session_ID | 数组   | 是    | ["373808cb37bd63f5f7d92415e736e85f", "705cc4c139e69b729a2fd277f30e1863"] | 要查询的环境ID                  |

### 请求示例
```json
{
    "Session_ID": ["373808cb37bd63f5f7d92415e736e85f", "705cc4c139e69b729a2fd277f30e1863"]
}
```

### 成功响应
```json
{
    "message": "环境脚本列表成功",
    "code": 0,
    "data": {
        "listcontainer": [{
            "Session_Name": "商用业务环境一",
            "Session_ID": "373808cb37bd63f5f7d92415e736e85f",
            "Group_Name": "默认",
            "Actived_script_id": "O73808cb37bd63f5f7d92415e736e999",
            "Actiived_script_name": "这是一个脚本例子",
            "Actiived_script_encode": "true",
            "Script_Count": "4",
            "UnActived_script_list": [{
                "UnActived_script_encode": "false",
                "UnActived_script_Name": "AAA",
                "UnActived_script_ID": "17c70e014d61b1fa43d3638ca5a1bc21"
            }, {
                "UnActived_script_encode": "false",
                "UnActived_script_Name": "BBB",
                "UnActived_script_ID": "17c70e014d61b1fa43d3638ca5a1bc22"
            }],
            "status": 0
        }]
    }
}
```

---

## API 25: 查询指定环境ID的配置数据
- **路径**: `/api/v1/session/id_container`
- **方法**: POST
- **内容类型**: application/json
- **描述**: 查询并返回指定环境ID集合的内部配置数据。

### 请求参数
| 参数名称               | 类型   | 是否必传 | 示例/默认值                             | 说明       |
|--------------------|------|------|-------------------------------------|----------|
| Session_ID         | 数组  | 是    | ["373808cb37bd63f5f7d92415e736e85f"]| 要查询的环境ID |
| Session_container_type | 整数  | 否    | 1                                   | 1: 完整内容, 2: 简化内容, 3: 包含插件/脚本的简化内容 |

### 请求示例
```json
{
    "Session_ID": ["373808cb37bd63f5f7d92415e736e85f"],
    "Session_container_type": 1
}
```

### 成功响应
```json
{
    "message": "成功",
    "code": 0,
    "data": {
        "listcontainer": [{
            "Session_Name": "商用业务环境一",
            "Session_ID": "373808cb37bd63f5f7d92415e736e85f",
            "Group_Name": "默认",
            "Actived_script_id": "O73808cb37bd63f5f7d92415e736e999",
            "Actiived_script_name": "这是一个脚本例子",
            "Actiived_script_encode": "true",
            "Weblogin_Account_Count": "4",
            "Weblogin_Account_name": "<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>",
            "Plugins_Count": "4",
            "Plugin_Id": "jjbnhpnlakcdgfnnldamfeinfmahhdlm,jjbnhpnlakcdgfnnldamfeinfmahhdlm,jjbnhpnlakcdgfnnldamfeinfmahhdlm,jjbnhpnlakcdgfnnldamfeinfmahhdlm",
            "template_id": "123456",
            "template_name": "TikTok国际版",
            "browser_Path": "D:\\mbbrowser\\Chromium_x64\\chromium.exe",
            "browser_CDP_Port": 46973,
            "MBData_Path": "C:\\MBDATA\\xxxxxxxxxx\\xxxxxxxxxx\\xxxxxxxxxxx",
            "Public_ip": "************",
            "Internel_ip": "**************",
            "isDynamicIp": false,
            "StartPage": "about:blank",
            "System": "windows",
            "Resolution": "1024x768",
            "UserAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "proxyType": "socks5",
            "proxy_ip": "127.0.0.1",
            "proxy_port": "1080",
            "webdriver": "C:\\Users\\<USER>\\houniao\\Driver\\100\\chromedriver.exe",
            "updatetime": "2022-12-13 13:23:09",
            "createtime": "2022-09-23 08:47:36",
            "item_version": "3030",
            "UnActived_script_list": [{
                "UnActived_script_encode": "false",
                "UnActived_script_Name": "AAA",
                "UnActived_script_ID": "17c70e014d61b1fa43d3638ca5a1bc21"
            }, {
                "UnActived_script_encode": "false",
                "UnActived_script_Name": "BBB",
                "UnActived_script_ID": "17c70e014d61b1fa43d3638ca5a1bc22"
            }],
            "status": 0
        }],
        "total": 1
    }
}
```

---

## API 26: 清除环境本地缓存
- **路径**: `/api/v1/session/BrowserCache-clean`
- **方法**: POST
- **内容类型**: application/json
- **描述**: 清除指定环境的本地缓存。

### 请求参数
| 参数名称          | 类型   | 是否必传 | 示例/默认值                                              | 说明                                |
|---------------|------|------|-------------------------------------------------------|-----------------------------------|
| Session_ID    | 数组   | 是    | ["373808cb37bd63f5f7d92415e736e85f", "7579a099e6fcee76fd1272ccdc30c1cc", "c1f3f1b3d5072985581fe54343f1e524"] | 要清除的环境ID                  |
| Is_Clean_Cookies | 位     | 否    | 1                                                      | 1: 仅清除Cookie, 0: 清除所有缓存                  |

### 请求示例
```json
{
    "Session_ID": ["373808cb37bd63f5f7d92415e736e85f", "7579a099e6fcee76fd1272ccdc30c1cc", "c1f3f1b3d5072985581fe54343f1e524"],
    "Is_Clean_Cookies": 1
}
```

### 成功响应
```json
{
    "msg": "浏览器缓存清除成功",
    "code": 0,
    "data": {
        "Clean_SessionID_Success": "373808cb37bd63f5f7d92415e736e85f",
        "Clean_SessionID_Failed": "f994d8e641ce7006acfa36c901829ff2"
    }
}
```

---

## API 27: 获取成员列表
- **路径**: `/members`
- **方法**: POST
- **内容类型**: application/json
- **描述**: 获取与此帐户关联的所有主帐户和子帐户的数据。

### 请求参数
| 参数名称   | 类型    | 是否必传 | 示例/默认值                             | 说明       |
|--------|-------|------|-------------------------------------|----------|
| Account| 字符串 | 是    | "<EMAIL>"                     | 用户凭证：帐号 |

### 请求示例
```json
{
    "Account": "<EMAIL>"
}
```

### 成功响应
```json
{
    "msg": "成功",
    "status": 0,
    "data": "<EMAIL>:0",
    "account_expire_date": "2023-07-26 18:46:48",
    "account_reg_date": "2020-07-29 15:13:28"
}
```

---

## API 28: 获取环境分组列表
- **路径**: `/api/v1/group/list`
- **方法**: POST
- **内容类型**: application/json
- **描述**: 返回当前帐户凭据下所有环境分组的列表。

### 请求参数
| 参数名称       | 类型    | 是否必传 | 示例/默认值                             | 说明       |
|------------|-------|------|-------------------------------------|----------|
| Session_ID | 字符串 | 否    | "373808cb37bd63f5f7d92415e736e85f"  | 返回指定环境ID的分组 |
| Session_Name | 字符串 | 否    | "环境名称一"                          | 返回指定环境名称的分组 |

### 请求示例
```json
{
    "Session_ID": "373808cb37bd63f5f7d92415e736e85f",
    "Session_Name": "环境名称一"
}
```

### 成功响应
```json
{
    "msg": "分组列表返回成功",
    "code": 0,
    "data": {
        "Group_Name_List": "我的分组一, 我的分组二, 我的分组三"
    }
}
```

---

## API 29: 获取环境列表
- **路径**: `/api/v1/session/listid`
- **方法**: POST
- **内容类型**: application/json
- **描述**: 查询所有符合条件的环境ID。用户只能查询自己的环境和共享给他们的环境。

### 请求参数
| 参数名称               | 类型    | 是否必传 | 示例/默认值                             | 说明       |
|--------------------|-------|------|-------------------------------------|----------|
| Session_Name       | 字符串 | 否    | "商用业务环境一"                        | 按环境名称查询 |
| Session_GroupName  | 字符串 | 否    | "分组名称"                           | 按分组名称查询 |
| Session_CreateTime_Start | 字符串 | 否    | "2022-09-01 13:14:15"               | 创建时间起始   |
| Session_CreateTime_End | 字符串 | 否    | "2022-09-20 13:14:15"               | 创建时间结束   |
| Session_UpdateTime_Start | 字符串 | 否    | "2022-09-01 13:14:15"               | 最后更新时间起始 |
| Session_UpdateTime_End | 字符串 | 否    | "2022-09-20 13:14:15"               | 最后更新时间结束 |
| Session_Recv       | 位     | 否    | 1                                   | 返回接收到的共享环境 |
| Session_Recv_Account | 字符串 | 否    | "<EMAIL>"                    | 返回由特定帐户共享的环境 |
| Session_Sent       | 位     | 否    | 1                                   | 返回共享的环境 |
| Session_Sent_Account | 字符串 | 否    | "<EMAIL>"                    | 返回共享给特定帐户的环境 |
| Session_User_Agent | 字符串 | 否    | "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36" | 按UA值查询 |
| Proxy_Ip           | 字符串 | 否    | "127.0.0.1"                         | 按代理IP查询 |
| Proxy_Type         | 数组   | 否    | ["smartproxy"]                      | 按代理类型查询 |
| Comment            | 字符串 | 否    | "关键字"                            | 按备注关键字查询 |
| Session_ISWebAccount | 位     | 否