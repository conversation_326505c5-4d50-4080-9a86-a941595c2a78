标题: Socket5代理
英文标题: Socket5 Proxy
ID: 102
分类ID: 25
添加时间: 1614752855
更新时间: 1685430239
访问次数: 0
SEO标题: socket5代理
SEO关键词: socket5代理
SEO描述: socket5代理

================================================== 内容 ==================================================
采用socks协议的代理服务器就是SOCKS服务器，是一种通用的代理服务器。Socks是个电路级的底层网关，是DavidKoblas在1990年开发的，此后就一直作为Internet RFC标准的开放标准。Socks 不要求应用程序遵循特定的操作系统平台，Socks 代理与应用层代理、 HTTP 层代理不同，Socks 代理只是简单地传递数据包，而不必关心是何种应用协议（比如FTP、HTTP和NNTP请求）。所以，Socks代理比其他应用层代理要快得多。

================================================== 英文内容 ==================================================
A proxy server that uses socks is a universal SOCKS server. Socks is a circuit-level underlying gateway developed by DavidKoblas in 1990 and has been an open standard for Internet RFC standards ever since. Socks does not require the application program to comply with a specific operating system platform. The Socks proxy is different from the application proxy and HTTP proxy. The Socks proxy simply transmits packets, regardless of the application protocol (such as FTP, HTTP, and NNTP requests). Therefore, the Socks proxy is much faster than other application-layer proxies.