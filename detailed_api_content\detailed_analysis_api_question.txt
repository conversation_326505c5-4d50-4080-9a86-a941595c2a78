详细内容分析报告
URL: https://www.mbbrowser.com/api/question
分析时间: 2025-07-28 12:50:25
============================================================

发现的API功能:
------------------------------
1. 帐号登录
2. 获取成员列表
3. 环境开启/关闭
4. 环境管理
5. 分组管理
6. 脚本管理
7. 插件管理
8. 附录（国家码、时区、语言、系统和分辨率）
9. 错误码对照表
1. 通过命令行启动客户端报错
2. 调用API是否可以同时手动打开客户端
3. 打开环境失败

API相关描述:
------------------------------
1. API使用须知
简介
• 使用须知
• HTTP模式说明
• 常见问题
• API接口文档
1、帐号登录
• 2、获取成员列表
• 3、环境开启/关闭
• 4、环境管理
• 5、分组管理
• 6、脚本管理
• 7、插件管理
• 8、附录（国家码、时区、语言、系统和分辨率）
• 9、错误码对照表
• 候鸟API接口实时调试工具
POSTMAN下载及安装
• POSTMAN调试候鸟API接口
• 调试接口JSON数据官方更新、下载
• 多种语言脚本示例
• JSON在线格式化工具

完整页面内容:
============================================================
API使用文档-候鸟防关联浏览器•

首页

应用

价格

下载

APInew
使用教程

常见问题

佣金计划

博客中心

登录&注册
简体中文

首页

应用

价格

下载

API

使用教程

佣金计划

博客中心

登录&注册

API
====================
候鸟浏览器API使用文档

API使用须知
简介
• 使用须知
• HTTP模式说明
• 常见问题
• API接口文档
1、帐号登录
• 2、获取成员列表
• 3、环境开启/关闭
• 4、环境管理
• 5、分组管理
• 6、脚本管理
• 7、插件管理
• 8、附录（国家码、时区、语言、系统和分辨率）
• 9、错误码对照表
• 候鸟API接口实时调试工具
POSTMAN下载及安装
• POSTMAN调试候鸟API接口
• 调试接口JSON数据官方更新、下载
• 多种语言脚本示例
• JSON在线格式化工具

常见问题
====================
1、通过命令行启动客户端报错

• 请确认客户端版本在V3.9.2.114及以上

• 请确认是否使用管理员身份运行CMD或者PowerShell

• 请确认是否在候鸟浏览器主目录打开，或已进入候鸟浏览器主路径

2、调用API是否可以同时手动打开客户端

• 客户端用命令行启动的情况下，无法手动打开客户端

• 客户端用命令行启动的情况下，无法再用命令行重新启动客户端（命令无法输入）

• 手动启动客户端的情况下，命令行执行启动客户端后会把手动开启的客户端关闭，然后重新开启客户端

3、打开环境失败

• 请检查参数是否正确，http模式通过环境SESSION_ID打开环境

• 环境SESSION_ID可以通过控制台环境列表获得

支持邮箱: <EMAIL>

©MBBROWSER @2025

京ICP备 2020047947号

本系统不提供代理IP服务，禁止用户使用本系统进行任何违法犯罪活动，用户使用本系统带来的任何责任由用户自行承担。

MBbrowser.com  All Rights Reserved. 候鸟防关联浏览器对网站内容拥有最终解释权。
工作日客服(微信)
工作日09-18点

夜间/周末客服(微信)

工作日 18-24点，周末全天

商务(微信)

mbbrowser_official

全国咨询服务热线
====================

400-112-6050

在线咨询

微信咨询

电话咨询

售后咨询
