#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证切分后的文件
"""

import os
from pathlib import Path
from docx import Document

def verify_split_files(docx_dir):
    """验证切分后的文件"""
    docx_path = Path(docx_dir)
    
    # 查找原文件和切分后的文件
    original_file = docx_path / "API_环境管理.docx"
    split_files = [
        docx_path / "API_环境管理(1).docx",
        docx_path / "API_环境管理(2).docx",
        docx_path / "API_环境管理(3).docx"
    ]
    
    print("🔍 验证切分结果")
    print("=" * 60)
    
    # 验证原文件
    if original_file.exists():
        try:
            original_doc = Document(str(original_file))
            original_paragraphs = len(original_doc.paragraphs)
            original_tables = len(original_doc.tables)
            original_size = original_file.stat().st_size / 1024
            
            print(f"📄 原文件: API_环境管理.docx")
            print(f"   段落数: {original_paragraphs}")
            print(f"   表格数: {original_tables}")
            print(f"   文件大小: {original_size:.1f} KB")
            print()
        except Exception as e:
            print(f"❌ 读取原文件失败: {str(e)}")
            return
    else:
        print("❌ 原文件不存在")
        return
    
    # 验证切分后的文件
    total_split_paragraphs = 0
    total_split_tables = 0
    total_split_size = 0
    
    print("📋 切分后的文件:")
    for i, split_file in enumerate(split_files, 1):
        if split_file.exists():
            try:
                split_doc = Document(str(split_file))
                paragraphs = len(split_doc.paragraphs)
                tables = len(split_doc.tables)
                size = split_file.stat().st_size / 1024
                
                total_split_paragraphs += paragraphs
                total_split_tables += tables
                total_split_size += size
                
                print(f"   📄 API_环境管理({i}).docx")
                print(f"      段落数: {paragraphs}")
                print(f"      表格数: {tables}")
                print(f"      文件大小: {size:.1f} KB")
                
                # 显示前几个段落的标题
                print(f"      内容预览:")
                for j, para in enumerate(split_doc.paragraphs[:3]):
                    text = para.text.strip()
                    if text:
                        preview = text[:50] + "..." if len(text) > 50 else text
                        print(f"        {j+1}. {preview}")
                print()
                
            except Exception as e:
                print(f"❌ 读取文件 {split_file.name} 失败: {str(e)}")
        else:
            print(f"❌ 文件不存在: {split_file.name}")
    
    # 统计对比
    print("=" * 60)
    print("📊 统计对比:")
    print(f"原文件段落数: {original_paragraphs}")
    print(f"切分后总段落数: {total_split_paragraphs}")
    print(f"段落数差异: {total_split_paragraphs - original_paragraphs}")
    print()
    print(f"原文件表格数: {original_tables}")
    print(f"切分后总表格数: {total_split_tables}")
    print(f"表格数差异: {total_split_tables - original_tables}")
    print()
    print(f"原文件大小: {original_size:.1f} KB")
    print(f"切分后总大小: {total_split_size:.1f} KB")
    print(f"大小差异: {total_split_size - original_size:.1f} KB")
    
    # 评估切分质量
    print()
    print("=" * 60)
    print("📈 切分质量评估:")
    
    if abs(total_split_paragraphs - original_paragraphs) <= 3:
        print("✅ 段落数量: 良好 (差异在可接受范围内)")
    else:
        print("⚠️  段落数量: 需要注意 (差异较大)")
    
    if abs(total_split_tables - original_tables) <= 1:
        print("✅ 表格数量: 良好 (差异在可接受范围内)")
    else:
        print("⚠️  表格数量: 需要注意 (差异较大)")
    
    if total_split_size > original_size * 0.9:
        print("✅ 文件大小: 良好 (内容保留完整)")
    else:
        print("⚠️  文件大小: 需要注意 (可能有内容丢失)")
    
    # 检查文件分布均匀性
    if len(split_files) == 3:
        sizes = []
        for split_file in split_files:
            if split_file.exists():
                size = split_file.stat().st_size / 1024
                sizes.append(size)
        
        if sizes:
            avg_size = sum(sizes) / len(sizes)
            max_deviation = max(abs(size - avg_size) for size in sizes)
            
            if max_deviation < avg_size * 0.3:
                print("✅ 文件分布: 均匀 (大小差异小)")
            else:
                print("⚠️  文件分布: 不够均匀 (大小差异较大)")

if __name__ == "__main__":
    docx_directory = r"F:\augment\output\docx_files"
    
    print("🔍 验证API_环境管理.docx切分结果")
    print(f"📂 目录: {docx_directory}")
    print()
    
    verify_split_files(docx_directory)
