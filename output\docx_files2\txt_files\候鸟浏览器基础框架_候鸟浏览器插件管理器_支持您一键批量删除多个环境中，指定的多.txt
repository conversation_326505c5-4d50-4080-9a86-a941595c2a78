候鸟浏览器插件管理器 支持您一键批量删除多个环境中，指定的多个插件。

候鸟浏览器插件管理器 支持您一键批量删除多个环境中，指定的多个插件。

操作方法：

通过在B区选择 已安装插件的环境 下拉列表项， 勾选您需要删除的环境范围（通过观察C区列表和B区列表的环境插件数）。之后，在C区勾选需要删除的插件。点击删除插件，即可一次性将多个环境中多个插件一次性彻底删除。

导致的结果如下：

1、此方式删除插件后此环境将不再拥有此插件带来的所有功能。

2、您在异地使用产品，此环境也不再看到此插件和此插件带来的所有功能。

3、如果需要再次使用此插件，您必须通过在A区进行重新安装此插件过程。

异地使用已安装过插件的环境。

前述：

候鸟浏览器产品支持首次插件安装到环境后，异地无缝使用此环境（无须二次安装插件），支持多用户协同合作环境分享模式下无缝使用（无须二次安装插件）。 支持环境本地导出后，交付给第三方帐户使用无缝使用此环境（无须二次安装插件）。

当首次将插件安装到此环境后，异地使用：

如图：

在异地首次使用（点击运行）时，候鸟浏览器产品会检测并自动下载并安装此环境中所有需要的商业插件，并显示整个下载安装过程，此时您的机械硬盘会有明显的数据写入过程（建议使用NVME盘或SSD硬盘来保证浏览器的高效运行。）

在进行完成上述动作后，此环境对应的指纹浏览器将会第一时间打开，在浏览器的右上角会显示已安装的插件，如图。

注：部份GOOGLE浏览器插件会有此默认功能：Let Chromium run in background。 表示默认将此浏览器窗口在后台运行。为保证客户业务稳定运营，此时在候鸟客户端点击

停止将不会产生效力。需客户自行修改插件设置或在屏幕右下角tray托盘区，手动关闭此环境之浏览器窗体。

第二十八章 候鸟客户端/服务器端

团队协作版基础约定与流程接口说明

第二十八章 候鸟客户端/服务器端团队协作版基础约定与流程接口说明

红色与蓝色为新增逻辑

定义：

单个环境配置完整数据包（zip包 / 简称: 单环境包）:

数据包里只有一个item节点的xml数据和这个数据的相关资源数据，包括当前item环境的浏览器数据。在

数据结构：(遵循候鸟浏览器第十三版.docx所有约定)

单个环境配置的完整数据包Zip包结构：

19_e5962982027b32244920aeae6316dc7b_item.zip  （单环境完整包）

--（释放为）--

19_e5962982027b32244920aeae6316dc7b_Default.zip (单环境浏览器数据包)

19_maindata.zip (单环境item数据包)

19_maindata.zip

--（释放为）--

19_ConfgData.zip

--（释放为）--

19_e5962982027b32244920aeae6316dc7b_Default.zip (单环境uaconf和proxy文件)

179_ConfigData.zip (单环境包xml加码数据)

数据源：

19_e5962982027b32244920aeae6316dc7b_full.zip （多环境full完整包）

从80-120kb的full包中获取单个环境的所有数据。

说明：服务器端根据用户在后台的分享操作来生成单环境包。 单环境包生成应从full包中获取单个环境的所有数据。 对full包仅有只读权限。即对客户端提交上来的full包仅读取，不作其它任何修改操作。

基础约定：(遵循候鸟浏览器第十三版.docx所有约定)

在服务器数据库里，子帐户和母帐户均以标准的用户帐户形式存在。区别在于标记不同，子帐户标记的为子帐户。

单个子帐户同一时间只允许一个机器登录。

主帐号只分享，主帐户不会直接在其帐户下直接修改子帐户的item中的各个细节。主帐户的使用者要修改子帐户，只能自行登录子帐户去修改，或通过官网后台进行查阅。

子帐号具备有母帐户的所有基础功能，唯一的区别为子帐户是隶属于母帐户。

多个子帐户可以在同一时间，同一台机器上同时登录成功。

母帐户与子帐户之间，子帐户与子帐户之间，相互完全独立。

服务器端异步定时采集和实时（登录时）从FULL包中获取单个环境数据集合，并更新数据库，当服务器端从full包中获取的每个item数据及item数据附带的浏览器数据(80k-120k)，必须保存在服务器端指定的文件夹中（不和full路径重叠）。根据SESSION_UNIQUE_ID 每个item只保留一份。Item具有累加属性。

其中二个场景：

对于非团队协作套餐的用户，需在后台提供环境列表，每个item环境数据保存在服务器端指定的文件夹中。（此类套餐用户在后台无分享功能，但有ITEM环境列表查看功能并支持未来功能扩展。）

对于团队协作协作套餐用户，界面与数据存储流程同上，仅多出分享功能和基于分享的各附属功能。

（注：）后台客服职能：在前期没有单独的客服平台情况下，为尽快解决客服与用户之间信息不对称的矛盾， 要支持客服可以通过内部密码登录用户控制台查看用户的item数据列表和用户的各类信息（用户使用情况，登录情况，数据情况等）。并在后期提供用户自行通过控制台将历史item添加到用户自有客户端的功能。

团队协作版的数据向下兼容，具体指：1.0.1.6版本可以正常加载后面的新版本数据。对于新版本完全向下兼容，兼容版本数据格式到1.0.1.3版。

协作版与普通版中，用户对item进行分享的动作，用户对item进行删除的动作、和用户间进行自有item传递的动作，操作日志必须写入一个单独的表中，以记录用户操作行为。(2021-01-17 第九版文档约定新增)

(附)

SESSION_UNIQUE_ID 唯一不重复标识

定义与作用：

保证面板中ITEM的唯一性。所有流程根据SESSION_UNIQUE_ID识别ITEM并关联ITEM下会话所有业务逻辑，ITEM间通过SESSION_UNIQUE_ID的不同进行流程区分。

通过SESSION_UNIQUE_ID，进行数据上传与同步到本地加载到内存的重要依据。

唯一不重复标识, 仅仅在ITEM被用户创建时生成，并写入到XML中。在之后任何用户进行记录的增删改，此唯一不重复标识不变。

服务器端/客户端接口流程详述：

接口约定(版本号校验约定)：

PC端每次请求服务器端单环境数据包下载之前，不进行版本号判断，并不进行对比。直接将服务器发来的分享单环境数据包队列 更新/整合到本地configdata.xml。

A、（客户端部份1）  configdata.xml版本号和item的版本号之间 没有关联，即没有任何关系。

B、（客户端部份2）  分享item包新加入到xml中之时，根据第九版约定，等同于item被修改变更过，要触发即时同步或定时同步。

C、（客户端部份3） 客户端通过分享接口拿到item包后，整合于configdata.xml中的时侯， 必须在 item xml 里加上二个参数：from_uid= uid的值 和 share_time (分享时间)：xxxxxxxxxx 。 客户端判定item中的from_uid是否为空或是否存在此参数来判定此item是否属于分享类型item。

D、（客户端部份4）对于服务器给过来的分享item包，客户端判定，无论在configdata.xml中是否存在此item， 此item版本号写服务器端给过来的版本号。

E、（客户端部份5）对于服务器端发来的分享item版本号，在客户端整合后，此item的版本号不加1，以服务器端给过来的版本号为准进行写值。

F、（客户端部份6）对于所有类型的item版本号，在客户端中（不区分母帐户和子帐户），用户在客户端对此item进行任何修改，此item的版本号要加1

G、 (服务器端部份) 服务器端定时逻辑和登录逻辑根据解析full包中的configdata.xml中的item节点数据进行版本号对比，对item版本号大于当前表中同SESSION_UNIQUE_ID的 item版本号的给予表记录更新和数据存储更新。

单环境完整包 C/S包数据请求交互完整流程

流程与步骤：

1、服务器与客户端之间通过心跳 触发客户端即时请求sharelist串。

心跳接口：

当token为在线状态时，再使用此账号登录，服务端会通过code=-104通知客户端该用户已在线。
    每次websocket请求发送内容为token=123456**&uid=9**(新增参数uid，以记录心跳到uid下，而不是token下。该uid即登录接口返回data中的id)加AES后的字符串，如果后续需要其他参数可以往后添加类似“&email=221”。如果传递的字符串无法解析获得token，服务端也会直接关闭连接。
    服务端收到请求会回复connected

约定：  心跳通知扩展约定： 通过”|”间隔符来判定后续操作。

==========================================================================

*     列表请求：connected|req_sharelist

==========================================================================

流程详述：

1、客户端在心跳流程中收到connected|req_sharelist 标志位后，实时发起请求串:

sharelist请求串：

2、服务器端收到请求串，进行数据库查询，下发数据，返回Json格式：

成功：

{"msg":"操作成功","code":0,"data":{"sourceurl":"\/uid\/itemshare\/2\/11_cf9395daa4bcfdb29880488d880a9fd5_item.zip?a9cfd52509c8e13554176b785f939522",”version”:”11”,”itemid”:” SESSION_UNIQUE_ID”},{"sourceurl":"\/uid\/itemshare\/2\/11_cf9395daa4bcfdb29880488d880a9fd5_item.zip?a9cfd52509c8e13554176b785f939522",”version”:”11”,”itemid”:” SESSION_UNIQUE_ID”},{"sourceurl":"\/uid\/itemshare\/2\/11_cf9395daa4bcfdb29880488d880a9fd5_item.zip?a9cfd52509c8e13554176b785f939522",”version”:”11”,”itemid”:” SESSION_UNIQUE_ID”}}

失败：

{"msg":"没有可用数据包","code":-1,"data":""}

客户端通过获得的数据，进行下载完成数据hash校验、SESSION_UNIQUE_ID与本地configdata.xml数据校对。

数据hash校验：

客户端下载完成数据包后，获取文件hash码，与返回json串中的hash进行对比。一致表示下载成功。

客户端下载完成单item数据包需根据服务器端接口返回给服务器端成功/失败状态标志位。

对于用户从控制台任何时侯发过来的item分享包，客户端收到后不再判断和验证版本号，直接将item分享包更新/整合到本地configdata.xml中。


================================================== 表格内容 ==================================================

参数名称 | 类型 | 必传 | 样例串/默认值 | 说明
Session_ID | String | 是 | 373808cb37bd63f5f7d92415e736e85f | 指定导入的环境ID
Cookie_File | String | 是 | C:\cookie.txt | 填入cookie文件路径
支持text,json格式

{
"message": "Import Cookie Success",
"code": 0,
"data": {
            “Session_Id” : 373808cb37bd63f5f7d92415e736e85f, 	//环境ID
            "cookie": “cookie content”
       }
}