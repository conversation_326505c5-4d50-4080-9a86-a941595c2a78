#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RAGFlow MCP 客户端 - 参考官方实现
基于 RAGFlow 官方 MCP 客户端实现，确保与服务器的兼容性
"""

import json
import uuid
import requests
import time
from typing import Dict, Any, Optional, List

class RAGFlowMCPClientOfficial:
    """RAGFlow MCP 客户端 - 官方实现风格"""
    
    def __init__(self, 
                 base_url: str = "http://************:9382", 
                 api_key: str = "ragflow-UzYTlhMjIwNjI0YzExZjA4NzUyMDI0Mm"):
        """
        初始化 RAGFlow MCP 客户端
        
        Args:
            base_url: RAGFlow MCP Server 地址
            api_key: API 密钥
        """
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.session_id = str(uuid.uuid4())
        self.request_id = 0
        
        # 设置请求头 - 参考官方实现
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}",
            "User-Agent": "RAGFlow-MCP-Client-Official/1.0",
            "Accept": "application/json"
        }
        
        # 会话状态
        self.initialized = False
        self.server_capabilities = {}
        self.available_tools = []
        
        print(f"🤖 RAGFlow MCP 客户端初始化 (官方实现风格)")
        print(f"   服务器: {self.base_url}")
        print(f"   会话ID: {self.session_id}")
        print(f"   API密钥: {self.api_key[:20]}...")
    
    def _get_next_request_id(self) -> int:
        """获取下一个请求ID"""
        self.request_id += 1
        return self.request_id

    def get_server_session_id(self) -> bool:
        """从服务器获取会话ID"""
        print(f"\n🔑 获取服务器会话ID...")

        try:
            # 访问 SSE 端点获取服务器分配的会话ID
            sse_url = f"{self.base_url}/sse"
            print(f"   请求: {sse_url}")

            response = requests.get(sse_url, headers=self.headers, timeout=10, stream=True)

            if response.status_code == 200:
                # 读取响应数据，寻找会话ID
                import re
                session_id_found = None

                for line in response.iter_lines(decode_unicode=True):
                    if line and 'session_id=' in line:
                        # 提取会话ID: /messages/?session_id=dd588888168244b3bd26c2fd9a763f91
                        match = re.search(r'session_id=([a-f0-9]+)', line)
                        if match:
                            session_id_found = match.group(1)
                            break

                    # 只读取前几行，避免长时间等待
                    if session_id_found or len(line) > 1000:
                        break

                if session_id_found:
                    old_session_id = self.session_id
                    self.session_id = session_id_found
                    print(f"   ✅ 获取到服务器会话ID: {self.session_id}")
                    print(f"   📝 替换客户端会话ID: {old_session_id}")
                    return True
                else:
                    print(f"   ⚠️  未找到会话ID，使用客户端生成的ID: {self.session_id}")
                    return True  # 仍然返回True，使用客户端生成的ID
            else:
                print(f"   ❌ SSE 端点响应异常: {response.status_code}")
                return False

        except Exception as e:
            print(f"   ❌ 获取会话ID失败: {e}")
            print(f"   📝 将使用客户端生成的会话ID: {self.session_id}")
            return True  # 即使获取失败，也继续使用客户端生成的ID
    
    def _send_mcp_request(self, method: str, params: Optional[Dict] = None) -> Dict[str, Any]:
        """
        发送 MCP 协议请求
        
        Args:
            method: MCP 方法名
            params: 请求参数
            
        Returns:
            MCP 响应
        """
        # 构建 JSON-RPC 2.0 请求
        request_data = {
            "jsonrpc": "2.0",
            "id": self._get_next_request_id(),
            "method": method
        }
        
        if params is not None:
            request_data["params"] = params
        
        # 构建请求 URL - 参考官方实现
        url = f"{self.base_url}/messages/?session_id={self.session_id}"
        
        print(f"\n📤 发送 MCP 请求:")
        print(f"   URL: {url}")
        print(f"   方法: {method}")
        print(f"   请求ID: {request_data['id']}")
        if params:
            print(f"   参数: {json.dumps(params, ensure_ascii=False, indent=2)}")
        
        try:
            # 发送请求 - 使用更长的超时时间，因为知识库查询可能需要更多时间
            response = requests.post(
                url,
                headers=self.headers,
                json=request_data,
                timeout=60  # 增加到60秒
            )
            
            print(f"📥 响应状态: {response.status_code}")

            # MCP 服务器可能返回 202 Accepted（异步处理）或 200 OK
            if response.status_code not in [200, 202]:
                print(f"❌ HTTP 错误: {response.status_code}")
                print(f"   响应内容: {response.text}")
                return {"error": {"code": response.status_code, "message": response.text}}

            # 处理 202 Accepted 响应
            if response.status_code == 202:
                print(f"📋 请求已接受，正在异步处理...")
                # 对于 202 响应，我们可能需要等待或轮询结果
                # 但先尝试解析响应内容
                response_text = response.text.strip()
                if response_text == "Accepted":
                    # 返回一个成功的响应，表示请求已被接受
                    return {
                        "jsonrpc": "2.0",
                        "id": request_data.get("id"),
                        "result": {"status": "accepted", "message": "Request accepted for processing"}
                    }
                else:
                    # 尝试解析为 JSON
                    try:
                        return response.json()
                    except:
                        return {
                            "jsonrpc": "2.0",
                            "id": request_data.get("id"),
                            "result": {"status": "accepted", "message": response_text}
                        }
            
            # 解析响应
            response_data = response.json()
            print(f"   响应数据: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
            
            return response_data
            
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求异常: {e}")
            return {"error": {"code": -1, "message": str(e)}}
        except json.JSONDecodeError as e:
            print(f"❌ JSON 解析错误: {e}")
            return {"error": {"code": -2, "message": f"JSON 解析错误: {e}"}}
    
    def test_connectivity(self) -> bool:
        """测试服务器连通性"""
        print(f"\n🔗 测试服务器连通性...")

        try:
            # 测试健康检查端点
            health_url = f"{self.base_url}/health"
            print(f"   测试健康检查: {health_url}")

            response = requests.get(health_url, headers=self.headers, timeout=5)
            print(f"   健康检查状态: {response.status_code}")

            if response.status_code in [200, 404, 405]:
                print("✅ 服务器连接正常")
                return True
            else:
                print(f"❌ 服务器响应异常: {response.status_code}")
                return False

        except requests.exceptions.Timeout:
            print("⚠️  健康检查超时，尝试其他端点...")

            # 尝试测试 SSE 端点
            try:
                sse_url = f"{self.base_url}/sse"
                response = requests.get(sse_url, headers=self.headers, timeout=3, stream=True)
                print(f"   SSE 端点状态: {response.status_code}")

                if response.status_code == 200:
                    print("✅ 服务器连接正常（通过SSE端点）")
                    return True
                else:
                    return False
            except:
                print("❌ 所有端点都无法访问")
                return False

        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    def initialize(self) -> bool:
        """初始化 MCP 连接"""
        print(f"\n🔗 初始化 MCP 连接...")
        
        # MCP 初始化参数 - 参考官方实现
        params = {
            "protocolVersion": "2024-11-05",
            "capabilities": {
                "tools": {}
            },
            "clientInfo": {
                "name": "RAGFlow-MCP-Client-Official",
                "version": "1.0.0"
            }
        }
        
        response = self._send_mcp_request("initialize", params)
        
        if "error" not in response and "result" in response:
            self.initialized = True
            self.server_capabilities = response.get("result", {}).get("capabilities", {})
            print("✅ MCP 连接初始化成功")
            print(f"   服务器能力: {self.server_capabilities}")
            return True
        else:
            print("❌ MCP 连接初始化失败")
            if "error" in response:
                error = response["error"]
                print(f"   错误: {error.get('message', 'Unknown error')}")
            return False
    
    def list_tools(self) -> List[Dict[str, Any]]:
        """获取可用工具列表"""
        print(f"\n🛠️ 获取工具列表...")
        
        if not self.initialized:
            print("❌ 请先初始化 MCP 连接")
            return []
        
        response = self._send_mcp_request("tools/list")
        
        if "error" not in response and "result" in response:
            result = response["result"]
            tools = result.get("tools", [])
            
            self.available_tools = tools
            print(f"✅ 获取到 {len(tools)} 个工具:")
            
            for tool in tools:
                name = tool.get("name", "Unknown")
                description = tool.get("description", "No description")
                print(f"   - {name}: {description}")
            
            return tools
        else:
            print("❌ 获取工具列表失败")
            if "error" in response:
                error = response["error"]
                print(f"   错误: {error.get('message', 'Unknown error')}")
            return []
    
    def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        调用工具
        
        Args:
            tool_name: 工具名称
            arguments: 工具参数
            
        Returns:
            工具执行结果
        """
        print(f"\n🔧 调用工具: {tool_name}")
        print(f"   参数: {json.dumps(arguments, ensure_ascii=False, indent=2)}")
        
        if not self.initialized:
            print("❌ 请先初始化 MCP 连接")
            return None
        
        params = {
            "name": tool_name,
            "arguments": arguments
        }
        
        response = self._send_mcp_request("tools/call", params)
        
        if "error" not in response and "result" in response:
            result = response["result"]
            print("✅ 工具调用成功")
            return result
        else:
            print("❌ 工具调用失败")
            if "error" in response:
                error = response["error"]
                print(f"   错误: {error.get('message', 'Unknown error')}")
            return None
    
    def query_ragflow(self, question: str, dataset_ids: Optional[List[str]] = None) -> Optional[str]:
        """
        查询 RAGFlow 知识库
        
        Args:
            question: 查询问题
            dataset_ids: 数据集ID列表（可选）
            
        Returns:
            查询结果
        """
        print(f"\n💬 查询 RAGFlow: {question}")
        
        # 构建查询参数
        arguments = {"question": question}
        if dataset_ids:
            arguments["dataset_ids"] = dataset_ids
        
        # 调用 ragflow_retrieval 工具
        result = self.call_tool("ragflow_retrieval", arguments)
        
        if result:
            # 解析结果 - 根据 RAGFlow 的响应格式
            content = result.get("content", [])
            if content and len(content) > 0:
                # 提取第一个结果的文本
                first_result = content[0]
                if isinstance(first_result, dict):
                    text = first_result.get("text", "")
                    if text:
                        print(f"🤖 查询结果: {text[:200]}...")
                        return text
                elif isinstance(first_result, str):
                    print(f"🤖 查询结果: {first_result[:200]}...")
                    return first_result
            
            # 如果没有找到标准格式，尝试其他可能的格式
            if "answer" in result:
                answer = result["answer"]
                print(f"🤖 查询结果: {answer[:200]}...")
                return answer
            
            print("⚠️ 查询成功但无法解析结果格式")
            print(f"   原始结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
            return None
        else:
            print("❌ 查询失败")
            return None


def main():
    """主测试函数"""
    print("🚀 RAGFlow MCP 客户端测试 (官方实现风格)")
    print("=" * 60)
    
    # 创建客户端
    client = RAGFlowMCPClientOfficial()
    
    try:
        # 1. 获取服务器会话ID
        if not client.get_server_session_id():
            print("❌ 获取服务器会话ID失败")
            return

        # 2. 测试连通性
        if not client.test_connectivity():
            print("❌ 服务器连通性测试失败")
            return

        # 3. 初始化连接
        if not client.initialize():
            print("❌ MCP 连接初始化失败")
            return
        
        # 3. 获取工具列表
        tools = client.list_tools()
        if not tools:
            print("❌ 无法获取工具列表")
            return
        
        # 4. 测试查询
        test_questions = [
            "候鸟浏览器如何配置代理？",
            "RAGFlow是什么？",
            "如何创建知识库？"
        ]
        
        print(f"\n🧪 开始测试查询功能...")
        
        for i, question in enumerate(test_questions, 1):
            print(f"\n" + "─" * 50)
            print(f"📝 测试问题 {i}/{len(test_questions)}: {question}")
            
            answer = client.query_ragflow(question)
            
            if answer:
                print(f"✅ 查询成功")
            else:
                print(f"❌ 查询失败")
            
            # 添加延迟
            if i < len(test_questions):
                time.sleep(2)
        
        print(f"\n" + "=" * 60)
        print(f"🎉 测试完成！")
        
    except KeyboardInterrupt:
        print(f"\n\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")


if __name__ == "__main__":
    main()
