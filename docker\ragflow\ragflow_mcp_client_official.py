#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RAGFlow MCP 客户端 - 参考官方实现
基于 RAGFlow 官方 MCP 客户端实现，确保与服务器的兼容性
"""

import json
import uuid
import requests
import time
from typing import Dict, Any, Optional, List

class RAGFlowMCPClientOfficial:
    """RAGFlow MCP 客户端 - 官方实现风格"""
    
    def __init__(self, 
                 base_url: str = "http://************:9382", 
                 api_key: str = "ragflow-UzYTlhMjIwNjI0YzExZjA4NzUyMDI0Mm"):
        """
        初始化 RAGFlow MCP 客户端
        
        Args:
            base_url: RAGFlow MCP Server 地址
            api_key: API 密钥
        """
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.session_id = str(uuid.uuid4())
        self.request_id = 0
        
        # 设置请求头 - 参考官方实现
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}",
            "User-Agent": "RAGFlow-MCP-Client-Official/1.0",
            "Accept": "application/json"
        }
        
        # 会话状态
        self.initialized = False
        self.server_capabilities = {}
        self.available_tools = []
        
        print(f"🤖 RAGFlow MCP 客户端初始化 (官方实现风格)")
        print(f"   服务器: {self.base_url}")
        print(f"   会话ID: {self.session_id}")
        print(f"   API密钥: {self.api_key[:20]}...")
    
    def _get_next_request_id(self) -> int:
        """获取下一个请求ID"""
        self.request_id += 1
        return self.request_id
    
    def _send_mcp_request(self, method: str, params: Optional[Dict] = None) -> Dict[str, Any]:
        """
        发送 MCP 协议请求
        
        Args:
            method: MCP 方法名
            params: 请求参数
            
        Returns:
            MCP 响应
        """
        # 构建 JSON-RPC 2.0 请求
        request_data = {
            "jsonrpc": "2.0",
            "id": self._get_next_request_id(),
            "method": method
        }
        
        if params is not None:
            request_data["params"] = params
        
        # 构建请求 URL - 参考官方实现
        url = f"{self.base_url}/messages/?session_id={self.session_id}"
        
        print(f"\n📤 发送 MCP 请求:")
        print(f"   URL: {url}")
        print(f"   方法: {method}")
        print(f"   请求ID: {request_data['id']}")
        if params:
            print(f"   参数: {json.dumps(params, ensure_ascii=False, indent=2)}")
        
        try:
            # 发送请求 - 使用更长的超时时间，因为知识库查询可能需要更多时间
            response = requests.post(
                url,
                headers=self.headers,
                json=request_data,
                timeout=60  # 增加到60秒
            )
            
            print(f"📥 响应状态: {response.status_code}")
            
            if response.status_code != 200:
                print(f"❌ HTTP 错误: {response.status_code}")
                print(f"   响应内容: {response.text}")
                return {"error": {"code": response.status_code, "message": response.text}}
            
            # 解析响应
            response_data = response.json()
            print(f"   响应数据: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
            
            return response_data
            
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求异常: {e}")
            return {"error": {"code": -1, "message": str(e)}}
        except json.JSONDecodeError as e:
            print(f"❌ JSON 解析错误: {e}")
            return {"error": {"code": -2, "message": f"JSON 解析错误: {e}"}}
    
    def test_connectivity(self) -> bool:
        """测试服务器连通性"""
        print(f"\n🔗 测试服务器连通性...")

        try:
            # 测试 SSE 端点 - 使用更短的超时时间，因为 SSE 是流式响应
            sse_url = f"{self.base_url}/sse"
            print(f"   正在测试: {sse_url}")

            # SSE 端点会持续发送数据，所以我们只需要确认连接建立即可
            response = requests.get(sse_url, headers=self.headers, timeout=5, stream=True)

            print(f"   SSE 端点状态: {response.status_code}")

            # 读取一点数据来确认连接正常
            if response.status_code == 200:
                try:
                    # 读取前几行数据
                    lines = []
                    for line in response.iter_lines(decode_unicode=True):
                        if line:
                            lines.append(line)
                            if len(lines) >= 3:  # 读取前3行就够了
                                break

                    print(f"   SSE 响应示例:")
                    for line in lines[:2]:  # 只显示前2行
                        print(f"     {line}")

                    # 检查是否包含预期的内容
                    response_text = '\n'.join(lines)
                    if 'endpoint' in response_text or 'ping' in response_text:
                        print("✅ SSE 端点正常，服务器连接成功")
                        return True
                    else:
                        print("⚠️  SSE 端点响应格式异常")
                        return False

                except Exception as read_error:
                    print(f"   读取 SSE 数据时出错: {read_error}")
                    # 即使读取出错，如果状态码是200，也认为连接成功
                    return True

            elif response.status_code == 404:
                print("⚠️  SSE 端点返回404，但服务器可达")
                return True
            else:
                print(f"❌ 服务器响应异常: {response.status_code}")
                return False

        except requests.exceptions.Timeout:
            print("⚠️  连接超时，但这对于 SSE 端点是正常的")
            print("   尝试测试其他端点...")

            # 如果 SSE 超时，尝试测试一个简单的端点
            try:
                test_url = f"{self.base_url}/health"
                response = requests.get(test_url, headers=self.headers, timeout=3)
                print(f"   健康检查端点: {response.status_code}")
                return response.status_code in [200, 404, 405]  # 这些状态码都表示服务器可达
            except:
                print("❌ 所有端点都无法访问")
                return False

        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    def initialize(self) -> bool:
        """初始化 MCP 连接"""
        print(f"\n🔗 初始化 MCP 连接...")
        
        # MCP 初始化参数 - 参考官方实现
        params = {
            "protocolVersion": "2024-11-05",
            "capabilities": {
                "tools": {}
            },
            "clientInfo": {
                "name": "RAGFlow-MCP-Client-Official",
                "version": "1.0.0"
            }
        }
        
        response = self._send_mcp_request("initialize", params)
        
        if "error" not in response and "result" in response:
            self.initialized = True
            self.server_capabilities = response.get("result", {}).get("capabilities", {})
            print("✅ MCP 连接初始化成功")
            print(f"   服务器能力: {self.server_capabilities}")
            return True
        else:
            print("❌ MCP 连接初始化失败")
            if "error" in response:
                error = response["error"]
                print(f"   错误: {error.get('message', 'Unknown error')}")
            return False
    
    def list_tools(self) -> List[Dict[str, Any]]:
        """获取可用工具列表"""
        print(f"\n🛠️ 获取工具列表...")
        
        if not self.initialized:
            print("❌ 请先初始化 MCP 连接")
            return []
        
        response = self._send_mcp_request("tools/list")
        
        if "error" not in response and "result" in response:
            result = response["result"]
            tools = result.get("tools", [])
            
            self.available_tools = tools
            print(f"✅ 获取到 {len(tools)} 个工具:")
            
            for tool in tools:
                name = tool.get("name", "Unknown")
                description = tool.get("description", "No description")
                print(f"   - {name}: {description}")
            
            return tools
        else:
            print("❌ 获取工具列表失败")
            if "error" in response:
                error = response["error"]
                print(f"   错误: {error.get('message', 'Unknown error')}")
            return []
    
    def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        调用工具
        
        Args:
            tool_name: 工具名称
            arguments: 工具参数
            
        Returns:
            工具执行结果
        """
        print(f"\n🔧 调用工具: {tool_name}")
        print(f"   参数: {json.dumps(arguments, ensure_ascii=False, indent=2)}")
        
        if not self.initialized:
            print("❌ 请先初始化 MCP 连接")
            return None
        
        params = {
            "name": tool_name,
            "arguments": arguments
        }
        
        response = self._send_mcp_request("tools/call", params)
        
        if "error" not in response and "result" in response:
            result = response["result"]
            print("✅ 工具调用成功")
            return result
        else:
            print("❌ 工具调用失败")
            if "error" in response:
                error = response["error"]
                print(f"   错误: {error.get('message', 'Unknown error')}")
            return None
    
    def query_ragflow(self, question: str, dataset_ids: Optional[List[str]] = None) -> Optional[str]:
        """
        查询 RAGFlow 知识库
        
        Args:
            question: 查询问题
            dataset_ids: 数据集ID列表（可选）
            
        Returns:
            查询结果
        """
        print(f"\n💬 查询 RAGFlow: {question}")
        
        # 构建查询参数
        arguments = {"question": question}
        if dataset_ids:
            arguments["dataset_ids"] = dataset_ids
        
        # 调用 ragflow_retrieval 工具
        result = self.call_tool("ragflow_retrieval", arguments)
        
        if result:
            # 解析结果 - 根据 RAGFlow 的响应格式
            content = result.get("content", [])
            if content and len(content) > 0:
                # 提取第一个结果的文本
                first_result = content[0]
                if isinstance(first_result, dict):
                    text = first_result.get("text", "")
                    if text:
                        print(f"🤖 查询结果: {text[:200]}...")
                        return text
                elif isinstance(first_result, str):
                    print(f"🤖 查询结果: {first_result[:200]}...")
                    return first_result
            
            # 如果没有找到标准格式，尝试其他可能的格式
            if "answer" in result:
                answer = result["answer"]
                print(f"🤖 查询结果: {answer[:200]}...")
                return answer
            
            print("⚠️ 查询成功但无法解析结果格式")
            print(f"   原始结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
            return None
        else:
            print("❌ 查询失败")
            return None


def main():
    """主测试函数"""
    print("🚀 RAGFlow MCP 客户端测试 (官方实现风格)")
    print("=" * 60)
    
    # 创建客户端
    client = RAGFlowMCPClientOfficial()
    
    try:
        # 1. 测试连通性
        if not client.test_connectivity():
            print("❌ 服务器连通性测试失败")
            return
        
        # 2. 初始化连接
        if not client.initialize():
            print("❌ MCP 连接初始化失败")
            return
        
        # 3. 获取工具列表
        tools = client.list_tools()
        if not tools:
            print("❌ 无法获取工具列表")
            return
        
        # 4. 测试查询
        test_questions = [
            "候鸟浏览器如何配置代理？",
            "RAGFlow是什么？",
            "如何创建知识库？"
        ]
        
        print(f"\n🧪 开始测试查询功能...")
        
        for i, question in enumerate(test_questions, 1):
            print(f"\n" + "─" * 50)
            print(f"📝 测试问题 {i}/{len(test_questions)}: {question}")
            
            answer = client.query_ragflow(question)
            
            if answer:
                print(f"✅ 查询成功")
            else:
                print(f"❌ 查询失败")
            
            # 添加延迟
            if i < len(test_questions):
                time.sleep(2)
        
        print(f"\n" + "=" * 60)
        print(f"🎉 测试完成！")
        
    except KeyboardInterrupt:
        print(f"\n\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")


if __name__ == "__main__":
    main()
