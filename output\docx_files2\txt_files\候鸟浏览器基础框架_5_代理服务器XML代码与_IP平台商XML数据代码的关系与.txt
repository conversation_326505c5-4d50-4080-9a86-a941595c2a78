44.5 代理服务器XML代码与 IP平台商XML数据代码的关系与功能说明

44.5 代理服务器XML代码与 IP平台商XML数据代码的关系与功能说明

(因轻重缓急因素此章节模块放后设计)

[********]

第四十五章
候鸟客户端 环境高级配置项
完整流程说明

依据传统，具体工作步骤、顺序安排，人员分配在工作群里具体阐述。

注： ITEM CONFIGDATA.XML原始结构（已增补新增节点到105页）

45.1.1

交互界面：
(图1)

图2）

45.1.2

控件说明：（图3）

新增约定（一） (2024-11-11)：

利用ACCOUNT_LIST 节点来支持新逻辑：环境运行后，自动打开多个页面。
      默认起始页 与 环境运行后自动打开多个标签页 作为两套相互独立的机制来运转，相互不冲突。

用户可以将两个功能视为同一个功能，也可以将两个功能中，环境运行后自动打开多个标签页 理解为加强功能来使用。

Item的Configdata.xml中，原节点：

<ACCOUNT_LIST NUM="1">

<ACCOUNT DOMAIN="iuse.com.cn" PLATFORM="自定义类型" DOMAINURL="https://www.iuse.com.cn" LOGIN="111" ORGIN_PASSWORD="1111" ENCRYPT_PASSWORD="" PASSWORD2FA="" IS_VALID="1" action_url="" signon_realm="https://www.iuse.com.cn" BOOKMARK_URL="https://www.iuse.com.cn"  auto_open=”0” CREATE_TIME="" UPDATE_TIME=""/>

</ACCOUNT_LIST>

auto_open值：空值或0(或此参数不存在),  1,  2

auto_open = 0/空/参数不存在 :  无处理。

auto_open = 1 : 运行环境时，此项页面自动打开，(在环境配置窗口中的默认打开列表中显示)。

auto_open = 2 : 在环境配置窗口中仅显示，即勾选去除动作，运行环境不自动打开。

如图：（图4）

图4中，各网址输入控件为下拉列表，列出所有ACCOUNT_LIST项，内容为网址完整串（不允许重复）。

ACCOUNT_LIST NUM 新增auto_open项：

当auto_open参数不存在，或auto_open=”” 或auto_open参数值不等于1时，则此项不作为环境运行的自动打开项。

如同时对指定环境设定了默认起始页和ACCOUNT_LIST auto_open=”1” 的页面，则运行环境时，这些页面均自动打开。

新增约定（二）（图5）

    环境登录帐户管理器 添加帐号 逻辑
    调整为：允许用户不输入帐号和密码 也可成功添加。

5、新增项：设定为自动打开页面（图6）

说明1：A区列表中不包含任何自动打开的标识数据，仅C区列表（item的configdata.xml中保存自动打开的参数值）。

说明2：设定为自动打开页面 按钮，必须用户在C区勾选记录后，才可以变亮显示，并可点击，点击后，要在右下角显示系统MESSAGEBOX提示：

                https://xxxxxxxxxxxxxxxxxxxxxxxx                      
                网址已成功设定为自动打开网页

说明3：C区中，对于 已设定为 自动打开页面的 记录项，当用户点击到记录项上后，设定为自动打开页面 按钮 变更显示为：关闭此项自动打开。

           多选的记录如果都为open_open=1 默认显示&亮起按钮：关闭此项自动打开
           多选的记录如果都为open_open<>1 默认显示&亮起按钮：设定为自动打开页面
           多选的记录不为上述情况的： 默认置灰按钮

说明4：（图7）

对于auto_open=1的记录，帐户名称前显示固定前缀：(T)

含义为TAB。

说明5：如果用户的环境配置面板configpad.xml窗口在开启状态，同时环境登录帐户管理器窗口在开启状态，则用户在C区 设定为自动打开页面 项时，要同步更新 环境配置面板configpad.xml 中的（图4）的各个下拉控件信息。

说明6：C区的右键菜单 新增 设定为自动打开页面 项 和 关闭此项自动打开。（auto_open 置0）

说明7：套餐区别对待逻辑：    

            个人自定义版本用户：只允许最多添加2个。

个人无限制用户：允许添加10个。

团队用户：允许添加20个。

“X” 按钮说明：

X按钮，点击后左边的那一项删除，相当于item 的configdata.xml中的ACCOUNT_LIST节点中的对应的记录中的auto_open值写0

“应用” 按钮说明：

       此按钮点击后，将用户确认的默认打开网页的数据写入到configdata.xml。

[********]

第四十六章
候鸟客户端

API自动提取代理IP功能

流程与数据结构设计

完整流程说明

依据传统，具体工作步骤、顺序安排，人员分配在工作群里具体阐述。

综述：

潜在客户需求


================================================== 表格内容 ==================================================

{
"message "Plugin Installed Success",
"code": 0,
"data": {
"listcontainer": [
        {
"Session_Name": “商用业务环境一”
        "Session_ID": "373808cb37bd63f5f7d92415e736e85f",
"Group_Name": “default”,
"Plugins_Count": "4",
"Plugin_list": 
                    [{
                     "Plugin_Name" : "AAA",
                     "Plugin_Id" : " jjbnhpnlakcdgfnnldamfeinfmahhdlm"
                         },
{
                     "Plugin_Name" : "BBB",
                     "Plugin_Id" : " jjbnhpnlakcdgfnnldamfeinfmahhdlm "
                         }],
"status": 0
}

}
}

参数名称 | 类型 | 必传 | 样例串/默认值 | 说明
Script_ID | array | 是 | 373808cb37bd63f5f7d92415e736e85f, 705cc4c139e69b729a2fd277f30e1863 | 指派的所有脚本ID
（支持一次最多100个）
Session_ID | String | 是 | 3e8448bcfb9f35cbeaef5ac376771932 | 指定环境
(支持一次最多1个环境)