#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
候鸟浏览器基础框架文档切分脚本
根据章节结构切分成多个文件，适用于RAGFlow向量库
"""

import os
import re
from pathlib import Path
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.style import WD_STYLE_TYPE

def analyze_document_structure(docx_path):
    """分析文档结构，识别章节"""
    try:
        doc = Document(docx_path)
        
        print(f"📄 分析文档: {Path(docx_path).name}")
        print(f"📊 总段落数: {len(doc.paragraphs)}")
        print(f"📊 总表格数: {len(doc.tables)}")
        print()
        
        # 分析段落结构，找到章节标题
        chapters = []
        current_chapter = None
        
        for i, paragraph in enumerate(doc.paragraphs):
            text = paragraph.text.strip()
            if not text:
                continue
            
            # 识别章节标题的模式
            is_chapter = False
            chapter_name = ""
            
            # 模式1: 数字开头的章节 (如: "1. 基础介绍", "第一章 基础介绍")
            if re.match(r'^(\d+\.|\d+、|第\d+章|第[一二三四五六七八九十]+章)', text):
                is_chapter = True
                chapter_name = re.sub(r'^(\d+\.|\d+、|第\d+章|第[一二三四五六七八九十]+章)\s*', '', text)
            
            # 模式2: 标题样式
            elif paragraph.style.name.startswith('Heading'):
                is_chapter = True
                chapter_name = text
            
            # 模式3: 特定关键词开头
            elif any(text.startswith(keyword) for keyword in ['基础', '安装', '配置', '使用', '管理', '高级', '故障', '附录']):
                if len(text) < 50:  # 短文本更可能是标题
                    is_chapter = True
                    chapter_name = text
            
            # 模式4: 全大写或特殊格式
            elif text.isupper() and len(text) < 30:
                is_chapter = True
                chapter_name = text
            
            if is_chapter and chapter_name:
                if current_chapter:
                    current_chapter['end_index'] = i - 1
                    chapters.append(current_chapter)
                
                current_chapter = {
                    'name': chapter_name,
                    'start_index': i,
                    'end_index': None,
                    'paragraph_count': 0,
                    'table_count': 0
                }
        
        # 处理最后一个章节
        if current_chapter:
            current_chapter['end_index'] = len(doc.paragraphs) - 1
            chapters.append(current_chapter)
        
        # 计算每个章节的内容量
        for chapter in chapters:
            chapter['paragraph_count'] = chapter['end_index'] - chapter['start_index'] + 1
        
        # 统计表格分布（简化处理）
        tables_per_chapter = len(doc.tables) // max(1, len(chapters))
        for i, chapter in enumerate(chapters):
            chapter['table_count'] = tables_per_chapter
            if i == len(chapters) - 1:  # 最后一章包含剩余表格
                chapter['table_count'] += len(doc.tables) % len(chapters)
        
        print("📋 发现的章节结构:")
        for i, chapter in enumerate(chapters):
            print(f"  {i+1:2d}. {chapter['name'][:50]}...")
            print(f"      段落: {chapter['start_index']}-{chapter['end_index']} ({chapter['paragraph_count']}个)")
            print(f"      表格: ~{chapter['table_count']}个")
        
        return doc, chapters
        
    except Exception as e:
        print(f"❌ 分析文档失败: {str(e)}")
        return None, None

def setup_document_styles(doc):
    """设置文档样式"""
    try:
        # 标题1样式
        heading1 = doc.styles['Heading 1']
        heading1.font.size = Pt(18)
        heading1.font.bold = True
    except:
        pass
    
    try:
        # 标题2样式
        heading2 = doc.styles['Heading 2']
        heading2.font.size = Pt(16)
        heading2.font.bold = True
    except:
        pass
    
    try:
        # 正文样式
        normal = doc.styles['Normal']
        normal.font.size = Pt(12)
    except:
        pass

def copy_paragraph_with_style(source_para, target_doc):
    """复制段落及其样式到目标文档"""
    try:
        # 创建新段落
        new_para = target_doc.add_paragraph()
        
        # 复制段落样式
        try:
            new_para.style = source_para.style
        except:
            pass
        
        # 复制段落对齐方式
        new_para.alignment = source_para.alignment
        
        # 复制文本和格式
        for run in source_para.runs:
            new_run = new_para.add_run(run.text)
            new_run.bold = run.bold
            new_run.italic = run.italic
            new_run.underline = run.underline
            try:
                new_run.font.size = run.font.size
                new_run.font.name = run.font.name
            except:
                pass
        
        return new_para
    except Exception as e:
        # 如果复制失败，至少复制文本
        return target_doc.add_paragraph(source_para.text)

def copy_table_to_doc(source_table, target_doc):
    """复制表格到目标文档"""
    try:
        # 获取表格尺寸
        rows = len(source_table.rows)
        cols = len(source_table.columns) if source_table.rows else 0
        
        if rows == 0 or cols == 0:
            return
        
        # 创建新表格
        new_table = target_doc.add_table(rows=rows, cols=cols)
        new_table.style = 'Table Grid'
        
        # 复制表格内容
        for i, row in enumerate(source_table.rows):
            for j, cell in enumerate(row.cells):
                if i < len(new_table.rows) and j < len(new_table.rows[i].cells):
                    new_table.cell(i, j).text = cell.text
                    
                    # 复制单元格格式
                    for paragraph in cell.paragraphs:
                        if paragraph.runs:
                            for run in paragraph.runs:
                                if run.bold:
                                    for new_para in new_table.cell(i, j).paragraphs:
                                        for new_run in new_para.runs:
                                            new_run.bold = True
                                    break
        
        return new_table
    except Exception as e:
        print(f"⚠️  复制表格失败: {str(e)}")
        return None

def clean_filename(name):
    """清理文件名，移除非法字符"""
    # 移除或替换非法字符
    name = re.sub(r'[<>:"/\\|?*]', '_', name)
    name = re.sub(r'\s+', '_', name)
    name = name.strip('_')
    
    # 限制长度
    if len(name) > 50:
        name = name[:50]
    
    return name

def split_large_chapter(chapter, source_doc, max_paragraphs=50):
    """如果章节过大，进一步切分"""
    if chapter['paragraph_count'] <= max_paragraphs:
        return [chapter]
    
    # 计算需要切分的数量
    num_parts = (chapter['paragraph_count'] + max_paragraphs - 1) // max_paragraphs
    paragraphs_per_part = chapter['paragraph_count'] // num_parts
    
    parts = []
    start_idx = chapter['start_index']
    
    for i in range(num_parts):
        end_idx = start_idx + paragraphs_per_part - 1
        if i == num_parts - 1:  # 最后一部分包含剩余段落
            end_idx = chapter['end_index']
        
        part = {
            'name': chapter['name'],
            'part_number': i + 1,
            'start_index': start_idx,
            'end_index': end_idx,
            'paragraph_count': end_idx - start_idx + 1,
            'table_count': chapter['table_count'] // num_parts
        }
        
        if i == num_parts - 1:  # 最后一部分包含剩余表格
            part['table_count'] += chapter['table_count'] % num_parts
        
        parts.append(part)
        start_idx = end_idx + 1
    
    return parts

def split_framework_document(source_doc, chapters, output_dir):
    """根据章节切分文档"""
    output_files = []
    table_index = 0
    
    print("=" * 60)
    print("开始按章节切分文档...")
    print("=" * 60)
    
    for chapter_idx, chapter in enumerate(chapters):
        # 检查章节是否过大，如果是则进一步切分
        chapter_parts = split_large_chapter(chapter, source_doc, max_paragraphs=80)
        
        for part in chapter_parts:
            # 创建新文档
            doc = Document()
            setup_document_styles(doc)
            
            # 生成文件名
            clean_chapter_name = clean_filename(part['name'])
            if len(chapter_parts) > 1:
                filename = f"候鸟浏览器基础框架_{clean_chapter_name}({part['part_number']}).docx"
            else:
                filename = f"候鸟浏览器基础框架_{clean_chapter_name}.docx"
            
            # 添加文档标题
            if len(chapter_parts) > 1:
                doc.add_heading(f'{part["name"]} (第{part["part_number"]}部分)', 0)
            else:
                doc.add_heading(part['name'], 0)
            
            # 复制段落内容
            for para_idx in range(part['start_index'], part['end_index'] + 1):
                if para_idx < len(source_doc.paragraphs):
                    copy_paragraph_with_style(source_doc.paragraphs[para_idx], doc)
            
            # 分配表格（简化处理）
            tables_to_add = part['table_count']
            for _ in range(tables_to_add):
                if table_index < len(source_doc.tables):
                    copy_table_to_doc(source_doc.tables[table_index], doc)
                    table_index += 1
            
            # 保存文档
            filepath = Path(output_dir) / filename
            doc.save(str(filepath))
            output_files.append(str(filepath))
            
            # 统计信息
            para_count = len(doc.paragraphs)
            table_count = len(doc.tables)
            file_size = filepath.stat().st_size / 1024
            
            print(f"✅ 创建文件: {filename}")
            print(f"   章节: {part['name']}")
            if len(chapter_parts) > 1:
                print(f"   部分: {part['part_number']}/{len(chapter_parts)}")
            print(f"   段落数: {para_count}")
            print(f"   表格数: {table_count}")
            print(f"   文件大小: {file_size:.1f} KB")
            print()
    
    return output_files

def split_framework_doc_main(docx_path, output_dir=None):
    """主函数：切分候鸟浏览器基础框架文档"""
    if output_dir is None:
        output_dir = Path(docx_path).parent
    
    # 确保输出目录存在
    Path(output_dir).mkdir(exist_ok=True)
    
    # 分析文档结构
    source_doc, chapters = analyze_document_structure(docx_path)
    if source_doc is None or not chapters:
        print("❌ 无法分析文档结构或未找到章节")
        return []
    
    # 执行切分
    output_files = split_framework_document(source_doc, chapters, output_dir)
    
    print("=" * 60)
    print(f"🎉 切分完成! 生成了 {len(output_files)} 个文件")
    print()
    
    # 显示文件列表
    print("📋 生成的文件:")
    total_size = 0
    for file_path in output_files:
        file_size = Path(file_path).stat().st_size / 1024
        total_size += file_size
        print(f"   📄 {Path(file_path).name} ({file_size:.1f} KB)")
    
    print(f"\n📊 总大小: {total_size:.1f} KB")
    print(f"📊 平均大小: {total_size/len(output_files):.1f} KB")
    
    return output_files

if __name__ == "__main__":
    # 设置文件路径
    source_file = r"F:\augment\output\docx_files\候鸟浏览器基础框架第七十七版.docx"
    output_directory = r"F:\augment\output\docx_files"
    
    print("📄 候鸟浏览器基础框架文档切分工具")
    print(f"📂 源文件: {source_file}")
    print(f"📂 输出目录: {output_directory}")
    print()
    
    # 检查源文件是否存在
    if not Path(source_file).exists():
        print(f"❌ 源文件不存在: {source_file}")
        exit(1)
    
    # 执行切分
    result_files = split_framework_doc_main(source_file, output_directory)
    
    if result_files:
        print("\n🎯 切分完成，文件已准备好用于RAGFlow向量库！")
    else:
        print("❌ 切分失败")
