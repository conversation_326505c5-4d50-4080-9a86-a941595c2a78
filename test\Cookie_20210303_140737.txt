标题: Cookie
英文标题: Cookie
ID: 95
分类ID: 25
添加时间: 1614751657
更新时间: 1685429965
访问次数: 0
SEO标题: Cookie
SEO关键词: Cookie
SEO描述: Cookie

================================================== 内容 ==================================================
Cookie，类型为“小型文本文件”，指某些网站为了辨别用户身份而储存在用户本地终端（Client Side）上的数据（通常经过加密）。


因为HTTP协议是无状态的，即服务器不知道用户上一次做了什么，这严重阻碍了交互式Web应用程序的实现。在典型的网上购物场景中，用户浏览了几个页面，买了一盒饼干和两瓶饮料。最后结帐时，由于HTTP的无状态性，不通过额外的手段，服务器并不知道用户到底买了什么，所以Cookie就是用来绕开HTTP的无状态性的“额外手段”之一。服务器可以设置或读取Cookies中包含信息，借此维护用户跟服务器会话中的状态。


在刚才的购物场景中，当用户选购了第一项商品，服务器在向用户发送网页的同时，还发送了一段Cookie，记录着那项商品的信息。当用户访问另一个页面，浏览器会把Cookie发送给服务器，于是服务器知道他之前选购了什么。用户继续选购饮料，服务器就在原来那段Cookie里追加新的商品信息。结帐时，服务器读取发送来的Cookie就行了。


Cookie另一个典型的应用是当登录一个网站时，网站往往会请求用户输入用户名和密码，并且用户可以勾选“下次自动登录”。如果勾选了，那么下次访问同一网站时，用户会发现没输入用户名和密码就已经登录了。这正是因为前一次登录时，服务器发送了包含登录凭据（用户名加密码的某种加密形式）的Cookie到用户的硬盘上。第二次登录时，如果该Cookie尚未到期，浏览器会发送该Cookie，服务器验证凭据，于是不必输入用户名和密码就让用户登录了。

================================================== 英文内容 ==================================================
Cookies, of the type "small text file," refer to data (usually encrypted) stored on the Client Side of a user by some Web sites in order to identify the user.


Interactive Web applications are severely hampered by the fact that the HTTP protocol is stateless, meaning that the server does not know what the user did last time. In a typical online shopping scenario, a user browses a few pages and buys a box of cookies and two drinks. At the end of the checkout, because HTTP is stateless, the server does not know what the user has bought without additional means, so cookies are one of the "extra means" used to circumvent HTTP statelessness. The server can set or read the information contained in Cookies to maintain the state of the user's session with the server.


In the shopping scenario just now, when the user chooses the first item, the server sends the user a web page along with a Cookie, which records the information about that item. When a user visits another page, the browser sends a Cookie to the server, so the server knows what he purchased earlier. The user continues to buy drinks, and the server adds new product information to the original Cookie. At checkout, the server reads the cookies sent.


Another typical use of cookies is that when logging into a website, the site often asks the user for a username and password, and the user can check the box "Automatic login next time." If it is checked, the next time the user visits the same site, they will find that they have logged in without entering their username and password. This is precisely because on the previous login, the server sent a Cookie containing login credentials (some encrypted form of username plus password) to the user's hard drive. On the second login, if the Cookie has not expired, the browser sends the Cookie, the server validates the credentials and lets the user log in without having to enter a username and password.