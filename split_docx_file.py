#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DOCX文件切分脚本
将API_环境管理.docx文件切分成3个文件
"""

import os
from pathlib import Path
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.style import WD_STYLE_TYPE

def analyze_document_structure(docx_path):
    """分析文档结构，找到合适的切分点"""
    try:
        doc = Document(docx_path)
        
        print(f"📄 分析文档: {Path(docx_path).name}")
        print(f"📊 总段落数: {len(doc.paragraphs)}")
        print(f"📊 总表格数: {len(doc.tables)}")
        print()
        
        # 分析段落结构，找到标题
        headings = []
        for i, paragraph in enumerate(doc.paragraphs):
            text = paragraph.text.strip()
            if text and (paragraph.style.name.startswith('Heading') or 
                        text.startswith('##') or text.startswith('#') or
                        len(text) < 100 and ('API' in text or '环境' in text or '管理' in text)):
                headings.append({
                    'index': i,
                    'text': text,
                    'style': paragraph.style.name
                })
        
        print("📋 发现的标题结构:")
        for heading in headings:
            print(f"  {heading['index']:3d}: {heading['text'][:60]}...")
        
        return doc, headings
        
    except Exception as e:
        print(f"❌ 分析文档失败: {str(e)}")
        return None, None

def setup_document_styles(doc):
    """设置文档样式"""
    try:
        # 标题1样式
        heading1 = doc.styles['Heading 1']
        heading1.font.size = Pt(18)
        heading1.font.bold = True
    except:
        pass
    
    try:
        # 标题2样式
        heading2 = doc.styles['Heading 2']
        heading2.font.size = Pt(16)
        heading2.font.bold = True
    except:
        pass
    
    try:
        # 正文样式
        normal = doc.styles['Normal']
        normal.font.size = Pt(12)
    except:
        pass

def copy_paragraph_with_style(source_para, target_doc):
    """复制段落及其样式到目标文档"""
    try:
        # 创建新段落
        new_para = target_doc.add_paragraph()
        
        # 复制段落样式
        try:
            new_para.style = source_para.style
        except:
            pass
        
        # 复制段落对齐方式
        new_para.alignment = source_para.alignment
        
        # 复制文本和格式
        for run in source_para.runs:
            new_run = new_para.add_run(run.text)
            new_run.bold = run.bold
            new_run.italic = run.italic
            new_run.underline = run.underline
            try:
                new_run.font.size = run.font.size
                new_run.font.name = run.font.name
            except:
                pass
        
        return new_para
    except Exception as e:
        # 如果复制失败，至少复制文本
        return target_doc.add_paragraph(source_para.text)

def copy_table_to_doc(source_table, target_doc):
    """复制表格到目标文档"""
    try:
        # 获取表格尺寸
        rows = len(source_table.rows)
        cols = len(source_table.columns) if source_table.rows else 0
        
        if rows == 0 or cols == 0:
            return
        
        # 创建新表格
        new_table = target_doc.add_table(rows=rows, cols=cols)
        new_table.style = 'Table Grid'
        
        # 复制表格内容
        for i, row in enumerate(source_table.rows):
            for j, cell in enumerate(row.cells):
                if i < len(new_table.rows) and j < len(new_table.rows[i].cells):
                    new_table.cell(i, j).text = cell.text
                    
                    # 复制单元格格式
                    for paragraph in cell.paragraphs:
                        if paragraph.runs:
                            for run in paragraph.runs:
                                if run.bold:
                                    for new_para in new_table.cell(i, j).paragraphs:
                                        for new_run in new_para.runs:
                                            new_run.bold = True
                                    break
        
        return new_table
    except Exception as e:
        print(f"⚠️  复制表格失败: {str(e)}")
        return None

def split_document_by_content(source_doc, output_dir):
    """根据内容智能切分文档"""
    total_paragraphs = len(source_doc.paragraphs)
    total_tables = len(source_doc.tables)
    
    # 计算每个文件的大致内容量
    paragraphs_per_file = total_paragraphs // 3
    tables_per_file = total_tables // 3
    
    print(f"📊 切分计划:")
    print(f"   总段落: {total_paragraphs}, 每文件约: {paragraphs_per_file}")
    print(f"   总表格: {total_tables}, 每文件约: {tables_per_file}")
    print()
    
    # 定义切分点 (大致均分)
    split_points = [
        0,
        total_paragraphs // 3,
        (total_paragraphs * 2) // 3,
        total_paragraphs
    ]
    
    # 创建三个文档
    docs = []
    for i in range(3):
        doc = Document()
        setup_document_styles(doc)
        
        # 添加文档标题
        doc.add_heading(f'环境管理 API 文档 (第{i+1}部分)', 0)
        docs.append(doc)
    
    # 分配段落到不同文档
    table_index = 0
    
    for para_index, paragraph in enumerate(source_doc.paragraphs):
        # 确定当前段落应该放在哪个文档中
        if para_index < split_points[1]:
            target_doc = docs[0]
        elif para_index < split_points[2]:
            target_doc = docs[1]
        else:
            target_doc = docs[2]
        
        # 复制段落
        copy_paragraph_with_style(paragraph, target_doc)
        
        # 检查是否有表格需要插入
        # 这里简化处理，将表格按顺序分配
        if table_index < len(source_doc.tables):
            # 每个文档分配相应数量的表格
            if (para_index == split_points[1] - 1 and table_index < tables_per_file) or \
               (para_index == split_points[2] - 1 and table_index < tables_per_file * 2) or \
               (para_index == split_points[3] - 1):
                
                tables_to_add = min(tables_per_file, len(source_doc.tables) - table_index)
                if para_index >= split_points[2]:
                    tables_to_add = len(source_doc.tables) - table_index
                
                for _ in range(tables_to_add):
                    if table_index < len(source_doc.tables):
                        copy_table_to_doc(source_doc.tables[table_index], target_doc)
                        table_index += 1
    
    # 保存文档
    output_files = []
    for i, doc in enumerate(docs):
        filename = f"API_环境管理({i+1}).docx"
        filepath = Path(output_dir) / filename
        doc.save(str(filepath))
        output_files.append(str(filepath))
        
        # 统计信息
        para_count = len(doc.paragraphs)
        table_count = len(doc.tables)
        print(f"✅ 创建文件: {filename}")
        print(f"   段落数: {para_count}, 表格数: {table_count}")
    
    return output_files

def split_document_by_sections(source_doc, headings, output_dir):
    """根据章节标题切分文档"""
    if len(headings) < 3:
        print("⚠️  标题数量不足，使用内容均分方式")
        return split_document_by_content(source_doc, output_dir)
    
    # 找到合适的切分点
    total_headings = len(headings)
    split_indices = [
        0,
        total_headings // 3,
        (total_headings * 2) // 3,
        total_headings
    ]
    
    print(f"📊 按章节切分:")
    for i in range(3):
        start_idx = split_indices[i]
        end_idx = split_indices[i + 1]
        if start_idx < len(headings):
            start_heading = headings[start_idx]['text'][:30]
        else:
            start_heading = "文档末尾"
        
        if end_idx < len(headings):
            end_heading = headings[end_idx]['text'][:30]
        else:
            end_heading = "文档末尾"
        
        print(f"   文件{i+1}: {start_heading} -> {end_heading}")
    
    # 创建三个文档
    docs = []
    for i in range(3):
        doc = Document()
        setup_document_styles(doc)
        doc.add_heading(f'环境管理 API 文档 (第{i+1}部分)', 0)
        docs.append(doc)
    
    # 根据标题位置分配内容
    for para_index, paragraph in enumerate(source_doc.paragraphs):
        # 确定当前段落属于哪个部分
        section = 0
        for i, heading in enumerate(headings):
            if para_index >= heading['index']:
                if i < split_indices[1]:
                    section = 0
                elif i < split_indices[2]:
                    section = 1
                else:
                    section = 2
        
        # 复制段落到对应文档
        copy_paragraph_with_style(paragraph, docs[section])
    
    # 分配表格
    tables_per_section = len(source_doc.tables) // 3
    for i, table in enumerate(source_doc.tables):
        section = min(i // max(1, tables_per_section), 2)
        copy_table_to_doc(table, docs[section])
    
    # 保存文档
    output_files = []
    for i, doc in enumerate(docs):
        filename = f"API_环境管理({i+1}).docx"
        filepath = Path(output_dir) / filename
        doc.save(str(filepath))
        output_files.append(str(filepath))
        
        para_count = len(doc.paragraphs)
        table_count = len(doc.tables)
        print(f"✅ 创建文件: {filename}")
        print(f"   段落数: {para_count}, 表格数: {table_count}")
    
    return output_files

def split_api_environment_doc(docx_path, output_dir=None):
    """切分API环境管理文档"""
    if output_dir is None:
        output_dir = Path(docx_path).parent
    
    # 确保输出目录存在
    Path(output_dir).mkdir(exist_ok=True)
    
    # 分析文档结构
    source_doc, headings = analyze_document_structure(docx_path)
    if source_doc is None:
        return []
    
    print("=" * 60)
    print("开始切分文档...")
    print("=" * 60)
    
    # 根据文档结构选择切分方式
    if headings and len(headings) >= 6:
        output_files = split_document_by_sections(source_doc, headings, output_dir)
    else:
        output_files = split_document_by_content(source_doc, output_dir)
    
    print("=" * 60)
    print(f"🎉 切分完成! 生成了 {len(output_files)} 个文件")
    
    return output_files

if __name__ == "__main__":
    # 设置文件路径
    source_file = r"F:\augment\output\docx_files\API_环境管理.docx"
    output_directory = r"F:\augment\output\docx_files"
    
    print("📄 DOCX文件切分工具")
    print(f"📂 源文件: {source_file}")
    print(f"📂 输出目录: {output_directory}")
    print()
    
    # 检查源文件是否存在
    if not Path(source_file).exists():
        print(f"❌ 源文件不存在: {source_file}")
        exit(1)
    
    # 执行切分
    result_files = split_api_environment_doc(source_file, output_directory)
    
    if result_files:
        print("\n📋 生成的文件:")
        for file_path in result_files:
            file_size = Path(file_path).stat().st_size / 1024
            print(f"   📄 {Path(file_path).name} ({file_size:.1f} KB)")
    else:
        print("❌ 切分失败")
