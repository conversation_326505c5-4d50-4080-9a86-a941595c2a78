explorerswnd.xml  候鸟指纹浏览器平铺管理器

第三十三章

候鸟浏览器

产品窗体XML对照清单

依据传统，具体工作步骤、顺序安排，人员分配在工作群里具体阐述。

autoscript_export_mgr.xml 批量导出候鸟自动化脚本到本地

Localautoscript_import_mgr.xml 批量导入本地自动化脚本到候鸟

newautoscriptwnd.xml 新建自动化脚本

Scriptpass.xml 加密脚本/解密脚本

mainUrl.xml 设定环境起始页

Localpackage_import_mgr.xml 导入本地会话环境包到候鸟

package_export_mgr.xml 导出会话环境到本地

configpad.xml 环境编辑面板

copyitemswnd.xml 环境克隆面板

OwnerUI.xml 主面板

contact_list.xml 次面板(从主面板右上角排序按钮点击进入)

cookie_export_mgr.xml Cookie 导出

cookie_import_mgr.xml Cookie 导入

copyitemswnd.xml 批量创建/克隆会话环境

explorerswnd.xml  候鸟指纹浏览器平铺管理器

feedback.xml 提交产品使用意见

import_proxy.xml 代理服务器批量导入

login.xml 登录窗体

passinfo_mgr.xml 环境登录帐户管理器

plugin_mgr.xml 浏览器插件管理器

proxy.xml 添加新代理服务器（小窗体）

proxy_mgr.xml 网络代理服务器管理

ExpireWnd.xml 到期提示窗体

setting.xml 候鸟SETUP面板

plugin_mgr.xml 浏览器插件管理器

backup_mgr.xml 数据备份管理

autoscript_mgr.xml 自动化脚本管理器

backup_mgr.xml 数据备份管理

password_mgr.xml 网页登录帐户管理器

session_package_mgr.xml 会话数据包管理器

localimportwnd.xml 本地帐户批量导入环境管理器

messagecheck.xml 消息报警弹窗

message.xml 消息弹窗

message1.xml 消息弹窗1

useragent_mgr.xml  UserAgent 管理器

grouplist.xml 分组列表管理

sessionlist.xml 环境列表管理

localimportwnd.xml  本地帐户批量导入环境管理器

language.xml 多语言menu菜单

proxyip_mgr.xml 代理IP批量指定到环境

changepass.xml 修改帐户密码面板

renamewnd.xml 批量自定义环境名称

updatemessage.xml 在线升级

groupitemmenu.xml MENU_自动化API管理子菜单

menu_troy.xml MENU_右下角托盘菜单

sessionpluginsitemmenu.xml MENU_复制环境ID到剪帖板菜单

pluginsitemsitemmenu.xml MENU_复制插件ID到剪帖板菜单

uaitemmenu.xml MENU_我的本地UA库列表右键菜单

sessionlistitemverify.xml 环境版本验证列表控件

autoscriptsitemmenu.xml 脚本管理器列表右键菜单

proxyitemmenu.xml 代理服务器管理器列表右键菜单

tooltipplugins.xml 插件列表TIP窗口控件

DiskSpaceWnd.xml 磁盘空间不足弹出窗口

explorerswnd.xml候鸟指纹浏览器平铺管理器

sessionlistitem.xml 环境列表控件

sessionitemmenu2.xml 环境验证右键菜单

pluginsitemmenu.xml 插件列表右键菜单

desc.xml 修改环境注释窗口

messagecheck.xml 二次确认提示窗

Localautoscript_import_mgr.xml导入本地会话环境包到候鸟

backupitem.xml 备份管理器列表

MessageWnd.xml 标准弹窗

session_advandce.xml 环境高级配置窗

import_proxy.xml 代理服务器批量导入

group.xml 创建新分组

grouplistitem.xml 分组管理器列表

localpluginwnd.xml 本地自定义插件导入窗体

asexportfwnd.xml

autoscriptsitemsitemmenu.xml 已选中环境包含的脚本列表右键菜单

ExpireWnd.xml 环境过期弹窗

package_export_mgr.xml 导出会话环境到本地

Localpackage_import_mgr.xml 导入本地会话环境包到候鸟

loglist.xml 日志管理器

session_package_mgr.xml 会话数据包管理器

setmainurl.xml 批量自定义环境名称 ？？

comboitemproxytype.xml  CONFIGPAD的代理列表

updateloginwnd.xml 环境登录帐户管理器 – 添加新帐户

排序后：

asexportfwnd.xml

autoscript_export_mgr.xml 批量导出候鸟自动化脚本到本地

autoscript_mgr.xml 自动化脚本管理器

autoscriptsitem.xml

autoscriptsitem2.xml

autoscriptsitemmenu.xml 脚本管理器列表右键菜单

autoscriptsitemmenu2.xml

autoscriptsitemsitem.xml

autoscriptsitesitemmenu.xml 已选中环境包含的脚本列表右键菜单

*autoscript_import_mgr.xml 批量导入本地自动化脚本到候鸟

backup_mgr.xml 数据备份管理

backupitem.xml 备份管理器列表

Bubble_edit.xml

changepass.xml 修改帐户密码面板

CN.xml

comboboxitem.xml

comboboxitem2.xml

comboboxitemdel.xml

comboboxitemsel.xml

comboitemproxytype.xml  CONFIGPAD的代理列表

configpad.xml 环境编辑面板

contact_list.xml 次面板(从主面板右上角排序按钮点击进入)

contact_listn.xml

contact_plist.xml

cookie_export_mgr.xml Cookie 导出COOKIE

cookie_import_mgr.xml Cookie 导入COOKIE

cookieupdatewnd.xml

copyitemswnd.xml 环境克隆面板, 批量创建/克隆会话环境

Default.xml

desc.xml 修改环境注释窗口

DiskSpaceWnd.xml 磁盘空间不足弹出窗口

downloadMsg.xml

EN.xml

ExpirePackWnd.xml

ExpireWnd.xml 到期提示窗体, 环境过期弹窗

explorerswnd.xml 候鸟指纹浏览器平铺管理器

feedback.xml 提交产品使用意见

friend_list.xml

group.xml 创建新分组

groupitemmenu.xml MENU_自动化API管理子菜单

grouplist.xml 分组列表管理

grouplistitem.xml 分组管理器列表

hisveritem.xml

import_proxy.xml 代理服务器批量导入

importsessionitemmenu.xml

Item_Chrome_preview.xml

itemcookie.xml

itemimessage.xml

itemmenu.xml

language.xml 多语言menu菜单

listitemsel.xml

listRow.xml

Localautoscript_import_mgr.xml 批量导入本地自动化脚本到候鸟

localimportwnd.xml 本地帐户批量导入环境管理器

Localpackage_import_mgr.xml

localpluginwnd.xml 本地自定义插件导入窗体

login.xml 登录窗体

logitemmenu.xml

loglist.xml 日志管理器

loglistitem.xml

loglistitemlogin.xml

mainUrl.xml 设定环境起始页

menu_troy.xml MENU_右下角托盘菜单

menugroup.xml

menulogo.xml

message.xml 消息弹窗

message1.xml 消息弹窗1

message3.xml 消息弹窗3

messagecheck.xml 消息报警弹窗

messagel.xml

messageplugin.xml

messagesecurity.xml

MessageWnd.xml 标准弹窗

MsgList.xml

msgview.xml

NetBridgeWnd.xml

newautoscriptwnd.xml 新建自动化脚本

OwnerUI.xml 主面板

package_export_mgr.xml 导出会话环境到本地

panel.xml

passinfo_mgr.xml 环境登录帐户管理器

passinfoitemsitemmenu.xml

passinfositem.xml

passinfositemmenu.xml

passinfositemsitem.xml

passlistitem.xml

password_mgr.xml 网页登录帐户管理器

plugin_mgr.xml 浏览器插件管理器

pluginsitem.xml

pluginsitemmenu.xml 插件列表右键菜单

pluginsitemsitemmenu.xml MENU_复制插件ID到剪帖板菜单

pluginsitemsystem.xml

progress.xml

proxy.xml 添加新代理服务器（小窗体）

proxy_mgr.xml 网络代理服务器管理

proxyinfo.xml

proxyip_mgr.xml 代理IP批量指定到环境

proxyipitemmenu.xml

proxyipitemsitem.xml

proxyipitemsitemmenu.xml

proxyiplistitem.xml

proxyitemmenu.xml 代理服务器管理器列表右键菜单

proxylistitem.xml

renamewnd.xml 批量自定义环境名称

Scriptpass.xml 加密脚本/解密脚本

serveritem.xml

serverlist.xml

serverlistmenu.xml


================================================== 表格内容 ==================================================

参数名称 | 类型 | 必传 | 样例串/默认值 | 说明
Session_ID | array | 是 | 373808cb37bd63f5f7d92415e736e85f | 单个或多个环境ID
Orgin_Group_Name | string | 否 | 我的分组一 | 环境当前分组名称
Target_Group_Name | string | 是 | 我的分组二 | 环境转移到目标分组名称

{
"message "Session move Success",
"code": 0,
"data": [
{
          "Session_ID":"373808cb37bd63f5f7d92415e736e85f "
          "Orgin_Group_Name":"我的分组一"
          " Target_Group_Name ":"我的分组二"
    "status":"0"
},
{
          "Session_ID":"373808cb37bd63f5f7d92415e736e85E "
          "Orgin_Group_Name":"我的分组一"
          " Target_Group_Name ":"我的分组二"
    "status":"0"
}
]
}