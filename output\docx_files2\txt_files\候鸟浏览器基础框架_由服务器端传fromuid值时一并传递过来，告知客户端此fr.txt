由服务器端传fromuid值时一并传递过来，告知客户端此fromuid对应的帐户名是多少。

由服务器端传fromuid值时一并传递过来，告知客户端此fromuid对应的帐户名是多少。

客户端在原接受更新ITEM fromuid值时，加入更新from_account帐号信息的逻辑。

对于历史旧版没有FROM_ACCOUNT参数的XML，在fromuid值过来时进行判断参数是否存在，如不存在此参数名则进行加入此参数。

GROUP_ID ： 分组编号，默认为0，0表示默认分组。

之后新建的分组，GROUP_ID基于已存在的GROUP_ID最大值 进行 +1

分组列表显示顺序以分组编号为基础，顺序排列。

对于历史旧版没有GROUP_ID的XML，在用户修改item时触发判断是否存在此GROUP_ID参数，如不存在，则在XML节点中补充上此参数，默认值为0。

同时补充上GROUP_NAME，默认值为 ”default”。

GROUP_NAME ： 分组名称，默认值为： default , default表示默认分组名称。

用户未将ITEM加入到分组的集合，默认分组ID为0，分组名为：default。

用户将默认分组下的ITEM转移到某个分组里，则此ITEM的XML节点中，分组ID变更为目标分组的GROUP_ID，分组名变更为目标分组名。

单个ITEM或多个ITEM在分组间转移(批量转移)，XML节点GROUP信息需实时更新，同时需进行三大同步模块逻辑触发（FULL.ZIP同步）。

对于历史旧版没有GROUP_NAME的XML，在用户修改item时触发判断是否存在此GROUP_ID参数，如不存在，则在XML节点中补充上此参数，默认值为0。

用户行为：

新会话环境创建

新会话环境创建需默认在XML中加入GROUP_ID,GROUP_NAME参数。

GROUP_ID ：”0”  GROUP_NAME ：”default”

GROUP_ID =0 时，GROUP_NAME不允许用户修改分组名称。

XML节点时间更新：

ITEM进行分组间转移，需更新 UPDATETIME 节点值。

单ITEM ZI包存储约定：

ITEM.ZIP单ITEM包中不保存GROUP_ID，GROUP_NAME节点值，但要补充并保存FROM_ACCOUNT 参数值。

此处要注意：单ITEM包禁止保存GROUP_ID和GROUP_NAME 的非零值和非default值。

即：单ITEM包里的XML中，GROUP_ID永远为0，GROUP_NAME永远为default

GROUP分组信息存储于，且仅存在于xxxxxxxxxxxxxx_FULL.ZIP中。

单ITEM ZIP包导入导出约定：

ITEM导出后，XML内容中GROUP_ID和GROUP_NAME 为 0 和 default。

ITEM导入后，导入的ITEM集合 默认显示在无分组的 GROUP_ID = 0 的列表中。

分组界面与流程

程序启动后，在加载完成configdata.xml后，

依据configdata.xml中的GROUP_ID、GROUP_NAME值

默认在后台进行各分组数据统计，并保存到内存中。

主面板默认显示未分组环境所有数据。

（2）用户点击分组菜单，列出用户已有各分组项，每项后显示分组中有多少个环境。

用户点击菜单中的分组，在主面板的列表区显示分组下的所有环境。

（3）

新增：会话环境分组管理器 窗体

默认显示未分组环境，此时 删除分组，重命名分组按钮置灰不允许点击。

点击左上角下拉条，列出已创建的所有分组并显示分组中环境数量。

用户在左上角下拉条中选择不同的分组，需将删除分组，重命名分组按钮亮起。

用户在左上角下拉条中选择不同的分组，列表区域要显示分组下的所有环境列表。

删除分组 按钮点击后，程序将configdata.xml中此分组ID下的所有环境的GROUP_ID 置0，GROUP_NAME置 default。

重命名分组 按钮点击后，程序将configdata.xml中此分组ID下的所有环境的GROUP_NAME进行更名并存盘（要有同步到服务器动作）。

用户勾选列表中的环境后，右下角SELECT下拉条和按钮亮起，用户点击选中转移到指定分组。程序如果判断是转移到未分组环境，必须进行弹窗提示，用户点确认后才可以进行转移（二次确认）。

如果用户选中的右下角下拉条不是“未分组环境”，则直接进行转移。

转移逻辑为：程序将configdata.xml中的group_id 将用户选中的记录，更改为转移到目标group_id值，并将group_name进行更换。   更换完成后，需右下角弹MESSAGE窗体显示已转移完成数量。同时刷新当前列表窗体。

（4）

原： 会话环境管理器 更新如下：

原搜索关键字列出会话环境逻辑不变，仍旧全局搜索。

右侧增加分组环境下拉条控件。

允许用户通过下拉条显示不同分组下的会话环境。

（5）

主面板右键菜单调整：

原cookies三项，放到子菜单中。

原导入导出环境包，放到子菜单中。

增加 “转移此环境到..”项，用户点击此项，在子菜单中显示所有分组列表（此处可以不显示各分组的当前数字，也可显示）

用户点击某一个分组，将此环境转移到指定的分组中，数据逻辑变更同上。


================================================== 表格内容 ==================================================

参数名称 | 类型 | 必传 | 样例串/默认值 | 说明
Session_Name | string | 否 | 商业环境一 | 指定环境名称查询环境ID
Session_GroupName | string | 否 | 环境分组名称 | 指定环境分组名称查询环境ID
Session_CreateTime_Start | string | 否 | 2022-12-18 13:23:09 | 指定创建时间查询环境ID (start)
Session_CreateTime_End | string | 否 | 2022-12-19 13:23:09 | 指定创建时间查询环境ID (end)
Session_UpdateTime_Start | string | 否 | 2022-12-18 13:23:09 | 指定最近使用时间查询环境ID (start)
Session_UpdateTime_End | string | 否 | 2022-12-19 13:23:09 | 指定环境最近使用时间查询环境ID (end)
Session_Recv | bit | 否 | 1 | 指定返回我收到的分享环境ID
Session_Recv_Account | string | 否 | <EMAIL> | 返回我收到指定帐户分享的环境ID
[生效前题: Session_Recv必须为1]
Session_Sent | bit | 否 | 1 | 指定返回我分享的环境ID
Session_Sent_Account | string | 否 | <EMAIL> | 返回我分享给指定帐户的环境ID
[生效前题: Session_Send必须为1]
Session_User_Agent | string | 否 | Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 | 返回符合指定UA值的所有环境ID
[2023/07/03新增]
Proxy_Ip | string | 否 | ************* | 指定代理IP返回环境ID
Proxy_Type | array | 否 | HTTP,HTTPS,SSH,SOCKS4,SOCKS4A,SOCKS5,
Oxylabsauto,Lumauto,Luminati_HTTP,
Luminati_HTTPS,smartproxy,noproxy | 指定代理类型返回环境ID
[无代理:noproxy]
Comment | string | 否 | 环境备注信息 | 指定返回符合备注关键字环境ID
Session_ISWebAccount | bit | 否 | 1 | 指定返回所有存在网页登录帐户环境ID
Session_WebAccounts | array | 否 | <EMAIL> | 返回存在指定网页登录帐户环境ID
[生效前题: Session_ISWebAccount必须为1]
Session_ISPlugins | bit | 否 | 1 | 返回所有存在浏览器插件的环境ID
Session_Plugins_id | array | 否 | Aknhicdgfejabmkpjlmnkmakhlmedfga, agedeoibceidbaeajbehgiejlekicbfd | 指定插件ID返回所有环境ID
[生效前题: Session_ISPlugins 必须为1]
Session_ISAutoScript | bit | 否 | 1 | 指定返回所有存在脚本帐户环境ID
Session_AutoScript_id | array | 否 | 17c70e014d61b1fa43d3638ca5a1bc25, 962a35b816655312db7ddf4d0807829a | 返回存在指定脚本ID的环境ID
[生效前题: Session_ISAutoScript 必须为1]
CurrentPage | int | 否 | 1 | 分页/页数值
ListNum | int | 否 | 50 | 分页条数，一次最多返回500个环境ID
注：如未给任何参数，则系统默认按UpdateTime最近使用时间，倒序返回最近使用不超过50条环境ID。 | 注：如未给任何参数，则系统默认按UpdateTime最近使用时间，倒序返回最近使用不超过50条环境ID。 | 注：如未给任何参数，则系统默认按UpdateTime最近使用时间，倒序返回最近使用不超过50条环境ID。 | 注：如未给任何参数，则系统默认按UpdateTime最近使用时间，倒序返回最近使用不超过50条环境ID。 | 注：如未给任何参数，则系统默认按UpdateTime最近使用时间，倒序返回最近使用不超过50条环境ID。

{"message": "Success",
"code": 0,
"data": {
       "listid": [
            {
                "373808cb37bd63f5f7d92415e736e85f", 	//符合条件的环境id
                "705cc4c139e69b729a2fd277f30e1863"    //符合条件的环境id
            },

        ],
        "total": 2
    }