<?xml version="1.0" encoding="UTF-8"?>
<Window size="540,350" trans="true"  caption="0,0,0,30" roundcorner="2,2,2,2" showshadow="true" shadowsize="3" shadowposition="0,0" shadowcolor="#ff6272A4" fademode="true">
  <Include source="default.xml" />
    <Default name="Button" />
  <VerticalLayout bkcolor="#FF282A36">
    <HorizontalLayout height="26">
    	<Button bkimage="icon21px.png" padding="6,6,0,0" width="27" height="21"/>
      <Control />
      <Button name="closebtn" width="28" height="26"  tooltip="关闭" normalimage="closebtn.png" hotimage="closebtn_hover.png" pushedimage="closebtnpush.png" />
    </HorizontalLayout>
		<VerticalLayout>
			<HorizontalLayout height="220" inset="0,20,0,0">
				 <Control width="10"/>
				    <!--<RichEdit name="items" float="center" align="center" textpadding="8,0,0,0" textcolor="#ff000000"/>-->
        <RichEdit name="items" vscrollbar="true" bordersize="1" bordercolor="#ffDDDDDD" tipvaluecolor="#FF333333" borderround="3,3" bkcolor="#fffffff3" font="4" textpadding="12,8,20,0" multiline="true" textcolor="#ff333333" rich="false" readonly="true" mouse="false" transparent="false" />
        <Control width="10"/>
			</HorizontalLayout>
      <HorizontalLayout height="10" ></HorizontalLayout>
			<Label name="msessage" textcolor="ff000000" align="center" valign="center" font="8" />
	       <HorizontalLayout childpadding="2" inset="0,10,0,0">
           <Control />
				      <Button name="okbtn" bkcolor="#ffffffff" bordercolor="#ffcccccc" focusbordercolor="#ffaaaaaa" bordersize="1" borderround="2,2"  padding="0,4,0,0" float="left" font="0" width="120" height="26" text="确定"/>
           <Control />
			   </HorizontalLayout>
		</VerticalLayout>
  </VerticalLayout>
</Window>