说明：此表为官方插件库主表，记录所有用户通过客户端成功上传的插件。

说明：此表为官方插件库主表，记录所有用户通过客户端成功上传的插件。

条件为：根据 Plugins_id与 Plugins_version 保证唯一。

即当客户端通过插件管理器 或 通过GOOGLE市场安装的插件进行安装新插件后，当客户端提交上传相同版本号和相同ID的数据包时，在此表中只允许有一条记录、服务器目录中仅允许存放一份。

表二：（候鸟用户自有插件集合，用户自有库）表名： USER_PLUGINS

字段：

[主键]ID									主键索引

UID					(varchar)          	用户UID

Plugins_name    	(varchar)          	插件名称

Plugins_version   	(varchar)			插件版本

Plugins_size      	(int)					插件包大小使用字节数记录

Plugins_id       	(varchar)			插件ID，GOOGLE市场内插件ID

Plugins_dec			(varchar)			插件说明，默认为空

Plugins_md5     	(varchar)			插件包MD5,由客户端提供

Plugins_filename  	(varchar)			插件存储在服务器上文件名称

Create_Time 		(datetime)			插件插入表中时间，字段属性默认填值

IS_VALID   			(bit)					是否有效，默认为1（有效）

注：此表为用户自有插件库表，记录用户已使用的所有插件。（A区列表）

条件为：根据 Plugins_id与 Plugins_version 保证唯一。

即当客户端通过插件管理器 或 通过GOOGLE市场安装的插件进行安装新插件后，客户端 提交上传 插件的ID 与版本号 和插件目录的数据压缩包到服务器时，根据 Plugins_id与 Plugins_version 保证唯一，基于UID为条件 在此表中只允许有一条记录。

八、客户端与服务器端 流程说明：

【显示列表，新增插件】

1、用户自有插件列表显示 与 客户端获取流程：

插件管理器在打开后，使用线程请求服务器API/GET_USER_PLUGINS 接口

请求参数：UID、TOKEN

2、服务器端通过此接口读取USER_PLUGINS表中 UID所有记录并通过JSON格式进行返回给客户端。

客户端收到列表数据，显示在插件管理器的A区中。

2、用户安装 新插件 显示与提交到服务器 流程：

用户通过浏览器进入GOOGLE市场 安装新插件，或通过 使用官方所有浏览器插件 直接安装插件时。

【客户端部份】通过浏览器进入GOOGLE市场安装新插件

-> 客户端在用户成功安装完成插件后（用户关闭浏览器窗口时，进行插件包上传和提交到服务器端 触发）

-> 客户端将插件目录进行打包，打包成功后，请求服务器API/UPLOAD_PLUGINS 接口，将插件ID、SIZE、MD5、用户UID、插件文件包、插件名称、插件版本 提交给服务器。

-> 客户端将item.zip中的configdata.xml中plugins节点进行更新

客户端对此环境进行打包，获取服务器端版本号，增加版本号，更新上传此环境包到服务器。

【服务器端部份】通过浏览器进入GOOGLE市场安装新插件

服务器接收到API/UPLOAD_PLUGINS 接口，客户端上传上来的所有插件包数据。

判断是否存在于表一和表二中，如果不存在，则进行入库，数据包存储到 PLUGINS目录处理。

返回 成功处理状态报告给客户端。

【客户端部份】通过使用官方所有浏览器插件库 安装新插件

用户点击 使用官方所有浏览器插件 下拉菜单项， 客户端使用线程请求服务器API/Get_PLUGINS 接口

请求参数：UID、TOKEN

客户端插件面板上A区列表显示所有官方插件列表、版本号、创建时间

用户选择其中一个或多个插件，并在B区勾选需要安装的环境（此时在C区用户看到勾选的环境已安装了哪些插件）

用户点击右下角按钮：将选中的插件安装到勾选的环境中

此时，客户端请求服务器端接口 API/GET_PLUGINS_DOWNLOAD 发送参数：用户UID、插件主表记录ID、TOKEN。

获得需要下载到本地并安装的插件包URL地址。

客户端完成插件安装。

插件安装成功后，请求服务器API/UPLOAD_USER_PLUGINS 接口，将插件ID、SIZE、MD5、用户UID、插件名称、插件版本 提交给服务器。

客户端将已完成安装的环境下item.zip中的configdata.xml中plugins节点进行更新

客户端对此环境进行打包，获取服务器端版本号，增加版本号，更新上传此环境包到服务器。

【服务器端部份】通过使用官方所有浏览器插件库 安装新插件

服务器端通过 API/Get_PLUGINS 接口 收到客户端请求参数：UID,TOKEN。

服务器端 返回JSON格式所有插件数据：表记录ID、插件ID、SIZE、MD5、用户UID、插件文件包、插件名称、插件版本。
-------------------------------------------------------------------------------------------------

服务器端通过 API/GET_PLUGINS_DOWNLOAD 接口 收到客户端请求参数：用户UID、插件主表记录ID、TOKEN。

服务器端通过 主表记录ID，查找主表：PLUGINS，取出对应的记录，组合成下载地址，插件ID、SIZE、MD5、插件文件包、插件名称、插件版本 通过JSON返回给客户端。

-------------------------------------------------------------------------------------------------

服务器端通过API/UPLOAD_USER_PLUGINS 接口，收到客户端请求参数：TOKEN、插件ID、SIZE、MD5、用户UID、插件名称、插件版本（没有文件包）

服务器端根据插件ID和VER版本号，用户UID，判断是否存在于表二中，如果不存在，则进行入库。

九、在新机器上运行已有历史环境的客户端，插件的逻辑描述：

运行环境A -> 获得环境A目录下的configdata.xml中的插件列表，判断环境目录中是否存在这些插件包。 如果不存在 -> 判断用户本地目录：/plugins/目录里是否存在此插件  ->  如果不存在，下载服务器上此插件包，存放到用户本地的/plugins/目录里。

     -> 将用户本地的/plugins/目录里 插件包解压缩到 环境A目录下。

综述：当本地的某个ITEM没有这个插件目录时，先从本地的/plugins/里拿，如果本地没有，从服务器下载到本地/plugins里。再从本地的plugins里拿。

注：不从首个安装过此插件的ITEM下的目录里找插件数据源。

考虑到客服工作和用户对插件数据的本地需求层面，即：用户如果需要在本地找对应的插件包，客服直接让用户去plugins目录里拿就行。

十、增加 Secure Preferences 文件到 chrome目录中，并提供同步。

如图：

插件包同步到本地，只需要把插件目录下载复制到对应目录中

十一、插件列表右键菜单支持用户访问插件所在的GOOGLE商店地址，了解插件的具体内容、最新版本、用户评价等根信息。

插件列表右键菜单访问地址为：

https://chrome.google.com/webstore/search/   + 插件的ID

此URL打开时 调用为系统默认的浏览器。

十二、【删除插件】

当A区列表显示的是官方提供的插件包时，下方的删除插件和清除插件按钮置灰，处于不可点击状态。  当C区列表显示为空的时侯，下方的 删除勾选的插件 按钮置灰，处于不可点击状态。

附：

同一个插件被反复安装的问题【通过放在MBDATA/UID/PLUGIN目录可避免反复下载增加服务器无意义的负荷】

插件配置数据泄漏与插件同时安装到其它环境中后的配置信息丢失的问题。【无此问题】

窗口拖动大小，窗口内控件是否能跟随变大小的问题。【无此问题】

如果用户需要查看某个插件已安装到了哪些环境下的需求如何满足。【暂时不满足用户此需求】

[2023-07-21新增]

导入自定义插件

前述：

因部份客户有能力自行编写CHROMIUM插件，同时因其个人原因插件无法通过GOOGLE官方审核的部份，候鸟平台在新版本中给予支持（合理合法合规）。

此功能新增支持将有力带来进一步与同类产品的差异化，提升候鸟市场竞争力，提高用户使用候鸟粘性。

【图1】

简述：

一、本地私有性

为保证客户业务隐私，用户导入的自定义插件不与服务器进行交互，所有数据仅保存在本地，不存储在服务器中。

二、单独分类：

如上图所示，用户导入的插件单独归为一栏：我的自定义插件，此栏仅存放用户导入的插件，包括用户下载GOOGLE的插件进行导入，即凡是通过导入方式进入到候鸟体系的插件，均约定为我的自定义插件。后面客户说明文档中将加入自定义插件与常规插件的区别定义。

三、导入自定义插件入口及数据格式存储约定

（一）如上图所示，用户点击 导入自定义插件后，弹窗提示用户输入【图2】

1、插件ID  
（**重要约定：待导入插件ID不能与已有官方库的插件ID相同，如上图，相同将会导致数据混乱。因此在用户点击导入时，要验证插件ID与内存中所有插件ID是否存在冲突或提交服务器指定API接口来验证均可。）

2、插件名称  （由用户填写，必填 非空）

3、插件版本  （由用户填写，必须和插件包版本一致，必填 非空）
4、插件描述  （由用户填写，可选）
5、插件路径  （由用户选择本地文件，必选 非空）

（二）

  a、插件数据格式与原有插件本地默认存储格式相同，需要把用户加密的CRX包（无法解压缩的情况考虑进来），存储位置发（上图）可不作变化。仍旧以目录方式保存。

【图3】

b、在图3中，增加一个xml: Plugins_local.xml

用来存储用户导入的自定义插件内容。
（与服务器无交互）

  格式如下：


此内容参见plugins.xml，filepath可填入用户选择的本地插件路径。

此XML供【图1】显示我的自定义插件列表用途。

（三）原插件批量指派到环境逻辑不变，与原约定一致。即原约定当本地已存储插件数据包，则不再从服务器端获取。自定义插件导入完成需要将文件保存在正确的位置使之有效。
      同理，插件从环境中删除逻辑不变。

（四） 导入自定义插件 按钮 增加TIP内容 ：导入自编写的插件包（支持未通过Google扩展商店审核的自编写插件、开发者模式插件）

【官网导入自定义插件 完整指导教程】【20230803新增】


================================================== 表格内容 ==================================================

参数名称 | 类型 | 必传 | 样例串/默认值 | 说明
Proxy_Type | string | 是 | HTTP,HTTPS,SSH,SOCKS4,SOCKS4A,SOCKS5,
Oxylabsauto,Lumauto,Luminati_HTTP,
Luminati_HTTPS,smartproxy,noproxy | 指定代理类型
[无代理:noproxy]
Proxy_Ip | string | 是 | ************* | 指定代理IP
Proxy_Port | string | 是 | 8080 | 指定代理端口
Proxy_Username | string | 否 | admin | 指定代理帐户
Proxy_Password | string | 否 | Password | 指定代理密码
Is_CheckProxy | bit | 否 | 1 | 是否自动检测代理【默认:1】
TimeZone | string | 否 | US/Alaska -09:000 | 时区
CountryCode | string | 否 | US | 国家CODE

{
"message": "Add Proxy Server Success",
"code": 0
}