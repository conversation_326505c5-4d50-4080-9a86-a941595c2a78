标题: 浏览器插件管理器使用
英文标题: Browser Plugin Manager
ID: 111
分类ID: 7
添加时间: 1654495973
更新时间: 1695795935
访问次数: 0
SEO标题: 候鸟浏览器插件管理器使用
SEO关键词: 候鸟浏览器插件管理器使用
SEO描述: 候鸟浏览器插件管理器使用

================================================== 内容 ==================================================
# 候鸟 浏览器插件管理器 使用说明书

候鸟浏览器插件管理器为首款支持一键批量安装管理浏览器环境插件扩展
候鸟支持无限制数量级浏览器插件一键安装到任意环境中。所有环境的插件数量、状态一目了然，支持环境与插件包的分享，导入与导出。安装多个插件到100个环境中仅需要数秒即可完成，彻底摒除以往繁杂的插件安装，维护等业务日常管理。

### 候鸟 浏览器插件管理器 使用前述
基于Google chrome浏览器研发的候鸟浏览器内核， 开启全面支持插件扩展服务。用户可通过GOOGLE市场，下载插件直接安装到候鸟浏览器中，也可以直接在候鸟插件管理器自带的官方插件库（插件库所有插件包原生态来源于GOOGLE市场），支持候鸟客户利用全球数万款强大的浏览器插件开展各类合法合规的商业运营服务。


下面通过图文模式，详细描述候鸟插件管理器的使用方法：


### 一、开启并进入 浏览器插件管理器

启动候鸟客户端，点击主面板左上角的功能菜单，选择浏览器插件管理器并点击进入浏览器插件管理器。

方式一：

<p><img src="6d7b2882624511f09a0d0242ac130006/images/image_b326b833c9ea.png" width="360" /></p>

方式二：
通过在环境上点击右键唤出菜单，点击 环境插件管理 进入

<p><img src="6d7b2882624511f09a0d0242ac130006/images/image_c6b96f5f72af.png" width="360" /></p>

方式一和方式二的区别： 方式二进入环境插件管理器时，默认焦点指向到 环境二的插件管理。

#### 浏览器插件管理器界面：

![](6d7b2882624511f09a0d0242ac130006/images/image_a72873741d09.png)

功能区域分布图：

![](6d7b2882624511f09a0d0242ac130006/images/image_52a22c93d294.png)

### 二、插件管理器界面功能说明

在插件管理器中，您可以为您的环境一键添加、删除、查找、分配各种插件。支持单个插件的安装，也支持批量一次性插件安装。

![](6d7b2882624511f09a0d0242ac130006/images/image_0addb1022e12.png)
#### 如图示，A区：
#### 下拉菜单项：
【已安装过的浏览器插件】
列出您已经安装过的所有插件的总集合，即您在历史中曾经安装过哪些插件，当前您的所有环境中已安装过的插件，都会在这个列表里显示出来。如果您删除此列表中的插件，候鸟产品会将会将此插件从您本地硬盘插件目录中删除，但不会删除您的环境中此插件。

【使用官方所有浏览器插件】
列出了候鸟官方提供的所有谷歌市场插件，这些插件来源于GOOGLE市场，优点在于您无需进入GOOGLE市场即可通过此列表一键安装到您的环境中。
如果您在此列表中未找到需要的插件，您仍需要进入谷歌插件市场，在候鸟浏览器页面中下载插件并进行常规安装。

#### 请输入关键字查找插件：
插件管理器支持您动态实时查找需要的插件，无须在列表中逐个人工检索，通过查找插件功能，可快速定位到您需要的插件上并进行安装。

![](6d7b2882624511f09a0d0242ac130006/images/image_c481416a5530.png)

A区 列表：
  操作列：勾选框 在勾选后，即可进行后续操作。
  插件名称：列出插件的正式名称，正式名称与GOOGLE市场的插件名称完全一致。
  插件版本：列出官方可以提供下载安装的所有版本。
  描述：商业插件官方描述，由插件作者提供，**通过将光标移到描述上可以快速查看描述的完整内容而无须拖动插件管理器窗体大小**。

#### 如图示，B区：

B区主要功能是列出您的所有环境，并提供已安装过插件的环境集合，并提供环境搜索功能，方便您定位到需要处理的环境。提供每个环境已安装的插件数量，让您很直观的进行环境的插件分配与管理。
![](6d7b2882624511f09a0d0242ac130006/images/image_67b96d6d35a9.png)

【已安装插件的环境】
 如果您曾经在某些环境中安装过插件，则进入插件管理器，此项默认显示。默认列出已安装插件的环境集合。

【其它】
列出未分组的环境和已分组的环境分组。  

【请输入关键字查找会话】
输入您的会话环境关键字，可快速列出您需要操作的环境。

![](6d7b2882624511f09a0d0242ac130006/images/image_92686e1d7c34.png)

B区列表：
操作列：勾选框 在勾选后，即可进行后续操作。 
环境名称：列出您此分组下环境的名称。
创建时间：列出您的环境的创建时间。
插件数：显示您的环境中已安装的插件数量。

#### 如图示，C区：

C 区主要功能是列出您的某个环境中已安装的插件详单，并提供此环境下插件的管理。默认在未选择环境时，C区插件列表为空。仅在您勾选或点击某个环境时，或您在勾选多个环境时，列出环境的插件详单。

操作重点：
点击B区环境 和 勾选B区环境之区别：
![](6d7b2882624511f09a0d0242ac130006/images/image_396a7b83c601.png)

当您 仅 点击（选中）B区某个环境，C区只会列出此单个环境下的所有插件详单。如果您同时点击了B区多个环境，C区也仅会列出最后点击的那单个环境的所有插件。

![](6d7b2882624511f09a0d0242ac130006/images/image_8ce95b9c8dde.png)

当您 勾选（在操作列勾选了小方框）某个环境，C区会列出已勾选
的此环境所有插件详单。 如果您同时勾选了B区多个环境，C区会列出这些多个环境中的所有插件详单。

### 三、安装新插件

![](6d7b2882624511f09a0d0242ac130006/images/image_0f22968e0ddd.png)

安装插件的过程，是从A区到B区再到C区，再点击插件管理器右下角按钮的过程。

安装方式一：
通过【**候鸟官方插件库**】来安装插件到我的环境中：

![](6d7b2882624511f09a0d0242ac130006/images/image_e8636ebdb24d.png)
 第一步：在A区 选择 使用官方所有浏览器插件 下拉项。

![](6d7b2882624511f09a0d0242ac130006/images/image_9b26e9ea990d.png)

第二步：在A区列表中，找到自已需要安装的插件，并勾选此插件。

![](6d7b2882624511f09a0d0242ac130006/images/image_e82784dbc024.png)

第三步：在B区列表中，勾选要将此插件安装到的那些环境上。
![](6d7b2882624511f09a0d0242ac130006/images/image_7c22b011e378.png)

第四步：点击按钮 将选中的插件安装到勾选的环境中。

![](6d7b2882624511f09a0d0242ac130006/images/image_782e47c108ec.png)


  如上图所示，
  此时表示此插件已成功安装到 2个环境中，此时您可以在B区和C区看到 环境三和环境四的插件数由0变为了1。表示已成功安装。同时在C区可以看到环境三和环境四的插件列表中已出现刚刚安装的插件。

![](6d7b2882624511f09a0d0242ac130006/images/image_c3e216d8869a.png)

点击 已安装过的浏览器插件，您可以看到，刚刚从官方插件库中下载安装的插件已分别保存到了您的本地硬盘插件目录和环境目录中。

### 四、分配多个插件到各个环境中

通过批量分配多个插件到多个环境中，可以实现一键批量将插件安装到您指定的环境中，而且支持您在异地无须再次安装插件使用，真正做到了一次分配（安装），在任何地点随心使用。

![](6d7b2882624511f09a0d0242ac130006/images/image_91033f1eb7fc.png)

通过【**已安装过的浏览器插件**】列表进行分配：

如图，选择A区 已安装过的浏览器插件 下拉项，您可以直接将本地的插件指派到B区指定的环境中，如果勾选的插件在本地候鸟目录中存在，则这是一个本地全自动安装的过程，如果勾选的插件在您的本地硬盘中不存在，则会自动从官方插件库下载，并再次保存到您的本地硬盘中，之后会自动再进行指定的环境中插件安装。
批量分配（安装）完成后，成功分配 (安装) 的插件会显示在C区列表中。

注：如已安装过相同的插件到环境中，则再次安装不会影响原已安装的插件使用。

![](6d7b2882624511f09a0d0242ac130006/images/image_e95cf504bddf.png)

通过【使用官方所有浏览器插件】列表进行分配：

如图，选择A区 使用官方所有浏览器插件 下拉项，您可以直接将官方插件库的插件指派到B区指定的环境中，如果勾选的插件在本地候鸟目录中存在，则这是一个本地全自动安装的过程，如果勾选的插件在您的本地硬盘中不存在，则会自动从官方插件库下载，并再次保存到您的本地硬盘中，之后会自动再进行指定的环境中插件安装。
批量分配（安装）完成后，成功分配 (安装) 的插件会显示在C区列表中。

### 五、删除指定环境插件

当您发现某些 商业环境不再需要使用某些插件，您可以对指定的环境进行插件删除操作。
注：您无法对官方插件库提供的插件进行删除，但您可以对自有已安装的插件进行删除。

![](6d7b2882624511f09a0d0242ac130006/images/image_2a25c9125e87.png)

A区 本地插件目录中指定插件进行删除：
通过在A区，选择 已安装过的浏览器插件，勾选要删除的插件，点击删除插件。候鸟插件管理器 仅会删除您的硬盘中此插件的插件目录中的此插件数据副本。 并不会删除您的浏览器环境中的此插件数据存根。因此，您对A区列表的插件进行删除，并不会影响到您环境中的插件和插件数据。您此时删除的仅仅为本地硬盘中的曾经下载过的插件副本。
也就是说，您虽然在A区列表中看不到此插件了，但是在环境的插件列表中（C区列表），仍旧可以看到此插件。

那么，如果我想彻底删除环境下的指定插件，该怎么操作呢？

![](6d7b2882624511f09a0d0242ac130006/images/image_ad84140bef80.png)


如图：在B区勾选环境八，可以看到C区已列出此环境下所有插件。 勾选其中单个或多个插件。点击删除插件。将会立即彻底删除此环境下指定的插件。
导致的结果如下：
1、此方式删除插件后此环境将不再拥有此插件带来的所有功能。
2、您在异地使用产品，此环境也不再看到此插件和此插件带来的所有功能。
3、如果需要再次使用此插件，您必须通过在A区进行重新安装此插件过程。

### 六、批量删除环境中的指定插件

![](6d7b2882624511f09a0d0242ac130006/images/image_30bd94dada38.png)

候鸟浏览器插件管理器 支持您一键批量删除多个环境中，指定的多个插件。

操作方法：
通过在B区选择 已安装插件的环境 下拉列表项， 勾选您需要删除的环境范围（通过观察C区列表和B区列表的环境插件数）。之后，在C区勾选需要删除的插件。点击删除插件，即可一次性将多个环境中多个插件一次性彻底删除。

导致的结果如下：
1、此方式删除插件后此环境将不再拥有此插件带来的所有功能。
2、您在异地使用产品，此环境也不再看到此插件和此插件带来的所有功能。
3、如果需要再次使用此插件，您必须通过在A区进行重新安装此插件过程。

### 七、异地使用已安装过插件的环境

前述：
候鸟浏览器产品支持首次插件安装到环境后，异地无缝使用此环境（无须二次安装插件），支持多用户协同合作环境分享模式下无缝使用（无须二次安装插件）。 支持环境本地导出后，交付给第三方帐户使用无缝使用此环境（无须二次安装插件）。

当首次将插件安装到此环境后，异地使用：
如图：

<p><img src="6d7b2882624511f09a0d0242ac130006/images/image_29d29fd5594e.png" width="360" /></p>

在异地首次使用（点击运行）时，候鸟浏览器产品会检测并自动下载并安装此环境中所有需要的商业插件，并显示整个下载安装过程，此时您的机械硬盘会有明显的数据写入过程（建议使用NVME盘或SSD硬盘来保证浏览器的高效运行。）
在进行完成上述动作后，此环境对应的指纹浏览器将会第一时间打开，在浏览器的右上角会显示已安装的插件，如图。

![](6d7b2882624511f09a0d0242ac130006/images/image_1c428cad7f9d.png)

![](6d7b2882624511f09a0d0242ac130006/images/image_6b5729129e57.png)

注：部份GOOGLE浏览器插件会有此默认功能：Let Chromium run in background。 表示默认将此浏览器窗口在后台运行。为保证客户业务稳定运营，此时在候鸟客户端点击

![](6d7b2882624511f09a0d0242ac130006/images/image_381669974ee3.png)

停止将不会产生效力。需客户自行修改插件设置或在屏幕右下角tray托盘区，手动关闭此环境之浏览器窗体。

================================================== 英文内容 ==================================================
# Mbbrowser Browser Plugin Manager Instruction Manual

The first version of the Mbbrowser Plugin Manager supports one-click batch installation and management of browser environment plug-in extensions
Mbbrowser supports unlimited order of magnitude browser plug-ins to be installed in any environment with one click. The number and status of plug-ins in all environments are clear, and the environment and plug-in package sharing, import and export are supported. It only takes a few seconds to install multiple plug-ins into 100 environments, completely eliminating the usual complicated business management such as plug-in installation and maintenance.

### Mbbrowser Browser Plugin Manager Use The Foregoing
The Mbbrowser kernel developed based on Google chrome opens the plug-in extension service. Users can download the plug-in and install it directly into the Mbbrowser through GOOGLE Market, or directly in the official plug-in library of the Mbbrowser plugin manager (all plug-in packages of the plug-in library are originally from the GOOGLE market), supporting the Migratory Bird customers to carry out various legal and compliant business operation services with tens of thousands of powerful browser plug-ins around the world.


The following is a detailed description of the use of the mbbrowser plugin manager through the graphic mode:


### I、Open and go to the Browser Plugin Manager

Start the Mbbrowser client, click the function menu in the upper left corner of the main panel, select Browser Plugin Manager and click to enter the Browser Plugin Manager.

Method 1:

<p><img src="6d7b2882624511f09a0d0242ac130006/images/image_3e54e50ba193.png" width="360" /></p>

Method 2:
Right-click on the environment to call up the menu, click Plugin management to enter

<p><img src="6d7b2882624511f09a0d0242ac130006/images/image_a52407ad2502.png" width="360" /></p>

Differences between Methods 1 and 2: When Method 2 enters the environment plugin manager, the default focus is on plugin management in Environment 2.

#### Browser plugin manager interface:

![](6d7b2882624511f09a0d0242ac130006/images/image_aaa1de53d4b6.png)

Functional area distribution map:

![](6d7b2882624511f09a0d0242ac130006/images/image_ebabb2854eec.png)

### II、Function description of the Plugin Manager interface

In the Plugin Manager, you can add, remove, find, and assign various plug-ins to your environment with one click. You can install single plug-ins or batch plug-ins at a time.

![](6d7b2882624511f09a0d0242ac130006/images/image_5b71ddbdd778.png)
#### As shown below, Area A:
#### Drop-down menu items:
【Installed browser plug-ins】
Lists the total collection of plug-ins you have installed, that is, which plug-ins you have installed in your history. Plug-ins currently installed in all of your environments will be displayed in this list. If you delete a plug-in from this list, Migratory Bird Products will remove the plug-in from your local hard drive plug-in directory, but not from your environment.

【Official browser plug-ins】
This list lists all the GOOGLE Market plug-ins officially provided by Migratory Bird. These plug-ins are derived from the GOOGLE Marketplace and can be installed into your environment with one click without entering the Google Marketplace.
If you do not find the plug-in you want in this list, you will still need to go to the Google Plug-in Marketplace, download the plug-in from the Migratory Bird Browser page and perform a regular installation.

#### Please enter the keyword to find the plug-in:
Plug-in manager allows you to dynamically search for plug-ins in real time without manually searching one by one in the list. The plug-in search function enables you to quickly locate the plug-ins you need and install them.

![](6d7b2882624511f09a0d0242ac130006/images/image_1414bed7320d.png)

List of Area A:
  Operation column: Check box After you select this box, you can perform subsequent operations.
  Plugin name: Lists the official name of the plug-in. The official name is the same as the name of the plug-in in the GOOGLE Marketplace.
  Versions: Lists all versions officially available for download and installation.
  Description: Official description of the commercial plug-in, provided by the author of the plug-in. **Quickly view the full content of the description by moving the cursor over the description without dragging the plug-in manager form size**.

#### As shown below, Area B:

The main function of Section B is to list all of your environments, provide a collection of environments where plug-ins have been installed, and provide an environment search function so you can locate the environment you need to work with. Provides the number of installed plug-ins for each environment, allowing you to easily allocate and manage plug-ins for your environment.
![](6d7b2882624511f09a0d0242ac130006/images/image_74c70774123e.png)

【Session plug-ins installed】
If you have installed plug-ins in some environment, you go to the plug-in Manager, which is displayed by default. The collection of environments where plug-ins are installed is listed by default.

【Other】
Lists the ungrouped and grouped environment groups.

【Please enter the keyword to find the session】
Enter your session environment keyword to quickly list the environments you need to operate in.

![](6d7b2882624511f09a0d0242ac130006/images/image_652c605f0814.png)

Section B list:
Operation column: Check box After you select this box, you can perform subsequent operations.
Session Name: Lists the name of the environment under your group.
Creation time: Lists the creation time of your environment.
Num: Displays the number of plug-ins installed in your environment.

#### As shown below, Area C:

The main function of Area C is to list the installed plug-ins in your environment and provide management of the plug-ins in that environment. By default, when no environment is selected, the plug-in list in area C is empty. Lists the plug-in list of the environment only when you check or click on an environment, or when you check multiple environments.

Operation focus:
Difference between clicking Zone B environment and checking Zone B environment:
![](6d7b2882624511f09a0d0242ac130006/images/image_5183fc5563c2.png)

When you only click (select) an environment in section B, section C will only list all plug-ins in that single environment. If you click on multiple environments in section B, section C will only list all the plug-ins for the last environment clicked.

![](6d7b2882624511f09a0d0242ac130006/images/image_8d21b073bfbe.png)

When you check an environment (the little box is checked in the action column), area C lists the checked environment
List of all plug-ins for this environment. If you check multiple environments in section B, section C will list all plug-ins in those environments.

### III、Install new plug-ins

![](6d7b2882624511f09a0d0242ac130006/images/image_9f4cddb385c0.png)

The process of installing plug-ins is from zone A to Zone B to Zone C, and then click the button at the lower right corner of the plug-in manager.

Installation method 1:
To install plugins into my environment through【**Official browser plug-ins**】：

![](6d7b2882624511f09a0d0242ac130006/images/image_1ebcbfd62136.png)

Step 1: In Section A, select the official all browser plug-ins drop-down.

![](6d7b2882624511f09a0d0242ac130006/images/image_e2d1516c9619.png)

Step 2: In the list in Area A, find the plug-in you want to install and select it.

![](6d7b2882624511f09a0d0242ac130006/images/image_b9eedaa52ebf.png)

Step 3: In the B section list, check the environments to which you want to install this plug-in.

![](6d7b2882624511f09a0d0242ac130006/images/image_ce3e57cb258c.png)

Step 4: Click the button to install the selected plug-in into the selected environment.

![](6d7b2882624511f09a0d0242ac130006/images/image_aaa56e4d9dfb.png)


As is shown in the picture above,
In this case, the plug-in is successfully installed in two environments. In area B and Area C, you can see that the number of plug-ins in environment 3 and environment 4 changes from 0 to 1. Indicates that the installation is successful. In the C area, you can see that the newly installed plug-in is displayed in the plug-in list of environment 3 and environment 4.

![](6d7b2882624511f09a0d0242ac130006/images/image_595245bd3326.png)

Click on the installed browser plug-ins, and you can see that the plug-in you just downloaded and installed from the official plug-in library is saved to your local hard drive plug-in directory and environment directory, respectively.

### IV、Assign multiple plug-ins to various environments

By batch allocation of multiple plug-ins to multiple environments, you can realize batch installation of plug-ins to your specified environment in one click, and support you do not need to install plug-ins again in different places to use, truly once allocation (installation), anywhere you want to use.

![](6d7b2882624511f09a0d0242ac130006/images/image_4862ec42800a.png)

Allocate via the 【**Installed browser plug-ins**】 list:

As shown in the figure, select the browser plug-in drop-down that has been installed in area A, you can directly assign the local plug-in to the environment specified in area B. If the selected plug-in exists in the local migratory bird directory, this is a local automatic installation process. If the selected plug-in does not exist in your local hard disk, it will be automatically downloaded from the official plug-in library. Save it to your local hard disk again, and the plug-in installation in the specified environment will be performed automatically.
After batch allocation (installation) is complete, the plug-ins that have been successfully allocated (installed) are displayed in the C area list.

Note: If the same plug-in has been installed in the environment before, the reinstallation will not affect the use of the original plug-in.

![](6d7b2882624511f09a0d0242ac130006/images/image_2d55ec412a48.png)

Allocate via the 【**Official browser plug-ins**】 list:

As shown in the figure, select Area A and use the dropdown of all official browser plug-ins. You can directly assign the plug-ins of the official plug-in library to the environment specified in Area B. If the selected plug-ins exist in the local Migratory bird directory, this is a local automatic installation process; if the selected plug-ins do not exist in your local hard disk, they will be automatically downloaded from the official plug-in library. Save it to your local hard disk again, and the plug-in installation in the specified environment will be performed automatically.
After batch allocation (installation) is complete, the plug-ins that have been successfully allocated (installed) are displayed in the C area list.

### V、Deletes the specified environment plug-in

When you find that certain plug-ins are no longer required for certain commercial environments, you can delete plug-ins for the specified environment.
Note: You cannot delete plug-ins provided with the official plug-in library, but you can delete your own installed plug-ins.

![](6d7b2882624511f09a0d0242ac130006/images/image_ecee6806198d.png)

Delete the specified plug-in from the local plug-in directory in Area A:
In area A, select the installed browser plug-in, select the plug-in to be deleted, and click Delete plug-in. The Migratory Bird Plug-in Manager will only delete a copy of this plug-in data in the plug-in directory of this plug-in on your hard drive. This plug-in data stub will not be deleted from your browser environment. Therefore, your removal of the plug-ins listed in Section A does not affect the plug-ins and plug-in data in your environment. You are deleting only a copy of the plug-in that you have downloaded on your local hard drive.
That is, while you no longer see the plug-in in the A section list, you still see it in the environment's plug-in list (the C section list).

So, what if I want to completely remove a specified plug-in from the environment?

![](6d7b2882624511f09a0d0242ac130006/images/image_a30124f7f46f.png)


Figure: Select Environment 8 in area B, and you can see that all plug-ins in this environment have been listed in Area C. Select one or more of the plug-ins. Click Delete plug-in. Plug-ins specified in this environment will be removed immediately and completely.
The results are as follows:
1. After the plug-in is deleted in this way, the environment will no longer have all the functions brought by the plug-in.
2. If you use the product in a different location, the environment will no longer see this plug-in and all the functions brought by this plug-in.
3. If you need to use the plug-in again, you must go through the process of reinstalling the plug-in in Section A.

### Ⅵ、Delete the specified plug-ins from the environment in batches

![](6d7b2882624511f09a0d0242ac130006/images/image_145b67b9fe89.png)

The Mbbrowser Browser Plug-in Manager allows you to batch delete multiple plug-ins specified in multiple environments with one click.

Operation method:
Select the environment range that you want to delete by observing the number of environment plug-ins in area C and B by selecting the environment drop-down list in area B. Then, in area C, check the plug-in that you want to remove. Click Delete plug-in to delete multiple plug-ins from multiple environments.

The results are as follows:
1. After the plug-in is deleted in this way, the environment will no longer have all the functions brought by the plug-in.
2. If you use the product in a different location, the environment will no longer see this plug-in and all the functions brought by this plug-in.
3. If you need to use the plug-in again, you must go through the process of reinstalling the plug-in in Section A.

### Ⅶ、Use an environment where plug-ins have been installed

The foregoing:
The Mbbrowser product supports seamless use of the environment in different places after the plug-in is installed in the environment for the first time (no need to install the plug-in again), and seamless use in the sharing mode of multi-user collaborative environment (no need to install the plug-in again). After the environment is exported locally and delivered to a third-party account, the environment can be used seamlessly (no plug-ins need to be installed twice).

When the plug-in is installed into this environment for the first time, use it elsewhere:
As shown below:

<p><img src="6d7b2882624511f09a0d0242ac130006/images/image_2c024621d35f.png" width="360" /></p>

When it is used in a remote location for the first time (click to run), the Migrating Bird browser product will detect, automatically download and install all required commercial plug-ins in the environment, and display the whole download and installation process. At this time, your mechanical hard disk will have obvious data writing process (you are advised to use NVME disks or SSDS to ensure the efficient operation of the browser).
After completing the above actions, the fingerprint browser corresponding to this environment will be opened immediately, and the installed plug-in will be displayed in the upper right corner of the browser, as shown in the figure.

![](6d7b2882624511f09a0d0242ac130006/images/image_1c428cad7f9d.png)

![](6d7b2882624511f09a0d0242ac130006/images/image_6b5729129e57.png)

Note: Some GOOGLE browser add-ons will have this default feature: Let Chromium run in background. Indicates that the browser window runs in the background by default. To ensure the stable operation of the customer's business, click on the Migratory Bird client at this time

![](6d7b2882624511f09a0d0242ac130006/images/image_1f08d7fa47ef.png)

Stopping will have no effect. You need to modify your plug-in Settings or manually close the browser form for this environment in the tray area at the lower right corner of the screen.