POSTMAN调试候鸟API接口 - 2、需要本地安装POSTMAN，见
====================================

API文档: POSTMAN调试候鸟API接口 - 2、需要本地安装POSTMAN，见
URL: https://www.mbbrowser.com/api/postman-debug
抓取时间: 2025-07-28 12:36:17

2、需要本地安装POSTMAN，见【POSTMAN下载及安装】
### 开启POSTMAN调试API接口
• 需先配合使用CLI命令行启动客户端，见【HTTP模式说明】

• 启动POSTMAN

• 在启动界面的右侧点击 + 号新建一个请求窗口

• 在请求窗口界面，请求模式要改成POST，请求地址输入本地启动APISERVER后的API地址+端口号+要调试的接口，请求数据格式为raw模式的JOSN数据，具体如下图：

• 请求地址和请求参数填写完后点击右边的Send按钮进行请求，请求结果就会在下方的位置显示

支持邮箱: <EMAIL>
©MBBROWSER @2025

京ICP备 2020047947号

本系统不提供代理IP服务，禁止用户使用本系统进行任何违法犯罪活动，用户使用本系统带来的任何责任由用户自行承担。

MBbrowser.com  All Rights Reserved. 候鸟防关联浏览器对网站内容拥有最终解释权。
工作日客服(微信)
工作日09-18点

夜间/周末客服(微信)

工作日 18-24点，周末全天

商务(微信)

mbbrowser_official

###### 全国咨询服务热线

400-112-6050
在线咨询

微信咨询

电话咨询

售后咨询