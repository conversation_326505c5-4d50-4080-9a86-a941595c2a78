标题: 配置多级代理IP - Proxifier桥接教程
英文标题: Proxifier usage instructions
ID: 124
分类ID: 7
添加时间: 1721636636
更新时间: 1729674511
访问次数: 0
SEO标题: Proxifier多级网络桥接工具使用说明
SEO关键词: Proxifier多级网络桥接工具使用说明
SEO描述: Proxifier多级网络桥接工具使用说明

================================================== 内容 ==================================================
<a href="https://www.mbbrowser.com/api/code#curl_error" target="_blank" style="float:right;padding:6px 15px;border-radius:2px;color: #fff;background: #4384f3;border-color: #4384f3;text-shadow: 0 -1px 0 rgba(0, 0, 0, .12);text-decoration: none;">代理检测错误码表</a>

### 前述

由于部份客户本地网络访问海外互联网受阻的客观因素，当您购买并使用IP平台商的海外代理IP时，您直接在候鸟客户端的环境配置中填入海外代理IP有一定机率是无法联接成功，因此您需要先将本地网络置入到海外，进入国际互联网后，使用Proxifier工具进行流量高速转发，您才可以成功使用购买的海外代理IP。

#### 网络拓扑结构：

![](6d7b2882624511f09a0d0242ac130006/images/image_191c1ef65e38.png)

**我的PC**  –> **本地网络** (国内互联网) –> **v2..y** (或其它vpn软件跨境成功)  –> **国际互联网** (本地网络已进入国际互联网) –> **Proxifier** （已将v2..y的代理与候鸟客户端/候鸟浏览器内核配置好绑定）  –> **候鸟客户端** –> **环境配置中填入海外代理IP**（<font color=green>检测代理IP成功通过</font>）  –>  **开展跨境电商业务** (<font color=green>此时目标网站看到的是在海外用户在访问</font>)

#### 使用场景：

**场景一：** 在本地国内网络环境下，直接使用海外代理IP失败的情况，您需要使用此方式来保证海外代理IP可正常商用。

**场景二：** 已经将本地网络置入到国际互联网中的，但需要通过HTTP/SOCKE5填入本地 IP:PORT 方式，才能进行国际互联网业务访问与开展的，报错现像为：在候鸟中使用海外代理IP失败的情况，则您需要使用此方式来保证海外代理IP和浏览器环境访问海外站点可正常商用。

**场景三：** 在本地网络环境下已经开启了多级代理，各代理服务器层层嵌套模式下，使用候鸟检测海外代理IP失败的情况，您需要使用此方式来保证海外代理IP可正常使用。

#### Proxifier运行后，代理IP生效图示：

在候鸟客户端里，进入环境配置面板，运行并配置Proxifier，再填入您的海外代理IP，并检测海外代理IP成功，如图：

**多级代理下，<font color=red>未启用Proxifier时</font>，候鸟环境检测代理：**

![](6d7b2882624511f09a0d0242ac130006/images/image_3df64fa5e67f.png)

**多级代理下，已启用Proxifier：**

![](6d7b2882624511f09a0d0242ac130006/images/image_29144ddd61ef.png)

### 一、Proxifier 下载

下载地址 (点击下载原版Proxifier安装包)：[Mbbrowser_Officially_Designated_Bridge_Software_ProxifierSetup.exe](https://www.mbbrowser.com/down/tools/%5b网桥工具_原版%5dBridge_Software_ProxifierSetup.exe "Mbbrowser_Officially_Designated_Bridge_Software_ProxifierSetup.exe")

### 二、Proxifier 安装

运行安装包，安装过程全部选择默认，直至安装完成。

![](6d7b2882624511f09a0d0242ac130006/images/image_d46d455422fb.png)

安装完成后可以看到这样的文件。

- Proxifier Service Manager：将 Proxifier作为系统服务来运行，如果您需要系统启动后默认启动Proxifier，则可以通过此工具设置Proxifier作为系统服务来运行。
- Proxifier： 主程序
- Proxy Checker： 上级代理服务器检测工具，用来检测上级代理工具是否正常，如v2..y/vpn等，非常好用。
- Uninstall Proxifier： Proxifier卸载程序。

### 三、运行并配置Proxifier

安装完成，并运行Proxifier，您可以看到此默认界面，说明此网桥软件已开始正常运行，此时开始配置软件。

![](6d7b2882624511f09a0d0242ac130006/images/image_e08ed287fce4.png)

点击 1 箭头指向按钮，弹出Proxy Servers窗口。
点击2箭头指向按钮Add…，弹出添加上级代理服务器IP+端口窗口

![](6d7b2882624511f09a0d0242ac130006/images/image_721241b01ac3.png)

箭头3：作用是在您添加了代理IP和代理端口后，可检测上级代理工具是否正常运行。

箭头4：当上级代理工具检测通过后，点OK进行保存。

![](6d7b2882624511f09a0d0242ac130006/images/image_f4736350036a.png)

添加上级代理工具转发流量的的IP和端口。

![](6d7b2882624511f09a0d0242ac130006/images/image_9ff8c890df32.png)

在这里填入上级代理的IP，端口，和代理协议。点击OK进行保存。


例如：如果您的上级代理工具是v2..y，则v2..y的数据转发代理IP和端口见下图：

![](6d7b2882624511f09a0d0242ac130006/images/image_05f252b26bcc.png)

注：如果v2..y在你的局域网中另一台机器运行，则需要填那台机器的内网IP和上图中的端口。如果v2..y和Proxifier在同一台机器上运行，则IP写127.0.0.1即可。

### Proxifier 规则添加

将候鸟客户端和候鸟内核添加入Proxifier

![](6d7b2882624511f09a0d0242ac130006/images/image_02cb6dd8147e.png)

1、点击上图中按钮，将弹出规则设置窗口。将候鸟的可执行文件加入到规则中。这样代理的流量就可以直接发送到候鸟客户端和候鸟内核中。

![](6d7b2882624511f09a0d0242ac130006/images/image_0c195842de24.png)

2、点击ADD按钮，添加候鸟可执行文件到Proxifier中，也叫添加规则。

![](6d7b2882624511f09a0d0242ac130006/images/image_67588f2d7135.png)

3、见上图，先在Name那里填入规则名称。
4、点击Browser按钮，将本地已安装好的候鸟客户端中的mbbrowser.exe、apiserver.exe、mbservice.exe、子目录MBbrowser中的chrome.exe、chrome_proxy.exe添加到上图的框中。

5、在上图Action：下拉列表中，选中 Proxy Socks5 127.0.0.1，注意这里的地址即是上级代理工具的代理IP:端口地址，请一定要选对。

6、点击OK，保存。

### Proxifier + MBBROWSER联合测试

![](6d7b2882624511f09a0d0242ac130006/images/image_d1b3c72bd6e6.png)

Proxifier的规则配置好后，您此时可以进入候鸟客户端，进入环境配置面板，开始测试您购买的代理IP，此时代理IP就可以成功联接。同时您在Proxifier中可以看到已经有流量转到候鸟客户端。

![](6d7b2882624511f09a0d0242ac130006/images/image_e9802169c034.png)

此时，您运行环境后，在YALALA.COM中，可以看到代理IP和环境已正常运行。

如果您习惯使用中文版，可以下载网上的汉化版本，如图：

![](6d7b2882624511f09a0d0242ac130006/images/image_33bd89d201fe.png)

汉化版下载地址： [点击下载](https://www.mbbrowser.com/down/tools/%5b网桥工具_汉化版%5dBridge_Software_ProxifierSetup.exe "点击下载")

**其它Proxifier相关教程：**
[https://blog.csdn.net/zkaqlaoniao/article/details/134310784](https://blog.csdn.net/zkaqlaoniao/article/details/134310784 "https://blog.csdn.net/zkaqlaoniao/article/details/134310784")

================================================== 英文内容 ==================================================
<a href="https://www.mbbrowser.com/en/api/code/#curl_error" target="_blank" style="float:right;padding:6px 15px;border-radius:2px;color: #fff;background: #4384f3;border-color: #4384f3;text-shadow: 0 -1px 0 rgba(0, 0, 0, .12);text-decoration: none;">Proxy detection error code table</a>

### Previous

Due to the objective factors that some customers' local network access to overseas Internet is blocked, when you purchase and use the overseas proxy IP of the IP platform provider, there is a certain chance that you cannot connect successfully by directly entering the overseas proxy IP in the environment configuration of the migratory bird client. Therefore, you need to first place the local network into overseas, enter the international Internet, and use the Proxifier tool for high-speed traffic forwarding before you can successfully use the purchased overseas proxy IP.

#### Network Topology:

![](6d7b2882624511f09a0d0242ac130006/images/image_191c1ef65e38.png)

**My PC**  -> **Local Network** (domestic internet) -> **v2..y** (or other VPN software successfully crossing borders) -> **International Internet** (the local network has entered the international internet) -> **Proxifier** (the v2..y proxy has been configured and bound with the Migratory Bird client/Migratory Bird browser kernel) -> **Migratory Bird Client** -> **Fill in the overseas proxy IP in the environment configuration** (<font color=green>The proxy IP has been successfully detected</font>) -> **Carry out cross-border e-commerce business** (<font color=green>At this time, the target website sees that overseas users are accessing</font>)

#### Usage Scenario:

**Scenario 1:** In the local domestic network environment, if you directly use an overseas proxy IP and it fails, you need to use this method to ensure that the overseas proxy IP can be used normally for commercial purposes.

**Scenario 2:** The local network has been integrated into the Internet, but international Internet business access and development require the use of HTTP/SOCKE5 to fill in the local IP:PORT method. The error message is: If using an overseas proxy IP in a migratory bird fails, you need to use this method to ensure that the overseas proxy IP and browser environment can access overseas sites for normal commercial use.

**Scenario 3:** In a local network environment where multiple levels of proxies have been enabled, and each proxy server is nested within another, using the migratory bird detection method to detect the failure of overseas proxy IPs requires you to use this method to ensure that the overseas proxy IPs can be used normally.

#### The diagram illustrating the activation of the proxy IP after Proxifier is running:

In the Migratory Bird Client, go to the Environment Configuration panel, run and configure Proxifier, fill in your overseas proxy IP, and check that the overseas proxy IP is successful, as shown in the figure:

**Under multi-level proxy, <font color=red>when Proxifier is not enabled</font>, the migratory bird environment detection proxy:**

![](6d7b2882624511f09a0d0242ac130006/images/image_3df64fa5e67f.png)

**Under multi-level proxy, Proxifier has been enabled:**

![](6d7b2882624511f09a0d0242ac130006/images/image_29144ddd61ef.png)

### I. Proxifier Download

Download address (click to download the original Proxifier installation package): [Mbbrowser_Officially_Designated_Bridge_Software_ProxifierSetup.exe](https://www.mbbrowser.com/down/tools/%5bBridge Tool_Original %5dBridge_Software_ProxifierSetup.exe "Mbbrowser_Officially_Designated_Bridge_Software_ProxifierSetup.exe")

### II. Proxifier Installation

Run the installation package and select the default settings for all installation processes until the installation is complete.

![](6d7b2882624511f09a0d0242ac130006/images/image_d46d455422fb.png)

After installation, you can see such files.

- Proxifier Service Manager: Run Proxifier as a system service. If you need Proxifier to start automatically after system startup, you can use this tool to set Proxifier to run as a system service.
- Proxifier: Main Program
- Proxy Checker: A tool for detecting the functionality of upstream proxy servers, such as v2..y/vpn, and is highly useful.
- Uninstall Proxifier: Proxifier uninstaller.

### III. Run and configure Proxifier

After the installation is complete and Proxifier is running, you can see this default interface, indicating that the bridge software has started running normally. Now start configuring the software.

![](6d7b2882624511f09a0d0242ac130006/images/image_e08ed287fce4.png)

Click the 1 arrow pointing button to pop up the Proxy Servers window.
Click the 2 arrow pointing to the button Add..., and a window will pop up to add the IP+port of the upper-level proxy server

![](6d7b2882624511f09a0d0242ac130006/images/image_721241b01ac3.png)

Arrow 3: It is used to detect whether the superior proxy tool is running normally after you add the proxy IP and proxy port.

Arrow 4: After the superior agent tool passes the detection, click OK to save.

![](6d7b2882624511f09a0d0242ac130006/images/image_f4736350036a.png)

Add the IP and port of the upstream proxy tool to forward traffic.

![](6d7b2882624511f09a0d0242ac130006/images/image_9ff8c890df32.png)

Fill in the IP, port, and proxy protocol of the superior proxy here.Click OK to save.


For example, if your superior proxy tool is v2..y, the data forwarding proxy IP and port of v2..y are shown in the following figure:

![](6d7b2882624511f09a0d0242ac130006/images/image_05f252b26bcc.png)

Note: If v2..y is running on another machine in your local network, you need to fill in the intranet IP of that machine and the port in the image above.If v2..y and Proxifier are running on the same machine, write 127.0.0.1 for IP.

### Proxifier rule addition

Add the Migratory Bird Client and Migratory Bird Kernel to Proxifier

![](6d7b2882624511f09a0d0242ac130006/images/image_02cb6dd8147e.png)

1. Click the button in the above figure to pop up the rule setting window.Add the executable file of migratory birds to the rules.In this way, the traffic of the proxy can be directly sent to the migratory bird client and migratory bird kernel.

![](6d7b2882624511f09a0d0242ac130006/images/image_0c195842de24.png)

2. Click the ADD button to add the executable file of migratory birds to Proxifier, also known as adding rules.

![](6d7b2882624511f09a0d0242ac130006/images/image_67588f2d7135.png)

3. See the figure above. First, fill in the rule name in the Name field.
4. Click the Browser button to open the mbbrowser.exe in the locally installed Migratory Bird Clientapiserver.exe, mbservice.exeAdd chrome.exe and chrome_proxy.exe in the subdirectory MBbrowser to the box in the figure above.

5. In the Action: drop-down list in the above figure, select Proxy Socks5 127.0.0.1. Note that the address here is the proxy IP: port address of the upper-level proxy tool. Be sure to choose the correct one.

6. Click OK to save.

### Proxifier + MBBROWSER joint test

![](6d7b2882624511f09a0d0242ac130006/images/image_d1b3c72bd6e6.png)

After configuring the rules for Proxifier, you can now enter the Migratory Bird client and access the environment configuration panel to start testing your purchased proxy IP. At this point, the proxy IP can successfully connect.At the same time, you can see in Proxifier that there is already traffic going to the migratory bird client.

![](6d7b2882624511f09a0d0242ac130006/images/image_e9802169c034.png)

At this time, after you run the environment, in YALALAIn COM, you can see that the proxy IP and environment are running normally.

If you are accustomed to using the Chinese version, you can download the online Chinese version, as shown in the figure:

![](6d7b2882624511f09a0d0242ac130006/images/image_33bd89d201fe.png)

Chinese version download address: [click to download](https://www.mbbrowser.com/down/tools/%5bBridge tool_Chinese version %5dBridge_Software_ProxifierSetup.exe "click to download")

**Other Proxifier-related tutorials:**
[https://blog.csdn.net/zkaqlaoniao/article/details/134310784](https://blog.csdn.net/zkaqlaoniao/article/details/134310784 "https://blog.csdn.net/zkaqlaoniao/article/details/134310784")