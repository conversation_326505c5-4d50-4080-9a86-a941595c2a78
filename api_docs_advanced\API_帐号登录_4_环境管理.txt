API文档: 帐号登录 - 4、环境管理
URL: https://www.mbbrowser.com/api/login
抓取时间: 2025-07-28 12:33:26
============================================================

API使用文档-候鸟防关联浏览器•

首页

应用

价格

下载

APInew
使用教程

常见问题

佣金计划

博客中心
登录&注册 简体中文
首页

应用

价格

下载

API

使用教程

佣金计划

博客中心

登录&注册

# API
候鸟浏览器API使用文档

API使用须知简介
• 使用须知
• HTTP模式说明
• 常见问题
• API接口文档1、帐号登录
• 2、获取成员列表
• 3、环境开启/关闭
• 4、环境管理
• 5、分组管理
• 6、脚本管理
• 7、插件管理
• 8、附录（国家码、时区、语言、系统和分辨率）
• 9、错误码对照表
• 候鸟API接口实时调试工具POSTMAN下载及安装
• POSTMAN调试候鸟API接口
• 调试接口JSON数据官方更新、下载
• 多种语言脚本示例
• JSON在线格式化工具

## 帐号登录
• 1、候鸟浏览器支持本地API的功能，帮助用户通过程序化的方式来启动和关闭浏览器等基础API功能，还可以配合Selenium和Puppeteer等自动化框架来实现浏览器操作的自动化。

• 2、仅支持客户端V3.9.2.114以上版本，请下载客户端最新版本【下载候鸟浏览器最新版】

### 【HTTP】帐号登录
• http模式需配合使用CLI命令行启动客户端，见【HTTP模式说明】，也可以用下面的bat文件示例修改对应的account、app_id和app_key并放到apiserver.exe的同级目录中，就可以轻松快捷地把apiserver运行起来。

apiserver.bat

`ApiServer.exe --port=8186 --account=<EMAIL> --app_id=7e147176e1d756eb03c0e18e7b640c23 --app_key=ExOTNjMWNWYzZTU5ZjYzMGEzZDU4ZDI3 --return=on --logs=on` 复制
### 【HTTP】与APISERVER交互、请求地址详述
• Apiserver成功运行后，您可以使用以下方式与Apiserver进行通讯

• http://127.0.0.1:8186 或 http://localhost:8186

• 例如实时切换帐号并重新登录，您可以：

• Post方式 请求地址：http://127.0.0.1:8186/login

• POST格式：Content-Type：application/json

• 然后POST您的json数据到此地址，Apiserver实时收到并实时执行，中间没有任何延迟。

### 【HTTP】实时切换账号并重新登录
• 在登录成功apiserver之后，您可以使用以下接口实时切换账号

• 【最大请求频率：10次/分钟】

• Path：/login

• Method：POST

• Content-Type：application/json

请求参数

以下参数获取可前往【使用须知】查看

参数名称 类型 必传 样例串/默认值 说明 Account string 是 <EMAIL> 用户凭证 Account APP_ID string 是 7e147176e1d756eb03c0e18e7b640c23 用户凭证 app_Id APP_KEY string 是 kwMTYxNG_MzlkZjhlZmM2YWNiY2M5MTkw 用户凭证 app_key return string 否 on (default) on: APISERVER.EXE 所有需返回给用户的数据[JSON/其它]，所有状态信息，返回到 CONSOLE 界面。（当用户未填写此参数时，默认为 ON）

off: 所有返回给用户的数据[JSON/其它]、所有状态信息，不在 CONSOLE 界面显示。

logs string 否 on (default) on: APISERVER.EXE 所有需返回给用户的 JSON 数据，所有状态信息，明文写入 API_LOG 目录【非 LOG 目录】，LOG 文件前缀例：test@qq.com_API_log。（当用户未填写此参数时，默认为 ON）

off: 所有返回给脚本的 JSON 数据、所有状态信息，不写入LOG。

hide string 否 on (default) on: APISERVER.EXE 以(全自动化)模式启动，启动后会同时自动屏蔽已登录的 mbbrowser.exe 主面板，保障客户在自动化运行中，避免人工通过主面板同时操控环境所带来的业务风险。（当用户未填写此参数时，默认为 ON）

off: APISERVER.EXE 以支持(半自动化/全自动化)模式启动，启动后会同时显示 mbbrowser.exe 主面板，供客户在自动化运行中，可人工通过主面板操控环境，强化业务控制能力。

[此参数仅在产品版本号: 4.8.20.134 及以后有效]

请求示例

`{
"APP_ID": "7e147176e1d756eb03c0e18e7b640c23",
"APP_KEY": "kwMTYxNG_MzlkZjhlZmM2YWNiY2M5MTkw",
"Account": "<EMAIL>"
}` 复制 返回参数

`{
"msg": "Login Success",
"status": 0,
"data": "Login Aaccount: <EMAIL>"
}` 复制 使用POSTMAN调试此接口

### 【HTTP】退出 APISERVER 并关闭客户端(退出登录)
• Path：/api/v1/quit

• Method：POST

• Content-Type：application/json

• 接口描述：停止并关闭APISERVER，关闭成功Code返回0。

返回参数

`{
"message": "APISERVER shut down.",
"code": 0,
"data": true
}` 复制 使用POSTMAN调试此接口

支持邮箱: <EMAIL>
©MBBROWSER @2025

京ICP备 **********号

本系统不提供代理IP服务，禁止用户使用本系统进行任何违法犯罪活动，用户使用本系统带来的任何责任由用户自行承担。

MBbrowser.com  All Rights Reserved. 候鸟防关联浏览器对网站内容拥有最终解释权。
工作日客服(微信)
工作日09-18点

夜间/周末客服(微信)

工作日 18-24点，周末全天

商务(微信)

mbbrowser_official

###### 全国咨询服务热线

400-112-6050
在线咨询

微信咨询

电话咨询

售后咨询