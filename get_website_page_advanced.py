#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
候鸟浏览器API文档抓取器 (高级版)
智能分析页面结构，抓取所有子页面内容并保存为独立的docx文件
"""

import requests
import re
import time
import os
from urllib.parse import urljoin, urlparse
from pathlib import Path
import logging
import json

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('get_website_page_advanced.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AdvancedAPIDocumentScraper:
    def __init__(self, base_url='https://www.mbbrowser.com/api/', output_dir='api_docs_advanced'):
        self.base_url = base_url
        self.output_dir = output_dir
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        
        # 创建输出目录
        Path(self.output_dir).mkdir(exist_ok=True)
        
        # 主要页面列表
        self.main_pages = [
            {'url': '/api/', 'title': '简介'},
            {'url': '/api/help', 'title': '使用须知'},
            {'url': '/api/http', 'title': 'HTTP模式说明'},
            {'url': '/api/question', 'title': '常见问题'},
            {'url': '/api/login', 'title': '帐号登录'},
            {'url': '/api/members', 'title': '获取成员列表'},
            {'url': '/api/browser', 'title': '环境开启关闭'},
            {'url': '/api/session', 'title': '环境管理'},
            {'url': '/api/group', 'title': '分组管理'},
            {'url': '/api/script', 'title': '脚本管理'},
            {'url': '/api/plugin', 'title': '插件管理'},
            {'url': '/api/appendix', 'title': '附录'},
            {'url': '/api/code', 'title': '错误码对照表'},
            {'url': '/api/postman', 'title': 'POSTMAN下载及安装'},
            {'url': '/api/postman-debug', 'title': 'POSTMAN调试候鸟API接口'},
            {'url': '/api/postman-example', 'title': '调试接口JSON数据官方更新下载'},
            {'url': '/api/example', 'title': '多种语言脚本示例'},
            {'url': '/api/json', 'title': 'JSON在线格式化工具'}
        ]
    
    def sanitize_filename(self, filename):
        """清理文件名，移除不合法字符"""
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        filename = filename.replace('/', '_').replace('\\', '_')
        if len(filename) > 200:
            filename = filename[:200]
        return filename.strip()
    
    def fetch_page_content(self, url):
        """获取页面内容"""
        try:
            full_url = urljoin(self.base_url, url)
            logger.info(f"正在获取页面: {full_url}")
            
            response = self.session.get(full_url, timeout=30)
            response.raise_for_status()
            response.encoding = 'utf-8'
            
            return response.text
            
        except Exception as e:
            logger.error(f"获取页面失败: {url}, 错误: {str(e)}")
            return None
    
    def extract_subsections(self, html_content, page_title):
        """从页面内容中提取子章节"""
        subsections = []
        
        try:
            # 查找编号列表模式 (如: 1、获取环境列表)
            numbered_pattern = r'(\d+)、([^<>\n]+)'
            matches = re.findall(numbered_pattern, html_content)
            
            for number, title in matches:
                if len(title.strip()) > 2:  # 过滤掉过短的标题
                    subsections.append({
                        'number': number,
                        'title': title.strip(),
                        'full_title': f"{number}、{title.strip()}"
                    })
            
            # 如果没找到编号模式，尝试查找其他模式
            if not subsections:
                # 查找标题模式
                header_patterns = [
                    r'<h[1-6][^>]*>([^<]+)</h[1-6]>',
                    r'###?\s*([^<>\n]+)',
                    r'##?\s*([^<>\n]+)'
                ]
                
                for pattern in header_patterns:
                    matches = re.findall(pattern, html_content, re.IGNORECASE)
                    for i, title in enumerate(matches, 1):
                        title = re.sub(r'<[^>]+>', '', title).strip()
                        if len(title) > 3 and title not in [page_title, 'API', '候鸟浏览器API使用文档']:
                            subsections.append({
                                'number': str(i),
                                'title': title,
                                'full_title': title
                            })
                    if subsections:
                        break
            
            logger.info(f"在页面 {page_title} 中找到 {len(subsections)} 个子章节")
            return subsections
            
        except Exception as e:
            logger.error(f"提取子章节失败: {str(e)}")
            return []
    
    def extract_content_by_section(self, html_content, section_info, page_title):
        """根据章节信息提取特定内容"""
        try:
            # 尝试多种方式提取章节内容
            section_title = section_info['title']
            section_number = section_info['number']
            
            # 方法1: 查找从当前章节到下一章节的内容
            current_pattern = rf'{re.escape(section_number)}、{re.escape(section_title)}'
            next_number = str(int(section_number) + 1)
            next_pattern = rf'{next_number}、'
            
            # 查找当前章节的位置
            current_match = re.search(current_pattern, html_content, re.IGNORECASE)
            if current_match:
                start_pos = current_match.start()
                
                # 查找下一章节的位置
                next_match = re.search(next_pattern, html_content[start_pos + len(current_match.group()):], re.IGNORECASE)
                if next_match:
                    end_pos = start_pos + len(current_match.group()) + next_match.start()
                    section_html = html_content[start_pos:end_pos]
                else:
                    # 如果没有下一章节，取到页面结尾
                    section_html = html_content[start_pos:]
                
                # 转换为文本
                section_text = self.html_to_text(section_html)
                
                if len(section_text.strip()) > 50:  # 确保有足够的内容
                    return section_text
            
            # 方法2: 如果方法1失败，尝试查找包含章节标题的段落
            title_pattern = rf'.*{re.escape(section_title)}.*'
            matches = re.finditer(title_pattern, html_content, re.IGNORECASE | re.DOTALL)
            
            for match in matches:
                # 提取匹配段落前后的内容
                start_pos = max(0, match.start() - 500)
                end_pos = min(len(html_content), match.end() + 2000)
                section_html = html_content[start_pos:end_pos]
                section_text = self.html_to_text(section_html)
                
                if section_title.lower() in section_text.lower() and len(section_text.strip()) > 50:
                    return section_text
            
            # 方法3: 返回基本信息
            return f"章节: {section_info['full_title']}\n\n暂无详细内容，请查看完整页面文档。"
            
        except Exception as e:
            logger.error(f"提取章节内容失败: {str(e)}")
            return f"章节: {section_info['full_title']}\n\n内容提取失败。"
    
    def html_to_text(self, html_content):
        """改进的HTML转文本"""
        try:
            # 移除script和style标签
            html_content = re.sub(r'<script[^>]*>.*?</script>', '', html_content, flags=re.DOTALL | re.IGNORECASE)
            html_content = re.sub(r'<style[^>]*>.*?</style>', '', html_content, flags=re.DOTALL | re.IGNORECASE)
            
            # 处理标题
            for i in range(1, 7):
                html_content = re.sub(rf'<h{i}[^>]*>(.*?)</h{i}>', rf'\n{"#" * i} \1\n', html_content, flags=re.DOTALL | re.IGNORECASE)
            
            # 处理段落和换行
            html_content = re.sub(r'<p[^>]*>(.*?)</p>', r'\1\n\n', html_content, flags=re.DOTALL | re.IGNORECASE)
            html_content = re.sub(r'<br[^>]*/?>', '\n', html_content, flags=re.IGNORECASE)
            
            # 处理列表
            html_content = re.sub(r'<li[^>]*>(.*?)</li>', r'• \1\n', html_content, flags=re.DOTALL | re.IGNORECASE)
            
            # 处理代码块
            html_content = re.sub(r'<pre[^>]*>(.*?)</pre>', r'\n```\n\1\n```\n', html_content, flags=re.DOTALL | re.IGNORECASE)
            html_content = re.sub(r'<code[^>]*>(.*?)</code>', r'`\1`', html_content, flags=re.DOTALL | re.IGNORECASE)
            
            # 移除所有HTML标签
            html_content = re.sub(r'<[^>]+>', '', html_content)
            
            # 解码HTML实体
            html_entities = {
                '&nbsp;': ' ', '&lt;': '<', '&gt;': '>', '&amp;': '&',
                '&quot;': '"', '&#39;': "'", '&hellip;': '...'
            }
            for entity, char in html_entities.items():
                html_content = html_content.replace(entity, char)
            
            # 清理空行
            lines = [line.strip() for line in html_content.split('\n')]
            cleaned_lines = []
            prev_empty = False
            
            for line in lines:
                if not line:
                    if not prev_empty:
                        cleaned_lines.append('')
                    prev_empty = True
                else:
                    cleaned_lines.append(line)
                    prev_empty = False
            
            return '\n'.join(cleaned_lines).strip()
            
        except Exception as e:
            logger.error(f"HTML转文本失败: {str(e)}")
            return html_content
    
    def save_as_txt(self, content, filename):
        """保存为TXT文件"""
        try:
            filepath = os.path.join(self.output_dir, f"{filename}.txt")
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            logger.info(f"已保存TXT文件: {filename}.txt")
            return True
        except Exception as e:
            logger.error(f"保存TXT文件失败: {str(e)}")
            return False
    
    def process_page_with_subsections(self, page_info):
        """处理包含子章节的页面"""
        try:
            url = page_info['url']
            title = page_info['title']
            
            # 获取页面内容
            html_content = self.fetch_page_content(url)
            if not html_content:
                return False
            
            # 提取子章节
            subsections = self.extract_subsections(html_content, title)
            
            if subsections:
                logger.info(f"页面 {title} 包含 {len(subsections)} 个子章节，将分别保存")
                
                # 为每个子章节创建单独的文件
                for section in subsections:
                    section_content = self.extract_content_by_section(html_content, section, title)
                    
                    # 创建文件内容
                    file_content = f"API文档: {title} - {section['full_title']}\n"
                    file_content += f"URL: {urljoin(self.base_url, url)}\n"
                    file_content += f"抓取时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n"
                    file_content += "=" * 60 + "\n\n"
                    file_content += section_content
                    
                    # 保存文件
                    safe_title = self.sanitize_filename(f"API_{title}_{section['number']}_{section['title']}")
                    self.save_as_txt(file_content, safe_title)
                    
                    time.sleep(1)  # 避免过快处理
            else:
                # 如果没有子章节，保存整个页面
                logger.info(f"页面 {title} 没有子章节，保存整个页面")
                full_content = self.html_to_text(html_content)
                
                file_content = f"API文档: {title}\n"
                file_content += f"URL: {urljoin(self.base_url, url)}\n"
                file_content += f"抓取时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n"
                file_content += "=" * 60 + "\n\n"
                file_content += full_content
                
                safe_title = self.sanitize_filename(f"API_{title}")
                self.save_as_txt(file_content, safe_title)
            
            return True
            
        except Exception as e:
            logger.error(f"处理页面失败: {page_info}, 错误: {str(e)}")
            return False
    
    def scrape_all_pages(self):
        """抓取所有页面"""
        logger.info("开始抓取API文档页面...")
        
        success_count = 0
        total_count = len(self.main_pages)
        
        for i, page_info in enumerate(self.main_pages, 1):
            logger.info(f"正在处理第 {i}/{total_count} 个页面: {page_info['title']}")
            
            if self.process_page_with_subsections(page_info):
                success_count += 1
            
            # 避免请求过于频繁
            time.sleep(3)
        
        logger.info(f"抓取完成! 总页面数: {total_count}, 成功: {success_count}, 失败: {total_count - success_count}")

def main():
    """主函数"""
    print("候鸟浏览器API文档抓取器 (高级版) 启动...")
    print("目标网站: https://www.mbbrowser.com/api/")
    print("输出格式: TXT文件 (每个子章节单独保存)")
    print("="*60)
    
    scraper = AdvancedAPIDocumentScraper()
    scraper.scrape_all_pages()
    
    print("="*60)
    print("抓取完成!")
    print("- 文档文件: api_docs_advanced目录")
    print("- 日志文件: get_website_page_advanced.log")

if __name__ == "__main__":
    main()
