标题: 浏览器指纹：字体
英文标题: Browser Fingerprint：Font
ID: 88
分类ID: 25
添加时间: 1613976527
更新时间: 1685607156
访问次数: 0
SEO标题: 浏览器指纹：字体
SEO关键词: 浏览器指纹：字体
SEO描述: 浏览器指纹：字体

================================================== 内容 ==================================================
字体指纹识别是一种身份验证的方式。基于用户所使用的字体种类及字体在浏览器中被绘出的方式，网站可以进行指纹追踪。通常，网站在浏览器指纹识别时有两种运用字体的方法。

- 枚举字体列表
- 基于字体测量的指纹识别

您可以通过Borwserleaks.com查看这些方法是如何被应用的。

### 枚举字体列表
收集电子设备上已安装的字列表的最常用方法是CSS的自我检测。简而言之，该方法通过测量浏览器上特定字体显示的一个短语的宽度来获取您的字体列表。如果宽度匹配，就意味着您安装了这种字体。如不匹配，则推定这种字体没有被安装。

通过循环检测可能安装的字体列表及宽度，网站可准确窥探一台设备安装了哪些字。

候鸟浏览器使用一种特殊算法来对抗这种探测方法，允许您控制可供网站枚举的字体以反追踪。

当您创建和保存一个浏览器配置文件时，候鸟浏览器会自动生成一个随机字体表，将其显示给网站终端。

### 基于字体测量的指纹识别（Unicode glyphs和DOMRect）
基于字体测量的指纹识别技术，即测量HTML元素的边界和尺寸，HTML元素可以用有着特定字体系列的文本进行填充。接着测量结果会被转换成哈希字符串标识符，以用于更精确的指纹识别。这种技术可被划为两种子分支。

- Unicode glyphs, 是针对特定字体系列中的单个字符的测量。
- DomRect (也被称为 getClientRects), 是对HTML元素的测量，其中的文本以特定字体系列呈现。

候鸟浏览器利用一系列不同的方法来对抗基于测量的字体指纹识别，最终，每个浏览器配置文件都会有一个唯一的Unicode glyphs和DOMRect哈希值。

================================================== 英文内容 ==================================================
Font fingerprinting is a way of authentication. Based on the type of font a user uses and how it is drawn in the browser, websites can track fingerprints. In general, websites use fonts in browser fingerprinting in one of two ways.

- Enumerated font list
- Fingerprint recognition based on font measurement

You can see how these methods are being applied at Borwserleaks.com.

### Enumerated font list
The most common way to collect a list of installed words on an electronic device is CSS self-checking. In a nutshell, this method gets your list of fonts by measuring the width of a phrase displayed in a particular font on the browser. If the widths match, that means you have the font installed. If it does not match, it is assumed that the font is not installed.

By iterating through the list of possible fonts and widths, websites can see exactly which words are installed on a device.

Migratory Bird Browser uses a special algorithm to combat this detection method, allowing you to control the fonts that can be enumerated by websites for backtracking.

When you create and save a browser profile, Migratory Bird Browser automatically generates a random font table to display to the site terminal.

### Fingerprint Recognition Based on Font Measurement (Unicode glyphs and DOMRect)
Fingerprinting is based on font measurement, which measures the boundaries and dimensions of HTML elements that can be filled with text with a particular font family. The measurements are then converted into hash string identifiers for more accurate fingerprint identification. This technology can be divided into two subdivisions.

- Unicode glyphs, a measurement for a single character in a particular font family.
- Domrects (also known as getClientRects) are measurements of HTML elements in which text is rendered in a specific series of fonts.

Mbbrowser uses a number of different approaches to combat measuring-based font fingerprinting, and eventually, each browser profile will have a unique Unicode glyphs and DOMRect hash.