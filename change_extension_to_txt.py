#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将skin目录下所有文件的后缀名改为.txt
"""

import os
from pathlib import Path

def change_extensions_to_txt(directory):
    """将目录下所有文件的后缀名改为.txt"""
    dir_path = Path(directory)
    
    if not dir_path.exists():
        print(f"❌ 目录不存在: {directory}")
        return
    
    # 获取所有文件
    all_files = [f for f in dir_path.iterdir() if f.is_file()]
    
    print(f"📁 处理目录: {directory}")
    print(f"📊 找到 {len(all_files)} 个文件")
    print("=" * 80)
    
    if not all_files:
        print("❌ 没有找到任何文件")
        return
    
    # 统计信息
    renamed_count = 0
    skipped_count = 0
    failed_count = 0
    
    # 按扩展名分类
    extension_stats = {}
    
    for file_path in sorted(all_files):
        old_name = file_path.name
        old_extension = file_path.suffix.lower()
        
        # 统计扩展名
        if old_extension in extension_stats:
            extension_stats[old_extension] += 1
        else:
            extension_stats[old_extension] = 1
        
        # 检查是否已经是.txt
        if old_extension == '.txt':
            print(f"⏭️  跳过（已是TXT）: {old_name}")
            skipped_count += 1
            continue
        
        # 生成新文件名
        base_name = file_path.stem
        new_name = base_name + '.txt'
        new_path = file_path.parent / new_name
        
        # 检查新文件名是否已存在
        if new_path.exists():
            print(f"⚠️  跳过（目标文件已存在）: {old_name} -> {new_name}")
            skipped_count += 1
            continue
        
        try:
            # 重命名文件
            file_path.rename(new_path)
            renamed_count += 1
            
            # 获取文件大小
            file_size = new_path.stat().st_size / 1024
            print(f"✅ 重命名: {old_name} -> {new_name} ({file_size:.1f} KB)")
            
        except Exception as e:
            failed_count += 1
            print(f"❌ 重命名失败: {old_name} - {str(e)}")
    
    # 输出统计结果
    print("=" * 80)
    print(f"📊 处理统计:")
    print(f"   总文件数: {len(all_files)}")
    print(f"   成功重命名: {renamed_count}")
    print(f"   跳过文件: {skipped_count}")
    print(f"   失败文件: {failed_count}")
    print(f"   成功率: {renamed_count/(len(all_files)-skipped_count)*100:.1f}%" if (len(all_files)-skipped_count) > 0 else "N/A")
    
    print(f"\n📈 原始扩展名分布:")
    for ext, count in sorted(extension_stats.items()):
        ext_display = ext if ext else "(无扩展名)"
        print(f"   {ext_display}: {count} 个文件")
    
    return {
        'total': len(all_files),
        'renamed': renamed_count,
        'skipped': skipped_count,
        'failed': failed_count,
        'extensions': extension_stats
    }

def verify_results(directory):
    """验证处理结果"""
    dir_path = Path(directory)
    all_files = [f for f in dir_path.iterdir() if f.is_file()]
    
    print(f"\n📋 处理结果验证:")
    print("=" * 80)
    
    # 按扩展名分类
    extension_count = {}
    total_size = 0
    
    for file_path in sorted(all_files):
        extension = file_path.suffix.lower()
        file_size = file_path.stat().st_size / 1024
        total_size += file_size
        
        if extension in extension_count:
            extension_count[extension] += 1
        else:
            extension_count[extension] = 1
        
        # 显示文件信息
        if extension == '.txt':
            status = "✅"
        else:
            status = "🔴"
        
        print(f"{status} {file_path.name} ({file_size:.1f} KB)")
    
    print("=" * 80)
    print(f"📊 最终统计:")
    print(f"   总文件数: {len(all_files)}")
    print(f"   总大小: {total_size:.1f} KB")
    print(f"   平均大小: {total_size/len(all_files):.1f} KB")
    
    print(f"\n📈 最终扩展名分布:")
    txt_count = 0
    for ext, count in sorted(extension_count.items()):
        ext_display = ext if ext else "(无扩展名)"
        if ext == '.txt':
            txt_count = count
            status = "✅"
        else:
            status = "🔴"
        print(f"   {status} {ext_display}: {count} 个文件")
    
    # 检查是否全部转换成功
    if txt_count == len(all_files):
        print(f"\n🎉 所有文件都已成功转换为TXT格式！")
    else:
        non_txt_count = len(all_files) - txt_count
        print(f"\n⚠️  还有 {non_txt_count} 个文件不是TXT格式")
    
    return extension_count

def show_sample_files(directory, sample_count=10):
    """显示示例文件"""
    dir_path = Path(directory)
    all_files = [f for f in dir_path.iterdir() if f.is_file() and f.suffix.lower() == '.txt']
    
    if not all_files:
        print("\n❌ 没有找到TXT文件")
        return
    
    print(f"\n📋 TXT文件示例 (显示前{min(sample_count, len(all_files))}个):")
    print("-" * 80)
    
    for i, file_path in enumerate(sorted(all_files)[:sample_count]):
        file_size = file_path.stat().st_size / 1024
        print(f"{i+1:2d}. {file_path.name} ({file_size:.1f} KB)")
    
    if len(all_files) > sample_count:
        print(f"    ... 还有 {len(all_files) - sample_count} 个TXT文件")

if __name__ == "__main__":
    source_directory = r"F:\augment\output\skin"
    
    print("📄 文件扩展名批量修改工具")
    print("功能：将所有文件的扩展名改为.txt")
    print("=" * 80)
    
    # 执行扩展名修改
    result = change_extensions_to_txt(source_directory)
    
    if result:
        # 验证结果
        extension_count = verify_results(source_directory)
        
        # 显示示例文件
        show_sample_files(source_directory, sample_count=10)
        
        print(f"\n📁 处理完成的文件位置: {source_directory}")
        print("🎯 所有文件扩展名修改完成！")
    else:
        print("❌ 处理失败")
