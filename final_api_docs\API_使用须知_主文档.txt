# 使用须知

## 描述
候鸟浏览器API使用前的准备工作和基础信息，包括获取API凭证、查看凭证信息和获取环境ID的详细步骤。

## 基础介绍
候鸟浏览器支持本地API的功能，帮助用户通过程序化的方式来启动和关闭浏览器等基础API功能，还可以配合Selenium和Puppeteer等自动化框架来实现浏览器操作的自动化。

**重要提示**: 后续客户端API将采用HTTP模式连接持续更新。

## 使用前准备

使用前请根据以下指引完成操作并获取信息，然后参照[HTTP模式说明](/api/http)启动客户端并开始使用API。

## 1. 获取API凭证

### 凭证说明
- API启动候鸟浏览器需要占用1个API凭证
- 1个API凭证同一时间只能允许1台设备使用
- **使用前请确保账号至少有1个API凭证**

### 申请限制
- **非团队版用户**: 只能申请1个凭证
- **团队版用户**: 仅限主账户申请，可申请的凭证数量等于团队成员总数

### 申请步骤
1. 登录候鸟浏览器控制台
2. 进入API管理页面
3. 点击"申请API凭证"
4. 等待审核通过

## 2. 查看API凭证

### 获取步骤
1. 打开候鸟控制台
2. 点击"API" - "查看凭证"
3. 获取 **APP_ID** 和 **APP_KEY**

### 凭证信息
- **APP_ID**: 应用程序标识符
- **APP_KEY**: 应用程序密钥
- **重要**: 请妥善保管这两个信息，不要泄露给他人

### 示例格式
```
APP_ID: 7e147176e1d756eb03c0e18e7b640c23
APP_KEY: kwMTYxNG_MzlkZjhlZmM2YWNiY2M5MTkw
```

## 3. 获取环境ID

### 获取方式

#### 方式一：通过控制台界面
1. 登录候鸟浏览器控制台
2. 进入环境管理页面
3. 查看环境列表中的Session_ID（环境ID）

#### 方式二：通过API接口
使用"获取环境列表接口"获取：
- **Path**: /api/v1/session/listid
- **Method**: POST
- **说明**: 详见[环境管理文档](/api/session)

### Session_ID说明
- **格式**: 32位字符串，如：373808cb37bd63f5f7d92415e736e85f
- **用途**: API通过Session_ID（环境ID）打开指定环境
- **获取**: 可通过控制台界面或API接口获取

## 快速开始流程

1. **准备阶段**
   - 确保有API凭证
   - 获取APP_ID和APP_KEY
   - 获取要操作的环境Session_ID

2. **启动阶段**
   - 参照[HTTP模式说明](/api/http)启动客户端
   - 使用[帐号登录接口](/api/login)进行认证

3. **使用阶段**
   - 调用各种API接口进行环境操作
   - 参考[错误码对照表](/api/code)处理错误

## 重要注意事项

1. **凭证限制**: 一个API凭证同时只能在一台设备上使用
2. **版本要求**: 客户端版本需要V3.9.2.114以上
3. **网络要求**: 确保本地网络可以访问127.0.0.1:8186
4. **权限要求**: 启动时可能需要管理员权限

## 常见问题

### Q: 如何知道我有多少个API凭证？
A: 登录控制台，在API管理页面可以查看当前凭证数量和使用状态。

### Q: API凭证可以在多台设备同时使用吗？
A: 不可以，一个凭证同时只能在一台设备上使用。

### Q: 忘记了APP_ID和APP_KEY怎么办？
A: 登录控制台，在API-查看凭证页面可以重新查看。

### Q: 环境ID在哪里查看？
A: 可以在控制台的环境管理页面查看，或通过API接口获取。

## 相关链接

- [进入控制台](https://www.mbbrowser.com/console/)
- [HTTP模式说明](/api/http)
- [帐号登录接口](/api/login)
- [环境管理接口](/api/session)
- [错误码对照表](/api/code)
- [常见问题](/api/question)
