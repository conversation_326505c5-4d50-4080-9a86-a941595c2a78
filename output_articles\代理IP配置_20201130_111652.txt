标题: 代理IP配置
英文标题: Proxy IP Configuration
ID: 30
分类ID: 7
添加时间: 1606706212
更新时间: 1689909895
访问次数: 0
SEO标题: 候鸟浏览器代理IP配置
SEO关键词: 候鸟浏览器代理IP配置
SEO描述: 候鸟浏览器代理IP配置

================================================== 内容 ==================================================
1、在候鸟浏览器主面板中“新建环境配置”或在已建有的环境上右键“修改”打开环境配置界面

![](6d7b2882624511f09a0d0242ac130006/images/image_ee6a136e6098.png)

2、在弹出的环境配置窗口，选择代理方式和代理信息，填写完毕后可点击“检查代理”进行代理检测

![](6d7b2882624511f09a0d0242ac130006/images/image_4edbdb7e6d26.png)

3、点击“自定义代理”可以预设代理信息和批量导入代理IP，导入后可以点击“自动填入代理”选择已导入的代理配置

![](6d7b2882624511f09a0d0242ac130006/images/image_d7999fc9367d.png)

![](6d7b2882624511f09a0d0242ac130006/images/image_dbebb254cfc1.png)

================================================== 英文内容 ==================================================
1. On the main panel of Mbbrowser, choose Create New Session or right-click an existing environment and choose Modify to open the environment configuration page

![](6d7b2882624511f09a0d0242ac130006/images/image_0c962c5ae498.png)

2. In the pop-up environment configuration window, select the agent mode and agent information, and click "Test Proxy" to check the agent

![](6d7b2882624511f09a0d0242ac130006/images/image_bb3f065b17ad.png)

3. Click Add Proxy to set proxy information and import proxy IP addresses in batches. After the import, click Add History to select the imported proxy configuration

![](6d7b2882624511f09a0d0242ac130006/images/image_f93c65501022.png)

![](6d7b2882624511f09a0d0242ac130006/images/image_2ab65b1fa10e.png)