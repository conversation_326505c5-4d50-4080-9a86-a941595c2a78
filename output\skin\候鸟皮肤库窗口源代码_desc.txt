﻿<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Window size="500,400" caption="0,0,0,40" roundcorner="5,5,5,5" showshadow="true" shadowsize="3" shadowposition="0,0" shadowcolor="#ff6272A4" fademode="true">
	<Include source="Default.xml" />

  <VerticalLayout width="953" height="590" bkcolor="#FF282A36">

    <HorizontalLayout height="37">

    	 <Button name="login" bkimage="icon.png" padding="8,6,0,0" width="28" height="28"/>
      <Label name="Wndtitle" padding="6,4,0,0" text="添加注释" endellipsis="true" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" align="left" font="8"></Label>

      <Control width="10"/>
      <Button name="minbtn" width="28" height="26" tooltip="最小化" normalimage="minbtn.png" hotimage="minbtn_hover.png" pushedimage="minbtnpush.png" />
      <Button name="closebtn" width="28" height="26"  tooltip="关闭" normalimage="closebtn.png" hotimage="closebtn_hover.png" pushedimage="closebtnpush.png" />

    </HorizontalLayout>

  <HorizontalLayout name="bkground" visible="true">

		<VerticalLayout height="603">


			<HorizontalLayout height="246" padding="16,20,0,2">

					<VerticalLayout name="groupname_title" width="76">
               <Label name="notesl" padding="0,12,0,0" text="环境注释：" textcolor="#FFF8F8F2" hottextcolor="#FFBD93F9" font="8"></Label>
          </VerticalLayout>

			    	 <VerticalLayout width="364">
				         <RichEdit name="groupname" maxchar="1000" vscrollbar="true" padding="0,10,0,2" height="232" width="364" tipvaluecolor="#FFF8F8F2" borderround="3,3" bkcolor="#FF44475A" font="8" textpadding="10,6,4,0" tipvalue="请输入会话环境注释" textcolor="#FFF8F8F2" wantreturn="true" rich="false" transparent="false">
				      </RichEdit>
				     </VerticalLayout>

			</HorizontalLayout>

      <HorizontalLayout width="500" height="26">
        <Control />
        <Button name="addusername" padding="50,0,0,0" align="left" height="20" width="120" text="添加备注：登录账号" font="23" textcolor="#FFF1FA8C" />
        <Control width="32" />
      </HorizontalLayout>

      <HorizontalLayout inset="0,0,0,0" height="403">

				</HorizontalLayout>






		</VerticalLayout>


	</HorizontalLayout>




    <HorizontalLayout height="52" bkcolor="#FF44475A">
         <Control />
      <!--<VerticalLayout width="420">

       </VerticalLayout>-->
        <VerticalLayout width="140">
      		<Control />
          <Button text="确定" name="btnok"   padding="16,2,0,0" width="120" height="30" textcolor="#FFF8F8F2" disabledtextcolor="#FF6272A4" align="center" font="14"	bkimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
          <Control />
       </VerticalLayout>

<Control />
    </HorizontalLayout>
  </VerticalLayout>
</Window>
