#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
候鸟浏览器API文档抓取器
抓取 https://www.mbbrowser.com/api/ 下所有页面并保存为docx文件
"""

import requests
import re
import time
import os
from urllib.parse import urljoin, urlparse
from pathlib import Path
import logging
from bs4 import BeautifulSoup
from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('get_website_page.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class APIDocumentScraper:
    def __init__(self, base_url='https://www.mbbrowser.com/api/', output_dir='api_docs'):
        self.base_url = base_url
        self.output_dir = output_dir
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        
        # 创建输出目录
        Path(self.output_dir).mkdir(exist_ok=True)
        
        # 定义要抓取的页面列表（基于分析的左侧栏目）
        self.api_pages = [
            # API使用须知
            {'url': '/api/', 'title': '简介'},
            {'url': '/api/help', 'title': '使用须知'},
            {'url': '/api/http', 'title': 'HTTP模式说明'},
            {'url': '/api/question', 'title': '常见问题'},
            
            # API接口文档
            {'url': '/api/login', 'title': '帐号登录'},
            {'url': '/api/members', 'title': '获取成员列表'},
            {'url': '/api/browser', 'title': '环境开启关闭'},
            {'url': '/api/session', 'title': '环境管理'},
            {'url': '/api/group', 'title': '分组管理'},
            {'url': '/api/script', 'title': '脚本管理'},
            {'url': '/api/plugin', 'title': '插件管理'},
            {'url': '/api/appendix', 'title': '附录'},
            {'url': '/api/code', 'title': '错误码对照表'},
            
            # POSTMAN工具
            {'url': '/api/postman', 'title': 'POSTMAN下载及安装'},
            {'url': '/api/postman-debug', 'title': 'POSTMAN调试候鸟API接口'},
            {'url': '/api/postman-example', 'title': '调试接口JSON数据官方更新下载'},
            
            # 其他
            {'url': '/api/example', 'title': '多种语言脚本示例'},
            {'url': '/api/json', 'title': 'JSON在线格式化工具'}
        ]
    
    def sanitize_filename(self, filename):
        """清理文件名，移除不合法字符"""
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        filename = filename.replace('/', '_').replace('\\', '_')
        if len(filename) > 200:
            filename = filename[:200]
        return filename.strip()
    
    def fetch_page_content(self, url):
        """获取页面内容"""
        try:
            full_url = urljoin(self.base_url, url)
            logger.info(f"正在获取页面: {full_url}")
            
            response = self.session.get(full_url, timeout=30)
            response.raise_for_status()
            response.encoding = 'utf-8'
            
            return response.text
            
        except Exception as e:
            logger.error(f"获取页面失败: {url}, 错误: {str(e)}")
            return None
    
    def parse_content(self, html_content):
        """解析HTML内容，提取主要内容"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')

            # 移除不需要的元素
            for element in soup(['script', 'style', 'nav', 'header', 'footer', 'noscript']):
                element.decompose()

            # 移除导航和菜单相关的元素
            for element in soup.find_all(['div'], class_=lambda x: x and any(nav in str(x).lower() for nav in ['nav', 'menu', 'sidebar', 'header', 'footer'])):
                element.decompose()

            # 获取页面的主体内容
            main_content = soup.find('body')
            if not main_content:
                return "无法解析页面内容"

            # 提取所有文本内容，保持结构
            content_parts = []

            # 递归处理所有元素
            def process_element(element, level=0):
                if element.name in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
                    text = element.get_text().strip()
                    if text and len(text) > 1:  # 过滤掉单字符标题
                        header_level = int(element.name[1:])
                        content_parts.append(f"\n{'#' * header_level} {text}\n")

                elif element.name == 'p':
                    text = element.get_text().strip()
                    if text and len(text) > 3:  # 过滤掉过短的段落
                        content_parts.append(f"{text}\n\n")

                elif element.name in ['pre', 'code']:
                    text = element.get_text().strip()
                    if text:
                        if element.name == 'pre':
                            content_parts.append(f"\n```\n{text}\n```\n\n")
                        else:
                            content_parts.append(f"`{text}`")

                elif element.name in ['ul', 'ol']:
                    # 处理列表
                    for li in element.find_all('li', recursive=False):
                        li_text = li.get_text().strip()
                        if li_text and len(li_text) > 2:
                            content_parts.append(f"• {li_text}\n")
                    content_parts.append("\n")

                elif element.name == 'table':
                    content_parts.append("\n**表格内容：**\n")
                    for row in element.find_all('tr'):
                        row_data = []
                        for cell in row.find_all(['td', 'th']):
                            cell_text = cell.get_text().strip()
                            if cell_text:
                                row_data.append(cell_text)
                        if row_data:
                            content_parts.append("| " + " | ".join(row_data) + " |\n")
                    content_parts.append("\n")

                elif element.name == 'blockquote':
                    text = element.get_text().strip()
                    if text:
                        content_parts.append(f"\n> {text}\n\n")

                elif element.name == 'div':
                    # 对于div元素，检查是否包含重要内容
                    text = element.get_text().strip()
                    if text and len(text) > 10:
                        # 检查是否是代码块或特殊内容
                        if any(keyword in text.lower() for keyword in ['json', 'http', 'api', 'post', 'get']):
                            content_parts.append(f"\n{text}\n\n")
                        elif not any(child.name in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'ul', 'ol', 'table', 'pre', 'code'] for child in element.find_all()):
                            # 如果div没有包含其他结构化元素，直接添加文本
                            content_parts.append(f"{text}\n\n")

                # 递归处理子元素（除了已经处理过的）
                if element.name not in ['pre', 'code', 'ul', 'ol', 'table']:
                    for child in element.children:
                        if hasattr(child, 'name') and child.name:
                            process_element(child, level + 1)

            # 开始处理
            process_element(main_content)

            # 清理和格式化结果
            result = ''.join(content_parts)

            # 移除多余的空行
            lines = result.split('\n')
            cleaned_lines = []
            prev_empty = False

            for line in lines:
                line = line.strip()
                if not line:
                    if not prev_empty:
                        cleaned_lines.append('')
                    prev_empty = True
                else:
                    cleaned_lines.append(line)
                    prev_empty = False

            # 确保内容不为空
            final_result = '\n'.join(cleaned_lines).strip()
            if not final_result or len(final_result) < 100:
                # 如果解析结果太短，使用简单的文本提取
                return soup.get_text().strip()

            return final_result

        except Exception as e:
            logger.error(f"解析HTML内容失败: {str(e)}")
            # 备用方案：直接提取所有文本
            try:
                soup = BeautifulSoup(html_content, 'html.parser')
                return soup.get_text().strip()
            except:
                return "解析内容失败"
    
    def create_docx_document(self, title, content):
        """创建docx文档"""
        try:
            doc = Document()
            
            # 添加标题
            title_paragraph = doc.add_heading(title, 0)
            title_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # 添加分隔线
            doc.add_paragraph("=" * 50)
            
            # 处理内容
            lines = content.split('\n')
            current_paragraph = None
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                # 处理标题
                if line.startswith('#'):
                    level = len(line) - len(line.lstrip('#'))
                    title_text = line.lstrip('# ').strip()
                    if title_text:
                        doc.add_heading(title_text, level)
                
                # 处理代码块
                elif line.startswith('```'):
                    continue  # 跳过代码块标记
                
                # 处理列表项
                elif line.startswith('•'):
                    doc.add_paragraph(line[1:].strip(), style='List Bullet')
                
                # 处理表格标记
                elif line.startswith('[表格内容]'):
                    doc.add_paragraph(line, style='Intense Quote')
                
                # 处理表格行
                elif '|' in line and len(line.split('|')) > 1:
                    doc.add_paragraph(line, style='List Number')
                
                # 普通段落
                else:
                    doc.add_paragraph(line)
            
            return doc
            
        except Exception as e:
            logger.error(f"创建docx文档失败: {str(e)}")
            return None
    
    def save_page_as_docx(self, page_info):
        """保存单个页面为docx文件"""
        try:
            url = page_info['url']
            title = page_info['title']
            
            # 获取页面内容
            html_content = self.fetch_page_content(url)
            if not html_content:
                return False
            
            # 解析内容
            content = self.parse_content(html_content)
            
            # 创建docx文档
            doc = self.create_docx_document(title, content)
            if not doc:
                return False
            
            # 保存文件
            filename = f"API_{self.sanitize_filename(title)}.docx"
            filepath = os.path.join(self.output_dir, filename)
            
            doc.save(filepath)
            logger.info(f"已保存文档: {filename}")
            
            return True
            
        except Exception as e:
            logger.error(f"保存页面失败: {page_info}, 错误: {str(e)}")
            return False
    
    def scrape_all_pages(self):
        """抓取所有页面"""
        logger.info("开始抓取API文档页面...")
        
        success_count = 0
        total_count = len(self.api_pages)
        
        for i, page_info in enumerate(self.api_pages, 1):
            logger.info(f"正在处理第 {i}/{total_count} 个页面: {page_info['title']}")
            
            if self.save_page_as_docx(page_info):
                success_count += 1
            
            # 避免请求过于频繁
            time.sleep(2)
        
        logger.info(f"抓取完成! 总页面数: {total_count}, 成功: {success_count}, 失败: {total_count - success_count}")

def main():
    """主函数"""
    print("候鸟浏览器API文档抓取器启动...")
    print("目标网站: https://www.mbbrowser.com/api/")
    print("="*60)
    
    scraper = APIDocumentScraper()
    scraper.scrape_all_pages()
    
    print("="*60)
    print("抓取完成!")
    print("- 文档文件: api_docs目录")
    print("- 日志文件: get_website_page.log")

if __name__ == "__main__":
    main()
