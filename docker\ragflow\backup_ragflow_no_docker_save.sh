#!/bin/bash

# RAGFlow备份脚本（不使用docker save）
# 解决Docker临时目录空间不足问题

set -e

# 配置变量
BACKUP_BASE_DIR="/mnt/ragflow/temp3"
BACKUP_DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="$BACKUP_BASE_DIR/ragflow-backup-$BACKUP_DATE"
RAGFLOW_DIR="/mnt/ragflow"
DOCKER_COMPOSE_DIR="$RAGFLOW_DIR/ragflow/docker"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 创建备份目录
create_backup_dir() {
    log_info "创建备份目录: $BACKUP_DIR"
    mkdir -p "$BACKUP_DIR"
    log_success "备份目录创建完成"
}

# 停止RAGFlow服务
stop_ragflow() {
    log_info "停止RAGFlow服务..."
    cd "$DOCKER_COMPOSE_DIR"
    
    if docker-compose ps | grep -q "Up"; then
        docker-compose down
        log_success "RAGFlow服务已停止"
    else
        log_info "RAGFlow服务未运行"
    fi
}

# 备份配置文件（完整版本）
backup_config() {
    log_info "备份RAGFlow配置文件..."
    
    # 显示目录大小
    RAGFLOW_SIZE=$(du -sh "$RAGFLOW_DIR" | cut -f1)
    log_info "RAGFlow目录大小: $RAGFLOW_SIZE"
    
    # 备份整个ragflow目录，排除备份目录自身和其他不必要的文件
    log_info "开始打包完整配置目录（25GB）..."
    log_info "这可能需要几分钟时间，请耐心等待..."
    
    # 使用后台进程显示进度
    tar czf "$BACKUP_DIR/ragflow-config.tar.gz" \
      --exclude='*/temp3/*' \
      --exclude='*/logs/*.log' \
      --exclude='*/.git/*' \
      --exclude='*/node_modules/*' \
      --exclude='*/cache/*' \
      --exclude='*/tmp/*' \
      --exclude='*/__pycache__/*' \
      --exclude='*.tar.gz' \
      --exclude='*.tar' \
      -C "$(dirname $RAGFLOW_DIR)" "$(basename $RAGFLOW_DIR)" &
    
    # 显示进度
    TAR_PID=$!
    while kill -0 $TAR_PID 2>/dev/null; do
        if [ -f "$BACKUP_DIR/ragflow-config.tar.gz" ]; then
            CURRENT_SIZE=$(du -sh "$BACKUP_DIR/ragflow-config.tar.gz" 2>/dev/null | cut -f1 || echo "0")
            echo -ne "\r正在打包... 当前大小: $CURRENT_SIZE"
        else
            echo -ne "\r正在打包... 准备中"
        fi
        sleep 3
    done
    echo ""  # 换行
    
    wait $TAR_PID
    
    # 单独备份重要配置文件作为快速访问
    cp "$DOCKER_COMPOSE_DIR/docker-compose.yml" "$BACKUP_DIR/"
    [ -f "$DOCKER_COMPOSE_DIR/.env" ] && cp "$DOCKER_COMPOSE_DIR/.env" "$BACKUP_DIR/"
    
    CONFIG_SIZE=$(du -sh "$BACKUP_DIR/ragflow-config.tar.gz" | cut -f1)
    log_success "完整配置文件备份完成: $CONFIG_SIZE"
}

# 创建Docker镜像列表（不备份镜像文件）
create_docker_image_list() {
    log_info "创建Docker镜像列表..."
    
    # 获取RAGFlow相关镜像
    IMAGES=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep -E "(ragflow|mysql|minio|elasticsearch|valkey)")
    
    if [ -n "$IMAGES" ]; then
        # 创建镜像列表文件
        cat > "$BACKUP_DIR/docker-images-list.txt" << EOF
# RAGFlow所需Docker镜像列表
# 在目标服务器上使用以下命令拉取镜像：
# 
# 方法1: 逐个拉取
$(echo "$IMAGES" | sed 's/^/docker pull /')

# 方法2: 批量拉取脚本
#!/bin/bash
echo "拉取RAGFlow所需镜像..."
EOF
        
        echo "$IMAGES" | while read image; do
            echo "docker pull $image" >> "$BACKUP_DIR/docker-images-list.txt"
        done
        
        cat >> "$BACKUP_DIR/docker-images-list.txt" << EOF
echo "所有镜像拉取完成！"

# 镜像详细信息：
EOF
        
        # 添加镜像详细信息
        docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}" | grep -E "(REPOSITORY|ragflow|mysql|minio|elasticsearch|valkey)" >> "$BACKUP_DIR/docker-images-list.txt"
        
        chmod +x "$BACKUP_DIR/docker-images-list.txt"
        
        log_success "Docker镜像列表创建完成: $(echo "$IMAGES" | wc -l) 个镜像"
        log_info "镜像列表保存在: docker-images-list.txt"
    else
        log_warning "未找到RAGFlow相关镜像"
    fi
}

# 备份Docker数据卷
backup_docker_volumes() {
    log_info "备份Docker数据卷..."
    
    VOLUMES=$(docker volume ls -q | grep ragflow)
    
    if [ -n "$VOLUMES" ]; then
        for volume in $VOLUMES; do
            log_info "备份数据卷: $volume"
            
            # 检查数据卷大小
            VOLUME_SIZE=$(docker run --rm -v $volume:/data --tmpfs /tmp:size=100M alpine du -s /data | cut -f1)
            
            if [ "$VOLUME_SIZE" -gt 0 ]; then
                # 特别检查MinIO数据卷中的文档
                if [[ "$volume" == *"minio"* ]]; then
                    log_info "检查MinIO数据卷中的文档..."
                    DOC_COUNT=$(docker run --rm -v $volume:/data --tmpfs /tmp:size=100M alpine find /data -type f \( -name "*.pdf" -o -name "*.doc*" -o -name "*.txt" -o -name "*.md" -o -name "*.ppt*" -o -name "*.xls*" \) | wc -l)
                    TOTAL_FILES=$(docker run --rm -v $volume:/data --tmpfs /tmp:size=100M alpine find /data -type f | wc -l)
                    log_info "MinIO数据卷统计: 总文件数=$TOTAL_FILES, 文档文件数=$DOC_COUNT"
                fi
                
                # 备份数据卷，使用临时文件系统
                docker run --rm \
                    -v $volume:/source \
                    -v "$BACKUP_DIR":/backup \
                    --tmpfs /tmp:size=500M \
                    alpine tar czf /backup/${volume}.tar.gz -C /source .
                
                BACKUP_SIZE=$(du -sh "$BACKUP_DIR/${volume}.tar.gz" | cut -f1)
                log_success "数据卷 $volume 备份完成: $BACKUP_SIZE"
            else
                log_warning "数据卷 $volume 为空"
                touch "$BACKUP_DIR/${volume}.tar.gz"
            fi
        done
    else
        log_warning "未找到RAGFlow相关数据卷"
    fi
}

# 创建备份信息文件
create_backup_info() {
    log_info "创建备份信息文件..."
    
    cat > "$BACKUP_DIR/backup-info.txt" << EOF
RAGFlow完整备份信息
==================
备份时间: $(date)
源服务器: $(hostname -I | awk '{print $1}')
备份脚本: 无Docker镜像版本（解决空间不足问题）
RAGFlow安装路径: $RAGFLOW_DIR

重要说明:
由于Docker临时目录空间不足，此备份不包含Docker镜像文件
请在目标服务器上手动拉取镜像，详见 docker-images-list.txt

系统信息:
- 操作系统: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)
- Docker版本: $(docker --version)

备份内容:
- 完整配置目录: ragflow-config.tar.gz (包含整个25GB RAGFlow目录)
- 数据卷备份: $(ls $BACKUP_DIR/*-data.tar.gz 2>/dev/null | wc -l) 个文件
- 快速配置: docker-compose.yml, .env
- 镜像列表: docker-images-list.txt

备份文件大小:
$(du -sh $BACKUP_DIR/* 2>/dev/null | head -10)

MinIO文档统计:
$(docker run --rm -v docker_ragflow-minio-data:/data --tmpfs /tmp:size=100M alpine sh -c "
echo '总文件数:' \$(find /data -type f | wc -l)
echo '文档文件数:' \$(find /data -type f \( -name '*.pdf' -o -name '*.doc*' -o -name '*.txt' -o -name '*.md' -o -name '*.ppt*' -o -name '*.xls*' \) | wc -l)
echo '数据卷大小:' \$(du -sh /data | cut -f1)
" 2>/dev/null || echo "无法获取MinIO统计信息")

Docker镜像列表:
$(docker images --format "{{.Repository}}:{{.Tag}} {{.Size}}" | grep -E "(ragflow|mysql|minio|elasticsearch|valkey)")

API配置:
- API密钥: ragflow-UzYTlhMjIwNjI0YzExZjA4NzUyMDI0Mm
- MCP Server: 已启用
- 端口配置: 80, 443, 9380, 9382

恢复说明:
1. 在目标服务器安装Docker和Docker Compose
2. 拉取所需镜像: 执行 docker-images-list.txt 中的命令
3. 解压并恢复配置: tar xzf ragflow-config.tar.gz -C /mnt/
4. 创建并恢复数据卷
5. 修改IP地址配置
6. 启动服务
EOF
    
    log_success "备份信息文件创建完成"
}

# 创建最终备份包
create_final_backup() {
    log_info "创建最终备份包..."
    
    cd "$BACKUP_BASE_DIR"
    FINAL_BACKUP="ragflow-complete-backup-no-images-$BACKUP_DATE.tar.gz"
    
    tar czf "$FINAL_BACKUP" "$(basename $BACKUP_DIR)"
    
    # 计算备份包大小
    BACKUP_SIZE=$(du -sh "$FINAL_BACKUP" | cut -f1)
    
    log_success "最终备份包创建完成:"
    echo "  文件名: $FINAL_BACKUP"
    echo "  大小: $BACKUP_SIZE"
    echo "  路径: $BACKUP_BASE_DIR/$FINAL_BACKUP"
    
    # 创建符号链接
    ln -sf "$BACKUP_BASE_DIR/$FINAL_BACKUP" "/tmp/ragflow-latest-backup.tar.gz" 2>/dev/null || true
}

# 重启RAGFlow服务
restart_ragflow() {
    log_info "重启RAGFlow服务..."
    cd "$DOCKER_COMPOSE_DIR"
    docker-compose up -d
    log_success "RAGFlow服务已重启"
}

# 清理临时文件
cleanup() {
    log_info "清理临时备份目录..."
    rm -rf "$BACKUP_DIR"
    log_success "清理完成"
}

# 主函数
main() {
    echo "========================================"
    echo "  RAGFlow完整备份脚本（无Docker镜像）"
    echo "========================================"
    echo "备份时间: $(date)"
    echo "备份目录: $BACKUP_DIR"
    echo "备份内容: 完整25GB目录 + 数据卷 + 镜像列表"
    echo "说明: 由于空间限制，不备份Docker镜像文件"
    echo "========================================"
    
    create_backup_dir
    stop_ragflow
    backup_config
    create_docker_image_list
    backup_docker_volumes
    create_backup_info
    create_final_backup
    restart_ragflow
    cleanup
    
    echo "========================================"
    log_success "RAGFlow备份完成！"
    echo "========================================"
    echo "备份文件: $BACKUP_BASE_DIR/ragflow-complete-backup-no-images-$BACKUP_DATE.tar.gz"
    echo "快捷链接: /tmp/ragflow-latest-backup.tar.gz"
    echo ""
    echo "传输命令:"
    echo "scp $BACKUP_BASE_DIR/ragflow-complete-backup-no-images-$BACKUP_DATE.tar.gz root@58.49.146.17:/mnt/ragflow-backup/"
    echo ""
    echo "⚠️  重要提醒:"
    echo "此备份不包含Docker镜像，请在目标服务器上："
    echo "1. 解压备份文件"
    echo "2. 执行 docker-images-list.txt 中的命令拉取镜像"
    echo "3. 然后进行恢复"
    echo "========================================"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
