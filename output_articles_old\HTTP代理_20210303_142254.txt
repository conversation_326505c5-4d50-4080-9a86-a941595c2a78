标题: HTTP代理
英文标题: HTTP Proxy
ID: 101
分类ID: 25
添加时间: 1614752574
更新时间: 1685430199
访问次数: 0
SEO标题: HTTP代理协议
SEO关键词: HTTP代理协议
SEO描述: HTTP代理协议

================================================== 内容 ==================================================
HTTP协议即超文本传输协议，是Internet上进行信息传输时使用最为广泛的一种非常简单的通信协议。部分局域网对协议进行了限制，只允许用户通过HTTP协议访问外部网站。


HTTP功能支持“直接连接”和通过”HTTP代理“形式的连接。选择其中的何种形式，要视用户所在的局域网（或其它上网环境）的具体情况。


以QQ为例：简单地说，如果用户所在局域网并未设置任何代理服务器，则选择“直接连接”就可以实现QQ连通；而如果用户所在局域网设置了代理服务器，则必须选择“通过HTTP代理”，并填写所在局域网的代理服务器地址、端口等详细资料，方有可能实现QQ连通。

================================================== 英文内容 ==================================================
HTTP (Hypertext Transfer Protocol) is a very simple communication protocol that is most widely used for information transmission on the Internet. Some Lans restrict the protocol and only allow users to access external websites through HTTP.


The HTTP functionality supports both direct connections and connections through an HTTP proxy. Which one you choose depends on your LAN (or other Internet access environment).


Take QQ as an example: Simply put, if the LAN where the user is not set up any proxy server, then select "direct connection" to achieve QQ connection; If the user's LAN is configured with a proxy server, you must select "through HTTP proxy", and fill in the LAN proxy server address, port and other details, it is possible to achieve QQ connectivity.