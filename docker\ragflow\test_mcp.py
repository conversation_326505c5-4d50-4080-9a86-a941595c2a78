#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RAGFlow MCP Server 测试脚本
模拟外部AI客户端与RAGFlow MCP Server的完整交互流程
"""

import json
import uuid
import time
import requests
from typing import Dict, List, Any, Optional


class RAGFlowMCPClient:
    """RAGFlow MCP客户端，模拟外部AI应用"""
    
    def __init__(self, base_url: str = "http://58.49.146.17:9382", api_key: str = "ragflow-UzYTlhMjIwNjI0YzExZjA4NzUyMDI0Mm"):
        self.base_url = base_url
        self.api_key = api_key
        self.session_id = str(uuid.uuid4())
        self.request_id = 0
        
        # 设置请求头
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}",
            "User-Agent": "RAGFlow-MCP-Test-Client/1.0"
        }
        
        print(f"🤖 RAGFlow MCP客户端初始化")
        print(f"   服务器地址: {self.base_url}")
        print(f"   会话ID: {self.session_id}")
        print(f"   API密钥: {self.api_key}")
    
    def _get_next_request_id(self) -> int:
        """获取下一个请求ID"""
        self.request_id += 1
        return self.request_id
    
    def _send_mcp_request(self, method: str, params: Optional[Dict] = None) -> Dict:
        """发送MCP协议请求"""
        request_data = {
            "jsonrpc": "2.0",
            "id": self._get_next_request_id(),
            "method": method
        }
        
        if params:
            request_data["params"] = params
        
        print(f"\n📤 发送MCP请求:")
        print(f"   方法: {method}")
        print(f"   请求ID: {request_data['id']}")
        if params:
            print(f"   参数: {json.dumps(params, ensure_ascii=False, indent=2)}")
        
        try:
            # 使用messages端点发送请求
            url = f"{self.base_url}/messages/?session_id={self.session_id}"
            response = requests.post(url, headers=self.headers, json=request_data, timeout=30)
            
            print(f"📥 收到响应:")
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                response_data = response.json()
                print(f"   响应数据: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
                return response_data
            else:
                print(f"   错误响应: {response.text}")
                return {"error": f"HTTP {response.status_code}: {response.text}"}
                
        except requests.exceptions.RequestException as e:
            print(f"   请求异常: {str(e)}")
            return {"error": f"请求异常: {str(e)}"}
    
    def initialize(self) -> bool:
        """初始化MCP连接"""
        print(f"\n🔗 初始化MCP连接...")
        
        params = {
            "protocolVersion": "2024-11-05",
            "capabilities": {
                "tools": {}
            },
            "clientInfo": {
                "name": "RAGFlow-Test-Client",
                "version": "1.0.0"
            }
        }
        
        response = self._send_mcp_request("initialize", params)
        
        if "error" not in response and response.get("result"):
            print("✅ MCP连接初始化成功")
            return True
        else:
            print("❌ MCP连接初始化失败")
            return False
    
    def list_tools(self) -> List[Dict]:
        """获取可用工具列表"""
        print(f"\n🛠️ 获取可用工具列表...")
        
        response = self._send_mcp_request("tools/list")
        
        if "error" not in response and response.get("result"):
            tools = response["result"].get("tools", [])
            print(f"✅ 获取到 {len(tools)} 个工具:")
            
            for i, tool in enumerate(tools, 1):
                print(f"   {i}. {tool['name']}")
                print(f"      描述: {tool['description'][:100]}...")
                print(f"      必需参数: {tool['inputSchema']['required']}")
            
            return tools
        else:
            print("❌ 获取工具列表失败")
            return []
    
    def ask_question(self, question: str, dataset_ids: List[str] = None, document_ids: List[str] = None) -> str:
        """向RAGFlow提问并获取答案"""
        print(f"\n❓ 向RAGFlow提问: {question}")
        
        # 如果没有指定数据集，先获取所有可用数据集
        if not dataset_ids:
            print("   未指定数据集，将使用所有可用数据集")
            dataset_ids = self._get_all_dataset_ids()
        
        if not dataset_ids:
            return "❌ 无法获取数据集信息，无法进行检索"
        
        # 构建工具调用参数
        tool_params = {
            "name": "ragflow_retrieval",
            "arguments": {
                "dataset_ids": dataset_ids,
                "question": question
            }
        }
        
        if document_ids:
            tool_params["arguments"]["document_ids"] = document_ids
        
        print(f"   使用数据集: {dataset_ids}")
        if document_ids:
            print(f"   限制文档: {document_ids}")
        
        # 调用检索工具
        response = self._send_mcp_request("tools/call", tool_params)
        
        if "error" not in response and response.get("result"):
            content = response["result"].get("content", [])
            if content and len(content) > 0:
                # 提取文本内容
                answer_text = content[0].get("text", "")
                print(f"✅ 检索成功，获得 {len(content)} 个结果")
                return answer_text
            else:
                return "❌ 检索成功但未找到相关内容"
        else:
            error_msg = response.get("error", {}).get("message", "未知错误")
            print(f"❌ 检索失败: {error_msg}")
            return f"❌ 检索失败: {error_msg}"
    
    def _get_all_dataset_ids(self) -> List[str]:
        """获取所有可用数据集ID（通过工具描述解析）"""
        tools = self.list_tools()
        
        for tool in tools:
            if tool["name"] == "ragflow_retrieval":
                description = tool["description"]
                # 尝试从描述中解析数据集信息
                # 这里简化处理，实际应用中可能需要更复杂的解析
                print("   从工具描述中解析数据集信息...")
                
                # 如果描述中包含数据集信息，尝试解析
                if "dataset" in description.lower():
                    # 这里返回一个示例数据集ID，实际使用时需要根据具体情况调整
                    return ["default-dataset"]
        
        return []
    
    def test_connectivity(self) -> bool:
        """测试MCP服务器连通性"""
        print(f"\n🔍 测试MCP服务器连通性...")
        
        try:
            # 测试SSE端点
            sse_response = requests.get(f"{self.base_url}/sse", timeout=10)
            print(f"   SSE端点状态: {sse_response.status_code}")
            
            # 测试messages端点
            messages_url = f"{self.base_url}/messages/?session_id={self.session_id}"
            messages_response = requests.get(messages_url, timeout=10)
            print(f"   Messages端点状态: {messages_response.status_code}")
            
            return sse_response.status_code == 200 or messages_response.status_code in [200, 400]
            
        except Exception as e:
            print(f"   连通性测试失败: {str(e)}")
            return False


def main():
    """主测试函数"""
    print("🚀 RAGFlow MCP Server 完整测试")
    print("=" * 60)
    
    # 初始化MCP客户端
    client = RAGFlowMCPClient()
    
    # 1. 测试连通性
    if not client.test_connectivity():
        print("❌ MCP服务器连通性测试失败，请检查服务器状态")
        return
    
    print("✅ MCP服务器连通性正常")
    
    # 2. 初始化MCP连接
    if not client.initialize():
        print("❌ MCP连接初始化失败")
        return
    
    # 3. 获取可用工具
    tools = client.list_tools()
    if not tools:
        print("❌ 无法获取工具列表")
        return
    
    # 4. 测试问题列表
    test_questions = [
        "候鸟浏览器如何配置代理？",
        "RAGFlow是什么？",
        "如何创建知识库？",
        "什么是向量检索？",
        "如何上传文档到系统中？"
    ]
    
    print(f"\n🧪 开始测试问答功能...")
    print(f"   准备测试 {len(test_questions)} 个问题")
    
    # 5. 逐个测试问题
    for i, question in enumerate(test_questions, 1):
        print(f"\n" + "─" * 50)
        print(f"📝 测试问题 {i}/{len(test_questions)}")
        
        # 提问并获取答案
        answer = client.ask_question(question)
        
        print(f"\n💬 问题: {question}")
        print(f"🤖 RAGFlow回答:")
        
        if answer.startswith("❌"):
            print(f"   {answer}")
        else:
            # 格式化显示答案（限制长度）
            if len(answer) > 500:
                print(f"   {answer[:500]}...")
                print(f"   [答案已截断，完整长度: {len(answer)} 字符]")
            else:
                print(f"   {answer}")
        
        # 添加延迟避免请求过快
        if i < len(test_questions):
            print(f"   ⏳ 等待 2 秒后继续下一个问题...")
            time.sleep(2)
    
    print(f"\n" + "=" * 60)
    print(f"🎉 RAGFlow MCP Server 测试完成！")
    print(f"   总共测试了 {len(test_questions)} 个问题")
    print(f"   会话ID: {client.session_id}")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(f"\n\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
