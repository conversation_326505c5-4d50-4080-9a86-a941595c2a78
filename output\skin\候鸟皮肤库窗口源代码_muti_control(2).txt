					        </HorizontalLayout>
					        <HorizontalLayout height="26">
					        	<Label name="win_num_title" padding="28,0,0,0" autocalcwidth="true" maxwidth="200" textpadding="8,0,8,0" texttooltip="true" endellipsis="true" text="窗口数量" align="center" borderround="7,7" font="3" textcolor="#ff373b40"></Label>
					        	<RichEdit name="win_num_value" padding="41,3,0,10" height="20" width="60" tipvaluecolor="#FF333333" borderround="7,7" bkcolor="#ffdce1e7" font="0" textpadding="6,2,0,0" maxchar="5" tipvalue="3" multiline="false" textcolor="#ff333333" rich="false" transparent="false"></RichEdit>
					        	<Label name="win_num_dec" padding="4,0,0,0" autocalcwidth="true" maxwidth="200" textpadding="2,0,8,0" texttooltip="true" endellipsis="true" text="个/行" align="center" borderround="7,7" font="5" textcolor="#ff373b40"></Label>
					        </HorizontalLayout>


					        <HorizontalLayout height="6">
					        </HorizontalLayout>

					        <HorizontalLayout height="26">
					        	<Label name="control_rule_title" padding="0,0,0,0" autocalcwidth="true" maxwidth="200" textpadding="8,0,8,0" texttooltip="true" endellipsis="true" text="排列方式" align="center" borderround="7,7" font="8" textcolor="#ff373b40"></Label>
					        	<CheckBox name="gg_list" width="18" height="18"  padding="10,4,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="true" />
		          			<Label name="gg_list_dec" padding="6,0,10,0" textpadding="0,0,20,0" text="宫格平铺" texttooltip="true" endellipsis="true" autocalcwidth="true" maxwidth="400" textcolor="#FF333333" hottextcolor="ffFF0000" font="3"></Label>
		          			<CheckBox name="cd_list" width="18" height="18"  padding="10,4,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="true" />
		          			<Label name="cd_list_dec" padding="6,0,10,0" textpadding="0,0,20,0" text="重叠平铺" texttooltip="true" endellipsis="true" autocalcwidth="true" maxwidth="400" textcolor="#FF333333" hottextcolor="ffFF0000" font="3"></Label>
					        </HorizontalLayout>

					        <HorizontalLayout inset="0,0,22,0" height="10"></HorizontalLayout>

					        <HorizontalLayout height="52">
		          				<Button name="Permutation" padding="8,8,0,0" height="41" width="460" text="一键排列 (Ctrl+Alt+Z)" textpadding="2,6,0,6" texttooltip="true" endellipsis="true" align="center" font="5" borderround="7,7" textcolor="#FFe6ebf2" hottextcolor="#FFFFFFFF" pushedtextcolor="FFe6ebf2" disabledtextcolor="#ffcccccc" bkcolor="#FF006fdf" hotbkcolor="#ff0055ab"	/>
									</HorizontalLayout>

					     </VerticalLayout>


					     <!--文本管理-->
					       <VerticalLayout inset="0,0,0,0" sepheight="1" scrollwheel="true" header="hidden" itemshowhtml="true" vscrollbar="true" scrollfbarFade="128">

					        <HorizontalLayout height="52">
		          				<Button name="clearText" padding="8,8,0,0" height="31" width="140" text="清空内容" textpadding="2,6,0,6" texttooltip="true" endellipsis="true" align="center" font="5" borderround="7,7" textcolor="#FFe6ebf2" hottextcolor="#FFFFFFFF" pushedtextcolor="FFe6ebf2" disabledtextcolor="#ffcccccc" bkcolor="#FF006fdf" hotbkcolor="#ff0055ab"	/>
		          				<Button name="pasteText" padding="26,8,0,0" height="31" width="140" text="粘帖内容" textpadding="2,6,0,6" texttooltip="true" endellipsis="true" align="center" font="5" borderround="7,7" textcolor="#FFe6ebf2" hottextcolor="#FFFFFFFF" pushedtextcolor="FFe6ebf2" disabledtextcolor="#ffcccccc" bkcolor="#FF006fdf" hotbkcolor="#ff0055ab"	/>
									</HorizontalLayout>

					        <HorizontalLayout inset="0,0,22,0" height="20"></HorizontalLayout>
					        <HorizontalLayout height="36">
					        	<Label name="muti_control_title" padding="0,0,0,0" autocalcwidth="true" maxwidth="200" textpadding="8,0,8,0" texttooltip="true" endellipsis="true" text="仿真操作" align="center" borderround="7,7" font="8" textcolor="#ff373b40"></Label>
					          <Button name="ip_provider_help" padding="2,10,0,0" height="15" width="15" tooltip="Proxy IP Provisioning Platform Provider" normalimage="file='helpbtn.png'" hotimage="file='helpbtn_hover.png'" pushedimage="file='helpbtn_push.png'"/>
					        </HorizontalLayout>

					        <HorizontalLayout height="22">
					        	<CheckBox name="Screen_rude_switch_delay_click" width="18" height="18"  padding="24,4,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="true" />
					        	<Label name="muti_control_title" padding="0,2,0,0" autocalcwidth="true" maxwidth="100" textpadding="8,0,8,0" texttooltip="true" endellipsis="true" text="延迟点击" align="center" borderround="7,7" font="8" textcolor="#ff373b40"></Label>
										<Button name="ip_provider_help" padding="0,5,0,0" height="15" width="15" tooltip="Proxy IP Provisioning Platform Provider" normalimage="file='helpbtn.png'" hotimage="file='helpbtn_hover.png'" pushedimage="file='helpbtn_push.png'"/>

										<CheckBox name="Screen_rude_switch_delay_input" width="18" height="18"  padding="24,4,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="true" />
					        	<Label name="muti_control_title" padding="0,2,0,0" autocalcwidth="true" maxwidth="100" textpadding="8,0,8,0" texttooltip="true" endellipsis="true" text="延迟输入" align="center" borderround="7,7" font="8" textcolor="#ff373b40"></Label>
										<Button name="ip_provider_help" padding="0,5,0,0" height="15" width="15" tooltip="Proxy IP Provisioning Platform Provider" normalimage="file='helpbtn.png'" hotimage="file='helpbtn_hover.png'" pushedimage="file='helpbtn_push.png'"/>
									</HorizontalLayout>

									 <HorizontalLayout height="6">
					        </HorizontalLayout>

									<HorizontalLayout height="36">
					        	<Label name="muti_control_title" padding="0,0,0,0" autocalcwidth="true" maxwidth="200" textpadding="8,0,8,0" texttooltip="true" endellipsis="true" text="随机数字" align="center" borderround="7,7" font="8" textcolor="#ff373b40"></Label>
					          <Button name="ip_provider_help" padding="2,10,0,0" height="15" width="15" tooltip="Proxy IP Provisioning Platform Provider" normalimage="file='helpbtn.png'" hotimage="file='helpbtn_hover.png'" pushedimage="file='helpbtn_push.png'"/>
					        </HorizontalLayout>


					        <HorizontalLayout height="40">

					        	<Label name="muti_control_title" padding="26,0,0,0" autocalcwidth="true" maxwidth="100" textpadding="8,0,8,0" texttooltip="true" endellipsis="true" text="范围" align="center" borderround="7,7" font="8" textcolor="#ff373b40"></Label>

										<RichEdit name="num_start" padding="5,3,0,10" height="36" width="100" tipvaluecolor="#FF333333" borderround="7,7" bkcolor="#ffdce1e7" font="33" textpadding="8,10,0,0" maxchar="8" text="0.001" multiline="false" textcolor="#ff333333" rich="false" transparent="false"></RichEdit>
					        	<Label name="win_start_posi_y_title" padding="4,0,0,0" width="8" textpadding="2,0,0,0" text="-" align="center" font="3" textcolor="#ff373b40"></Label>
					        	<RichEdit name="num_End" padding="5,3,0,10" height="36" width="100" tipvaluecolor="#FF333333" borderround="7,7" bkcolor="#ffdce1e7" font="33" textpadding="8,10,0,0" maxchar="8" text="0.009" multiline="false" textcolor="#ff333333" rich="false" transparent="false"></RichEdit>
										<Button name="InputRandnum" padding="26,4,0,0" height="34" width="140" text="输入 (Ctrl+Alt+F)" textpadding="2,6,0,6" texttooltip="true" endellipsis="true" align="center" font="5" borderround="7,7" textcolor="#FFe6ebf2" hottextcolor="#FFFFFFFF" pushedtextcolor="FFe6ebf2" disabledtextcolor="#ffcccccc" bkcolor="#FF006fdf" hotbkcolor="#ff0055ab"	/>
									</HorizontalLayout>





					        <HorizontalLayout height="6">
					        </HorizontalLayout>

					        <HorizontalLayout height="26">
					        	<Label name="control_rule_title" padding="0,0,0,0" autocalcwidth="true" maxwidth="200" textpadding="8,0,8,0" texttooltip="true" endellipsis="true" text="相同文本" align="center" borderround="7,7" font="8" textcolor="#ff373b40"></Label>
					        </HorizontalLayout>

					        <HorizontalLayout inset="0,0,22,0" height="6"></HorizontalLayout>

					        <HorizontalLayout height="104">
								    <VerticalLayout width="500">
						         <RichEdit name="same_Text" padding="34,0,0,0" height="60" width="426" tipvaluecolor="#FF333333" borderround="7,7" bkcolor="#ffdce1e7" font="0" textpadding="8,8,20,0" maxchar="300" tipvalue="请填入文本..." multiline="true" textcolor="#ff333333" rich="false" transparent="false" vscrollbar="true" hscrollbar="true">
						         </RichEdit>
										<Button name="InputSameText" padding="318,4,0,0" height="34" width="140" text="输入 (Ctrl+Alt+G)" textpadding="2,6,0,6" texttooltip="true" endellipsis="true" align="center" font="5" borderround="7,7" textcolor="#FFe6ebf2" hottextcolor="#FFFFFFFF" pushedtextcolor="FFe6ebf2" disabledtextcolor="#ffcccccc" bkcolor="#FF006fdf" hotbkcolor="#ff0055ab"	/>
						        </VerticalLayout>

				          </HorizontalLayout>

				        <HorizontalLayout height="6">
					        </HorizontalLayout>

					        <HorizontalLayout height="26">
					        	<Label name="control_rule_title" padding="0,0,0,0" autocalcwidth="true" maxwidth="200" textpadding="8,0,8,0" texttooltip="true" endellipsis="true" text="指定文本" align="center" borderround="7,7" font="8" textcolor="#ff373b40"></Label>
					        </HorizontalLayout>



					        <HorizontalLayout height="22">
					        	<Label name="control_rule_title" padding="34,0,0,0" autocalcwidth="true" maxwidth="200" textpadding="0,0,8,0" texttooltip="true" endellipsis="true" text="文本组1" align="center" borderround="7,7" font="5" textcolor="#ff373b40"></Label>
					        </HorizontalLayout>

					        <HorizontalLayout height="22">
					        	<CheckBox name="check_Order0" width="18" height="18"  padding="34,4,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="true" />
					        	<Label name="muti_control_title" padding="0,2,0,0" autocalcwidth="true" maxwidth="100" textpadding="8,0,8,0" texttooltip="true" endellipsis="true" text="顺序输入" align="center" borderround="7,7" font="8" textcolor="#ff373b40"></Label>
										<Button name="check_Order_provider_help" padding="0,5,0,0" height="15" width="15" tooltip="Proxy IP Provisioning Platform Provider" normalimage="file='helpbtn.png'" hotimage="file='helpbtn_hover.png'" pushedimage="file='helpbtn_push.png'"/>

										<CheckBox name="check_Randx0" width="18" height="18"  padding="24,4,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="false" />
					        	<Label name="muti_control_title" padding="0,2,0,0" autocalcwidth="true" maxwidth="100" textpadding="8,0,8,0" texttooltip="true" endellipsis="true" text="随机输入" align="center" borderround="7,7" font="8" textcolor="#ff373b40"></Label>
										<Button name="check_Rand_help" padding="0,5,0,0" height="15" width="15" tooltip="Proxy IP Provisioning Platform Provider" normalimage="file='helpbtn.png'" hotimage="file='helpbtn_hover.png'" pushedimage="file='helpbtn_push.png'"/>

										<CheckBox name="check_Point0" width="18" height="18"  padding="24,4,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="false" />
					        	<Label name="muti_control_title" padding="0,2,0,0" autocalcwidth="true" maxwidth="100" textpadding="8,0,8,0" texttooltip="true" endellipsis="true" text="指定输入" align="center" borderround="7,7" font="8" textcolor="#ff373b40"></Label>
										<Button name="check_Point_help" padding="0,5,0,0" height="15" width="15" tooltip="Proxy IP Provisioning Platform Provider" normalimage="file='helpbtn.png'" hotimage="file='helpbtn_hover.png'" pushedimage="file='helpbtn_push.png'"/>
									</HorizontalLayout>

					        <HorizontalLayout inset="0,0,22,0" height="8"></HorizontalLayout>

					        <HorizontalLayout height="124">
								    <VerticalLayout width="500">
						         <CustomRichEdit name="diff_Text0" padding="34,0,0,0" height="80" width="426" tipvaluecolor="#FF333333" borderround="7,7" bkcolor="#ffdce1e7" font="0" textpadding="8,8,20,0" maxchar="300" tipvalue="请填入文本..." multiline="true" textcolor="#ff333333" multiline="true" rich="true" wantreturn="true" wanttab="true" transparent="false" vscrollbar="true" hscrollbar="true" autovscroll="true">
						         </CustomRichEdit>
						         	<Button name="InputDiffText0" padding="318,4,0,0" height="34" width="140" text="输入 (Shift+F1)" textpadding="2,6,0,6" texttooltip="true" endellipsis="true" align="center" font="5" borderround="7,7" textcolor="#FFe6ebf2" hottextcolor="#FFFFFFFF" pushedtextcolor="FFe6ebf2" disabledtextcolor="#ffcccccc" bkcolor="#FF006fdf" hotbkcolor="#ff0055ab"	/>

						        </VerticalLayout>
				          </HorizontalLayout>


					        <HorizontalLayout height="52">
		          				<Button name="addMoreTextGroup" padding="8,8,0,0" height="41" width="460" text="添加更多文本组" textpadding="2,6,0,6" texttooltip="true" endellipsis="true" align="center" font="5" borderround="7,7" textcolor="#FFe6ebf2" hottextcolor="#FFFFFFFF" pushedtextcolor="FFe6ebf2" disabledtextcolor="#ffcccccc" bkcolor="#FF006fdf" hotbkcolor="#ff0055ab"	/>
									</HorizontalLayout>

									<HorizontalLayout inset="0,0,22,0" height="10"></HorizontalLayout>

					     </VerticalLayout>

					 	     <!--标签页管理-->
					       <VerticalLayout inset="0,0,0,0">

					        <HorizontalLayout height="40">
		          				<Button name="btn_UnifyTabs" padding="8,8,0,0" height="31" width="140" text="统一标签页" textpadding="2,6,0,6" texttooltip="true" endellipsis="true" align="center" font="5" borderround="7,7" textcolor="#FFe6ebf2" hottextcolor="#FFFFFFFF" pushedtextcolor="FFe6ebf2" disabledtextcolor="#ffcccccc" bkcolor="#FF006fdf" hotbkcolor="#ff0055ab"	/>
		          				<Button name="btn_CloseOtherTabs" padding="26,8,0,0" height="31" width="140" text="关闭其它页" textpadding="2,6,0,6" texttooltip="true" endellipsis="true" align="center" font="5" borderround="7,7" textcolor="#FFe6ebf2" hottextcolor="#FFFFFFFF" pushedtextcolor="FFe6ebf2" disabledtextcolor="#ffcccccc" bkcolor="#FF006fdf" hotbkcolor="#ff0055ab"	/>
									</HorizontalLayout>

									<HorizontalLayout height="40">
		          				<Button name="btn_CloseCurrentTab" padding="8,8,0,0" height="31" width="140" text="关闭当前标签页" textpadding="2,6,0,6" texttooltip="true" endellipsis="true" align="center" font="5" borderround="7,7" textcolor="#FFe6ebf2" hottextcolor="#FFFFFFFF" pushedtextcolor="FFe6ebf2" disabledtextcolor="#ffcccccc" bkcolor="#FF006fdf" hotbkcolor="#ff0055ab"	/>
		          				<Button name="btn_CloseEmptyTabs" padding="26,8,0,0" height="31" width="140" text="关闭空白标签页" textpadding="2,6,0,6" texttooltip="true" endellipsis="true" align="center" font="5" borderround="7,7" textcolor="#FFe6ebf2" hottextcolor="#FFFFFFFF" pushedtextcolor="FFe6ebf2" disabledtextcolor="#ffcccccc" bkcolor="#FF006fdf" hotbkcolor="#ff0055ab"	/>
									</HorizontalLayout>

					        <HorizontalLayout inset="0,0,22,0" height="20"></HorizontalLayout>
					        <HorizontalLayout height="36">
					        	<Label name="tab_urls_title" padding="0,0,0,0" autocalcwidth="true" maxwidth="200" textpadding="8,0,8,0" texttooltip="true" endellipsis="true" text="打开网址" align="center" borderround="7,7" font="8" textcolor="#ff373b40"></Label>
					          <Button name="tab_urls_help" padding="2,10,0,0" height="15" width="15" tooltip="Proxy IP Provisioning Platform Provider" normalimage="file='helpbtn.png'" hotimage="file='helpbtn_hover.png'" pushedimage="file='helpbtn_push.png'"/>
					        </HorizontalLayout>

					        <HorizontalLayout inset="0,0,22,0" height="6"></HorizontalLayout>

					        <HorizontalLayout height="122">
								    <VerticalLayout width="500">
						         <RichEdit name="api_links" padding="34,0,0,0" height="120" width="426" tipvaluecolor="#FF333333" borderround="7,7" bkcolor="#ffdce1e7" font="0" textpadding="8,8,20,0" maxchar="300" tipvalue="请输入网址,每行一个.." multiline="true" textcolor="#ff333333" rich="false" transparent="false">
						         </RichEdit>
						        </VerticalLayout>
				          </HorizontalLayout>
					        <HorizontalLayout height="26">
					        	<CheckBox name="check_FirstUrlInCurrentTab" width="18" height="18"  padding="34,4,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="true" />
					        	<Label name="first_url_label" padding="0,0,0,0" autocalcwidth="true" maxwidth="200" textpadding="8,0,8,0" texttooltip="true" endellipsis="true" text="首个网址在当前标签页打开" align="center" borderround="7,7" font="8" textcolor="#ff373b40"></Label>
					        </HorizontalLayout>

									<HorizontalLayout inset="0,0,22,0" height="6"></HorizontalLayout>

					        <HorizontalLayout height="52">
		          				<Button name="btn_OpenUrls" padding="8,8,0,0" height="41" width="460" text="打开网址" textpadding="2,6,0,6" texttooltip="true" endellipsis="true" align="center" font="5" borderround="7,7" textcolor="#FFe6ebf2" hottextcolor="#FFFFFFFF" pushedtextcolor="FFe6ebf2" disabledtextcolor="#ffcccccc" bkcolor="#FF006fdf" hotbkcolor="#ff0055ab"	/>
									</HorizontalLayout>

									<HorizontalLayout inset="0,0,22,0" height="10"></HorizontalLayout>

					     </VerticalLayout>





					 </TabLayout>

					    </VerticalLayout>
				</VerticalLayout>

			</HorizontalLayout>

      <HorizontalLayout height="52" bkcolor="#ffe9e9e9" inset="20,0,0,0">
        <HorizontalLayout>
          <CheckBox name="opt_checkAll" text="" textpadding="57,1,0,0" selected="false"  visible="true" padding="3,16,0,0" height="18" width="16" normalimage="file='list_check_normal.png'" hotimage="list_check_hover.png" selectedimage="file='list_checked_actvice_normal.png'" ></CheckBox>
          <Button name="refresh" padding="50,18,0,0" align="left" height="20" width="50" text="刷新" font="5" textcolor="#FF005ed3" hottextcolor="#FFff4444" />
        </HorizontalLayout>

        <VerticalLayout width="200">
          <Control />
          <Button text="打开" name="openselected" tooltip="候鸟浏览器批量调用勾选的会话配置环境" enabled="false"  padding="0,2,0,0" width="180" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	bkimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
          <Control />
        </VerticalLayout>
        <VerticalLayout width="200">
          <Control />
          <Button text="关闭" name="closeselected" tooltip="候鸟浏览器批量关闭勾选的会话配置环境" enabled="false"  padding="0,2,0,0" width="180" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	bkimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
          <Control />
        </VerticalLayout>

        <Control width="30"/>
      </HorizontalLayout>
		</VerticalLayout>

	<!--</HorizontalLayout>-->

    <Control name="dragicon" float="true" width="14" height="16" bkimage="dragicon.png"/>
  </VerticalLayout>
</Window>
