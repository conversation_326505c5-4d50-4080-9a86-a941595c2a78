					<HorizontalLayout height="20">
					    <CheckBox name="Set19" width="18" height="18" padding="36,0,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='check.png' dest='36,0,54,36'" selected="true" />
					    <Label name="clearCookiesSync_dec" padding="10,0,0,0" textpadding="0,0,0,0" text="禁止触发数据差异化云同步"  texttooltip="true" endellipsis="true" width="509" textcolor="#FF6272A4" hottextcolor="#FFFF5555" font="8"></Label>
					    <Button name="clearCookiesSync_help" padding="8,0,0,0" height="15" width="15" tooltip="仅允许数据在本地存储，不同步到云端空间" normalimage="file='helpbtn.png'" hotimage="file='helpbtn_hover.png'" pushedimage="file='helpbtn_push.png'"/>
					</HorizontalLayout>

					<HorizontalLayout height="8"></HorizontalLayout>

					<HorizontalLayout height="32">
					    <CheckBox name="Set20" width="18" height="18" padding="9,7,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='check.png' dest='36,0,54,36'" selected="true" />
					    <Label name="clearHistory_dec" padding="10,0,0,0" textpadding="0,0,0,0" text="启动环境时，清空当前环境内全部历史记录" texttooltip="true" endellipsis="true" width="506" textcolor="#FFF8F8F2" hottextcolor="#FFFF5555" font="8"></Label>
					    <Control width="30" />
					    <Button name="clearHistory_help" padding="8,9,0,0" height="15" width="15"  tooltip="过多的历史记录会影响环境的性能，清空它们可以帮助更快地加载网页" normalimage="file='helpbtn.png'" hotimage="file='helpbtn_hover.png'" pushedimage="file='helpbtn_push.png'"/>
					</HorizontalLayout>

					<HorizontalLayout height="4"></HorizontalLayout>

					<HorizontalLayout height="20">
					    <CheckBox name="Set21" width="18" height="18" padding="36,0,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='check.png' dest='36,0,54,36'" selected="true" />
					    <Label name="clearHistorySync_dec" padding="10,0,0,0" textpadding="0,0,0,0" text="禁止触发数据差异化云同步"  texttooltip="true" endellipsis="true" width="509" textcolor="#FF6272A4" hottextcolor="#FFFF5555" font="8"></Label>
              <Button name="clearHistorySync_help" padding="8,0,0,0" height="15" width="15"  tooltip="仅允许数据在本地存储，不同步到云端空间"  normalimage="file='helpbtn.png'" hotimage="file='helpbtn_hover.png'" pushedimage="file='helpbtn_push.png'"/>
		      </HorizontalLayout>


       <!--高级设置top4-->
					<HorizontalLayout height="20"></HorizontalLayout>

					<HorizontalLayout inset="1,0,22,0" height="2">
					    <Control height="2" bkcolor="#FF44475A"/>
					</HorizontalLayout>

					<HorizontalLayout height="14"></HorizontalLayout>

					<HorizontalLayout height="40">
					    <VerticalLayout width="386">
					        <HorizontalLayout height="36">
					            <Label name="envPerfSettings_title" bkcolor="#FF44475A" padding="0,10,0,0" autocalcwidth="true" maxwidth="300" textpadding="8,0,8,0" texttooltip="true" endellipsis="true" text="环境性能设置" align="center" borderround="7,7" font="3" textcolor="#FFF8F8F2"></Label>
					        </HorizontalLayout>
					    </VerticalLayout>

					    <VerticalLayout width="230">
					        <HorizontalLayout height="36">
					            <Button name="envPerfSettings_batchApply_btn" bkcolor="#FF44475A" hotbkcolor="#FF6272A4" padding="0,10,0,0" width="100" textpadding="8,0,8,0" texttooltip="true" endellipsis="true" text="批量应用到.."  tooltip="将此设定批量应用到指定分组所有环境中.." align="center" borderround="7,7" font="3" textcolor="#FFF8F8F2"></Button>
					            <Control width="10"/>
					            <Button name="envPerfSettings_resetDefaults_btn" bkcolor="#FF44475A" hotbkcolor="#FF6272A4" padding="0,10,0,0" width="100" textpadding="8,0,8,0" texttooltip="true" endellipsis="true" text="恢复到默认" tooltip="还原到初始默认状态" align="center" borderround="7,7" font="3" textcolor="#FFF8F8F2"></Button>
					        </HorizontalLayout>
					    </VerticalLayout>
					</HorizontalLayout>

					<HorizontalLayout height="8"></HorizontalLayout>
					<HorizontalLayout height="32">
					    <CheckBox name="Set22" width="18" height="18" padding="9,7,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='check.png' dest='36,0,54,36'" selected="true" />
					    <Label name="gpuAcceleration_dec" padding="10,0,0,0" textpadding="0,0,0,0" text="启用本机GPU显卡硬件加速，提升环境页面渲染性能" texttooltip="true" endellipsis="true" autocalcwidth="true" maxwidth="330" textcolor="#FFF8F8F2" hottextcolor="#FFFF5555" font="8"></Label>
					    <Label name="gpuAcceleration_note" padding="6,0,0,0" textpadding="0,0,0,0" text="(高安全敏感业务建议禁用)" texttooltip="true" endellipsis="true" autocalcwidth="true" maxwidth="200" textcolor="#FF6272A4" hottextcolor="#FFFF5555" font="8"></Label>
					    <Control />
					    <Button name="gpuAcceleration_help" padding="8,9,30,0" height="15" width="15" tooltip="GPU加速会改变浏览器的指纹特征，使得每个用户的浏览器在某些方面变得更加独特。浏览器指纹是通过收集各种设备和浏览器信息来唯一标识用户的技术。启用GPU加速可能会增加这些信息的独特性，导致更容易被指纹识别技术追踪。" normalimage="file='helpbtn.png'" hotimage="file='helpbtn_hover.png'" pushedimage="file='helpbtn_push.png'"/>
					</HorizontalLayout>

					<HorizontalLayout height="32">
					    <CheckBox name="Set23" width="18" height="18" padding="9,7,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='check.png' dest='36,0,54,36'" selected="true" />
					    <Label name="randomFingerprint_dec" padding="10,0,0,0" textpadding="0,0,0,0" text="启用环境运行时随机指纹" texttooltip="true" endellipsis="true" autocalcwidth="true" maxwidth="330" textcolor="#FFF8F8F2" hottextcolor="#FFFF5555" font="8"></Label>
					    <Label name="randomFingerprint_note" padding="6,0,0,0" textpadding="0,0,0,0" text="(高安全敏感业务建议禁用)" texttooltip="true" endellipsis="true" autocalcwidth="true" maxwidth="200" textcolor="#FF6272A4" hottextcolor="#FFFF5555" font="8"></Label>
					    <Control />
					    <Button name="randomFingerprint_help" padding="8,9,30,0" height="15" width="15" tooltip="候鸟独有的军用随机指纹模块可以使每个会话的浏览器特征变得独特，减少了被指纹识别技术追踪的风险。通过不断变化浏览器的指纹特征，用户可以更好地保护自己的在线隐私。" normalimage="file='helpbtn.png'" hotimage="file='helpbtn_hover.png'" pushedimage="file='helpbtn_push.png'"/>
					</HorizontalLayout>

					<HorizontalLayout height="32">
					    <CheckBox name="Set24" width="18" height="18" padding="9,7,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='check.png' dest='36,0,54,36'" selected="true" />
					    <Label name="memorySaving_dec" padding="10,0,0,0" textpadding="0,0,0,0" text="节省内存模式运行环境" texttooltip="true" endellipsis="true" autocalcwidth="true" maxwidth="230" textcolor="#FFF8F8F2" hottextcolor="#FFFF5555" font="8"></Label>
					    <Label name="memorySaving_note" padding="6,0,0,0" textpadding="0,0,0,0" text="(自动释放闲置标签页占用的内存)" texttooltip="true" endellipsis="true" autocalcwidth="true" maxwidth="300" textcolor="#FF6272A4" hottextcolor="#FFFF5555" font="8"></Label>
					    <Control />
					    <Button name="memorySaving_help" padding="8,9,30,0" height="15" width="15" tooltip="这种模式通过优化内存使用，减少了环境和系统对内存的需求，允许更多的环境同时运行和提高系统的响应速度。" normalimage="file='helpbtn.png'" hotimage="file='helpbtn_hover.png'" pushedimage="file='helpbtn_push.png'"/>
					</HorizontalLayout>



       <!--高级设置top5-->
					<HorizontalLayout height="20"></HorizontalLayout>

					<HorizontalLayout inset="1,0,22,0" height="2">
					    <Control height="2" bkcolor="#ffc6c9cd"/>
					</HorizontalLayout>

					<HorizontalLayout height="14"></HorizontalLayout>

					<HorizontalLayout height="40">
					    <VerticalLayout width="386">
					        <HorizontalLayout height="36">
					            <Label name="fingerprintSettings_title" bkcolor="#ffebeff1" padding="0,10,0,0" autocalcwidth="true" maxwidth="300" textpadding="8,0,8,0" texttooltip="true" endellipsis="true" text="指纹模板设置" align="center" borderround="7,7" font="3" textcolor="#ff949698"></Label>
					        </HorizontalLayout>
					    </VerticalLayout>

					    <VerticalLayout width="230">
					        <HorizontalLayout height="36">
					            <Button name="fingerprintSettings_batchApply_btn" bkcolor="#ffebeff1" hotbkcolor="#ffdee1e3" padding="0,10,0,0" width="100" textpadding="8,0,8,0" texttooltip="true" endellipsis="true" text="批量应用到.." tooltip="将此设定批量应用到指定分组所有环境中.." align="center" borderround="7,7" font="3" textcolor="#ff949698"></Button>
					            <Control width="10"/>
					            <Button name="fingerprintSettings_resetDefaults_btn" bkcolor="#ffebeff1" hotbkcolor="#ffdee1e3" padding="0,10,0,0" width="100" textpadding="8,0,8,0" texttooltip="true" endellipsis="true" text="恢复到默认" tooltip="还原到初始默认状态" align="center" borderround="7,7" font="3" textcolor="#ff949698"></Button>
					        </HorizontalLayout>
					    </VerticalLayout>
					</HorizontalLayout>

					<HorizontalLayout height="14"></HorizontalLayout>

					<HorizontalLayout height="40">
					    <VerticalLayout width="586">
					        <HorizontalLayout height="36">
					            <Button name="createTemplate_btn" padding="9,4,0,0" height="27" autocalcwidth="true" maxwidth="220" text="创建指纹模板" tooltip="支持用户根据业务需要，详细的设置专业级各指纹指标" textpadding="10,0,10,0" texttooltip="true" endellipsis="true" align="center" font="3" borderround="7,7" textcolor="#FFffffff" disabledtextcolor="#ffffffff" hottextcolor="#FFFFFFFF" bkcolor="#FF006fdf" hotbkcolor="#ff0055ab"/>
					            <Button name="templateGuide_btn" padding="12,4,0,0" height="27" autocalcwidth="true" maxwidth="320" text="模板使用指南" tooltip="候鸟官方专业指纹细节设置指南说明书" textpadding="10,0,10,0" texttooltip="true" endellipsis="true" align="center" font="3" borderround="7,7" textcolor="#FFffffff" disabledtextcolor="#ffffffff" hottextcolor="#FFFFFFFF" bkcolor="#FF006fdf" hotbkcolor="#ff0055ab"/>
					        </HorizontalLayout>
					    </VerticalLayout>
					</HorizontalLayout>

					<HorizontalLayout height="4"></HorizontalLayout>

					<HorizontalLayout height="40">
					    <CheckBox name="useTemplate_check" width="18" height="18" padding="18,11,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='check.png' dest='36,0,54,36'" selected="true" />
					    <Label name="useTemplate_dec" padding="10,0,0,0" textpadding="0,0,0,0" text="当前环境已使用模板" texttooltip="true" endellipsis="true" autocalcwidth="true" maxwidth="200" textcolor="#FF333333" hottextcolor="ffFF0000" font="8"></Label>
					    <Combo name="templates2" visible="false" bordersize="0" padding="20,2,0,0" textpadding="8,2,60,0" width="221" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" bkcolor="#ffdce1e7"
					        normalimage="file='Profile\Proxy_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\proxy_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\proxy_Combox_Click.png' corner='5,5,25,10'"
					        combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
					        itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0">
					    </Combo>
					    <Control />
					    <Button name="template_Help" padding="8,13,30,0" height="15" width="15" tooltip="允许当前环境使用指定的指纹模板" normalimage="file='helpbtn.png'" hotimage="file='helpbtn_hover.png'" pushedimage="file='helpbtn_push.png'"/>
					</HorizontalLayout>


        <!--高级设置top6-->
           <HorizontalLayout height="20"></HorizontalLayout>

           <HorizontalLayout inset="1,0,22,0" height="2">
        				<Control height="2" bkcolor="#ffc6c9cd"/>
      		 </HorizontalLayout>



      </VerticalLayout>

        <HorizontalLayout bktrans="true" height="14"></HorizontalLayout>

        <HorizontalLayout height="42">
			          <Control />
			            <Button name="env_gobol_batchApply_btn" padding="9,4,0,0" height="38" autocalcwidth="true" maxwidth="280" text="当前全局配置应用到.."  textpadding="20,0,20,0" texttooltip="true" endellipsis="true" align="center" font="8" borderround="7,7" textcolor="#FFffffff" disabledtextcolor="#ffffffff" hottextcolor="#FFFFFFFF" bkcolor="#FF006fdf" hotbkcolor="#ff0055ab"	/>
			            <Button name="env_gobol_resetdefault_btn" padding="12,4,0,0" height="38" autocalcwidth="true" maxwidth="280" text="恢复默认全局配置" textpadding="20,0,20,0" texttooltip="true" endellipsis="true" align="center" font="8" borderround="7,7" textcolor="#FFffffff" disabledtextcolor="#ffffffff" hottextcolor="#FFFFFFFF" bkcolor="#FF006fdf" hotbkcolor="#ff0055ab"	/>

			          <Control width="22"/>
		       </HorizontalLayout>

           <HorizontalLayout height="22"></HorizontalLayout>

    </VerticalLayout>

<!--指纹轨迹/历史版本-->

			<!--指纹轨迹-->
      <VerticalLayout inset="0,0,0,0">

      <HorizontalLayout inset="20,0,22,0" height="10">
      </HorizontalLayout>

      <HorizontalLayout inset="20,0,22,0" height="30">