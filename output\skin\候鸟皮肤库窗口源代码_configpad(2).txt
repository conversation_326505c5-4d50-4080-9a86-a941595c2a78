										  </ListLabelElement>-->
										</Combo>
                    <Edit name="lnginputedit" textpadding="12,0,0,0" pos="4,0,0,10" width="144" height="36" nativebkcolor="#FF21222C" bkcolor="#FF21222C" float="true" />
				     </VerticalLayout>



			</HorizontalLayout>





      <HorizontalLayout height="12">
			</HorizontalLayout>
			<HorizontalLayout height="24">
				<VerticalLayout width="50">
			      <CheckBox name="WAV" width="18" height="18"  padding="22,4,0,1" normalimage="checkbox_dark.png" selectedimage="checkbox_dark_selected.png" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="true" />
			  </VerticalLayout>
			  <VerticalLayout width="90">
			      <Label name="lwav" padding="0,0,0,0" text="音频指纹" width="90" textpadding="0,0,5,0" texttooltip="true" endellipsis="true" textcolor="#FFF8F8F2" hottextcolor="#FFFF5555" font="8"></Label>
			  </VerticalLayout>

			  <VerticalLayout width="20">
			  	<Control width="20"/>
			  </VerticalLayout>

			  <VerticalLayout width="50">
			      <CheckBox name="FONTCODE" width="18" height="18"  padding="22,4,0,1" normalimage="checkbox_dark.png" selectedimage="checkbox_dark_selected.png" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="true" />
			  </VerticalLayout>
			  <VerticalLayout width="120">
			      <Label name="lfontcode" padding="0,0,0,0" text="字体指纹" width="120" textpadding="0,0,5,0" endellipsis="true" textcolor="#FFF8F8F2" hottextcolor="#FFFF5555" font="8"></Label>
			  </VerticalLayout>

			  <VerticalLayout width="50">
			      <CheckBox name="DNS" width="18" height="18"  padding="22,4,0,1" normalimage="checkbox_dark.png" selectedimage="checkbox_dark_selected.png" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="true" />
			  </VerticalLayout>
			  <VerticalLayout width="90">
			      <Label name="ldns" padding="0,0,0,0" text="DNS指纹" width="90" textpadding="0,0,5,0" endellipsis="true" textcolor="#FFF8F8F2" hottextcolor="#FFFF5555" font="8"></Label>
			  </VerticalLayout>

			</HorizontalLayout>

      <HorizontalLayout height="6"></HorizontalLayout>

			<HorizontalLayout height="32">
				<VerticalLayout width="50">
			      <CheckBox name="CANVAS" width="18" height="18"  padding="22,4,0,1" normalimage="checkbox_dark.png" selectedimage="checkbox_dark_selected.png" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="true" />
			  </VerticalLayout>
			  <VerticalLayout width="90">
			      <Label name="lcanvas" padding="0,0,0,0" text="CANVAS指纹" textpadding="0,0,5,0" endellipsis="true" width="90" textcolor="#FFF8F8F2" hottextcolor="#FFFF5555" font="8"></Label>
			  </VerticalLayout>

			  <VerticalLayout width="20">
			  	<Control width="20"/>
			  </VerticalLayout>

        <HorizontalLayout name="UNCHANGEDAREA" width="170" height="32" visible="false">
        <VerticalLayout width="50">
          <CheckBox name="UNCHANGED" width="18" height="18"  padding="22,4,0,1" normalimage="checkbox_dark.png" selectedimage="checkbox_dark_selected.png" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="true" />
        </VerticalLayout>
        <VerticalLayout width="120" >
          <Label name="lunchange" padding="0,0,0,0" text="保持原指纹无变化" textpadding="0,0,5,0" texttooltip="true" endellipsis="true" width="120" textcolor="#FFF8F8F2" hottextcolor="#FFFF5555" font="8"></Label>
        </VerticalLayout>
        </HorizontalLayout>

        <VerticalLayout width="50">
          <CheckBox name="NOTALLOWRESIZE" width="18" height="18"  padding="22,4,0,1" normalimage="checkbox_dark.png" selectedimage="checkbox_dark_selected.png" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="true" />
        </VerticalLayout>
        <VerticalLayout width="202">
          <Label name="lnotallowresize" padding="0,0,0,0"  text="保持浏览器窗口大小锁定" textpadding="0,0,10,0" texttooltip="true" endellipsis="true" width="200" textcolor="#FFF8F8F2" hottextcolor="#FFFF5555" font="8"></Label>
        </VerticalLayout>

			</HorizontalLayout>



			 <HorizontalLayout height="2"></HorizontalLayout>

			  <HorizontalLayout inset="20,0,26,0" height="2">
        <Control height="2" bkcolor="#FF44475A"/>
      </HorizontalLayout>

			 <HorizontalLayout height="4"></HorizontalLayout>


			<HorizontalLayout height="74">


			  <VerticalLayout width="150">
			  	<Button name="rdnconfigbtn" padding="22,24,0,0" height="52" width="150" text="获取随机配置"  textpadding="0,0,20,0" texttooltip="true" endellipsis="true"  align="left" font="8" textcolor="#FFBD93F9" hottextcolor="#FF8BE9FD" />
			  </VerticalLayout>
			  <VerticalLayout width="280">
			  	<Label name="rdnconfigbtn_dec" padding="0,8,0,0" height="52" width="280" textpadding="0,0,10,0" texttooltip="true" endellipsis="true"  text="(使用代理服务器时，请检查代理获取配置)" align="center" font="8" textcolor="#FF6272A4" hottextcolor="#FF6272A4"></Label>
			  </VerticalLayout>

			  <Control width="24"/>
			    <VerticalLayout width="220">
             <Button name="configbtn" padding="6,8,0,0" height="50" width="161" text="创建环境" textpadding="10,0,10,0" texttooltip="true" endellipsis="true" font="11" borderround="7,7" textcolor="#FFF8F8F2" hottextcolor="#FFFFFFFF" bkcolor="#FFBD93F9" hotbkcolor="#FF8BE9FD"	/>
         </VerticalLayout>
			    <VerticalLayout width="8">

         </VerticalLayout>


			</HorizontalLayout>



		</VerticalLayout>


<!--高级配置-->
    <VerticalLayout inset="22,6,0,0">


    	<VerticalLayout sepheight="1" scrollwheel="true" header="hidden" itemshowhtml="true" vscrollbar="true" scrollfbarFade="128">
    	<!--高级设置top1-->
		     <HorizontalLayout height="2"></HorizontalLayout>
						<HorizontalLayout height="40">
						    <VerticalLayout width="386">
						        <HorizontalLayout height="36">
						            <Label name="env_run_title" bkcolor="#FF44475A" padding="0,10,0,0" autocalcwidth="true" maxwidth="300" textpadding="8,0,8,0" texttooltip="true" endellipsis="true" text="环境运行设置" align="center" borderround="7,7" font="3" textcolor="#FFF8F8F2"></Label>
						        </HorizontalLayout>
						    </VerticalLayout>

						    <VerticalLayout width="230">
						        <HorizontalLayout height="36">
                      <Button name="env_run_batch_apply_btn" bkcolor="#FF44475A" hotbkcolor="#FF6272A4" padding="0,10,0,0" width="100" textpadding="8,0,8,0" texttooltip="true" endellipsis="true" text="批量应用到.." tooltip="将此设定批量应用到指定分组所有环境中.." align="center" borderround="7,7" font="3" textcolor="#FFF8F8F2"></Button>
						            <Control width="10"/>
                      <Button name="env_run_reset_to_default_btn" bkcolor="#FF44475A" hotbkcolor="#FF6272A4" padding="0,10,0,0" width="100" textpadding="8,0,8,0" texttooltip="true" endellipsis="true" text="恢复到默认" tooltip="还原到初始默认状态" align="center" borderround="7,7" font="3" textcolor="#FFF8F8F2"></Button>
						        </HorizontalLayout>
						    </VerticalLayout>
						</HorizontalLayout>

						<HorizontalLayout height="10"></HorizontalLayout>

						<HorizontalLayout height="32">
						    <CheckBox name="firstpage_checkbox" selected="true" width="18" height="18" padding="9,4,0,1" normalimage="checkbox_dark.png" selectedimage="checkbox_dark_selected.png"/>
						    <Label name="default_open_webpage_dec" padding="10,-5,0,0" textpadding="0,0,0,0" text="环境启动时，自动打开网页" texttooltip="true" endellipsis="true" autocalcwidth="true" maxwidth="500" textcolor="#FFF8F8F2" hottextcolor="#FFFF5555" font="8"></Label>
						    <Button name="firstpage_help" padding="8,6,0,0" height="15" width="15" tooltip="Auto running webpages.." normalimage="file='helpbtn.png'" hotimage="file='helpbtn_hover.png'" pushedimage="file='helpbtn_push.png'"/>
						</HorizontalLayout>

						<HorizontalLayout name="firstpage_child_0" height="42">
						    <VerticalLayout width="56">
						        <CheckBox name="firstpage_checkbox_child_0" width="18" height="18" padding="36,11,0,1" normalimage="checkbox_dark.png" selectedimage="checkbox_dark_selected.png" selecteddisabledimage="file='disable_check.png' source='18,0,36,18'"/>
						    </VerticalLayout>
						    <VerticalLayout width="560">
						        <Combo name="firstpage_agent_combo_0" reselect="true" dropboxsize="0,450" bordersize="0" padding="11,2,0,10" width="530" height="36" borderround="7,7" textcolor="#FFF8F8F2" disabledtextcolor="#FF6272A4" itemalign="left" itemfont="0" textpadding="4,2,0,0" bkcolor="#FF21222C"
						            normalimage="file='Profile\Setting_Combox_Normal_big_large.png' corner='15,5,28,7'" hotimage="file='Profile\Setting_Combox_Hover_large.png' corner='15,5,28,7'" pushedimage="file='Profile\Setting_Combox_Click_large.png' corner='15,5,28,7'"
						            combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='15,5,28,7'"
						            itemhotbkcolor="#FF44475A" itemselectedbkcolor="#FF44475A" itemtextpadding="10,0,0,0">
						        </Combo>
						        <Edit name="firstpage_agent_text_0" pos="11,2,0,10" padding="21,0,0,10" height="36" width="484" tipvaluecolor="#FF6272A4" endellipsis="true" nativebkcolor="#FF21222C" borderround="7,7" bkcolor="#FF21222C" font="8" textpadding="10,0,10,0" maxchar="6000" multiline="false" textcolor="#FFF8F8F2" rich="false" transparent="false" float="true"/>
						    </VerticalLayout>
						</HorizontalLayout>







						<!--子项Combo动态多条显示区域- 开始 -->

						<HorizontalLayout name="firstpage_child_1" height="42" visible="false">
						    <VerticalLayout width="56">
						        <CheckBox name="firstpage_checkbox_child_1" width="18" height="18" padding="36,11,0,1" normalimage="checkbox_dark.png" selectedimage="checkbox_dark_selected.png" selecteddisabledimage="file='disable_check.png' source='18,0,36,18'"/>
						    </VerticalLayout>
						    <VerticalLayout width="506">
						        <Combo name="firstpage_agent_combo_1" reselect="true" dropboxsize="0,350" bordersize="0" padding="11,2,0,10" width="486" height="36" borderround="7,7" textcolor="#FFC5C8C6" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="4,2,0,0" bkcolor="#FF252526"
						            normalimage="file='Profile\Setting_Combox_Normal_big_large.png' corner='15,5,28,7'" hotimage="file='Profile\Setting_Combox_Hover_large.png' corner='15,5,28,7'" pushedimage="file='Profile\Setting_Combox_Click_large.png' corner='15,5,28,7'"
						            combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='15,5,28,7'"
						            itemhotbkcolor="#FF3A3D41" itemselectedbkcolor="#FF3A3D41" itemtextpadding="10,0,0,0">
						        </Combo>
						        <Edit name="firstpage_agent_text_1" pos="11,2,0,10" padding="21,0,0,10" height="36" width="440" tipvaluecolor="#FF9E9E9E" endellipsis="true" nativebkcolor="#FF252526" borderround="7,7" bkcolor="#FF252526" font="8" textpadding="10,0,10,0" maxchar="6000" multiline="false" textcolor="#FFC5C8C6" rich="false" transparent="false" float="true"/>
						    </VerticalLayout>

						    <VerticalLayout width="56">
						        <Button name="disvisable_btn_firstpage_child_1" width="20" height="20" padding="2,10,0,0" normalimage="file='delete_autorunpage_btn.png' source='0,0,20,20'" hotimage="file='delete_autorunpage_btn.png' source='20,0,40,20'" pushedimage="file='delete_autorunpage_btn.png' source='40,0,60,20'"/>
						    </VerticalLayout>
						</HorizontalLayout>


						<HorizontalLayout name="firstpage_child_2" height="42" visible="false">
						    <VerticalLayout width="56">
						        <CheckBox name="firstpage_checkbox_child_2" width="18" height="18" padding="36,11,0,1" normalimage="checkbox_dark.png" selectedimage="checkbox_dark_selected.png" selecteddisabledimage="file='disable_check.png' source='18,0,36,18'"/>
						    </VerticalLayout>
						    <VerticalLayout width="506">
						        <Combo name="firstpage_agent_combo_2" reselect="true" dropboxsize="0,350" bordersize="0" padding="11,2,0,10" width="486" height="36" borderround="7,7" textcolor="#FFC5C8C6" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="4,2,0,0" bkcolor="#FF252526"
						            normalimage="file='Profile\Setting_Combox_Normal_big_large.png' corner='15,5,28,7'" hotimage="file='Profile\Setting_Combox_Hover_large.png' corner='15,5,28,7'" pushedimage="file='Profile\Setting_Combox_Click_large.png' corner='15,5,28,7'"
						            combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='15,5,28,7'"
						            itemhotbkcolor="#FF3A3D41" itemselectedbkcolor="#FF3A3D41" itemtextpadding="10,0,0,0">
						        </Combo>
						        <Edit name="firstpage_agent_text_2" pos="11,2,0,10" padding="21,0,0,10" height="36" width="440" tipvaluecolor="#FF9E9E9E" endellipsis="true" nativebkcolor="#FF252526" borderround="7,7" bkcolor="#FF252526" font="8" textpadding="10,0,10,0" maxchar="6000" multiline="false" textcolor="#FFC5C8C6" rich="false" transparent="false" float="true"/>
						    </VerticalLayout>

						    <VerticalLayout width="56">
						        <Button name="disvisable_btn_firstpage_child_2" width="20" height="20" padding="2,10,0,0" normalimage="file='delete_autorunpage_btn.png' source='0,0,20,20'" hotimage="file='delete_autorunpage_btn.png' source='20,0,40,20'" pushedimage="file='delete_autorunpage_btn.png' source='40,0,60,20'"/>
						    </VerticalLayout>
						</HorizontalLayout>

        <HorizontalLayout name="firstpage_child_3" height="42" visible="false">
						    <VerticalLayout width="56">
						        <CheckBox name="firstpage_checkbox_child_3" width="18" height="18" padding="36,11,0,1" normalimage="checkbox_dark.png" selectedimage="checkbox_dark_selected.png" selecteddisabledimage="file='disable_check.png' source='18,0,36,18'"/>
						    </VerticalLayout>
						    <VerticalLayout width="506">
						        <Combo name="firstpage_agent_combo_3" reselect="true" dropboxsize="0,350" bordersize="0" padding="11,2,0,10" width="486" height="36" borderround="7,7" textcolor="#FFC5C8C6" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="4,2,0,0" bkcolor="#FF252526"
						            normalimage="file='Profile\Setting_Combox_Normal_big_large.png' corner='15,5,28,7'" hotimage="file='Profile\Setting_Combox_Hover_large.png' corner='15,5,28,7'" pushedimage="file='Profile\Setting_Combox_Click_large.png' corner='15,5,28,7'"
						            combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='15,5,28,7'"
						            itemhotbkcolor="#FF3A3D41" itemselectedbkcolor="#FF3A3D41" itemtextpadding="10,0,0,0">
						        </Combo>
						        <Edit name="firstpage_agent_text_3" pos="11,2,0,10" padding="21,0,0,10" height="36" width="440" tipvaluecolor="#FF5f5f5f" endellipsis="true" nativebkcolor="#FFDCE1E7" borderround="7,7" bkcolor="#ffdce1e7" font="8" textpadding="10,0,10,0" maxchar="6000" multiline="false" textcolor="#ff333333" rich="false" transparent="false" float="true"/>
						    </VerticalLayout>

						    <VerticalLayout width="56">
						        <Button name="disvisable_btn_firstpage_child_3" width="20" height="20" padding="2,10,0,0" normalimage="file='delete_autorunpage_btn.png' source='0,0,20,20'" hotimage="file='delete_autorunpage_btn.png' source='20,0,40,20'" pushedimage="file='delete_autorunpage_btn.png' source='40,0,60,20'"/>
						    </VerticalLayout>
						</HorizontalLayout>


        <HorizontalLayout name="firstpage_child_4" height="42" visible="false">
          <VerticalLayout width="56">
            <CheckBox name="firstpage_checkbox_child_4" width="18" height="18" padding="36,11,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" selecteddisabledimage="file='disable_check.png' source='18,0,36,18'"/>
          </VerticalLayout>
          <VerticalLayout width="506">
            <Combo name="firstpage_agent_combo_4" reselect="true" dropboxsize="0,350" bordersize="0" padding="11,2,0,10" width="486" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="4,2,0,0" bkcolor="#ffdce1e7"
                normalimage="file='Profile\Setting_Combox_Normal_big_large.png' corner='15,5,28,7'" hotimage="file='Profile\Setting_Combox_Hover_large.png' corner='15,5,28,7'" pushedimage="file='Profile\Setting_Combox_Click_large.png' corner='15,5,28,7'"
                combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='15,5,28,7'"
                itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0">
            </Combo>
            <Edit name="firstpage_agent_text_4" pos="11,2,0,10" padding="21,0,0,10" height="36" width="440" tipvaluecolor="#FF5f5f5f" endellipsis="true" nativebkcolor="#FFDCE1E7" borderround="7,7" bkcolor="#ffdce1e7" font="8" textpadding="10,0,10,0" maxchar="6000" multiline="false" textcolor="#ff333333" rich="false" transparent="false" float="true"/>
          </VerticalLayout>

          <VerticalLayout width="56">
            <Button name="disvisable_btn_firstpage_child_4" width="20" height="20" padding="2,10,0,0" normalimage="file='delete_autorunpage_btn.png' source='0,0,20,20'" hotimage="file='delete_autorunpage_btn.png' source='20,0,40,20'" pushedimage="file='delete_autorunpage_btn.png' source='40,0,60,20'"/>
          </VerticalLayout>
        </HorizontalLayout>


        <HorizontalLayout name="firstpage_child_5" height="42" visible="false">
          <VerticalLayout width="56">
            <CheckBox name="firstpage_checkbox_child_5" width="18" height="18" padding="36,11,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" selecteddisabledimage="file='disable_check.png' source='18,0,36,18'"/>
          </VerticalLayout>
          <VerticalLayout width="506">
            <Combo name="firstpage_agent_combo_5" reselect="true" dropboxsize="0,350" bordersize="0" padding="11,2,0,10" width="486" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="4,2,0,0" bkcolor="#ffdce1e7"
                normalimage="file='Profile\Setting_Combox_Normal_big_large.png' corner='15,5,28,7'" hotimage="file='Profile\Setting_Combox_Hover_large.png' corner='15,5,28,7'" pushedimage="file='Profile\Setting_Combox_Click_large.png' corner='15,5,28,7'"
                combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='15,5,28,7'"
                itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0">
            </Combo>
            <Edit name="firstpage_agent_text_5" pos="11,2,0,10" padding="21,0,0,10" height="36" width="440" tipvaluecolor="#FF5f5f5f" endellipsis="true" nativebkcolor="#FFDCE1E7" borderround="7,7" bkcolor="#ffdce1e7" font="8" textpadding="10,0,10,0" maxchar="6000" multiline="false" textcolor="#ff333333" rich="false" transparent="false" float="true"/>
          </VerticalLayout>

          <VerticalLayout width="56">
            <Button name="disvisable_btn_firstpage_child_5" width="20" height="20" padding="2,10,0,0" normalimage="file='delete_autorunpage_btn.png' source='0,0,20,20'" hotimage="file='delete_autorunpage_btn.png' source='20,0,40,20'" pushedimage="file='delete_autorunpage_btn.png' source='40,0,60,20'"/>
          </VerticalLayout>
        </HorizontalLayout>

        <HorizontalLayout name="firstpage_child_6" height="42" visible="false">
          <VerticalLayout width="56">
            <CheckBox name="firstpage_checkbox_child_6" width="18" height="18" padding="36,11,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" selecteddisabledimage="file='disable_check.png' source='18,0,36,18'"/>
          </VerticalLayout>
          <VerticalLayout width="506">
            <Combo name="firstpage_agent_combo_6" reselect="true" dropboxsize="0,350" bordersize="0" padding="11,2,0,10" width="486" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="4,2,0,0" bkcolor="#ffdce1e7"
                normalimage="file='Profile\Setting_Combox_Normal_big_large.png' corner='15,5,28,7'" hotimage="file='Profile\Setting_Combox_Hover_large.png' corner='15,5,28,7'" pushedimage="file='Profile\Setting_Combox_Click_large.png' corner='15,5,28,7'"
                combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='15,5,28,7'"
                itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0">
            </Combo>
            <Edit name="firstpage_agent_text_6" pos="11,2,0,10" padding="21,0,0,10" height="36" width="440" tipvaluecolor="#FF5f5f5f" endellipsis="true" nativebkcolor="#FFDCE1E7" borderround="7,7" bkcolor="#ffdce1e7" font="8" textpadding="10,0,10,0" maxchar="6000" multiline="false" textcolor="#ff333333" rich="false" transparent="false" float="true"/>
          </VerticalLayout>

          <VerticalLayout width="56">
            <Button name="disvisable_btn_firstpage_child_6" width="20" height="20" padding="2,10,0,0" normalimage="file='delete_autorunpage_btn.png' source='0,0,20,20'" hotimage="file='delete_autorunpage_btn.png' source='20,0,40,20'" pushedimage="file='delete_autorunpage_btn.png' source='40,0,60,20'"/>
          </VerticalLayout>
        </HorizontalLayout>

            <!--子项Combo动态多条显示区域- 结束-->

            <!--一共放20个，初始状态默认只显示1个,调试期显示3个-->


						<HorizontalLayout height="26">
						    <Control />
						    <Button name="account_manager_btn" padding="0,2,0,0" align="right" height="26" autocalcwidth="true" maxwidth="260" text="帐户管理器" font="3" textcolor="#FFF8F8F2" hottextcolor="#FFBD93F9" />
						    <Button name="add_tag_btn" padding="10,2,0,0" align="right" height="26" autocalcwidth="true" maxwidth="80" text="+标签" font="3" textcolor="#FFF8F8F2" hottextcolor="#FFBD93F9" />
						    <Button name="firstpage_apply_btn" enabled="false" padding="12,2,0,0" align="right" height="26" autocalcwidth="true" maxwidth="80" text="应用" font="3" textcolor="#FFBD93F9" hottextcolor="#FF8BE9FD" />
						    <Control width="36" />
						</HorizontalLayout>

      <!--高级设置top2-->
         <HorizontalLayout height="14"></HorizontalLayout>

					<HorizontalLayout inset="1,0,22,0" height="2">
					  <Control height="2" bkcolor="#FF44475A"/>
					</HorizontalLayout>

					<HorizontalLayout height="14"></HorizontalLayout>

					<HorizontalLayout height="40">
					  <VerticalLayout width="386">
					    <HorizontalLayout height="36">
					      <Label name="envParam_set_title" bkcolor="#FF44475A" padding="0,10,0,0" autocalcwidth="true" maxwidth="300" textpadding="8,0,8,0" texttooltip="true" endellipsis="true" text="环境参数设置" align="center" borderround="7,7" font="3" textcolor="#FFF8F8F2"></Label>
					    </HorizontalLayout>
					  </VerticalLayout>

					  <VerticalLayout width="230">
					    <HorizontalLayout height="36">
					      <Button name="envParam_set_batchApply_btn" bkcolor="#FF44475A" hotbkcolor="#FF6272A4" padding="0,10,0,0" width="100" textpadding="8,0,8,0" texttooltip="true" endellipsis="true" text="批量应用到.." tooltip="将此设定批量应用到指定分组所有环境中.." align="center" borderround="7,7" font="3" textcolor="#FFF8F8F2"></Button>
					      <Control width="10"/>
					      <Button name="envParam_set_restoreDefault_btn" bkcolor="#FF44475A" hotbkcolor="#FF6272A4" padding="0,10,0,0" width="100" textpadding="8,0,8,0" texttooltip="true" endellipsis="true" text="恢复到默认" tooltip="还原到初始默认状态" align="center" borderround="7,7" font="3" textcolor="#FFF8F8F2"></Button>
					    </HorizontalLayout>
					  </VerticalLayout>
					</HorizontalLayout>

					<HorizontalLayout height="8"></HorizontalLayout>

					<HorizontalLayout height="32">
					  <CheckBox name="Set01" width="18" height="18" padding="9,7,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="true" />
					  <Label name="noloadImg_dec" padding="10,0,0,0" textpadding="0,0,0,0" text="禁止加载网页图片资源" texttooltip="true" endellipsis="true" width="506" textcolor="#FFF8F8F2" hottextcolor="#FFFF5555" font="8"></Label>
					  <Control width="30" />
					  <Button name="noloadImg_help" padding="8,9,0,0" height="15" width="15" tooltip="开启后可节省流量，环境将不加载任何图片，包括图形验证图片" normalimage="file='helpbtn.png'" hotimage="file='helpbtn_hover.png'"  pushedimage="file='helpbtn_push.png'"/>
					</HorizontalLayout>