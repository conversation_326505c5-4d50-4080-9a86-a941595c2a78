5、客户端收到后需判断和验证版本号，将缺失的item自有包更新/整合到本地configdata.xml中。

5、客户端收到后需判断和验证版本号，将缺失的item自有包更新/整合到本地configdata.xml中。

前述：为避免sharelist中返回的json大量堆积的情况，同时在控制台允许母帐号（主动分享方帐户）可以获知子帐户（或指被动接收方帐户）在客户端对其分享的item进行了删除。

约定以下接口：

==========================================================================

用户在客户端针对分享的item进行删除（仅针对当前帐户已收到分享的item类型）

*     客户端主动发起请求：

请求参数:  Aes(token=xxxxxxx&uid=session_unique_id &act=del_report&rnd=rnd(datetime))

==========================================================================

详细逻辑流程说明

依据上述接口约定，逻辑流程如下：

用户在客户端对接收到的分享类型item进行删除完成后，线程方式请求服务器端上述接口。

服务器端接受到数据包串后进行解码，同时对此帐户下的sharelist列表进行标志位更新 更新为 is_del = 1。 表示此记录(此分享item)在客户端已经删除。

    同时在控制台给发送方（母帐户或发送帐户）显示此分享已被子帐户（或接收方）删除。（保证json数据保持在可用的记录数范围，数据量不会过度增涨）。

服务器端表处理完成后，返回给客户端成功/失败状态报告。

客户端收到状态报告后，不再进行重试。否则重试3次。

==========================================================================

服务器/客户端 
item子帐户间、
子帐户发送item到母帐户、

分享数据接口：

==========================================================================

增加分享按钮入口：

窗口界面调整：

说明：子帐户点击分享不再提示此窗口，而是列出此帐户归属的所有团队帐户列表,允许子帐户将item发送给RootID下的所有用户(包括RootID)。

接口：

使用 sharelist 接口标准流程。

（注意： 服务器端需增加判定， 如果 子帐户发送的 分享item 已存在目标帐户下，需在控制台后台进行弹窗提醒：“目标帐户已存在此环境，是否仍要强制发送？” 同时必须并提示发送者：如仍旧要强制发送则会覆盖接收帐户已有的配置信息。）

==========================================================================

批量分享接口说明 (sharelist接口扩展说明)：

==========================================================================

(遵循候鸟浏览器第十三版.docx所有约定)

批量分享接口遵循(sharelist接口)所有约定

非协作版/协作版母帐户及子帐户

发送item到其它普通会员帐户

（待进行）

[2022-07-26 新增][2022-08-03增订]

第二十九章

候鸟浏览器

帐户报表批量导入环境、

已导入环境批量管理

界面流程、逻辑详细流程及约定。

一、界面入口：

本地帐户批量导入 允许用户将xlsx格式的帐户数据批量导入到候鸟客户端，并自动转换为独立的环境。此管理器提供所有涉及数据导入、数据管理、批量修改等功能。

二、帐户导入环境管理器 界面：

点击菜单入口项后，默认弹出此窗口。用户点击左上角可下载xlsx模板文件，将自有数据粘帖入模板文件后，开始导入。

本地帐户批量导入窗体主界面，此界面提供所有导入项的分支功能。

关键字查找支持查找项为：环境名称、代理主机、代理帐号、帐户名称四项。

默认允许导入的有效配置项的下拉框中为checkbox勾选项，允许用户导入列表项中指定的字段。

列表框中 显示为已导入到客户端的xlsx数据，此数据在读取xlsx内容完成时的默认状态为等待导入（即：列表数据在右下角按钮：导入到指定环境分组 按钮还未点击时）。

全选 CHECKBOX 负责控制列表中各项的全局勾选。

总计成功加载数据数量为列表项的实际项数。

批量自定义环境名称 按钮点击后，弹窗提供用户输入环境名称定义，可批量将列表项的环境名称进行统一替换。

环境分组管理器按钮点击后：弹出环境分组管理器窗口，供用户在此时添加新分组和修改已有分组名称。

导入指定环境分组 按钮：点击后，将列表项中的已勾选项生成独立的ITEM（根据指定的环境分组，生成新的环境），之后进入同步环境到服务器端基础流程。

10、导入指定环境分组 按钮 点击走完生成新环境流程后，将 导入状态栏的 等待导入更新为 已成功导入，同时，列表框内的：分组字段列显示对应的已导入的分组名。

三、网站登录名密码、帐户存储数据结构。

Password_list.xml 保存用户所有的帐户【摘自第九章节点内容】

各节点描述说明如下：

LOGIN_ACCOUNT  password_list.xml归属于哪个登录帐户：例 <EMAIL>

DOMAIN TITLE  帐户名称

PLATFORM     归属于哪个平台（帐户销售平台：例：stripe）

DOMAINURL NAME 用户名/密码自动填入的URL网址。

LOGIN         登录用户名

ORGIN_PASSWORD     登录密码(明文)

ENCRYPT_PASSWORD 登录密码(密文)

IS_VALID	     是否有效

UPDATETIME    更新时间

CREATETIME    创建时间

用户通过在客户端批量导入的所有网站登录帐户密码集合，保存在password_list.xml中。

Password_list.xml中的记录集合用途：专用于支持用户通过管理面板，自由分配到各个环境中。

password_list.xml的增加途径：除用户将本地帐户批量导入的方式进行增加外，在用户手工进行登录网站时，在关闭浏览器的时间节点，检测一次chromium/defalut目 录下login.data db文件，将db中新增的记录，自动取出并存储到password_list.xml。

password_list.xml中 保存明文和密文的密码。明文密码用于提供用户在管理器面板进行修改，密文密码用于存储到chromium/login data中进行网页表单自动填入。

四、Login Data明文密码与密文密码的chromium加解密说明：

chrome浏览器的数据在：C:\Users\<USER>\AppData\Local\Google\Chrome\User Data\Default下

Login Data和Cookies文件,保存了一些敏感信息(sqllite),用db工具打开查看cookie文件没有加密

login data中的密码加密了，解析方法如下：

——————————————————————————————

Chromium其中有段密码加密的代码，是CryptProtectData，这是Windows的api函数，也有解密的函数

Bool Encrypt::EncryptString(const std::string& plaintext,std::string* ciphertext) {

DATA_BLOB input;

Input.pbData = static_cast<DWORD>(plaintext.length());

DATA_BLOB output;

BOOL result = CryptProtectData(&input, L””,NULL, NULL, NULL, 0,&output);

If (!result)

Return false;

//复制操作

Ciphertext->assign(reinterpret_cast<std::string::value_type*>(output.pbData);

LocalFree(output.pbData);

Return true;

}

解密Password

把文件复制出来，win32crypt安装会报错，需要换成 pywin32
下载地址：https://pypi.org/project/pywin32/#files

解密代码：

import os

import sys

import sqlite3

# import win32crypt

from win32 import win32crypt

db_file_path = 'F:\\test\\LoginData'

conn = sqlite3.connect(db_file_path)

cursor = conn.cursor()

cursor.execute(

'select username_value, password_value, signon_realm from logins')

# 接收全部返回结果

for data in cursor.fetchall():

passwd = win32crypt.CryptUnprotectData(data[1], None, None, None, 0)

print(passwd)

if passwd:

print('-------------------------')

print('[+]用户名: ' + data[0])

print('[+]密码: ' + str(passwd[1]))

print('[+]网站URL: ' + str(data[2]))

以上可获得所有login data中解密后的登录密码。

Proxy.xml 数据结构：

旧：

<?xml version="1.0" encoding="gb2312" ?>

<VERSION version="968">

<VER ID="4" LOGIN_ACCOUNT="" SESSION_UNIQUE_ID="">

<SESSION NAME="" />

<TYPE TYPE="http"/>

<PxyMode VALUE="2"/>

<CONNECT IP=""/>

<PROXY IP="127.0.0.1"/>

<PORT VALUE="8889"/>

<LOGIN NAME=""/>

<PASSWORD VALUE=""/>

<DNS VALUE=""/>

<IS_VALID VALUE="0" />

<CREATETIME VALUE="2021-08-07 21:44:22"/>

<UPDATETIME VALUE="2022-05-25 16:29:00"/>

<COUNTRY VALUE="CN"/>

<TIMEZONE VALUE="Asia/Shanghai (-480)"/>

<LANGVAGE VALUE="zh-CN"/>

</VER>

新：[********二次修正]

<?xml version="1.0" encoding="gb2312" ?>

<VERSION version="2805">

<VER ID="4" LOGIN_ACCOUNT="" SESSION_UNIQUE_ID="">

<SESSION NAME="" />

<TYPE TYPE="http"/>

<PxyMode VALUE="2"/>

<RealPxyMode VALUE="2"/>

<PROXY IP="************"/>

<PORT VALUE="11242"/>

<LOGIN NAME=""/>

<PASSWORD VALUE=""/>

<IS_VALID VALUE="1" />

<CREATETIME VALUE="2022-09-29 18:59:26"/>

<UPDATETIME VALUE="2022-10-02 18:19:46"/>

<COUNTRY VALUE="JP"/>

<TIMEZONE VALUE="Asia/Tokyo (-540)"/>

<LANGVAGE VALUE="ja-JP"/>

<Country_area VALUE=""/>

<SPEED VALUE="177ms"/>

</VER>

</VERSION>

RealPxyMode:  samrt代理需要


================================================== 表格内容 ==================================================

{
"message": "Export Cookie Success",
"code": 0,
"data": {
            “Session_Id” : 373808cb37bd63f5f7d92415e736e85f, 	//环境ID
            "cookie": “cookie content”
       }
}

{
"message": " Export Cookie failed",
"code": -14,
"data": {
            “Session_Id” : 373808cb37bd63f5f7d92415e736e85f, 	//环境ID
            "cookie": “”
       }
}