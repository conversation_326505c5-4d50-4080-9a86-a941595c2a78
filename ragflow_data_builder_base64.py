#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RAGFlow数据构建器
处理s_article.csv文件，为每条记录生成单独的txt文件
支持远程图片获取并转换为base64格式
"""

import csv
import os
import re
import base64
import requests
import time
from urllib.parse import urljoin, urlparse
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ragflow_data_builder.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class RAGFlowDataBuilder:
    def __init__(self, csv_file='s_article.csv', output_dir='output_articles'):
        self.csv_file = csv_file
        self.output_dir = output_dir
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        # 创建输出目录
        Path(self.output_dir).mkdir(exist_ok=True)
        
    def sanitize_filename(self, filename):
        """清理文件名，移除不合法字符"""
        # 移除或替换不合法的文件名字符
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        # 限制文件名长度
        if len(filename) > 200:
            filename = filename[:200]
        return filename.strip()
    
    def extract_image_urls(self, content):
        """从内容中提取图片URL"""
        if not content:
            return []
        
        # 匹配Markdown格式的图片: ![alt](url)
        markdown_pattern = r'!\[.*?\]\((.*?)\)'
        # 匹配HTML格式的图片: <img src="url">
        html_pattern = r'<img[^>]+src=["\']([^"\']+)["\'][^>]*>'
        
        urls = []
        urls.extend(re.findall(markdown_pattern, content))
        urls.extend(re.findall(html_pattern, content))
        
        return list(set(urls))  # 去重
    
    def download_image_as_base64(self, url, max_retries=3):
        """下载图片并转换为base64格式"""
        for attempt in range(max_retries):
            try:
                logger.info(f"正在下载图片: {url} (尝试 {attempt + 1}/{max_retries})")
                
                response = self.session.get(url, timeout=30)
                response.raise_for_status()
                
                # 获取内容类型
                content_type = response.headers.get('content-type', 'image/jpeg')
                
                # 转换为base64
                image_data = base64.b64encode(response.content).decode('utf-8')
                
                # 创建data URL格式
                data_url = f"data:{content_type};base64,{image_data}"
                
                logger.info(f"图片下载成功: {url}")
                return data_url
                
            except Exception as e:
                logger.warning(f"下载图片失败 (尝试 {attempt + 1}/{max_retries}): {url}, 错误: {str(e)}")
                if attempt < max_retries - 1:
                    time.sleep(2)  # 重试前等待2秒
                
        logger.error(f"图片下载最终失败: {url}")
        return None
    
    def process_content_with_images(self, content):
        """处理内容中的图片，将远程图片转换为base64格式"""
        if not content:
            return content
        
        image_urls = self.extract_image_urls(content)
        processed_content = content
        
        for url in image_urls:
            # 如果是相对URL，尝试构建完整URL
            if url.startswith('/') or not url.startswith('http'):
                # 假设基础域名（根据示例数据推测）
                base_url = 'https://admin.mbbrowser.com'
                full_url = urljoin(base_url, url)
            else:
                full_url = url
            
            # 下载图片并转换为base64
            base64_data = self.download_image_as_base64(full_url)
            
            if base64_data:
                # 替换原始URL为base64数据
                processed_content = processed_content.replace(url, base64_data)
            else:
                logger.warning(f"无法处理图片: {url}")
        
        return processed_content
    
    def format_timestamp(self, timestamp_str):
        """格式化时间戳为可读格式"""
        try:
            if timestamp_str and timestamp_str.strip():
                timestamp = int(timestamp_str)
                return time.strftime('%Y%m%d_%H%M%S', time.localtime(timestamp))
        except (ValueError, TypeError):
            pass
        return 'unknown_time'
    
    def create_article_file(self, row):
        """为单条记录创建txt文件"""
        try:
            # 获取标题和时间
            title = row.get('title', '').strip()
            add_time = row.get('add_time', '')
            
            if not title:
                title = f"article_{row.get('id', 'unknown')}"
            
            # 格式化时间
            formatted_time = self.format_timestamp(add_time)
            
            # 创建文件名
            filename = f"{self.sanitize_filename(title)}_{formatted_time}.txt"
            filepath = os.path.join(self.output_dir, filename)
            
            # 处理内容
            content = row.get('content', '')
            content_en = row.get('content_en', '')
            
            # 处理图片
            processed_content = self.process_content_with_images(content)
            processed_content_en = self.process_content_with_images(content_en)
            
            # 构建文件内容
            file_content = []
            file_content.append(f"标题: {title}")
            
            if row.get('title_en'):
                file_content.append(f"英文标题: {row['title_en']}")
            
            file_content.append(f"ID: {row.get('id', '')}")
            file_content.append(f"分类ID: {row.get('article_category_id', '')}")
            file_content.append(f"添加时间: {add_time}")
            file_content.append(f"更新时间: {row.get('upd_time', '')}")
            file_content.append(f"访问次数: {row.get('access_count', '0')}")
            
            if row.get('seo_title'):
                file_content.append(f"SEO标题: {row['seo_title']}")
            
            if row.get('seo_keywords'):
                file_content.append(f"SEO关键词: {row['seo_keywords']}")
            
            if row.get('seo_desc'):
                file_content.append(f"SEO描述: {row['seo_desc']}")
            
            file_content.append("\n" + "="*50 + " 内容 " + "="*50)
            file_content.append(processed_content)
            
            if processed_content_en:
                file_content.append("\n" + "="*50 + " 英文内容 " + "="*50)
                file_content.append(processed_content_en)
            
            # 写入文件
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write('\n'.join(file_content))
            
            logger.info(f"已创建文件: {filename}")
            return True
            
        except Exception as e:
            logger.error(f"创建文件失败，记录ID: {row.get('id', 'unknown')}, 错误: {str(e)}")
            return False
    
    def process_csv(self):
        """处理CSV文件"""
        try:
            logger.info(f"开始处理CSV文件: {self.csv_file}")
            
            with open(self.csv_file, 'r', encoding='utf-8') as csvfile:
                reader = csv.DictReader(csvfile)
                
                total_rows = 0
                success_count = 0
                
                for row in reader:
                    total_rows += 1
                    logger.info(f"正在处理第 {total_rows} 条记录: {row.get('title', 'unknown')}")
                    
                    if self.create_article_file(row):
                        success_count += 1
                    
                    # 每处理10条记录暂停一下，避免请求过于频繁
                    if total_rows % 10 == 0:
                        time.sleep(1)
                
                logger.info(f"处理完成! 总记录数: {total_rows}, 成功: {success_count}, 失败: {total_rows - success_count}")
                
        except Exception as e:
            logger.error(f"处理CSV文件时发生错误: {str(e)}")

def main():
    """主函数"""
    print("RAGFlow数据构建器启动...")
    print("Python版本: 3.13.3")
    print("="*60)
    
    builder = RAGFlowDataBuilder()
    builder.process_csv()
    
    print("="*60)
    print("处理完成! 请查看output_articles目录和ragflow_data_builder.log日志文件")

if __name__ == "__main__":
    main()
