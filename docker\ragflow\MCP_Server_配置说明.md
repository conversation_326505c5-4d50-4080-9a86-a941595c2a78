# RAGFlow MCP Server 配置说明

## 📋 配置概述

RAGFlow的MCP (Model Context Protocol) Server已成功启用，提供标准化的API接口用于外部应用集成。

## ✅ 已完成的配置

### 🔧 **Docker Compose配置**

在 `docker-compose.yml` 中已启用以下MCP Server配置：

```yaml
services:
  ragflow:
    image: ${RAGFLOW_IMAGE}
    # MCP server configuration - ENABLED
    command:
      - --enable-mcpserver          # ✅ 启用MCP服务器
      - --mcp-host=0.0.0.0         # ✅ 监听所有网络接口
      - --mcp-port=9382            # ✅ MCP服务端口
      - --mcp-base-url=http://127.0.0.1:9380  # ✅ RAGFlow内部API地址
      - --mcp-script-path=/ragflow/mcp/server/server.py  # ✅ MCP服务器脚本
      - --mcp-mode=self-host       # ✅ 自托管模式
      - --mcp-host-api-key=ragflow-mcp-2025  # ✅ API认证密钥
    ports:
      - 9382:9382  # ✅ MCP端口映射
```

### 📊 **配置参数说明**

| 参数 | 值 | 说明 |
|------|-----|------|
| **启用状态** | ✅ 已启用 | MCP Server功能已开启 |
| **监听地址** | 0.0.0.0 | 允许外部访问 |
| **服务端口** | 9382 | MCP API端口 |
| **内部API** | http://127.0.0.1:9380 | RAGFlow主服务地址 |
| **运行模式** | self-host | 自托管模式 |
| **API密钥** | ragflow-mcp-2025 | 认证密钥 |

## 🚀 启动和使用

### **方法1: 使用批处理脚本启动**

```batch
# 运行启动脚本
F:\augment\docker\ragflow\restart_with_mcp.bat
```

### **方法2: 手动Docker命令**

```bash
# 进入RAGFlow目录
cd F:\augment\docker\ragflow

# 停止现有服务
docker-compose down

# 启动服务（包含MCP Server）
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker logs ragflow-server
```

## 🌐 访问地址

### **服务端点**
- **RAGFlow Web界面**: http://localhost:9380
- **MCP Server API**: http://localhost:9382
- **健康检查**: http://localhost:9382/health

### **API认证**
- **认证方式**: Bearer Token
- **API密钥**: `ragflow-mcp-2025`
- **请求头**: `Authorization: Bearer ragflow-mcp-2025`

## 🧪 测试MCP Server

### **方法1: 使用Python测试脚本**

```bash
# 运行测试脚本
python F:\augment\docker\ragflow\test_mcp_server.py
```

### **方法2: 使用curl命令**

```bash
# 测试健康检查
curl http://localhost:9382/health

# 测试API端点（需要认证）
curl -H "Authorization: Bearer ragflow-mcp-2025" \
     -H "Content-Type: application/json" \
     http://localhost:9382/api/v1/status
```

### **方法3: 使用Python客户端**

```python
import requests

# 配置
base_url = "http://localhost:9382"
api_key = "ragflow-mcp-2025"
headers = {
    "Authorization": f"Bearer {api_key}",
    "Content-Type": "application/json"
}

# 测试连接
response = requests.get(f"{base_url}/health")
print(f"健康检查: {response.status_code}")

# 测试API查询
query_data = {
    "query": "候鸟浏览器如何使用？",
    "knowledge_base": "your_kb_id"
}

response = requests.post(
    f"{base_url}/api/v1/query",
    headers=headers,
    json=query_data
)
print(f"查询结果: {response.json()}")
```

## 🔍 故障排除

### **常见问题**

#### 1. **MCP Server无法访问**
```bash
# 检查容器状态
docker ps | grep ragflow

# 查看容器日志
docker logs ragflow-server

# 检查端口占用
netstat -an | findstr 9382
```

#### 2. **API认证失败**
- 确认API密钥正确: `ragflow-mcp-2025`
- 检查请求头格式: `Authorization: Bearer ragflow-mcp-2025`

#### 3. **服务启动失败**
```bash
# 重新构建并启动
docker-compose down
docker-compose up -d --force-recreate

# 查看详细日志
docker-compose logs ragflow
```

### **日志位置**
- **容器日志**: `docker logs ragflow-server`
- **文件日志**: `F:\augment\docker\ragflow\ragflow-logs\`

## 📚 API使用示例

### **基础查询API**

```python
import requests

class RAGFlowMCPClient:
    def __init__(self):
        self.base_url = "http://localhost:9382"
        self.api_key = "ragflow-mcp-2025"
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
    
    def query(self, question, kb_id=None):
        """查询知识库"""
        data = {"query": question}
        if kb_id:
            data["knowledge_base"] = kb_id
        
        response = requests.post(
            f"{self.base_url}/api/v1/query",
            headers=self.headers,
            json=data
        )
        return response.json()
    
    def get_status(self):
        """获取服务状态"""
        response = requests.get(
            f"{self.base_url}/api/v1/status",
            headers=self.headers
        )
        return response.json()

# 使用示例
client = RAGFlowMCPClient()

# 查询状态
status = client.get_status()
print("服务状态:", status)

# 执行查询
result = client.query("候鸟浏览器的主要功能是什么？")
print("查询结果:", result)
```

## 🛡️ 安全配置

### **生产环境建议**

1. **更换API密钥**
   ```yaml
   - --mcp-host-api-key=your-secure-api-key-here
   ```

2. **限制访问IP**
   ```yaml
   - --mcp-host=127.0.0.1  # 仅本地访问
   ```

3. **启用HTTPS**
   - 配置反向代理（如Nginx）
   - 使用SSL证书

4. **防火墙配置**
   - 限制9382端口的访问来源
   - 配置适当的网络安全组

## 📈 监控和维护

### **健康检查**
```bash
# 定期检查服务状态
curl -f http://localhost:9382/health || echo "MCP Server异常"
```

### **性能监控**
```bash
# 查看容器资源使用
docker stats ragflow-server

# 查看端口连接
netstat -an | findstr 9382
```

### **日志轮转**
- 定期清理 `ragflow-logs` 目录
- 配置日志轮转策略

## 🎯 总结

✅ **MCP Server已成功配置并启用**
- 服务地址: http://localhost:9382
- API密钥: ragflow-mcp-2025
- 支持外部应用集成
- 提供标准化API接口

🚀 **下一步操作**
1. 运行 `restart_with_mcp.bat` 启动服务
2. 使用 `test_mcp_server.py` 测试功能
3. 根据需要调整安全配置
4. 开始集成外部应用

现在RAGFlow的MCP Server已经准备就绪，可以为外部应用提供强大的知识检索和问答服务！
