# API_环境管理_03_创建环境

## 功能描述
创建一个环境，支持配置名称、描述、分组和代理设置。成功后返回环境ID

## 所属模块
环境管理

## API信息

- **路径**: `/api/v1/session/create`
- **方法**: POST
- **内容类型**: application/json
- **服务器地址**: http://127.0.0.1:8186

## 请求参数

| 参数名称               | 类型     | 是否必传 | 示例/默认值                                                                 | 说明                                                                                   |
|--------------------|--------|------|-------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------|
| Session_Name       | 字符串  | 是    | "20230223"                                                                    | 环境名称，最多60个字符                                                          |
| Session_Desc       | 字符串  | 否    | "这是一个测试"                                                              | 环境描述，最多150个字符                                                  |
| Session_Group      | 字符串  | 否    | "新分组"                                                                    | 环境分组，最多30个字符；未提供时默认为"默认"                  |
| Session_System     | 字符串  | 否    | "Windows"                                                                     | 环境操作系统                                                         |
| Session_Resolution | 字符串  | 否    | "1024x768"                                                                    | 环境分辨率                                                                       |
| Session_User_Agent | 字符串  | 否    | "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36" | 环境的UA值 |
| Proxy_Type         | 字符串  | 否    | "HTTP"                                                                        | 代理类型 [HTTP, HTTPS, SSH, SOCKS4, SOCKS4A, SOCKS5, Oxylabsauto, Lumauto, Luminati_HTTP, Luminati_HTTPS, smartproxy, noproxy] |
| Proxy_Ip           | 字符串  | 否    | "127.0.0.1"                                                                   | 代理IP                                                                                     |
| Proxy_Port         | 字符串  | 否    | "1080"                                                                        | 代理端口                                                                                   |
| Proxy_Username     | 字符串  | 否    | "TEST"                                                                        | 代理用户名                                                                               |
| Proxy_Password     | 字符串  | 否    | "TEST"                                                                        | 代理密码                                                                               |
| TimeZone           | 字符串  | 否    | "美国/阿拉斯加 -09:000"                                                           | 时区                                                                                    |
| CountryCode        | 字符串  | 否    | ""                                                                            | 国家代码                                                                                 |
| CityCode           | 字符串  | 否    | ""                                                                            | 城市代码                                                                                    |
| RegionCode         | 字符串  | 否    | ""                                                                            | 州/地区代码                                                                            |
| LanguageCode       | 字符串  | 否    | ""                                                                            | 环境默认语言                                                                 |
| Cookie             | 字符串  | 否    | "这是一个COOKIE"                                                               | JSON格式的cookie文本                                                                   |
| Automatic_Configure| 位     | 是    | 1                                                                             | 自动配置高级指纹参数 [0: 快速创建, 1: 包含代理IP验证] |
| Disable_video      | 位     | 否    | 0                                                                             | 视频限流 [0: 关闭, 1: 开启]                                                             |
| Disable_img        | 位     | 否    | 0                                                                             | 图片限流 [0: 关闭, 1: 开启]                                                             |
| HomePage_url       | 字符串  | 否    | "https://www.baidu.com"                                                       | 环境起始页；未设置时默认为"https://www.yalala.com/?wd=mb"              |

## 请求示例

```json
{
    "Session_Name": "20230223",
    "Session_Desc": "这是一个测试",
    "Session_Group": "新分组",
    "Proxy_Type": "HTTP",
    "Proxy_Ip": "127.0.0.1",
    "Proxy_Port": "1080",
    "Proxy_Username": "TEST",
    "Proxy_Password": "TEST",
    "TimeZone": "美国/阿拉斯加 -09:000",
    "CountryCode": "",
    "CityCode": "",
    "RegionCode": "",
    "LanguageCode": "",
    "Cookie": "这是一个COOKIE",
    "Automatic_Configure": 1,
    "Disable_video": 0,
    "Disable_img": 0
}
```

## 成功响应

```json
{
    "requestId": "8b558e5c5d1c437183c34aa03a09a368",
    "message": "添加成功",
    "code": 0,
    "data": {
        "Session_Id": "373808cb37bd63f5f7d92415e736e85f"
    }
}
```

## 使用说明

1. 环境名称为必填项，最多60个字符
2. 支持多种代理类型配置
3. 可配置高级指纹参数和限流设置
4. 成功创建后返回唯一的环境ID
5. 建议使用[POSTMAN调试工具](/api/postman-example)进行接口测试

## 相关链接

- [获取环境列表](/api/session/listid)
- [更新环境](/api/session/update)
- [删除环境](/api/session/delete)
- [POSTMAN调试工具](/api/postman-example)
- [错误码对照表](/api/code)
