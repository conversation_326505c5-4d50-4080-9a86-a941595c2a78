package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
)

// WingRAGFlowClient Wing 客户端的 RAGFlow 集成
type WingRAGFlowClient struct {
	BaseURL          string
	APIKey           string
	HouniaoDatasetID string
	Client           *http.Client
}

// NewWingRAGFlowClient 创建新的 Wing RAGFlow 客户端
func NewWingRAGFlowClient() *WingRAGFlowClient {
	return &WingRAGFlowClient{
		BaseURL:          "http://************:9380",
		APIKey:           "ragflow-UzYTlhMjIwNjI0YzExZjA4NzUyMDI0Mm",
		HouniaoDatasetID: "6d7b2882624511f09a0d0242ac130006",
		Client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// Dataset 数据集结构
type Dataset struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	ChunkCount  int    `json:"chunk_count"`
	Description string `json:"description"`
}

// RetrievalRequest 检索请求
type RetrievalRequest struct {
	Question   string   `json:"question"`
	DatasetIDs []string `json:"dataset_ids"`
	TopK       int      `json:"top_k"`
}

// RetrievalResponse 检索响应
type RetrievalResponse struct {
	Code int `json:"code"`
	Data struct {
		Chunks []struct {
			ContentWithWeight string  `json:"content_with_weight"`
			Similarity        float64 `json:"similarity"`
		} `json:"chunks"`
	} `json:"data"`
	Message string `json:"message"`
}

// GetDatasets 获取数据集列表
func (c *WingRAGFlowClient) GetDatasets() ([]Dataset, error) {
	url := fmt.Sprintf("%s/api/v1/datasets", c.BaseURL)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Authorization", "Bearer "+c.APIKey)
	req.Header.Set("Content-Type", "application/json")

	resp, err := c.Client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var result struct {
		Code int       `json:"code"`
		Data []Dataset `json:"data"`
	}

	if err := json.Unmarshal(body, &result); err != nil {
		return nil, err
	}

	if result.Code != 0 {
		return nil, fmt.Errorf("API error: code %d", result.Code)
	}

	return result.Data, nil
}

// QueryHouniaoKnowledgeBase 查询候鸟浏览器知识库
func (c *WingRAGFlowClient) QueryHouniaoKnowledgeBase(question string) (string, error) {
	url := fmt.Sprintf("%s/api/v1/retrieval", c.BaseURL)

	reqData := RetrievalRequest{
		Question:   question,
		DatasetIDs: []string{c.HouniaoDatasetID}, // 只使用候鸟数据集
		TopK:       5,
	}

	jsonData, err := json.Marshal(reqData)
	if err != nil {
		return "", fmt.Errorf("序列化请求失败: %v", err)
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("Authorization", "Bearer "+c.APIKey)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")

	resp, err := c.Client.Do(req)
	if err != nil {
		return "", fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应失败: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("HTTP错误 %d: %s", resp.StatusCode, string(body))
	}

	var result RetrievalResponse
	if err := json.Unmarshal(body, &result); err != nil {
		return "", fmt.Errorf("解析响应失败: %v", err)
	}

	if result.Code != 0 {
		return "", fmt.Errorf("查询失败: %s", result.Message)
	}

	// 提取最相关的答案
	if len(result.Data.Chunks) > 0 {
		bestMatch := result.Data.Chunks[0]
		return fmt.Sprintf("根据候鸟浏览器知识库查询结果：\n\n%s\n\n(相似度: %.3f)",
			bestMatch.ContentWithWeight, bestMatch.Similarity), nil
	}

	return "未找到相关信息", nil
}

// QueryRAGFlow 通用 RAGFlow 查询方法（兼容原有接口）
func (c *WingRAGFlowClient) QueryRAGFlow(question string) (string, error) {
	return c.QueryHouniaoKnowledgeBase(question)
}

// TestConnection 测试连接
func (c *WingRAGFlowClient) TestConnection() error {
	// 1. 测试数据集获取
	datasets, err := c.GetDatasets()
	if err != nil {
		return fmt.Errorf("获取数据集失败: %v", err)
	}

	fmt.Printf("✅ RAGFlow 连接成功，找到 %d 个数据集\n", len(datasets))

	// 显示数据集信息
	for i, dataset := range datasets {
		fmt.Printf("   %d. %s (ID: %s, 块数: %d)\n", i+1, dataset.Name, dataset.ID, dataset.ChunkCount)
	}

	// 2. 测试候鸟知识库查询
	testQuestion := "候鸟浏览器如何配置代理？"
	result, err := c.QueryHouniaoKnowledgeBase(testQuestion)
	if err != nil {
		return fmt.Errorf("测试查询失败: %v", err)
	}

	fmt.Printf("📄 测试查询结果: %s\n", result[:200]+"...")
	return nil
}

// GetKnowledgeBaseSummary 获取知识库摘要信息
func (c *WingRAGFlowClient) GetKnowledgeBaseSummary() (map[string]interface{}, error) {
	datasets, err := c.GetDatasets()
	if err != nil {
		return nil, err
	}

	summary := map[string]interface{}{
		"total_datasets": len(datasets),
		"datasets":       datasets,
		"houniao_dataset_available": false,
	}

	// 检查候鸟数据集是否可用
	for _, dataset := range datasets {
		if dataset.ID == c.HouniaoDatasetID {
			summary["houniao_dataset_available"] = true
			summary["houniao_dataset_info"] = dataset
			break
		}
	}

	return summary, nil
}

// BatchQuery 批量查询
func (c *WingRAGFlowClient) BatchQuery(questions []string) ([]map[string]interface{}, error) {
	results := make([]map[string]interface{}, 0, len(questions))

	for i, question := range questions {
		fmt.Printf("🔍 查询 %d/%d: %s\n", i+1, len(questions), question)

		answer, err := c.QueryHouniaoKnowledgeBase(question)
		
		result := map[string]interface{}{
			"question": question,
			"success":  err == nil,
		}

		if err != nil {
			result["error"] = err.Error()
		} else {
			result["answer"] = answer
		}

		results = append(results, result)

		// 添加延迟避免请求过快
		if i < len(questions)-1 {
			time.Sleep(1 * time.Second)
		}
	}

	return results, nil
}

func main() {
	client := NewWingRAGFlowClient()

	fmt.Println("🚀 Wing RAGFlow 客户端启动")
	fmt.Printf("服务器: %s\n", client.BaseURL)
	fmt.Printf("候鸟数据集ID: %s\n", client.HouniaoDatasetID)

	// 测试连接
	if err := client.TestConnection(); err != nil {
		fmt.Printf("❌ 连接失败: %v\n", err)
		return
	}

	// 获取知识库摘要
	summary, err := client.GetKnowledgeBaseSummary()
	if err != nil {
		fmt.Printf("⚠️  获取知识库摘要失败: %v\n", err)
	} else {
		fmt.Printf("\n📊 知识库摘要:\n")
		summaryJSON, _ := json.MarshalIndent(summary, "", "  ")
		fmt.Printf("%s\n", summaryJSON)
	}

	// 批量查询测试
	testQuestions := []string{
		"候鸟浏览器如何配置代理？",
		"候鸟浏览器的功能有哪些？",
		"如何使用候鸟浏览器？",
	}

	fmt.Printf("\n🧪 开始批量查询测试...\n")
	results, err := client.BatchQuery(testQuestions)
	if err != nil {
		fmt.Printf("❌ 批量查询失败: %v\n", err)
		return
	}

	// 显示结果统计
	successCount := 0
	for _, result := range results {
		if result["success"].(bool) {
			successCount++
		}
	}

	fmt.Printf("\n📋 查询结果统计:\n")
	fmt.Printf("   总查询数: %d\n", len(results))
	fmt.Printf("   成功查询: %d\n", successCount)
	fmt.Printf("   成功率: %.1f%%\n", float64(successCount)/float64(len(results))*100)

	// 显示成功的查询示例
	fmt.Printf("\n✅ 成功查询示例:\n")
	for i, result := range results {
		if result["success"].(bool) && i < 1 { // 只显示第一个成功的示例
			fmt.Printf("   问题: %s\n", result["question"])
			answer := result["answer"].(string)
			if len(answer) > 300 {
				answer = answer[:300] + "..."
			}
			fmt.Printf("   答案: %s\n", answer)
			fmt.Println(strings.Repeat("-", 60))
		}
	}

	fmt.Println("\n✅ Wing RAGFlow 客户端测试完成！")
	fmt.Println("💡 现在可以在 Wing 客户端中集成这个 RAGFlow 查询功能了！")
}
