服务器端分组数据传值约定：

服务器端分组数据传值约定：

客户端采用线程方式，将用户已更新的环境的group_name和session_unique_id传给服务器端。

服务器端在控制台中的实时环境、历史环境，增加下拉条，允许用户根据group_name的不同显示不同的分组中的环境。（默认仍旧显示所有环境）

FROM_ACCOUNT 值传递给客户端（重要）

在传递FROMUID和SHARETIME值的时侯，要在原接口基础上，一并将FROM_ACCOUNT值传给客户端。

同时，服务器端与客户端要确保增加传参、值不会影响到原旧版客户端。

4、服务器端接口支持批量模式，允许客户端批量将group_name和session_unique_id传给服务器端。

[2021-05-08 新增]

第二十一章 候鸟浏览器

浏览器内核后端自动更新与内核版本迭代

数据接口与详细图文逻辑流程及约定。

第二十一章 候鸟浏览器 浏览器内核后端自动更新与内核版本迭代 数据接口与详细图文逻辑流程及约定。[2021-05-08 新增]

第一节：

前述：

由于目前候鸟产品之浏览器内核更新仅依赖客户端版本升级，无法在用户使用客户端的过程中，实时对用户群体进行浏览器内核更新，也无法针对单个用户进行针对性的内核更新。同时，需要应对用户对浏览器访问需求的多样化和各类日益增涨的商业网站和业务需求来进行实时候鸟浏览器内核更新，现约定并新增 候鸟客户端“浏览器内核后端静默自动更新”完整接口、功能与图文流程、数据架构与详细逻辑。

第二节：

流程总述：

1、浏览器内核更新数据与可执行数据存放于服务器端。

2、在管理员后台的交互界面，，管理员采用全局或指定特定用户，通过心跳接口触发客户端进行指定网址请求，使客户端获取服务器端指定数据版本号下的所有浏览器数据并进行用户端本地浏览器内核部份静默更新。（覆盖）

3、通过管理员后台，可全局或指定特定用户客户端获取服务器端指定数据版本号下的可执行程序，并进行用户端本地浏览器内核部份静默更新（执行）。

第三节：

内核版本与数据存储格式约定：

候鸟浏览器内核版本以服务器端 /www/chrome/x.x.x.x/ 文件夹名称版本号为准。初始值： *******

当前版本中的内核所有文件存放在 /x.x.x.x 文件夹中。

文件包默认为.zip格式与exe文件格式和README.MD格式。

仅此三类格式。

Zip格式为覆盖模式进行数据覆盖。

EXE格式为更新，注入等需要静默运行模式。

每一次的内核版本，对应不同的版本号的文件夹。

服务器端管理员后台通过遍历文件夹给出下拉列表，提供指定用户的版本切换更新。

第四节：

服务器端接口流程详述：

全局用户：所有用户全自动后台静默自动更新内核版本。

指定用户：指定单个用户全自动后台静默自动更新内核版本。

在admin.mbbrowser.com 的后台管理员界面中，在上图增加一项： “内核更新”入口。

“全局内核更新”入口页面显示元素：

A： 标题：候鸟浏览器内核更新 – 全局所有用户更新

B、更新目标：RADIO控件  全局用户 /  指定用户

（全局用户：通常实际业务使用中较少使用甚至不使用，仅在极端情况下使用，通常在遇到罕见的所有用户同时大批量出现内核问题时，可使用此方式。）

指定用户后方显示input控件，里面允许管理员填写用户的邮箱。

默认RAIDO二项均不勾选

C、 更新类型：CHECKBOX控件  内核数据覆盖  /  EXE更新内核

（如全部勾选，将/x.x.x.x/下所有zip包和exe发往客户端

如仅勾选 内核数据覆盖，只将zip包进行下发。

如仅勾选 EXE更新内核，只将exe包进行下发。

默认所有项不勾选

）

D、 进程关闭：RADIO 控件  不关闭 / 关闭

E、版本号：下拉列表，根据服务器端文件夹列出所有版本的内核，最新的内核版本在最上方。（默认显示最新的版本号）

F、版本更新描述：根据B项的下拉列表，从/x.x.x.x/中读取README.MD文件，并显示在TEXTAREA控件中。(控件使用READONLY方式)

G、内核版本时间：当选中某个版本号后，在此显示内核版本号文件夹创建时间。

H、保存并更新： 底部按钮。 点击后将指令发往心跳队列。

（保存并更新按钮点击，需要进行弹窗二次确认）

I、备注：richedit控件，允许管理员将更新的缘由填写到控件中。

J、点击按钮记录日志：将此表单的提交内容，时间递增方式写入到chrome_update.log 日志中

K、点保存并更新时，表单需要验证是否勾选了B、C项。

第五节： 单个用户触发流程

1、

此界面增加一项按钮： 内核更新（仅在用户在线时显示）

点击后跳转到第四节页面，并将用户的邮箱等信息放到表单中。

2、此界面增加显示客户端内核版本号。（详见：第七节内容）

第六节

客户端浏览器内核版本用户端标记约定：

本章节约定内核版本可静默更新后，用户chrome目录下必须放置版本标识文件: version.md

默认原始版本起始值： *******

(即在首个客户端版本发布中，第20章节内容生效后的version.md值为*******)

指令下发约定与流程：

心跳接口：connected| req_chromeupdate

客户端收到此接口指令后，

客户端在心跳流程中收到connected| req_chromeupdate  标志位

客户端实时获取本地token值。

客户端实时发起请求串:

chromeupdate_request请求串：

4、

服务器端获取到chrome_version 对应的版本号后，对当前要下发的版本号进行对比，如果当前下发的版本与此版本号不相同，返回：

如成功：

{“op”:”chrome_update”,"msg":"操作成功","code":0,"data":{"sourceurl":"\/www\/chrome\/x.x.x.x\/xxxxxxxxx.zip?a9cfd52509c8e13554176b785f939522",”chrome_version”:”x.x.x.x”,”act”:” unzip”, ”close_process”:” 0”}}

{“op”:”chrome_update”,"msg":"操作成功","code":0,"data":{"sourceurl":"\/www\/chrome\/x.x.x.x\/xxxxxxxxx.exe?a9cfd52509c8e13554176b785f939522",”chrome_version”:”x.x.x.x”,”act”:” execute” ,”close_process”:” 0”}}

失败：

{"msg":"版本相同，无需更新","code":-1,"data":""}

5、


================================================== 表格内容 ==================================================

参数名称 | 类型 | 必传 | 样例串/默认值 | 说明
Session_ID | array | 是 | 373808cb37bd63f5f7d92415e736e85f, 705cc4c139e69b729a2fd277f30e1863 | 指定环境ID查询环境
Session_container_type | int | 否 | 1 | =1:返回环境完整内容
=2:返回环境精简内容
=3:返回环境包含plugin/script精简内容

{
    "message": "Success",
    "code": 0,
"data": {
  "listcontainer": [
        {
        "Session_Name": “商用业务环境一”
        "Session_ID": "373808cb37bd63f5f7d92415e736e85f",
"Group_Name": “default”
“Actived_script_id”:” O73808cb37bd63f5f7d92415e736e999”,
“Actiived_script_name”:”这是一个脚本例子”,
“Actiived_script_encode”:”true”,
"Weblogin_Account_Count": "4",
        "Weblogin_Account_name":"<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>",
"Plugins_Count": "4",
        "Plugin_Id": "jjbnhpnlakcdgfnnldamfeinfmahhdlm,jjbnhpnlakcdgfnnldamfeinfmahhdlm,jjbnhpnlakcdgfnnldamfeinfmahhdlm, jjbnhpnlakcdgfnnldamfeinfmahhdlm",
        “template_id”:”123456”
        “template_name”:”抖音国际版”
        "browser_Path": "D:\\mbbrowser\\Chromium_x64\\chromium.exe",
        "browser_CDP_Port": 46973,
        "MBData_Path": "C:\\MBDATA\xxxxxxxxxx\xxxxxxxxxx\xxxxxxxxxxx”,
        "Public_ip": "************",
        "Internel_ip": "**************",
        "isDynamicIp": false,
        "StartPage": "about:blank",
        "System": "windows",
        "Resolution: "1024x768",
        "UserAgent": " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "proxyType": "socks5",
        "proxy_ip": "127.0.0.1",
        "proxy_port": "1080",
        "webdriver":"C:\\Users\\<USER>\\houniao\\Driver\\100\\chromedriver.exe",        //根据当前打开环境的内核返回对应内核webdriver驱动路径
        "updatetime":”2022-12-13 13:23:09”,
        "createtime":”2022-09-23 08:47:36”,
        "item_version":”3030”,
"UnActived_script_list": 
                    [{
                     "UnActived_script_encode" : "false",
                     "UnActived_script_Name" : "AAA",
                     "UnActived_script_ID" : "17c70e014d61b1fa43d3638ca5a1bc21"
                         },{
                     "UnActived_script_encode" : "false",
                     "UnActived_script_Name" : "BBB",
                     "UnActived_script_ID" : "17c70e014d61b1fa43d3638ca5a1bc22"
                         }],
"status": 0
},
{
        "Session_Name": “商用业务环境二”
        "Session_ID": "705cc4c139e69b729a2fd277f30e1863",
"Group_Name": “default”
“Actived_script_id”:” O73808cb37bd63f5f7d92415e736e999”,
“Actiived_script_name”:”这是一个脚本例子”,
“Actiived_script_encode”:”true”,
"Weblogin_Account_Count": "4",
        "Weblogin_Account_name":"<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>",
"Plugins_Count": "4",
        "Plugin_Id": "jjbnhpnlakcdgfnnldamfeinfmahhdlm,jjbnhpnlakcdgfnnldamfeinfmahhdlm,jjbnhpnlakcdgfnnldamfeinfmahhdlm, jjbnhpnlakcdgfnnldamfeinfmahhdlm",
        “template_id”:”123456”
        “template_name”:”抖音国际版”
        "browser_Path": "D:\\mbbrowser\\Chromium_x64\\chromium.exe",
        "browser_CDP_Port": 46973,
        "MBData_Path": "C:\\MBDATA\xxxxxxxxxx\xxxxxxxxxx\xxxxxxxxxxx”,
        "Public_ip": "************",
        "Internel_ip": "**************",
        "isDynamicIp": false,
        "StartPage": "about:blank",
        "proxyType": "socks5",
        "proxy_ip": "127.0.0.1",
        "proxy_port": "1080",
        "webdriver":"C:\\Users\\<USER>\\houniao\\Driver\\100\\chromedriver.exe",        //根据当前打开环境的内核返回对应内核webdriver驱动路径
"updatetime":”2022-12-13 13:23:09”,
        "createtime":”2022-09-23 08:47:36”,
        "item_version":”1030”,
"UnActived_script_list": 
                    [{
                     "UnActived_script_encode" : "false",
                     "UnActived_script_Name" : "AAA",
                     "UnActived_script_ID" : "17c70e014d61b1fa43d3638ca5a1bc21"
                         },{
                     "UnActived_script_encode" : "false",
                     "UnActived_script_Name" : "BBB",
                     "UnActived_script_ID" : "17c70e014d61b1fa43d3638ca5a1bc22"
                         }],
        "status": 0
}],”total”:2
}