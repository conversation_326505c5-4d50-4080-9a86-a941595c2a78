#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度分析DOCX文件内容构成
"""

import os
import zipfile
from pathlib import Path
from docx import Document
import xml.etree.ElementTree as ET

def analyze_docx_structure(docx_path):
    """分析DOCX文件的内部结构"""
    print(f"📄 深度分析: {Path(docx_path).name}")
    
    file_size = Path(docx_path).stat().st_size
    print(f"📊 文件大小: {file_size/1024:.1f} KB ({file_size/1024/1024:.1f} MB)")
    
    # DOCX文件实际上是ZIP文件
    try:
        with zipfile.ZipFile(docx_path, 'r') as zip_file:
            print(f"\n📦 ZIP文件内容:")
            
            total_uncompressed = 0
            media_size = 0
            xml_size = 0
            
            for file_info in zip_file.filelist:
                total_uncompressed += file_info.file_size
                
                if file_info.filename.startswith('word/media/'):
                    media_size += file_info.file_size
                    print(f"   📷 {file_info.filename}: {file_info.file_size/1024:.1f} KB")
                elif file_info.filename.endswith('.xml'):
                    xml_size += file_info.file_size
                    if file_info.file_size > 1024:  # 只显示大于1KB的XML文件
                        print(f"   📄 {file_info.filename}: {file_info.file_size/1024:.1f} KB")
                else:
                    if file_info.file_size > 1024:
                        print(f"   📁 {file_info.filename}: {file_info.file_size/1024:.1f} KB")
            
            print(f"\n📊 内容构成:")
            print(f"   媒体文件: {media_size/1024:.1f} KB ({media_size/total_uncompressed*100:.1f}%)")
            print(f"   XML文件: {xml_size/1024:.1f} KB ({xml_size/total_uncompressed*100:.1f}%)")
            print(f"   其他文件: {(total_uncompressed-media_size-xml_size)/1024:.1f} KB")
            print(f"   未压缩总大小: {total_uncompressed/1024:.1f} KB")
            print(f"   压缩率: {(1-file_size/total_uncompressed)*100:.1f}%")
            
            # 分析主文档XML
            try:
                with zip_file.open('word/document.xml') as doc_xml:
                    content = doc_xml.read().decode('utf-8')
                    print(f"\n📄 主文档XML大小: {len(content)/1024:.1f} KB")
                    
                    # 统计一些关键元素
                    paragraph_count = content.count('<w:p ')
                    table_count = content.count('<w:tbl>')
                    image_count = content.count('<w:drawing>')
                    
                    print(f"   段落标签: {paragraph_count}")
                    print(f"   表格标签: {table_count}")
                    print(f"   图片标签: {image_count}")
                    
            except Exception as e:
                print(f"⚠️  无法读取主文档XML: {str(e)}")
    
    except Exception as e:
        print(f"❌ 分析ZIP结构失败: {str(e)}")

def analyze_docx_content(docx_path):
    """分析DOCX文档内容"""
    try:
        doc = Document(docx_path)
        
        print(f"\n📋 文档内容分析:")
        print(f"   段落数: {len(doc.paragraphs)}")
        print(f"   表格数: {len(doc.tables)}")
        
        # 统计文本内容
        total_text_chars = 0
        non_empty_paragraphs = 0
        
        for paragraph in doc.paragraphs:
            text = paragraph.text.strip()
            if text:
                non_empty_paragraphs += 1
                total_text_chars += len(text)
        
        print(f"   非空段落: {non_empty_paragraphs}")
        print(f"   总文本字符: {total_text_chars:,}")
        print(f"   平均段落长度: {total_text_chars/max(1, non_empty_paragraphs):.1f} 字符")
        
        # 统计表格内容
        total_table_chars = 0
        total_cells = 0
        
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    total_cells += 1
                    total_table_chars += len(cell.text)
        
        print(f"   表格单元格: {total_cells}")
        print(f"   表格文本字符: {total_table_chars:,}")
        
        total_content_chars = total_text_chars + total_table_chars
        print(f"   总内容字符: {total_content_chars:,}")
        print(f"   纯文本估算大小: {total_content_chars/1024:.1f} KB")
        
        # 分析内容类型
        print(f"\n📊 内容类型分析:")
        
        # 统计不同类型的段落
        heading_count = 0
        normal_count = 0
        list_count = 0
        
        for paragraph in doc.paragraphs:
            if paragraph.style.name.startswith('Heading'):
                heading_count += 1
            elif paragraph.style.name.startswith('List'):
                list_count += 1
            else:
                normal_count += 1
        
        print(f"   标题段落: {heading_count}")
        print(f"   列表段落: {list_count}")
        print(f"   普通段落: {normal_count}")
        
        # 查找图片和嵌入对象
        print(f"\n🖼️  媒体内容检测:")
        
        # 检查段落中的图片
        image_paragraphs = 0
        for paragraph in doc.paragraphs:
            if paragraph._element.xpath('.//w:drawing'):
                image_paragraphs += 1
        
        print(f"   包含图片的段落: {image_paragraphs}")
        
        # 检查表格中的图片
        image_cells = 0
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    if cell._element.xpath('.//w:drawing'):
                        image_cells += 1
        
        print(f"   包含图片的表格单元格: {image_cells}")
        
        return {
            'paragraphs': len(doc.paragraphs),
            'tables': len(doc.tables),
            'text_chars': total_text_chars,
            'table_chars': total_table_chars,
            'total_chars': total_content_chars,
            'image_paragraphs': image_paragraphs,
            'image_cells': image_cells
        }
        
    except Exception as e:
        print(f"❌ 分析文档内容失败: {str(e)}")
        return None

def suggest_split_strategy(analysis_result, file_size_kb):
    """建议切分策略"""
    if not analysis_result:
        return
    
    print(f"\n💡 切分策略建议:")
    
    # 计算文本内容占比
    text_size_kb = analysis_result['total_chars'] / 1024
    text_ratio = text_size_kb / file_size_kb * 100
    
    print(f"   文本内容占比: {text_ratio:.1f}%")
    
    if text_ratio < 10:
        print(f"   ⚠️  文档主要包含图片/媒体内容，文本内容较少")
        print(f"   建议: 按段落数量切分，每个文件800-1000段落")
        print(f"   预期: 每个文件包含大量图片，大小可能在1-3MB")
    elif text_ratio < 30:
        print(f"   📊 文档包含较多图片内容")
        print(f"   建议: 按段落数量切分，每个文件600-800段落")
        print(f"   预期: 每个文件大小在1-2MB")
    else:
        print(f"   📝 文档主要为文本内容")
        print(f"   建议: 按文本内容切分，每个文件50-100KB文本")
        print(f"   预期: 每个文件大小在100-200KB")
    
    # 计算建议的切分数量
    if analysis_result['image_paragraphs'] > 100:
        suggested_parts = max(8, file_size_kb // 2048)  # 每个文件约2MB
        print(f"   建议切分数量: {suggested_parts} 个文件")
    else:
        suggested_parts = max(5, analysis_result['paragraphs'] // 800)
        print(f"   建议切分数量: {suggested_parts} 个文件")

if __name__ == "__main__":
    source_file = r"F:\augment\output\docx_files\候鸟浏览器基础框架第七十七版.docx"
    
    print("🔍 DOCX文件深度分析工具")
    print("=" * 60)
    
    if not Path(source_file).exists():
        print(f"❌ 文件不存在: {source_file}")
        exit(1)
    
    # 分析文件结构
    analyze_docx_structure(source_file)
    
    # 分析文档内容
    analysis_result = analyze_docx_content(source_file)
    
    # 建议切分策略
    file_size_kb = Path(source_file).stat().st_size / 1024
    suggest_split_strategy(analysis_result, file_size_kb)
    
    print("\n" + "=" * 60)
    print("🎯 分析完成！")
