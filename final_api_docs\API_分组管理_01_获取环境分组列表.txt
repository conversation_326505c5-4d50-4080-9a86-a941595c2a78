# API_分组管理_01_获取环境分组列表

## 功能描述
获取环境分组列表

## 所属模块
分组管理

## API信息

- **服务器地址**: http://127.0.0.1:8186
- **HTTP方法**: POST
- **数据格式**: JSON
- **认证方式**: APP_ID + APP_KEY

## 请求示例

```json
{
  "APP_ID": "your_app_id",
  "APP_KEY": "your_app_key",
  // 其他参数根据具体功能而定
}
```

## 响应示例

```json
{
  "status": 0,
  "msg": "Success",
  "data": {}
}
```

## 使用说明

1. 确保候鸟浏览器客户端已启动
2. 确保已通过登录接口获得认证
3. 根据具体功能需求调整请求参数
4. 建议使用POSTMAN进行接口测试

## 相关链接

- [POSTMAN调试工具](/api/postman)
- [错误码对照表](/api/code)
- [使用须知](/api/help)
