session_advandce.xml 环境高级配置窗

session_advandce.xml 环境高级配置窗

session_package_mgr.xml 会话数据包管理器

sessionarchiveitem.xml

sessionitemmenu.xml

sessionitemmenu2.xml 环境验证右键菜单

sessionlist.xml 环境列表管理

sessionlistitem.xml 环境列表控件

sessionlistitemimport.xml

sessionlistitemmenu.xml

sessionlistitemverify.xml 环境版本验证列表控件

sessionpluginsitem.xml

sessionpluginsitemmenu.xml MENU_复制环境ID到剪帖板菜单

sessionpluginsitemmenu2.xml

sessionpproxyipitem.xml

setmainurl.xml 批量自定义环境名称 ？？

setting.xml 候鸟SETUP面板

sharesession_mgr.xml

syncmenu.xml

tipwnd.xml

tooltip.xml

tooltipplugins.xml 插件列表TIP窗口控件

uaitemmenu.xml MENU_我的本地UA库列表右键菜单

uaitemmenusys.xml

updateloginwnd.xml 环境登录帐户管理器 – 添加新帐户

updatemessage.xml 在线升级

useragent_mgr.xml  UserAgent 管理器

useragentitem.xml

useragentitem2.xml

userpasswnd.xml

validconfig.xml

validconfig2.xml

webrtcitem.xml

[20230718]

第三十四章

候鸟浏览器

多语言全自动生成、管理平台详述

依据传统，具体工作步骤、顺序安排，人员分配在工作群里具体阐述。

前述：

为扩大候鸟客户群体，支持海外客户使用其母语进行候鸟产品的使用，候鸟产品于2023年5月份开始，除支持中英文外，增加支持日，俄，法，韩，德，中文繁体等语种。同时将在后期提供海外支付网关，供海外用户通过新的网关进行付款，来增加产品收益。

由于多语种的对外提供，对于候鸟的新功能日常开发及维护将面临巨大的工作量。例如：添加任何一个新窗口、新的描述，新的注解，开发者将不得不将每一句描述手工翻译成各个语种，同时每次修改都需要重新进行一遍多语种翻译，并重新生成lng文件，随着日后语种的增多，此工作量将成倍增加，导致重复劳动巨幅增加，严重影响团队原高质量的开发效率。

基于上点，候鸟团队在服务器端，基于PHP+MYSQL+有道translate api 研发出全新的多语言全自动生成、管理平台。

地址：http://dev.soukesoft.com:3030

ID: admin PASS:88XXXXX

平台界面：

功能详述：

1、增删改查 基础功能：

支持在候鸟多语言管理平台里对每一条产品窗体的描述进行增加、删除、修改、全局查找功能。
增加新记录，如增加一条中文记录，将自动瞬间生成并翻译所有其它字段的语种、无需再人工手动翻译。

2、全自动调用有道API翻译功能：

支持针对有道API的翻译限制，文字长度限制，调用频率限制等，候鸟多语言管理平台当前能保证100%翻译出正确结果、通过文字自动分多段分多次调用API，并在结合集合收到后进行二次组合的方式，完成翻译结果的入库。

3、数据库记录备份与自动回滚功能：

支持每日数据库自动备份功能，同时支持数据库自动回滚功能，支持候鸟多语言管理平台所在的操作系统的硬盘快照备份功能。

4、一键生成所有语种LNG文件功能：

支持一键生成最终候鸟产品直接使用的所有LNG文件，并能保证所有lng文件格式100%无错误，最大程度避免人工修改所带来的各种不确定因素。大幅减少多语言所带来的额外工作量。

注：候鸟多语言管理平台仅提供给内部研发成员使用，不对外提供，经由团队成员历时一周全部完成，于2023-06中旬上线正式使用。

[20230718]

第三十五章

候鸟浏览器

客户端-环境版本校验管理功能新增

依据传统，具体工作步骤、顺序安排，人员分配在工作群里具体阐述。

入口： 候鸟客户端 主面板 -> 会话环境管理器 -> 版本验证

前述：

为保证候鸟客户对于自有环境的全面数据版本控制，原旧版本的简单管理模式更新为增强管理模式。避免用户在操作使用环境时，尤其是在团队模式下，多个团队成员对于同一个源环境的各种不同人为操作导致同一个环境最终演变为不同业务属性下的不同版本，在候鸟产品缺少相应功能、服务进而导致用户无法准确判断可能带来的数据缺失、云端/本地数据版本不一致而带来的各种业务层面不确定因素。

通过强化数据版本验证管理，让客户清楚的、一目了然的对帐户下各个环境版本进行查看，对比，溯源，给客户带来心理上的数据安全感。为其长期使用候鸟产品提供可靠保障。

详述：

因网络、候鸟服务器运行等客观不稳定因素，存在版本同步失败所带来的服务器 < > 客户端数据版本不一致的现像。

用户在使用不一致版本的环境会带来诸多深层次的问题、直接影响到其业务的正常有序开展。

通过此功能，首次支持用户能准确把握自有环境的数据完整状态，通过与服务器的版本号直达展示，能第一手时间对环境如存在版本不一致情况进行人工控制与管理。

优点如下：

1、通过此功能支持批量版本一致化校验，抛弃了原旧版本过于简单的校验界面所带来的细节缺失。新版本可高效的针对性进行环境版本批量二次同步、批量多次同步，最大程度保障版本的一致性。

2、减少和规避掉用户在运行环境时，校验和同步所带来时间等待损失。更加快速的让环境浏览器窗口展现出来并进行自有业务开展。

3、列表模式展示各个环境的版本号，准确的第一时间知晓哪些环境是存在版本过旧情况，二次弥补、规避服务器、网络因素导致版本不一致问题。

4、更加利于产品质量控制与客户在线服务支持、环境业务问题的数据故障分析。

注：此功能已于2023-07中旬上线正式使用。

[20230720]

第三十六章

候鸟浏览器

客户端-用户操作日志管理

依据传统，具体工作步骤、顺序安排，人员分配在工作群里具体阐述。

一、前述：

分析紫鸟的功能后，当前候鸟产品有一块存有缺失，即用户操作行为日志列表还少一个【供用户自行查阅自有单帐号下历史环境操作行为的功能（此功能增强用户对候鸟的使用长期依赖）】，新版推出后，当前需将这一块功能补上。

需要提供：登录日志、环境操作日志、环境包导入日志、环境包导出日志、COOKIE导入日志、COOKIE导出日志、环境指纹变化日志、代理检测日志 八项。【分享的日志在控制台里有，分享的功能当前计划仍旧由控制台主导，暂不在客户端二次提供】

在环境数量急剧增加，个人同时运营多个候鸟子帐户时，难免出现各种环境错误的管理，页面的错误操作，导致用户在出现业务异常时，仅根据其个人回忆，个人分析原因将变得异常困难，最后有可能无端将责任归咎于本不应背锅的候鸟产品，从而导致对候鸟产品的粘性降低，客户无端流失的客观矛盾。

日志的提供，帮助用户对自有环境的全面控制，通过日志的详细清单，让用户更加全面的分析环境的历史变化路径，其个人历史操作步骤，为用户全面管理错综复杂的海量环境细节提供了进一步的分析强化，对于商业用户的业务风险控制提供直接的帮助与管控。避免因环境问题归咎于候鸟产品，在候鸟历届版本中，用户对于环境的网站历史变化、误操作、业务被封停、环境数据意外变化仅凭个人记忆，仅通过个人回忆其候鸟使用史，很难自行保障其商用业务的稳健运营。

依据ADSPOWER，紫鸟类的团队协作职能参照：关于主帐号可查看，管理子帐号的所有环境（不仅限于分享环境）级别权限，控制子帐户的环境使用，子帐户操作日志查看，子帐户的环境分享控制范畴内，二次迭代的团队协作功能新增，在WEB控制台提供预测存在困难情况【原因 主要表现在 用户主要操作场景是在客户端，客户端/WEB控制台的用户两头切换操作存在操作效率低下从而降低用户使用体验的因素，另WEB页面操作存在响应慢，控制力度低【页面去动态显示多帐户实时状态困难，切换或同时显示多个帐户实时状态困难等】，服务器端新增额外压力等问题，需系统性文档化同时考虑是否在WEB控制台进行扩展还是在客户端上进行提供】。

二、约定名称： 本地环境日志管理器

三、功能入口：主面板设置菜单 -> 本地环境日志管理器

四、日志类别

总计八项：

登录日志、环境操作日志、环境包导入日志、环境包导出日志、COOKIE导入日志、COOKIE导出日志、环境指纹变化日志、代理检测日志

【COOKIE导入导出和COOKIE的变化可以放到一个表中?或者放入到环境操作日志表中】

五、各项日志说明及图示：

1、登录日志

2、环境操作日志

1、环境的开启，关闭，加入到日志中。

开启： (session_id) 已成功运行。
           (session_id) 运行失败。(原因)

关闭:   (session_id) 已成功关闭。

(session_id) 关闭失败。(原因)

2、环境的创建，批量创建，克隆，加入到日志中。（@177  ）

创建： (session_id) 已成功创建。(单创建面板操作)

(session_id) 创建失败。(原因) (单创建面板操作)

批量创建:  (session_id) 已成功创建。(批量创建面板操作)

(session_id) 创建失败。(原因) (批量创建面板操作)

克隆： (session_id) 已成功克隆。(克隆面板操作)

(session_id) 克隆失败。(原因) (克隆面板操作)

3、环境的分享（接收分享，加入到日志中）

(session_id) 已成功接收分享。（发送方：帐户[email]）

(session_id) 接收分享失败。(原因)（发送方：帐户[email]）

4、环境的删除，加入到日志中。

(session_id) 已成功删除。

(session_id) 删除失败。(原因)


================================================== 表格内容 ==================================================

{
"message "APISERVER shut down.",
"code": 0,
"data": true
}

参数名称 | 类型 | 必传 | 样例串/默认值 | 说明
Session_ID | array | 是 | 373808cb37bd63f5f7d92415e736e85f, 705cc4c139e69b729a2fd277f30e1863 | 指定环境ID查询环境