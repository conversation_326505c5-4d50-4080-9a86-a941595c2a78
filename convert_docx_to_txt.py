#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将DOCX文件批量转换为TXT文件
"""

import os
from pathlib import Path
from docx import Document

def extract_text_from_docx(docx_path):
    """从DOCX文件中提取纯文本"""
    try:
        doc = Document(docx_path)
        
        # 提取段落文本
        paragraphs = []
        for paragraph in doc.paragraphs:
            text = paragraph.text.strip()
            if text:  # 只保留非空段落
                paragraphs.append(text)
        
        # 提取表格文本
        tables_text = []
        for table in doc.tables:
            table_content = []
            for row in table.rows:
                row_content = []
                for cell in row.cells:
                    cell_text = cell.text.strip()
                    if cell_text:
                        row_content.append(cell_text)
                if row_content:
                    table_content.append(" | ".join(row_content))
            
            if table_content:
                tables_text.append("\n".join(table_content))
        
        # 合并所有文本
        all_text = []
        
        # 添加段落文本
        if paragraphs:
            all_text.extend(paragraphs)
        
        # 添加表格文本
        if tables_text:
            all_text.append("\n" + "="*50 + " 表格内容 " + "="*50)
            all_text.extend(tables_text)
        
        return "\n\n".join(all_text)
        
    except Exception as e:
        print(f"❌ 提取文本失败: {docx_path.name} - {str(e)}")
        return None

def convert_docx_to_txt(source_dir, output_dir=None):
    """批量转换DOCX文件为TXT文件"""
    source_path = Path(source_dir)
    
    if output_dir is None:
        output_path = source_path / "txt_files"
    else:
        output_path = Path(output_dir)
    
    # 创建输出目录
    output_path.mkdir(exist_ok=True)
    
    # 查找所有DOCX文件
    docx_files = list(source_path.glob("*.docx"))
    
    print(f"📁 源目录: {source_dir}")
    print(f"📁 输出目录: {output_path}")
    print(f"📊 找到 {len(docx_files)} 个DOCX文件")
    print("=" * 80)
    
    if not docx_files:
        print("❌ 没有找到DOCX文件")
        return []
    
    converted_files = []
    failed_files = []
    total_size = 0
    
    for i, docx_file in enumerate(sorted(docx_files), 1):
        print(f"🔄 [{i}/{len(docx_files)}] 转换: {docx_file.name}")
        
        try:
            # 提取文本
            text_content = extract_text_from_docx(docx_file)
            
            if text_content is None:
                failed_files.append(docx_file.name)
                continue
            
            # 生成TXT文件名
            txt_filename = docx_file.stem + ".txt"
            txt_filepath = output_path / txt_filename
            
            # 保存TXT文件
            with open(txt_filepath, 'w', encoding='utf-8') as f:
                f.write(text_content)
            
            # 统计信息
            file_size = txt_filepath.stat().st_size / 1024  # KB
            total_size += file_size
            char_count = len(text_content)
            line_count = text_content.count('\n') + 1
            
            converted_files.append(str(txt_filepath))
            
            print(f"   ✅ 成功: {txt_filename}")
            print(f"      大小: {file_size:.1f} KB")
            print(f"      字符数: {char_count:,}")
            print(f"      行数: {line_count}")
            print()
            
        except Exception as e:
            failed_files.append(docx_file.name)
            print(f"   ❌ 失败: {str(e)}")
            print()
    
    # 输出统计结果
    print("=" * 80)
    print(f"📊 转换完成统计:")
    print(f"   总文件数: {len(docx_files)}")
    print(f"   成功转换: {len(converted_files)}")
    print(f"   转换失败: {len(failed_files)}")
    print(f"   成功率: {len(converted_files)/len(docx_files)*100:.1f}%")
    print(f"   总大小: {total_size:.1f} KB")
    print(f"   平均大小: {total_size/max(1, len(converted_files)):.1f} KB")
    
    if failed_files:
        print(f"\n❌ 转换失败的文件:")
        for failed_file in failed_files:
            print(f"   - {failed_file}")
    
    return converted_files

def analyze_txt_files(txt_dir):
    """分析生成的TXT文件"""
    txt_path = Path(txt_dir)
    txt_files = list(txt_path.glob("*.txt"))
    
    if not txt_files:
        print("❌ 没有找到TXT文件")
        return
    
    print(f"\n📋 TXT文件分析:")
    print("=" * 80)
    
    total_size = 0
    total_chars = 0
    total_lines = 0
    
    size_distribution = {
        'small': 0,    # < 10KB
        'medium': 0,   # 10-50KB
        'large': 0     # > 50KB
    }
    
    for txt_file in sorted(txt_files):
        try:
            file_size = txt_file.stat().st_size / 1024  # KB
            total_size += file_size
            
            with open(txt_file, 'r', encoding='utf-8') as f:
                content = f.read()
                char_count = len(content)
                line_count = content.count('\n') + 1
                
                total_chars += char_count
                total_lines += line_count
            
            # 分类统计
            if file_size < 10:
                size_distribution['small'] += 1
                status = "🟢"
            elif file_size < 50:
                size_distribution['medium'] += 1
                status = "✅"
            else:
                size_distribution['large'] += 1
                status = "🟡"
            
            print(f"{status} {txt_file.name}")
            print(f"   大小: {file_size:.1f} KB, 字符: {char_count:,}, 行数: {line_count}")
            
        except Exception as e:
            print(f"❌ 分析失败: {txt_file.name} - {str(e)}")
    
    print("=" * 80)
    print(f"📊 TXT文件统计:")
    print(f"   总文件数: {len(txt_files)}")
    print(f"   总大小: {total_size:.1f} KB")
    print(f"   平均大小: {total_size/len(txt_files):.1f} KB")
    print(f"   总字符数: {total_chars:,}")
    print(f"   总行数: {total_lines:,}")
    print(f"   平均字符数: {total_chars//len(txt_files):,}")
    
    print(f"\n📈 大小分布:")
    print(f"   🟢 小文件 (< 10KB): {size_distribution['small']} 个")
    print(f"   ✅ 中等文件 (10-50KB): {size_distribution['medium']} 个")
    print(f"   🟡 大文件 (> 50KB): {size_distribution['large']} 个")

if __name__ == "__main__":
    source_directory = r"F:\augment\output\docx_files2"
    output_directory = r"F:\augment\output\docx_files2\txt_files"
    
    print("📄 DOCX转TXT批量转换工具")
    print("=" * 80)
    
    if not Path(source_directory).exists():
        print(f"❌ 源目录不存在: {source_directory}")
        exit(1)
    
    # 执行转换
    converted_files = convert_docx_to_txt(source_directory, output_directory)
    
    if converted_files:
        print(f"\n🎉 转换完成! 生成了 {len(converted_files)} 个TXT文件")
        
        # 分析生成的TXT文件
        analyze_txt_files(output_directory)
        
        print(f"\n📁 TXT文件保存位置: {output_directory}")
        print("🎯 所有TXT文件已准备就绪！")
    else:
        print("❌ 没有成功转换任何文件")
