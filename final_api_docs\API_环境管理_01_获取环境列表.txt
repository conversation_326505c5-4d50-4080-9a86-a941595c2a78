# API_环境管理_01_获取环境列表

## 功能描述
查询所有符合条件的环境ID。用户只能查询自己的环境和共享给他们的环境

## 所属模块
环境管理

## API信息

- **路径**: `/api/v1/session/listid`
- **方法**: POST
- **内容类型**: application/json
- **服务器地址**: http://127.0.0.1:8186

## 请求参数

| 参数名称               | 类型    | 是否必传 | 示例/默认值                             | 说明       |
|--------------------|-------|------|-------------------------------------|----------|
| Session_Name       | 字符串 | 否    | "商用业务环境一"                        | 按环境名称查询 |
| Session_GroupName  | 字符串 | 否    | "分组名称"                           | 按分组名称查询 |
| Session_CreateTime_Start | 字符串 | 否    | "2022-09-01 13:14:15"               | 创建时间起始   |
| Session_CreateTime_End | 字符串 | 否    | "2022-09-20 13:14:15"               | 创建时间结束   |
| Session_UpdateTime_Start | 字符串 | 否    | "2022-09-01 13:14:15"               | 最后更新时间起始 |
| Session_UpdateTime_End | 字符串 | 否    | "2022-09-20 13:14:15"               | 最后更新时间结束 |
| Session_Recv       | 位     | 否    | 1                                   | 返回接收到的共享环境 |
| Session_Recv_Account | 字符串 | 否    | "<EMAIL>"                    | 返回由特定帐户共享的环境 |
| Session_Sent       | 位     | 否    | 1                                   | 返回共享的环境 |
| Session_Sent_Account | 字符串 | 否    | "<EMAIL>"                    | 返回共享给特定帐户的环境 |
| Session_User_Agent | 字符串 | 否    | "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36" | 按UA值查询 |
| Proxy_Ip           | 字符串 | 否    | "127.0.0.1"                         | 按代理IP查询 |
| Proxy_Type         | 数组   | 否    | ["smartproxy"]                      | 按代理类型查询 |
| Comment            | 字符串 | 否    | "关键字"                            | 按备注关键字查询 |

## 请求示例

```json
{
    "Session_Name": "商用业务环境一",
    "Session_GroupName": "分组名称",
    "Session_CreateTime_Start": "2022-09-01 13:14:15",
    "Session_CreateTime_End": "2022-09-20 13:14:15"
}
```

## 使用说明

1. 支持多种查询条件组合
2. 可按环境名称、分组、创建时间等筛选
3. 支持查询共享环境
4. 用户只能查询自己的环境和共享给他们的环境
5. 建议使用[POSTMAN调试工具](/api/postman-example)进行接口测试

## 相关链接

- [创建环境](/api/session/create)
- [更新环境](/api/session/update)
- [POSTMAN调试工具](/api/postman-example)
- [错误码对照表](/api/code)
