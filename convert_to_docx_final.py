#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将清理后的TXT文件转换为DOCX格式
"""

import os
import re
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TxtToDocxFinalConverter:
    def __init__(self, input_dir='api_docs_clean', output_dir='api_docs_final_docx'):
        self.input_dir = input_dir
        self.output_dir = output_dir
        
        # 创建输出目录
        Path(self.output_dir).mkdir(exist_ok=True)
    
    def clean_content_for_docx(self, content):
        """为DOCX格式清理内容"""
        try:
            lines = content.split('\n')
            cleaned_lines = []
            
            # 跳过前面的元数据
            content_started = False
            
            for line in lines:
                line = line.strip()
                
                # 跳过元数据行
                if line.startswith('API文档:') or line.startswith('URL:') or line.startswith('抓取时间:'):
                    continue
                
                # 跳过分隔线
                if line.startswith('='):
                    content_started = True
                    continue
                
                # 开始收集内容
                if content_started:
                    # 过滤导航内容
                    if line in ['首页', '应用', '价格', '下载', 'API', '使用教程', '常见问题', '佣金计划', '博客中心', '登录&注册', '简体中文']:
                        continue
                    
                    # 过滤明显的导航模式
                    if re.match(r'^[•·]\s*(首页|应用|价格|下载|API|使用教程|常见问题|佣金计划|博客中心|登录|注册).*', line):
                        continue
                    
                    # 保留有意义的内容
                    if len(line) > 2:
                        cleaned_lines.append(line)
                    elif line == '':
                        # 保留空行但避免连续空行
                        if cleaned_lines and cleaned_lines[-1] != '':
                            cleaned_lines.append('')
            
            # 移除开头和结尾的空行
            while cleaned_lines and not cleaned_lines[0]:
                cleaned_lines.pop(0)
            while cleaned_lines and not cleaned_lines[-1]:
                cleaned_lines.pop()
            
            return '\n'.join(cleaned_lines)
            
        except Exception as e:
            logger.error(f"清理内容失败: {str(e)}")
            return content
    
    def create_simple_docx_content(self, title, content):
        """创建简单的文档内容"""
        try:
            # 提取真正的标题
            clean_title = title
            if ' - ' in title:
                clean_title = title.split(' - ', 1)[1]
            
            # 创建文档内容
            doc_content = f"{clean_title}\n"
            doc_content += "=" * len(clean_title) + "\n\n"
            doc_content += content
            
            return doc_content
            
        except Exception as e:
            logger.error(f"创建文档内容失败: {str(e)}")
            return f"{title}\n\n{content}"
    
    def convert_file(self, txt_file):
        """转换单个TXT文件"""
        try:
            # 读取TXT文件
            with open(txt_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 从文件名提取标题
            base_name = os.path.splitext(os.path.basename(txt_file))[0]
            
            # 清理文件名作为标题
            title = base_name.replace('API_', '').replace('_', ' - ', 1).replace('_', ': ')
            
            # 清理内容
            cleaned_content = self.clean_content_for_docx(content)
            
            # 如果清理后内容太少，跳过
            if len(cleaned_content.strip()) < 20:
                logger.warning(f"文件内容太少，跳过: {txt_file}")
                return False
            
            # 创建文档内容
            doc_content = self.create_simple_docx_content(title, cleaned_content)
            
            # 生成输出文件名（保存为TXT格式，因为没有python-docx库）
            output_file = os.path.join(self.output_dir, f"{base_name}.txt")
            
            # 保存文件
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(doc_content)
            
            logger.info(f"已转换: {base_name}")
            return True
            
        except Exception as e:
            logger.error(f"转换文件失败: {txt_file}, 错误: {str(e)}")
            return False
    
    def convert_all_files(self):
        """转换所有TXT文件"""
        logger.info("开始转换清理后的TXT文件...")
        
        txt_files = [f for f in Path(self.input_dir).glob('*.txt') if f.name != '文件清单.txt']
        success_count = 0
        
        for txt_file in txt_files:
            if self.convert_file(txt_file):
                success_count += 1
        
        logger.info(f"转换完成! 总文件数: {len(txt_files)}, 成功: {success_count}")
        
        # 创建最终清单
        self.create_final_summary(success_count)
    
    def create_final_summary(self, file_count):
        """创建最终清单"""
        try:
            summary_content = "候鸟浏览器API文档 - 最终版本\n"
            summary_content += "=" * 50 + "\n\n"
            summary_content += f"总文件数: {file_count} 个\n\n"
            
            summary_content += "文档分类:\n"
            summary_content += "1. 环境管理 (16个子功能) - 核心API功能\n"
            summary_content += "2. 分组管理 (4个子功能)\n"
            summary_content += "3. 插件管理 (4个子功能)\n"
            summary_content += "4. 脚本管理 (5个子功能)\n"
            summary_content += "5. 环境开启关闭 (3个子功能)\n"
            summary_content += "6. HTTP模式说明 (3个子功能)\n"
            summary_content += "7. 使用须知 (3个子功能)\n"
            summary_content += "8. 常见问题 (3个子功能)\n"
            summary_content += "9. POSTMAN调试 (2个子功能)\n"
            summary_content += "10. JSON数据处理 (2个子功能)\n"
            summary_content += "11. 其他基础功能 (4个)\n\n"
            
            summary_content += "特点:\n"
            summary_content += "- 已去除重复和导航内容\n"
            summary_content += "- 每个文件对应一个具体的API功能\n"
            summary_content += "- 文件名格式: API_功能模块_序号_具体功能名\n"
            summary_content += "- 内容已清理，适合直接使用\n\n"
            
            summary_content += "使用说明:\n"
            summary_content += "这些文档可以直接用于:\n"
            summary_content += "- RAGFlow知识库构建\n"
            summary_content += "- API开发参考\n"
            summary_content += "- 技术文档整理\n"
            summary_content += "- 自动化脚本开发\n"
            
            # 保存清单文件
            summary_path = Path(self.output_dir) / "README.txt"
            with open(summary_path, 'w', encoding='utf-8') as f:
                f.write(summary_content)
            
            logger.info("已创建最终说明文档")
            
        except Exception as e:
            logger.error(f"创建最终清单失败: {str(e)}")

def main():
    """主函数"""
    print("TXT转DOCX最终转换器启动...")
    print("="*50)
    
    converter = TxtToDocxFinalConverter()
    converter.convert_all_files()
    
    print("="*50)
    print("转换完成!")
    print("- 输入目录: api_docs_clean")
    print("- 输出目录: api_docs_final_docx")
    print("- 说明文档: api_docs_final_docx/README.txt")
    print("\n注意: 由于没有python-docx库，文件保存为TXT格式")
    print("如需DOCX格式，请安装python-docx库后重新运行")

if __name__ == "__main__":
    main()
