<?xml version="1.0" encoding="UTF-8"?>
<Window size="460,280" trans="true"  caption="0,0,0,100" roundcorner="2,2,2,2" showshadow="true" shadowsize="3" shadowposition="0,0" shadowcolor="#ff6272A4" fademode="true">
  <Include source="default.xml" />
    <Default name="Button" />
  <VerticalLayout width="460" bkcolor="#FF282A36">
    <HorizontalLayout height="30">
   	  <Button bkimage="icon21px.png" padding="6,6,0,0" width="27" height="21"/>
      <Label name="titlemsg" padding="6,4,0,0"  autocalcwidth="true" maxwidth="160" texttooltip="true" endellipsis="true"  height="28" text=""  font="8" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD"/>
      <Label name="msg3" text="" autocalcwidth="true" maxwidth="260" texttooltip="true" endellipsis="true" padding="6,4,0,0" height="28" textcolor="#FFFF5555" font="8"/>
      <Control />
      <Button name="closebtn" width="28" height="26"  tooltip="" normalimage="closebtn.png" hotimage="closebtn_hover.png" pushedimage="closebtnpush.png" />
    </HorizontalLayout>
		<VerticalLayout >

    <HorizontalLayout height="20">
			</HorizontalLayout>

			<HorizontalLayout height="30" inset="80,10,0,0">
				<Label name="msg1" text="" width="126" align="right" valign="center" font="8"/>
        <Control width="10"/>
        <Label name="expireviptype" text="" align="left" valign="center" font="8"/>
			</HorizontalLayout>


<HorizontalLayout height="10">
			</HorizontalLayout>


      <HorizontalLayout height="30" inset="80,10,0,0">
        <Label name="msg4" text="" width="126" align="right" texttooltip="true" endellipsis="true" font="8"/>
        <Control width="10"/>
        <Label name="expirevipdate" text="" textcolor="#ffff0000" align="left" valign="center" font="8"/>
      </HorizontalLayout>

 <HorizontalLayout height="20">
			</HorizontalLayout>

      <HorizontalLayout height="30" width="450">
         <Control />
          <Button name="neworder" padding="0,1,0,0" text="" textcolor="#ffff9600" hottextcolor="#FF0067ee" autocalcwidth="true" maxwidth="160" texttooltip="true" endellipsis="true" font="8"/>
          <Control width="10"/>
          <Label name="msg7" text="" autocalcwidth="true" maxwidth="206" texttooltip="true" endellipsis="true" font="8"/>
         <Control />
      </HorizontalLayout>


 <HorizontalLayout height="20" inset="60,10,0,0">
			</HorizontalLayout>

	       <HorizontalLayout childpadding="2" inset="0,20,0,0">
           <Control />
            <VerticalLayout width="170">
				      <Button name="neworderbtn" textcolor="#ffff9600" hottextcolor="#FF0067ee" bkimage="bk_quit_normal.png" hotimage="bk_quit_hot.png" pushedimage="bk_quit_hot.png" padding="0,4,0,0" textpadding="5,0,5,0" texttooltip="true" endellipsis="true" font="8" width="160" height="26" text=""/>
				    </VerticalLayout>

				    <VerticalLayout width="10"></VerticalLayout>

				    <VerticalLayout width="170">
               <Button name="itembtn" bkimage="bk_quit_normal.png" hotimage="bk_quit_hot.png" pushedimage="bk_quit_hot.png" padding="0,4,0,0" textpadding="10,0,10,0" texttooltip="true" endellipsis="true" font="8" width="160" height="26" text=""/>
            </VerticalLayout>
           <Control />
			   </HorizontalLayout>
		</VerticalLayout>
  </VerticalLayout>
</Window>