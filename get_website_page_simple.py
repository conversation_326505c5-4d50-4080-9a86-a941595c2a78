#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
候鸟浏览器API文档抓取器 (简化版)
抓取 https://www.mbbrowser.com/api/ 下所有页面并保存为txt文件
"""

import requests
import re
import time
import os
from urllib.parse import urljoin
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('get_website_page_simple.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SimpleAPIDocumentScraper:
    def __init__(self, base_url='https://www.mbbrowser.com/api/', output_dir='api_docs_txt'):
        self.base_url = base_url
        self.output_dir = output_dir
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        
        # 创建输出目录
        Path(self.output_dir).mkdir(exist_ok=True)
        
        # 定义要抓取的页面列表（基于分析的左侧栏目）
        self.api_pages = [
            # API使用须知
            {'url': '/api/', 'title': '简介'},
            {'url': '/api/help', 'title': '使用须知'},
            {'url': '/api/http', 'title': 'HTTP模式说明'},
            {'url': '/api/question', 'title': '常见问题'},
            
            # API接口文档
            {'url': '/api/login', 'title': '帐号登录'},
            {'url': '/api/members', 'title': '获取成员列表'},
            {'url': '/api/browser', 'title': '环境开启关闭'},
            {'url': '/api/session', 'title': '环境管理'},
            {'url': '/api/group', 'title': '分组管理'},
            {'url': '/api/script', 'title': '脚本管理'},
            {'url': '/api/plugin', 'title': '插件管理'},
            {'url': '/api/appendix', 'title': '附录'},
            {'url': '/api/code', 'title': '错误码对照表'},
            
            # POSTMAN工具
            {'url': '/api/postman', 'title': 'POSTMAN下载及安装'},
            {'url': '/api/postman-debug', 'title': 'POSTMAN调试候鸟API接口'},
            {'url': '/api/postman-example', 'title': '调试接口JSON数据官方更新下载'},
            
            # 其他
            {'url': '/api/example', 'title': '多种语言脚本示例'},
            {'url': '/api/json', 'title': 'JSON在线格式化工具'}
        ]
    
    def sanitize_filename(self, filename):
        """清理文件名，移除不合法字符"""
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        filename = filename.replace('/', '_').replace('\\', '_')
        if len(filename) > 200:
            filename = filename[:200]
        return filename.strip()
    
    def fetch_page_content(self, url):
        """获取页面内容"""
        try:
            full_url = urljoin(self.base_url, url)
            logger.info(f"正在获取页面: {full_url}")
            
            response = self.session.get(full_url, timeout=30)
            response.raise_for_status()
            response.encoding = 'utf-8'
            
            return response.text
            
        except Exception as e:
            logger.error(f"获取页面失败: {url}, 错误: {str(e)}")
            return None
    
    def simple_html_to_text(self, html_content):
        """简单的HTML转文本处理"""
        try:
            # 移除script和style标签
            html_content = re.sub(r'<script[^>]*>.*?</script>', '', html_content, flags=re.DOTALL | re.IGNORECASE)
            html_content = re.sub(r'<style[^>]*>.*?</style>', '', html_content, flags=re.DOTALL | re.IGNORECASE)
            
            # 移除HTML注释
            html_content = re.sub(r'<!--.*?-->', '', html_content, flags=re.DOTALL)
            
            # 处理常见的HTML标签
            # 标题标签
            html_content = re.sub(r'<h[1-6][^>]*>(.*?)</h[1-6]>', r'\n\1\n', html_content, flags=re.DOTALL | re.IGNORECASE)
            
            # 段落标签
            html_content = re.sub(r'<p[^>]*>(.*?)</p>', r'\1\n', html_content, flags=re.DOTALL | re.IGNORECASE)
            
            # 换行标签
            html_content = re.sub(r'<br[^>]*/?>', '\n', html_content, flags=re.IGNORECASE)
            
            # 列表项
            html_content = re.sub(r'<li[^>]*>(.*?)</li>', r'• \1\n', html_content, flags=re.DOTALL | re.IGNORECASE)
            
            # 代码块
            html_content = re.sub(r'<pre[^>]*>(.*?)</pre>', r'\n```\n\1\n```\n', html_content, flags=re.DOTALL | re.IGNORECASE)
            html_content = re.sub(r'<code[^>]*>(.*?)</code>', r'`\1`', html_content, flags=re.DOTALL | re.IGNORECASE)
            
            # 移除所有剩余的HTML标签
            html_content = re.sub(r'<[^>]+>', '', html_content)
            
            # 解码HTML实体
            html_content = html_content.replace('&nbsp;', ' ')
            html_content = html_content.replace('&lt;', '<')
            html_content = html_content.replace('&gt;', '>')
            html_content = html_content.replace('&amp;', '&')
            html_content = html_content.replace('&quot;', '"')
            html_content = html_content.replace('&#39;', "'")
            
            # 清理多余的空白
            lines = html_content.split('\n')
            cleaned_lines = []
            for line in lines:
                line = line.strip()
                if line:
                    cleaned_lines.append(line)
            
            # 合并连续的空行
            result = []
            prev_empty = False
            for line in cleaned_lines:
                if not line:
                    if not prev_empty:
                        result.append('')
                    prev_empty = True
                else:
                    result.append(line)
                    prev_empty = False
            
            return '\n'.join(result)
            
        except Exception as e:
            logger.error(f"HTML转文本失败: {str(e)}")
            return "内容解析失败"
    
    def save_page_as_txt(self, page_info):
        """保存单个页面为txt文件"""
        try:
            url = page_info['url']
            title = page_info['title']
            
            # 获取页面内容
            html_content = self.fetch_page_content(url)
            if not html_content:
                return False
            
            # 转换为文本
            text_content = self.simple_html_to_text(html_content)
            
            # 创建文件内容
            file_content = f"API文档: {title}\n"
            file_content += f"URL: {urljoin(self.base_url, url)}\n"
            file_content += f"抓取时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n"
            file_content += "=" * 60 + "\n\n"
            file_content += text_content
            
            # 保存文件
            filename = f"API_{self.sanitize_filename(title)}.txt"
            filepath = os.path.join(self.output_dir, filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(file_content)
            
            logger.info(f"已保存文档: {filename}")
            return True
            
        except Exception as e:
            logger.error(f"保存页面失败: {page_info}, 错误: {str(e)}")
            return False
    
    def scrape_all_pages(self):
        """抓取所有页面"""
        logger.info("开始抓取API文档页面...")
        
        success_count = 0
        total_count = len(self.api_pages)
        
        for i, page_info in enumerate(self.api_pages, 1):
            logger.info(f"正在处理第 {i}/{total_count} 个页面: {page_info['title']}")
            
            if self.save_page_as_txt(page_info):
                success_count += 1
            
            # 避免请求过于频繁
            time.sleep(2)
        
        logger.info(f"抓取完成! 总页面数: {total_count}, 成功: {success_count}, 失败: {total_count - success_count}")

def main():
    """主函数"""
    print("候鸟浏览器API文档抓取器 (简化版) 启动...")
    print("目标网站: https://www.mbbrowser.com/api/")
    print("输出格式: TXT文件")
    print("="*60)
    
    scraper = SimpleAPIDocumentScraper()
    scraper.scrape_all_pages()
    
    print("="*60)
    print("抓取完成!")
    print("- 文档文件: api_docs_txt目录")
    print("- 日志文件: get_website_page_simple.log")

if __name__ == "__main__":
    main()
