<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>/api/postman-example</title>
  <style>
/* 基础重置与排版 */
body {
  margin: 0;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.7;
  color: #333;
  background-color: #fff;
  max-width: 960px;
  margin: 0 auto;
  box-sizing: border-box;
}

/* 段落 */
p {
  margin: 1em 0;
}

/* 标题 */
h1, h2, h3, h4, h5, h6 {
  margin: 1.5em 0 0.8em;
  color: #2c3e50;
  font-weight: 600;
  line-height: 1.3;
}

h1 { font-size: 2em; border-bottom: 1px solid #eee; padding-bottom: 0.5em; }
h2 { font-size: 1.6em; }
h3 { font-size: 1.4em; }
h4 { font-size: 1.2em; }

/* 列表 */
ul, ol {
  margin: 1em 0;
  padding-left: 2em;
}

li {
  margin: 0.4em 0;
}

/* 引用块 */
blockquote {
  margin: 1.5em 0;
  padding: 0.8em 1.5em;
  background-color: #f9f9f9;
  border-left: 4px solid #ddd;
  color: #666;
  font-style: italic;
  border-radius: 0 4px 4px 0;
}

/* 代码行内 */
code {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  background-color: #f3f4f6;
  color: #e9602d;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-size: 0.95em;
  white-space: nowrap;
}

/* 代码块 */
pre {
  margin: 1.5em 0;
  padding: 1.2em;
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  overflow-x: auto;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
}

pre code {
  background: none;
  color: inherit;
  padding: 0;
  font-size: inherit;
  white-space: pre;
  display: block;
}

/* 表格 */
table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5em 0;
  font-size: 14px;
  overflow: hidden;
  box-shadow: 0 0 0 1px #e0e0e0;
  border-radius: 6px;
}

th, td {
  padding: 10px 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

th {
  background-color: #f8f9fa;
  color: #2c3e50;
  font-weight: 600;
  white-space: nowrap;
}

tr:nth-child(even) {
  background-color: #f9f9fb;
}

tr:hover {
  background-color: #f0f5ff;
}

/* 链接 */
a {
  color: #1a73e8;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

/* 分隔线 */
hr {
  border: 0;
  height: 1px;
  background: #ddd;
  margin: 2em 0;
}

/* 图片 */
img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 1em 0;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
  </style>
</head>
<body>
  <h2>调试接口JSON数据官方更新、下载</h2> <ul><li><p>候鸟官方提供POSTMAN所有最新调试接口JOSN数据，点击下载<a class="ant-btn ant-btn-primary">最新调试接口JSON数据</a></p></li></ul> <div class="ant-divider ant-divider-horizontal"></div> <h3>1、POSTMAN如何导入JSON数据</h3> <ul><li><p>下载<a class="ant-btn ant-btn-primary">最新调试接口JSON数据</a>，然后打开POSTMAN</p></li> <li><p>点击POSTMAN左侧列表的Import按钮</p></li></ul> <p><img></p> <p><img></p> <p><img></p> <ul><li><p><strong>导入成功即可看到左侧列表出现所有调试接口</strong></p></li></ul> <p><img></p> <ul><li><p><strong>点击选中需要调试的接口，右侧就会出现请求接口和请求参数案例，自行将参数进行更改即可开始进行调试</strong></p></li></ul> <p><img></p> <h3>2、POSTMAN如何导出JSON数据</h3> <ul><li><p><strong>鼠标移动到左侧列表中对应的库标题处，右键或点击右边显示的三个点图标，选择弹出菜单中的Export</strong></p></li></ul> <p><img></p> <ul><li><p><strong>弹出窗口中的导出版本选择默认即可，点击按钮然后选择导出目录即可完成导出</strong></p></li></ul> <p><img></p>
</body>
</html>