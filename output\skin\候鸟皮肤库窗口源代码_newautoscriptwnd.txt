﻿<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Window size="680,380" caption="0,0,0,50" roundcorner="5,5,5,5" showshadow="true" shadowsize="3" shadowposition="0,0" shadowcolor="#ff6272A4" fademode="true">
	<Include source="Default.xml" />

  <VerticalLayout width="953" height="590" bkcolor="#FF282A36">

    <HorizontalLayout height="37">

    	 <Button name="login" bkimage="icon.png" padding="8,6,0,0" width="28" height="28"/>
      <Label name="title" padding="6,4,0,0" text="新建自动化脚本" width="480" textcolor="#FF616161" hottextcolor="#ff000000" align="left" font="8"></Label>

      <Control />
      <Button name="minbtn" width="28" height="26" tooltip="最小化" normalimage="minbtn.png" hotimage="minbtn_hover.png" pushedimage="minbtnpush.png" />
      <Button name="closebtn" width="28" height="26"  tooltip="关闭" normalimage="closebtn.png" hotimage="closebtn_hover.png" pushedimage="closebtnpush.png" />

    </HorizontalLayout>

  <HorizontalLayout name="bkground" visible="true">

		<VerticalLayout width="953" height="603">
			<HorizontalLayout height="26">
			</HorizontalLayout>

      <HorizontalLayout height="36" >
        <VerticalLayout width="84">
          <Label name="ltype" visible="true" width="80" textpadding="22,2,0,0" text="脚本类型"  texttooltip="true" endellipsis="true" font="8" height="32" textcolor="#FF333333"/>
        </VerticalLayout>
        <VerticalLayout width="220">
          <Combo name="combotype" visible="true" bordersize="0" padding="16,0,0,0" width="200" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" bkcolor="#ffdce1e7"
          normalimage="file='Profile\Proxy_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\proxy_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\proxy_Combox_Click.png' corner='5,5,25,10'"
          combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
          itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0" textpadding="0,0,20,0" endellipsis="true">
          </Combo>
        </VerticalLayout>

        <Control width="44"/>

        <VerticalLayout width="84">
          <Label name="llanguage" width="80" textpadding="22,2,0,0" text="脚本语言"  texttooltip="true" endellipsis="true" font="8" height="32"  textcolor="#FF333333"/>
        </VerticalLayout>
        <VerticalLayout width="220">
          <Combo name="comboclass" bordersize="0" padding="16,0,0,0" width="200" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="8,2,0,0" bkcolor="#ffdce1e7"
          normalimage="file='Profile\Proxy_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\proxy_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\proxy_Combox_Click.png' corner='5,5,25,10'"
          combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
          itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0" textpadding="0,0,20,0" endellipsis="true">
            <!--<ListLabelElement height="36" pos="66,0,0,0" textpadding="10,0,0,0" text="点击并选择模板..." font="0" selected="true">
              <Label name="textLab" pos="66,0,0,0" textpadding="10,0,0,0" text="点击并选择模板..."  height="36" width="260" textcolor="#FF000000"/>
            </ListLabelElement>-->
          </Combo>
        </VerticalLayout>

      </HorizontalLayout>

      <HorizontalLayout height="12">
      </HorizontalLayout>

			 <HorizontalLayout height="48" >
         <VerticalLayout width="90">
           <Control />
            <Label name="lname" textpadding="22,3,0,0" text="脚本名称："  texttooltip="true" endellipsis="true" font="8" height="36" textcolor="#FF333333"/>
           <Control />
         </VerticalLayout>
         <VerticalLayout width="560" >
				         <RichEdit name="name" padding="10,10,0,10" height="32" tipvaluecolor="#FF333333" borderround="7,7" bkcolor="#ffdce1e7" font="0" textpadding="16,8,20,0" maxchar="300" tipvalue="请输入脚本名称" multiline="false" textcolor="#ff333333" rich="false" transparent="false">
				      </RichEdit>
				 </VerticalLayout>
			</HorizontalLayout>

      <HorizontalLayout  height="26" >
        <VerticalLayout width="300">
          <Control />
          <Label name="ldesc" textpadding="22,6,0,0" text="脚本描述："  width="300" font="5" height="30" textcolor="#FF333333"/>
          <Control />
        </VerticalLayout>
      </HorizontalLayout>

      <HorizontalLayout width="650" height="120">
        <VerticalLayout >
          <RichEdit name="desc" padding="20,6,0,10" tipvaluecolor="#FF333333" borderround="7,7" bkcolor="#ffdce1e7" font="0" textpadding="8,8,6,0" maxchar="300" tipvalue="请输入脚本描述" multiline="true" textcolor="#ff333333" rich="false" transparent="false">
          </RichEdit>
        </VerticalLayout>
      </HorizontalLayout>

      <HorizontalLayout width="650" height="22">
        <Label name="lmsg" padding="21,3,0,0" visible="false" texttooltip="true" endellipsis="true" text="自动化脚本已存在此实例名,请更换当前脚本名称." textcolor="#FFFF0000"></Label>
      </HorizontalLayout>

    </VerticalLayout>

	</HorizontalLayout>
	   <HorizontalLayout name="ProgressBarArea" visible="true" height="2">
        <Progress name="ProgressBar" height="2" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" forecolor="#FF3488ec" min="0" max="100" value="0" hor="true" />
    </HorizontalLayout>
    <HorizontalLayout height="52" bkcolor="#ffe9e9e9">
      <Label name="statusarea1" text="JAVA脚本语言支持包正在安装中...请稍候..." width="316" padding="10,0,0,0" textcolor="#FF17c717" hottextcolor="#ff000000" align="left" font="8"></Label>
      <Control />

      <VerticalLayout width="140">
        <Control />
        <Button text="应用修改" name="newedit" visible="false" textpadding="5,0,5,0" texttooltip="true" endellipsis="true" padding="0,6,0,0" width="120" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	bkimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
        <Button text="新建自动化脚本" name="new" tooltip="新建自动化脚本" padding="0,6,0,0"  texttooltip="true" endellipsis="true" textpadding="26,0,10,0" width="138" height="30" textcolor="#FF4f4f4f" disabledtextcolor="#FFB3B3B3" align="center" font="14"	bkimage="file=&apos;cbg_btn.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;cbg_btn_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;cbg_btn_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
        <Control />
      </VerticalLayout>

      <Control width="10"/>
      <VerticalLayout width="140" name="editarea">
        <Control />
        <Button text="编辑源代码" name="edit"   padding="0,6,0,0" width="120" height="30" texttooltip="true" endellipsis="true" textpadding="5,0,5,0" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	bkimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
        <Control />
      </VerticalLayout>

    </HorizontalLayout>
  </VerticalLayout>
</Window>
