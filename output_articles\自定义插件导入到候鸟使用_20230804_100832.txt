标题: 自定义插件导入到候鸟使用
英文标题: Custom Plugin Import To Mbbrowser
ID: 117
分类ID: 7
添加时间: 1691114912
更新时间: 1697440219
访问次数: 0
SEO标题: 自定义插件导入到候鸟使用
SEO关键词: 自定义插件导入到候鸟使用
SEO描述: 自定义插件导入到候鸟使用

================================================== 内容 ==================================================
### 视频教程

|  导入自定义插件 |  通过API安装自定义插件到指定环境 |
| ------------ | ------------ |
| <video controls="" preload style="width:100%"><source src="https://help.mbbrowser.com/video/custom_plugin.mp4" type="video/mp4"></video> |  <video controls="" preload style="width:100%"><source src="https://help.mbbrowser.com/video/custom_plugin2.mp4" type="video/mp4"></video> |


#### 1、在候鸟主面板点击设置 -> 浏览器插件管理器 进入插件管理界面。

<p><img src="6d7b2882624511f09a0d0242ac130006/images/image_a025b9076875.png" width="360" /></p>

#### 2、在确定我的自定义插件列表中没有要导入的插件后，请点击 导入自定义插件 按钮。

![](6d7b2882624511f09a0d0242ac130006/images/image_15d179582dc5.png)

#### 3、此时可以看到弹出了窗口：导入自定义插件。 

![](6d7b2882624511f09a0d0242ac130006/images/image_2d8b0282cb58.png)

此窗口表示候鸟已经开始为接收您给定的自定义插件做好了程序上的准备。在操作这个过程中，并不会影响您已经运行的业务环境，也就是说，您可以在已经运行的多个业务环境的同时，同时进行这一块的操作，不会出现冲突。

当看到这个窗口后，您可以开始操作您的CRX插件包文件，请将CRX插件包解压缩为目录格式。

举例说明：
例如 MyProject.crx 文件，您将此文件重命名为MyPorject.rar或MyProject.zip，然后用winrar或winzip进行解压缩。解压缩完成后，您会看到一个Myproject文件夹。此时说明您已经做好了导入到候鸟的准备。


#### 4、在此窗口上点击按钮：选择自定义插件并测试运行

![](6d7b2882624511f09a0d0242ac130006/images/image_f868ecdf04d4.png)

此时您可以看到弹出一个提示窗口，此窗口告诉您，请确认您已经将Crx插件包文件解压缩为文件夹。同时告诉您，下一步将打开候鸟浏览器窗口，供您将插件文件夹导入到浏览器中进行验证，验证过程需要8秒时间，在这8秒过程中，候鸟浏览器会将插件的ID，名称，版本号获取到并进行验证，最后保存在您的硬盘上，同时按GOOGLE规范存储在GOOGLE要求的指定位置。整个过程需要8秒的时长，在此期间您可以在浏览器中使用插件，也可以什么都不做，8秒后关闭浏览器即可。

#### 5、在您了解整个过程后，请点击：我知道了，请立即开启候鸟浏览器

此时您可以看到候鸟弹出了一个浏览器内核窗口供您添加入您的插件，此时您点击：

![](6d7b2882624511f09a0d0242ac130006/images/image_35ea7623e78f.png)

#### 6、然后选择您刚刚解压缩的插件文件夹即可。如下图例：

![](6d7b2882624511f09a0d0242ac130006/images/image_338167fda91f.png)

添加完成后，您会看到您的自定义插件已成功加入到浏览器中，这种方式不需要您的插件是否通过GOOGLE扩展商店审核，也不需要您提交任何审核，保证是您自用的插件就行。

#### 7、在等待或使用刚刚加入的插件满8秒后，可以关闭此浏览器窗口。

![](6d7b2882624511f09a0d0242ac130006/images/image_c9222b11e4a5.png)

然后您会看到您的自定义插件已经被候鸟检测并验证完成，成功获取到插件ID、插件名称、插件版本号。当您看到这些信息都是有值的时侯，表示候鸟检测并验证成功完成。

此时您为了方便管理和记忆，也可以填入插件描述（会显示自我的自定义插件列表栏的插件描述中），也可以不填写。

#### 8、然后点击 导入到我的自定义插件库

![](6d7b2882624511f09a0d0242ac130006/images/image_ec13f8b5779a.png)

此时表示您的自定义插件已经成功导入到您的候鸟私人插件库中，为保护您的个人隐私与业务运营安全，所有此类插件只存在于您的本地电脑中，不存在任何其它地方，也不会同步到候鸟官方服务器。因此在您以后异地电脑里使用时，如果自定义插件不存在，仍需要进行一次上述导入操作。

#### 9、此时，您现在可以按照候鸟的标准通用操作，以A,B,C,D的顺序，通过勾选（打勾），将任意自定义插件一键批量安装（指派）到您需要的多个环境中，避免了逐个人工安装插件的繁琐。

![](6d7b2882624511f09a0d0242ac130006/images/image_d25443c7986b.png)

================================================== 英文内容 ==================================================
### Video Tutorial

|  Import a custom plug-in |  Install custom plug-in to specified environment through API |
| ------------ | ------------ |
| <video controls="" preload style="width:100%"><source src="https://help.mbbrowser.com/video/custom_plugin.mp4" type="video/mp4"></video> |  <video controls="" preload style="width:100%"><source src="https://help.mbbrowser.com/video/custom_plugin2.mp4" type="video/mp4"></video> |


#### 1. On the main panel of Mbbrowser, click Settings -> Browser Plugin Manager to enter the plugin management interface.

<p><img src="6d7b2882624511f09a0d0242ac130006/images/image_87d47ef15175.png" width="360" /></p>

#### 2. After making sure that there is no plug-in to import in my custom plug-in list, please click the Import Custom Plug-in button.

![](6d7b2882624511f09a0d0242ac130006/images/image_2497623a4181.png)

#### 3. You can see the pop-up window: Import custom plug-in.

![](6d7b2882624511f09a0d0242ac130006/images/image_2e0630f82359.png)

This window indicates that Mbbrowser has begun to prepare itself programmatically to receive the custom plug-in you have given. The operation process does not affect the business environment you have already run, that is, you can run multiple business environments at the same time, at the same time, there will be no conflict.

When you see this window, you can start working with your CRX plug-in package file, unzip the CRX plug-in package into a directory format.

For example:
MyProject.crx file, you rename this file to MyPorject.rar or MyProject.zip, and then unzip it with winrar or winzip. When the unzipping is complete, you will see a Myproject folder. At this point, you are ready to import to migratory birds.


#### 4. Then click the button on this window: Select the Custom Plug-in and Install

![](6d7b2882624511f09a0d0242ac130006/images/image_fe2eeaf7631a.png)

You should see a pop-up window that tells you to make sure you have extracted the Crx plug-in package file into a folder. At the same time, it tells you that the next step will open the Mbbrowser window for you to import the plug-in folder into the browser for verification. The verification process takes 8 seconds. During the 8-second process, Mbbrowser will obtain and verify the ID, name and version number of the plug-in, and finally save it on your hard disk. At the same time, it is stored in the specified location required by GOOGLE according to GOOGLE specifications. The whole process takes 8 seconds, during which time you can use the plug-in in the browser or do nothing, and close the browser after 8 seconds.

#### 5. After you understand the whole process, please click: I see, Please open Migratory Bird browser immediately

At this point you can see that Mbbrowser pops up a browser kernel window for you to add your plug-in, at this time you click:

![](6d7b2882624511f09a0d0242ac130006/images/image_35ea7623e78f.png)

#### 6. Then select the plug-in folder that you just unzipped. The following illustration:

![](6d7b2882624511f09a0d0242ac130006/images/image_338167fda91f.png)

After the addition is complete, you will see that your custom plug-in has been successfully added to the browser, this way does not require your plug-in to be approved by the GOOGLE Extension Store, and does not require you to submit any review, so that it is your own plug-in.

#### 7. You can close this browser window after waiting or using the plugin you just added for 8 seconds.

![](6d7b2882624511f09a0d0242ac130006/images/image_839a040f5084.png)

Then you will see that your custom plug-in has been detected and verified by Mbbrowser, successfully obtaining the plug-in ID, plug-in name, plug-in version number. When you see that all of these messages are valuable, the migratory bird detection and verification has been successfully completed.

In order to facilitate management and memory, you can also fill in the plug-in description (the plug-in description will be displayed in the custom plug-in list bar), or you can not fill in.

#### 8. Then click Import to my custom plugin library

![](6d7b2882624511f09a0d0242ac130006/images/image_89e6600534b4.png)

This means that your custom plug-in has been successfully imported into your private plug-in library, in order to protect your personal privacy and business security, all such plug-ins only exist in your local computer, there is no other place, and will not be synchronized to the official server of Mbbrowser. Therefore, when you use it in a remote computer in the future, if the custom plug-in does not exist, you still need to perform the above import operation.

#### 9. At this time, you can now follow the standard general operation of mbbrowser, in the order of A,B,C,D, by checking (ticking), any custom plug-in one-key batch installation (assignment) to the multiple environments you need, avoiding the tedious manual installation of plug-ins one by one.

![](6d7b2882624511f09a0d0242ac130006/images/image_6d972bc4bf4a.png)