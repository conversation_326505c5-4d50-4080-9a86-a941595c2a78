A、用户在自动化脚本管理器里，勾取选择A区一项或多项脚本，则导出按钮亮起，用户点击导出按钮，弹出上述脚本导出对话框。

A、用户在自动化脚本管理器里，勾取选择A区一项或多项脚本，则导出按钮亮起，用户点击导出按钮，弹出上述脚本导出对话框。

B、待导出脚本包列表框里，以脚本名称（脚本名称超长的按16个字节最大限长，超长的加…）列出所有脚本项。

C、存储目录默认存储到document/houniao/autoscripts 目录下。

D、脚本明文导出默认勾选，默认导出解码之后的脚本内容。

E、点击导出脚本到本地按钮，导出成功在左下角显示：

Xxxxxxxxxxxxxxxxxxxxxx 导出成功。（蓝色字体）

（2）脚本导入

流程说明：

A、用户点击按钮，选择我的本地脚本包文件，在本地硬盘里，找到自已一项或多项脚本文件，则确定后，将脚本的名称置入对话框。（注意一下，是脚本的名称，不是文件名）

B、待导出脚本包列表框里，以脚本名称（脚本名称超长的按16个字节最大限长，超长的加…）列出所有脚本项。

C、导入的脚本放到脚本管理器窗口的A区列表中。

脚本导入要支持加密过的脚本导入和明文脚本导入。

E、导入成功在左下角显示：

Xxxxxxxxxxxxxxxxxxxxxx 成功导入到候鸟客户端。（蓝色字体）

第十三章 候鸟服务器端(单服务器与多服务器)域名与端口

第十三章 候鸟服务器端(单服务器与多服务器)域名与端口

宗旨：为保证中后期服务端具备可扩展，可实时扩展多服务器、支持负载均衡，对服务器与客户端的数据通讯进行初步规范化梳理与约定。

此章节在根据当前和后期实际工作进程中，逐步规范并完善。

服务器端：

官网域名:  80、443

客服平台： 80、443

控制台：  80、443

数据交互服务：

心跳数据 ws://admin.mbbrowser.com:3234

（后期需考虑将域名改换为 ws://heartbeat.mbbrowser.com:3234）

2、  8888 以及3000-4000端口段 （服务器暂约定端口）

客户端：

Dump/log  数据发送   (tcp: 8771 - 8780)

update    升级流程  80、443

基础数据同步：

当前： 80、443、8762

（后期需考虑将域名更换为  并只保留8762 - 8770 端口）

第十四章 候鸟计费、业务平台、数据存储等24小时不间断服务器防灾机制架设

第十四章 候鸟计费、业务平台、数据存储等24小时不间断服务器防灾机制架设

前述：鉴于大额付款会员用户在团队协作版推出后，将会带来一定幅度用户数增涨。当前单服务器架构将会带来数据安全与在线服务隐患。一旦服务器无法访问或无法正常响应超过6小时，所有用户的当前业务开展将快速转变成灾难性事件。

考虑到各种可能：竞争对手恶意攻击，服务器外部网络环境意外中断，运营商中继路由等各种意外事件，导致本应正常提供的服务被迫中断。

为保证平台与服务的高可靠性，仅依赖阿里云的快照则远远不够。

基于当前团队协作体系趋于完善与稳定，将在近期考虑增加一台阿里云/华为云服务器作为实时数据冗余服务器。在杭州阿里云主服务器突发事件出现后，可实时继续提供高可靠性稳定服务。

架设说明：

采用CENTOS系统自带 Rsync与 Lsyncd 协议进行数据实时同步。

主机	                        操作系统	           IP地址

主(杭州阿里云)              Lsyncd Server Centos	    内网IP/公网IP

备(异地阿里云或华为云)      Rsync Server	Centos	    内网IP/公网IP

注：如使用阿里云作为备服务器，则IP地址使用阿里云内网IP。如使用华为云，则IP地址使用公网IP。

主要的工作有：

Lsyncd Server与Rsync Server 的搭建。

1、客户端备用域名接口机制完善。

2、full数据同步体系测试与修正。

3、业务数据同步体系测试与修正。

4、网络无响应或数据异常时的服务器实时迁移手册。

5、完善双向实时同步服务。

第十五章 候鸟计费业务，会员套餐到期自动限制逻辑与界面流程说明

第十五章 候鸟计费业务，会员套餐到期自动限制逻辑与界面流程说明

约定：

候鸟客户端：

无论用户套餐是否到期，都允许用户成功登录并查看自已的item。

套餐到期仅限制用户无法打开浏览器即可。

客户端不通过判定用户本地机器时间作为套餐是否到期依据。客户端仅通过服务器返回的参数值来判定是否接近到期或已到期。

候鸟服务器端控制台：

1、   控制台在套餐到期后，分享入口，删除入口，发送到客户端入口在用户点击后均提示用户续费后才可使用。

===<< 登录接口约定扩展==============================================

登录接口扩展：

候鸟服务器端：通过历史已约定的登录请求接口返回（如图）

原约定：

is_browser: 1 （is_browser 0为禁止使用浏览器）

增加约定：

在vip_info子节点中 增加参数：

is_expire: 0 （       0，表示在套餐有效期内，

为套餐接近到期，

为套餐已到期

）

约束说明：

当is_browser=0时， is_expire可以为0，1，2。即根据服务器端后台管理，允许管理员控制，在用户套餐有效期内，可禁止用户使用浏览器内核。也可以在用户套餐失效期内，管理员控制禁止用户使用浏览器内核。

重点：

当is_expire为 2时：

is_browser 一定为 0

session_num 一定为0

son_num 一定为0

=== 登录接口约定扩展 >>=======================================

===<< 心跳接口约定扩展==============================================

说明：当用户通过休眠唤醒，虚拟机中运行场景中使用客户端，则通过心跳接口对客户端进行实时约束。此接口避免用户通过使用虚拟机等各种手段绕过服务器控制在到期后仍期望正常使用候鸟客户端及服务。

*  客户端全局套餐 即将到期/到期 接口

即将到期请求：connected|req_expire|1

到期请求：connected|req_expire|2

=== 心跳接口约定扩展 >>============================================


================================================== 表格内容 ==================================================

{ 
    " Session_ID":["373808cb37bd63f5f7d92415e736e85f","705cc4c139e69b729a2fd277f30e1863"], 
    "args": [ 
        "--disable-extensions", 
        "--blink-settings=imagesEnabled=false",
        "--interval-seconds=3" 
    ] 
}

{
    "message": "Success",
    "code": 0,
"data": {
  "listid": [
        {
        "Session_Name": “商用业务环境一”
        "Session_ID": "373808cb37bd63f5f7d92415e736e85f",
"Group_Name": “default”
“Actived_script_id”:” O73808cb37bd63f5f7d92415e736e999”,
“Actiived_script_name”:”这是一个脚本例子”,
“Actiived_script_encode”:”true”,
"Weblogin_Account_Count": "4",
        "Weblogin_Account_name":"<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>",
"Plugins_Count": "4",
        "Plugin_Id": "jjbnhpnlakcdgfnnldamfeinfmahhdlm,jjbnhpnlakcdgfnnldamfeinfmahhdlm,jjbnhpnlakcdgfnnldamfeinfmahhdlm, jjbnhpnlakcdgfnnldamfeinfmahhdlm",
        “template_id”:”123456”
        “template_name”:”抖音国际版”
        "browser_Path": "D:\\mbbrowser\\Chromium_x64\\chromium.exe",
        "browser_CDP_Port": 46973,
        "MBData_Path": "C:\\MBDATA\xxxxxxxxxx\xxxxxxxxxx\xxxxxxxxxxx”,
        "Public_ip": "************",
        "Internel_ip": "**************",
        "isDynamicIp": false,
        "StartPage": "about:blank",
        "proxyType": "socks5",
        "proxy_ip": "127.0.0.1",
        "proxy_port": 1080,
        "isHeadless": true,
        "webdriver":"C:\\Users\\<USER>\\houniao\\Driver\\100\\chromedriver.exe",        //根据当前打开环境的内核返回对应内核webdriver驱动路径
        "ChromeKey":"ws://localhost:9223/devtools/browser/b2f83369-541e-418b-92f9-0eaf4450f174"，
        "status": 0
},
{
        "Session_Name": “商用业务环境二”
        "Session_ID": "705cc4c139e69b729a2fd277f30e1863",
"Group_Name": “default”
“Actived_script_id”:” O73808cb37bd63f5f7d92415e736e999”,
“Actiived_script_name”:”这是一个脚本例子”,
“Actiived_script_encode”:”true”,
"Weblogin_Account_Count": "4",
        "Weblogin_Account_name":"<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>",
"Plugins_Count": "4",
        "Plugin_Id": "jjbnhpnlakcdgfnnldamfeinfmahhdlm,jjbnhpnlakcdgfnnldamfeinfmahhdlm,jjbnhpnlakcdgfnnldamfeinfmahhdlm, jjbnhpnlakcdgfnnldamfeinfmahhdlm",
        “template_id”:”123456”
        “template_name”:”抖音国际版”
        "browser_Path": "D:\\mbbrowser\\Chromium_x64\\chromium.exe",
        "browser_CDP_Port": 46973,
        "MBData_Path": "C:\\MBDATA\xxxxxxxxxx\xxxxxxxxxx\xxxxxxxxxxx”,
        "Public_ip": "************",
        "Internel_ip": "**************",
        "isDynamicIp": false,
        "StartPage": "about:blank",
        "proxyType": "socks5",
        "proxy_ip": "127.0.0.1",
        "proxy_port": 1080,
        "isHeadless": true,
        "webdriver":"C:\\Users\\<USER>\\houniao\\Driver\\100\\chromedriver.exe",        //根据当前打开环境的内核返回对应内核webdriver驱动路径
        "ChromeKey":"ws://localhost:9223/devtools/browser/a3f83369-541e-418b-92f9-0eaf4450f174"，
        "status": 0
}
],”total”:2
}