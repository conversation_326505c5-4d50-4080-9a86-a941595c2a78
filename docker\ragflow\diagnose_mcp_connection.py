#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RAGFlow MCP Server 连接诊断脚本
用于诊断从 Windows Wing 客户端到 CentOS RAGFlow MCP Server 的连接问题
"""

import json
import uuid
import requests
import time
from typing import Dict, Any, Optional

class RAGFlowMCPDiagnostic:
    """RAGFlow MCP Server 连接诊断工具"""
    
    def __init__(self, server_ip: str = "************"):
        self.server_ip = server_ip
        self.mcp_port = 9382
        self.ragflow_port = 9380
        
        # 从你的 curl 测试中看到的 API 密钥
        self.api_key = "ragflow-UzYTlhMjIwNjI0YzExZjA4NzUyMDI0Mm"
        
        self.base_url = f"http://{self.server_ip}:{self.mcp_port}"
        self.ragflow_url = f"http://{self.server_ip}:{self.ragflow_port}"
        
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}",
            "User-Agent": "Wing-MCP-Diagnostic/1.0"
        }
        
        print(f"🔍 RAGFlow MCP Server 连接诊断")
        print(f"   服务器IP: {self.server_ip}")
        print(f"   MCP端口: {self.mcp_port}")
        print(f"   RAGFlow端口: {self.ragflow_port}")
        print(f"   API密钥: {self.api_key}")
    
    def test_basic_connectivity(self) -> bool:
        """测试基础网络连接"""
        print(f"\n1️⃣ 测试基础网络连接...")
        
        # 测试 RAGFlow 主服务
        try:
            response = requests.get(f"{self.ragflow_url}/", timeout=10)
            print(f"✅ RAGFlow主服务 ({self.ragflow_port}): {response.status_code}")
        except Exception as e:
            print(f"❌ RAGFlow主服务连接失败: {e}")
            return False
        
        # 测试 MCP 端口
        try:
            response = requests.get(f"{self.base_url}/", timeout=10)
            print(f"✅ MCP端口 ({self.mcp_port}): {response.status_code}")
        except Exception as e:
            print(f"❌ MCP端口连接失败: {e}")
            return False
        
        return True
    
    def test_mcp_endpoints(self) -> Dict[str, Any]:
        """测试各种 MCP 端点"""
        print(f"\n2️⃣ 测试 MCP 端点...")
        
        results = {}
        
        # 测试健康检查端点
        endpoints_to_test = [
            "/health",
            "/sse", 
            "/messages/",
            "/api/v1/status"
        ]
        
        for endpoint in endpoints_to_test:
            try:
                url = f"{self.base_url}{endpoint}"
                response = requests.get(url, headers=self.headers, timeout=5)
                results[endpoint] = {
                    "status_code": response.status_code,
                    "response": response.text[:200] if response.text else "Empty"
                }
                print(f"✅ {endpoint}: {response.status_code}")
            except Exception as e:
                results[endpoint] = {"error": str(e)}
                print(f"❌ {endpoint}: {e}")
        
        return results
    
    def test_mcp_protocol(self) -> bool:
        """测试 MCP 协议通信"""
        print(f"\n3️⃣ 测试 MCP 协议通信...")
        
        session_id = str(uuid.uuid4())
        
        # MCP 初始化请求
        init_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {"tools": {}},
                "clientInfo": {"name": "Wing-Diagnostic", "version": "1.0"}
            }
        }
        
        try:
            url = f"{self.base_url}/messages/?session_id={session_id}"
            response = requests.post(url, headers=self.headers, json=init_request, timeout=15)
            
            print(f"   初始化请求状态: {response.status_code}")
            print(f"   响应内容: {response.text[:300]}...")
            
            if response.status_code == 200:
                result = response.json()
                if "result" in result:
                    print("✅ MCP 协议初始化成功")
                    return True
                else:
                    print("❌ MCP 协议初始化失败 - 无有效结果")
                    return False
            else:
                print(f"❌ MCP 协议初始化失败 - HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ MCP 协议测试异常: {e}")
            return False
    
    def test_tools_list(self) -> Optional[list]:
        """测试获取工具列表"""
        print(f"\n4️⃣ 测试获取工具列表...")
        
        session_id = str(uuid.uuid4())
        
        # 先初始化
        init_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {"tools": {}},
                "clientInfo": {"name": "Wing-Diagnostic", "version": "1.0"}
            }
        }
        
        try:
            url = f"{self.base_url}/messages/?session_id={session_id}"
            init_response = requests.post(url, headers=self.headers, json=init_request, timeout=15)
            
            if init_response.status_code != 200:
                print(f"❌ 初始化失败: {init_response.status_code}")
                return None
            
            # 获取工具列表
            tools_request = {
                "jsonrpc": "2.0",
                "id": 2,
                "method": "tools/list"
            }
            
            tools_response = requests.post(url, headers=self.headers, json=tools_request, timeout=15)
            
            print(f"   工具列表请求状态: {tools_response.status_code}")
            print(f"   响应内容: {tools_response.text[:300]}...")
            
            if tools_response.status_code == 200:
                result = tools_response.json()
                if "result" in result and "tools" in result["result"]:
                    tools = result["result"]["tools"]
                    print(f"✅ 获取到 {len(tools)} 个工具")
                    for tool in tools:
                        print(f"   - {tool.get('name', 'Unknown')}: {tool.get('description', 'No description')}")
                    return tools
                else:
                    print("❌ 工具列表格式错误")
                    return None
            else:
                print(f"❌ 获取工具列表失败: {tools_response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ 工具列表测试异常: {e}")
            return None
    
    def run_full_diagnostic(self) -> Dict[str, Any]:
        """运行完整诊断"""
        print("🚀 开始 RAGFlow MCP Server 完整诊断")
        print("=" * 60)
        
        diagnostic_results = {
            "server_info": {
                "ip": self.server_ip,
                "mcp_port": self.mcp_port,
                "ragflow_port": self.ragflow_port,
                "api_key": self.api_key
            },
            "tests": {}
        }
        
        # 1. 基础连接测试
        diagnostic_results["tests"]["connectivity"] = self.test_basic_connectivity()
        
        # 2. 端点测试
        diagnostic_results["tests"]["endpoints"] = self.test_mcp_endpoints()
        
        # 3. MCP 协议测试
        diagnostic_results["tests"]["mcp_protocol"] = self.test_mcp_protocol()
        
        # 4. 工具列表测试
        diagnostic_results["tests"]["tools"] = self.test_tools_list()
        
        print(f"\n" + "=" * 60)
        print(f"🎯 诊断完成！")
        
        # 生成诊断报告
        self.generate_report(diagnostic_results)
        
        return diagnostic_results
    
    def generate_report(self, results: Dict[str, Any]):
        """生成诊断报告"""
        print(f"\n📋 诊断报告:")
        
        connectivity = results["tests"]["connectivity"]
        mcp_protocol = results["tests"]["mcp_protocol"]
        tools = results["tests"]["tools"]
        
        if connectivity and mcp_protocol and tools:
            print("✅ 所有测试通过 - MCP Server 工作正常")
            print("💡 建议: 可以开始配置 Wing 客户端连接")
        elif connectivity and mcp_protocol:
            print("⚠️  基础连接正常，但工具列表获取失败")
            print("💡 建议: 检查 RAGFlow 知识库配置")
        elif connectivity:
            print("⚠️  网络连接正常，但 MCP 协议通信失败")
            print("💡 建议: 检查 MCP Server 配置和 API 密钥")
        else:
            print("❌ 基础连接失败")
            print("💡 建议: 检查服务器状态、防火墙和网络配置")


def main():
    """主函数"""
    print("RAGFlow MCP Server 连接诊断工具")
    print("用于诊断 Wing 客户端到 RAGFlow MCP Server 的连接问题")
    print()
    
    # 使用你提供的服务器 IP
    diagnostic = RAGFlowMCPDiagnostic("************")
    
    try:
        results = diagnostic.run_full_diagnostic()
        
        # 保存诊断结果
        with open("mcp_diagnostic_results.json", "w", encoding="utf-8") as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 诊断结果已保存到: mcp_diagnostic_results.json")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  诊断被用户中断")
    except Exception as e:
        print(f"\n❌ 诊断过程中发生错误: {str(e)}")


if __name__ == "__main__":
    main()
