标题: DNS域名系统
英文标题: DNS Domain Name System
ID: 99
分类ID: 25
添加时间: 1614752052
更新时间: 1685430119
访问次数: 0
SEO标题: DNS域名系统
SEO关键词: DNS域名系统
SEO描述: DNS域名系统

================================================== 内容 ==================================================
域名系统（英语：Domain Name System，缩写：DNS）是互联网的一项服务。它作为将域名和IP地址相互映射的一个分布式数据库，能够使人更方便地访问互联网。DNS使用TCP和UDP端口53。当前，对于每一级域名长度的限制是63个字符，域名总长度则不能超过253个字符。


开始时，域名的字符仅限于ASCII字符的一个子集。2008年，ICANN通过一项决议，允许使用其它语言作为互联网顶级域名的字符。使用基于Punycode码的IDNA系统，可以将Unicode字符串映射为有效的DNS字符集。因此，诸如“XXX.中国”、“XXX.美国”的域名可以在地址栏直接输入并访问，而不需要安装插件。

================================================== 英文内容 ==================================================
The Domain Name System (DNS) is a service of the Internet. It acts as a distributed database that maps domain names and IP addresses to each other, making it easier for people to access the Internet. DNS uses TCP and UDP ports 53. Currently, each level of a domain name contains 63 characters, and the total length of a domain name cannot exceed 253 characters.


Initially, domain names are limited to a subset of ASCII characters. In 2008, ICANN passed a resolution allowing the use of other languages as characters for Internet top-level domains. Using the Punycode-based IDNA system, Unicode strings can be mapped to a valid DNS character set. Therefore, domain names such as "XXX. China" and "XXX. USA" can be entered and accessed directly from the address bar without the need to install a plug-in.