					<HorizontalLayout height="30">
             <Label name="trayl" font="8" align="left" padding="24,0,0,0" height="20" width="200" text="候鸟托盘区设定"/>
					</HorizontalLayout>

          <HorizontalLayout height="40">
						<Option name="starttray" padding="24,0,0,0" font="8" normalimage="file='option_normal.png' dest='0,0,18,18'" hotimage="file='option_hot.png' dest='0,0,18,18'" align="left" selectedimage="file='option_pushed.png' dest='0,0,18,18'" width="320" height="18" text="启动后自动最小化到右下角" texttooltip="true" endellipsis="true" textpadding="30,-1,0,0" selected="false"/>
					</HorizontalLayout>

				<HorizontalLayout height="8"></HorizontalLayout>

					<HorizontalLayout height="30">
             <Label name="reset_shortcut_dec" font="8" align="left" padding="24,0,0,0" height="20" width="200" text="热键初始化"/>
					</HorizontalLayout>

					<HorizontalLayout height="40">
					    <VerticalLayout width="400" height="40" padding="24,0,0,0">
					       <Control />
						      <Button name="defaultset" padding="0,0,0,0" textpadding="10,0,10,0" bkcolor="#FFffffff" bordersize="1" bordercolor="#ffd3d3d3" borderround="11,11" hotbkcolor="#fff1f2f1"  width="208" height="26" texttooltip="true" endellipsis="true" text="初始化热键默认设置"/>
					       <Control />
					    </VerticalLayout>
					</HorizontalLayout>

					<HorizontalLayout height="50"></HorizontalLayout>

				</VerticalLayout>


				<VerticalLayout inset="0,6,0,0" vscrollbar="true">

					<HorizontalLayout height="32" inset="0,0,0,0">
					   <Button padding="30,0,0,0" height="32" width="32" bkimage="Setting\title\sync.png"/>
					   <Label name="set_common_name2" textpadding="10,0,0,0" width="220" align="left" font="8" text="通讯设置"/>
					</HorizontalLayout>

					<HorizontalLayout height="52" inset="0,0,0,0">
					   <Label name="set_common_dec2" padding="24,0,0,0" textpadding="0,6,0,0" width="220" align="left" font="8" textcolor="#FF6d6d6d" text="内核通讯端口配置"/>
					</HorizontalLayout>

					<HorizontalLayout height="14"></HorizontalLayout>

					<HorizontalLayout height="22" width="400">
						<Label name="cdpport" padding="24,0,0,0" align="left" height="20" width="400" font="8" text="CDP监听端口：" />
					</HorizontalLayout>

          <HorizontalLayout height="8"></HorizontalLayout>

					<HorizontalLayout height="28" width="400">
						<RichEdit name="port_cdp" wanttab="false" padding="24,0,0,10" borderround="5,5" height="26" width="155" killfocusimage="" bkimage="file_normal.png" font="0" textpadding="10,5,20,0" text="0" tipvaluecolor="ff333333" multiline="false" maxchar="5" textcolor="ff666666" rich="false">
				    </RichEdit>
				    <!--Button name="btnCDPport" padding="10,0,0,0" text="更改" align="center" bkimage="file_modify_normal.png" hotimage="file_modify_hot.png" pushedimage="file_modify_hot.png" width="60" height="26"/-->
					</HorizontalLayout>


					<HorizontalLayout height="18"></HorizontalLayout>

					<HorizontalLayout height="22" width="400">
						<Label name="mbport" padding="24,0,0,0" align="left" height="20" width="400" font="8" text="MBService监听端口：" />
					</HorizontalLayout>

					 <HorizontalLayout height="8"></HorizontalLayout>

					<HorizontalLayout height="28" width="400">
						<RichEdit name="port_mbsvr" wanttab="false" padding="24,0,0,10"  borderround="5,5" height="26" width="155" killfocusimage="" bkimage="file_normal.png" font="0" textpadding="10,5,20,0" text="0" tipvaluecolor="ff333333" multiline="false" maxchar="5" textcolor="ff666666" rich="false">
				    </RichEdit>
				    <!--Button name="btnCDPport" padding="10,0,0,0" text="更改" align="center" bkimage="file_modify_normal.png" hotimage="file_modify_hot.png" pushedimage="file_modify_hot.png" width="60" height="26"/-->
					</HorizontalLayout>



					<HorizontalLayout height="18"></HorizontalLayout>

					<HorizontalLayout height="22" width="400">
						<Label name="mainport" padding="24,0,0,0" align="left" height="20" width="400" font="8" text="MBBrowser监听端口：" />
					</HorizontalLayout>

					 <HorizontalLayout height="8"></HorizontalLayout>

					<HorizontalLayout height="28" width="400">
						<RichEdit name="port_mbb" wanttab="false" padding="24,0,0,10" borderround="5,5" height="26" width="155" killfocusimage="" bkimage="file_normal.png" font="0" textpadding="10,5,20,0" text="0" tipvaluecolor="ff333333" multiline="false" maxchar="5" textcolor="ff666666" rich="false">
				    </RichEdit>
				    <!--Button name="btnCDPport" padding="10,0,0,0" text="更改" align="center" bkimage="file_modify_normal.png" hotimage="file_modify_hot.png" pushedimage="file_modify_hot.png" width="60" height="26"/-->
					</HorizontalLayout>

					<HorizontalLayout height="18"></HorizontalLayout>

					<HorizontalLayout height="22" width="400">
						<Label name="mainaddr" padding="24,0,0,0" align="left" height="20" width="400" font="8" text="监听地址：" />
					</HorizontalLayout>

					<HorizontalLayout height="8"></HorizontalLayout>

					<HorizontalLayout height="28" width="400">
						<Combo padding="24,0,0,0" itemtextpadding="-25,0,0,0" textpadding="0,0,50,0" borderround="5,5" itemalign="center" bkimage="combo_normal_large.png" hotimage="combo_hot_large.png" pushedimage="combo_pushed_large.png" width="155" height="25"  itemhotbkcolor="fff1f1f1" itemselectedbkcolor="ffffffff" >
                            <ListLabelElement  text="0.0.0.0" selected="true"/>
                            <ListLabelElement  text="127.0.0.1"/>
            </Combo>

					</HorizontalLayout>


					<HorizontalLayout height="18"></HorizontalLayout>

					<HorizontalLayout height="22" width="400">
						<Label name="set_connect_restart_mb" padding="24,0,0,0" align="left" height="20" width="400" font="8" text="确认生效并重启客户端" />
					</HorizontalLayout>
					<HorizontalLayout height="8"></HorizontalLayout>
					<HorizontalLayout height="28" width="400">
						<Button name="btnModifyPort" padding="24,0,0,0" bkcolor="#FFffffff" bordersize="1" bordercolor="#ffd3d3d3" borderround="11,11" hotbkcolor="#fff1f2f1" float="left" textpadding="10,0,10,0" texttooltip="true" endellipsis="true" text="确认更新并重启生效" width="208" height="26"/>
					</HorizontalLayout>

					<HorizontalLayout height="18"></HorizontalLayout>

					<HorizontalLayout height="22" width="400">
						<Label name="set_select_line" padding="24,0,0,0" align="left" height="20" width="400" font="8" text="选择最优服务器线路" />
					</HorizontalLayout>
					<HorizontalLayout height="8"></HorizontalLayout>
					<HorizontalLayout height="28" width="400">
						<Button name="btnServerList"  padding="24,0,0,0" bkcolor="#FFffffff" bordersize="1" bordercolor="#ffd3d3d3" borderround="11,11" hotbkcolor="#fff1f2f1" float="left" textpadding="10,0,10,0" texttooltip="true" endellipsis="true" text="服务器线路选择" width="160" height="26"/>
					</HorizontalLayout>

					<HorizontalLayout height="50"></HorizontalLayout>

				</VerticalLayout>



        <VerticalLayout inset="0,6,0,0" vscrollbar="true">

          <HorizontalLayout height="32" inset="0,0,0,0">
					   <Button padding="30,0,0,0" height="32" width="32" bkimage="Setting\title\map.png"/>
					   <Label name="set_data_name2" textpadding="10,0,0,0" width="220" align="left" font="8" text="常用数据"/>
					</HorizontalLayout>

					<HorizontalLayout height="52" inset="0,0,0,0">
					   <Label name="set_data_dec2" padding="24,0,0,0" textpadding="0,6,0,0" width="220" align="left" font="8" textcolor="#FF6d6d6d" text="环境面板常用数据设置"/>
					</HorizontalLayout>

					<HorizontalLayout height="40">
					      <Label name="iteml11" float="left" font="8" padding="24,0,0,0" width="400" height="20" textcolor="ff868686" align="left" texttooltip="true" endellipsis="true" text="勾选常用各项列表作为环境配置面板常用数据项"/>
					</HorizontalLayout>

          <HorizontalLayout height="28" width="460">
            <Button name="systembtn" padding="24,5,0,0" text="系统" autocalcwidth="true" hottextcolor="#ff519cff" pushedtextcolor="ff519cff" align="left" font="4"></Button>
            <Label width="1" padding="8,0,0,0" bkimage="file='split.png'"></Label>
            <Button name="resobtn" padding="6,5,0,0" text="分辨率" autocalcwidth="true" hottextcolor="#ff519cff" pushedtextcolor="ff519cff" align="left" font="4"></Button>
            <Label width="1" padding="8,0,0,0" bkimage="file='split.png'"></Label>
            <Button name="proxybtn" padding="6,5,0,0" text="代理Proxy" autocalcwidth="true" hottextcolor="#ff519cff" pushedtextcolor="ff519cff" align="left" font="4"></Button>
            <Label width="1" padding="8,0,0,0" bkimage="file='split.png'"></Label>
            <Button name="timezonebtn" padding="6,5,0,0" text="时区" autocalcwidth="true" hottextcolor="#ff519cff" pushedtextcolor="ff519cff" align="left" font="4"></Button>
            <Label width="1" padding="8,0,0,0" bkimage="file='split.png'"></Label>
            <Button name="countrybtn" padding="6,5,0,0" text="国家" autocalcwidth="true" hottextcolor="#ff519cff" pushedtextcolor="ff519cff" align="left" font="4"></Button>
            <Label width="1" padding="8,0,0,0" bkimage="file='split.png'"></Label>
            <Button name="lngbtn" padding="6,5,0,0" text="语言" autocalcwidth="true" hottextcolor="#ff519cff" pushedtextcolor="ff519cff" align="left" font="4"></Button>
          </HorizontalLayout>

          <HorizontalLayout height="240">
          <TabLayout name="tabitem" inset="24,0,0,0" selectedid="0" >
            <VerticalLayout inset="0,0,0,0">
              <List name="itemlist0" inset="1,1,1,1" width="436" height="240" scrollwheel="true" bordersize="0" itembkcolor="#FFffffff" itemselectedbkcolor="#FFe0e0e0" itemhotbkcolor="#FFf2f2f2" bordercolor="#FFD9DADB" vscrollbar="true">
                <ListHeader height="1" bordersize="1,1,1,1">
                </ListHeader>
              </List>
            </VerticalLayout>
            <VerticalLayout inset="0,0,0,0">
              <List name="itemlist1" inset="1,1,1,1" width="436" height="240" scrollwheel="true" bordersize="0" itembkcolor="#FFffffff" itemselectedbkcolor="#FFe0e0e0" itemhotbkcolor="#FFf2f2f2" bordercolor="#FFD9DADB" vscrollbar="true">
                <ListHeader height="1" bordersize="1,1,1,1">
                </ListHeader>
              </List>
            </VerticalLayout>
            <VerticalLayout inset="0,0,0,0">
              <List name="itemlist2" inset="1,1,1,1" width="436" height="240" scrollwheel="true" bordersize="0"  itembkcolor="#FFffffff" itemselectedbkcolor="#FFe0e0e0" itemhotbkcolor="#FFf2f2f2" bordercolor="#FFD9DADB" vscrollbar="true">
                <ListHeader height="1" bordersize="1,1,1,1">
                </ListHeader>
              </List>
            </VerticalLayout>
            <VerticalLayout inset="0,0,0,0">
              <List name="itemlist3" inset="1,1,1,1" width="436" height="240" scrollwheel="true" bordersize="0"  itembkcolor="#FFffffff" itemselectedbkcolor="#FFe0e0e0" itemhotbkcolor="#FFf2f2f2" bordercolor="#FFD9DADB" vscrollbar="true">
                <ListHeader height="1" bordersize="1,1,1,1">
                </ListHeader>
              </List>
            </VerticalLayout>
            <VerticalLayout inset="0,0,0,0">
              <List name="itemlist4" inset="1,1,1,1" width="436" height="240" scrollwheel="true" bordersize="0"  itembkcolor="#FFffffff" itemselectedbkcolor="#FFe0e0e0" itemhotbkcolor="#FFf2f2f2" bordercolor="#FFD9DADB" vscrollbar="true">
                <ListHeader height="1" bordersize="1,1,1,1">
                </ListHeader>
              </List>
            </VerticalLayout>
            <VerticalLayout inset="0,0,0,0">
              <List name="itemlist5" inset="1,1,1,1" width="436" height="240" scrollwheel="true" bordersize="0"  itembkcolor="#FFffffff" itemselectedbkcolor="#FFe0e0e0" itemhotbkcolor="#FFf2f2f2" bordercolor="#FFD9DADB" vscrollbar="true">
                <ListHeader height="1" bordersize="1,1,1,1">
                </ListHeader>
              </List>
            </VerticalLayout>
          </TabLayout>
          </HorizontalLayout>
          <HorizontalLayout height="28" width="466">
            <Button name="selall" padding="24,4,0,0" align="left" height="20" width="26" text="全选" font="5" hottextcolor="#FF005ed3" />
            <Button name="unselall" padding="4,4,0,0" align="left" height="20" width="26" text="反选" font="5" hottextcolor="#FF005ed3" />
            <Button name="reset" padding="4,4,0,0" align="left" height="20" width="120" endellipsis="true" texttooltip="true" text="恢复默认设置" font="5" textcolor="#FFff6060" hottextcolor="#FF005ed3" />
          </HorizontalLayout>

					<HorizontalLayout height="18"></HorizontalLayout>

					<HorizontalLayout height="22" width="400">
						<Label name="set_effect" padding="24,0,0,0" align="left" height="20" width="400" font="8" text="应用列表更改并生效" />
					</HorizontalLayout>

					<HorizontalLayout height="8"></HorizontalLayout>

					<HorizontalLayout height="28" width="400">
						<Button name="itemok" enabled="false" padding="24,0,0,0" width="160" height="26" text="确定" font="5" bkcolor="#FFffffff" bordersize="1" bordercolor="#ffd3d3d3" borderround="5,5" hotbkcolor="#fff1f2f1"/>
					</HorizontalLayout>

					<HorizontalLayout height="50"></HorizontalLayout>

        </VerticalLayout>

        <VerticalLayout inset="0,6,0,0" vscrollbar="true">


        	<HorizontalLayout height="32" inset="0,0,0,0">
					   <Button padding="30,0,0,0" height="32" width="32" bkimage="Setting\title\timezone.png"/>
					   <Label name="set_timezone_name" textpadding="10,0,0,0" width="220" align="left" font="8" text="时区检测"/>
					</HorizontalLayout>

					<HorizontalLayout height="52" inset="0,0,0,0">
					   <Label name="set_timezone_dec" padding="24,0,0,0" textpadding="0,6,0,0" width="220" align="left" font="8" textcolor="#FF6d6d6d" text="环境时区检测设置"/>
					</HorizontalLayout>

					<HorizontalLayout height="46">
					      <Label name="ipchannell" padding="24,0,0,0" width="220" align="left" font="8" text="指定代理IP->时区检测引擎"/>
					</HorizontalLayout>

        	<HorizontalLayout height="16"></HorizontalLayout>

          <HorizontalLayout height="46">
					      <Combo name="cfgtimezone" padding="24,0,0,0" width="155"  borderround="5,5" textpadding="0,0,20,0" itemalign="center" bkimage="combo_normal_large.png" hotimage="combo_hot_large.png" pushedimage="combo_pushed_large.png" height="25"  itemhotbkcolor="fff1f1f1" itemselectedbkcolor="ffffffff" itemtextpadding="-25,0,0,0">
              </Combo>
					</HorizontalLayout>


					<HorizontalLayout height="16"></HorizontalLayout>

  				<HorizontalLayout height="26">
					      <Label name="ipchannell2" padding="24,0,0,0" height="20" width="280" align="left" font="8" text="温馨提示" />
					</HorizontalLayout>

          <HorizontalLayout height="160" inset="6,0,0,0">
            <Label name="ipinfo" padding="24,2,0,0" width="400" font="8" align="left" multiline="true" text="        在候鸟环境配置时，您添加的代理IP必须具备准确的时区、国家、语言。检测代理IP所在的时区、国家、语言因检测引擎不同，可能会带来差异，请选择最合适您业务的检索引擎来匹配您的业务需要。"/>
          </HorizontalLayout>

					<HorizontalLayout height="16"></HorizontalLayout>

  				<HorizontalLayout height="26">
					      <Label name="set_timezone_valid" padding="24,0,0,0" height="20" width="280" align="left" font="8" text="应用当前选择" />
					</HorizontalLayout>

					<HorizontalLayout height="8"></HorizontalLayout>

  				<HorizontalLayout height="28">
					    <Button name="timezoneset" padding="24,0,0,0" textpadding="10,0,10,0" bkcolor="#FFffffff" bordersize="1" bordercolor="#ffd3d3d3" borderround="11,11" hotbkcolor="#fff1f2f1"  width="208" height="26" texttooltip="true" endellipsis="true" text="设定为我的默认查询引擎"/>
					</HorizontalLayout>



        </VerticalLayout>

				<VerticalLayout inset="0,6,0,0" vscrollbar="true">


					<HorizontalLayout height="32" inset="0,0,0,0">
					   <Button padding="30,0,0,0" height="32" width="32" bkimage="Setting\title\tools.png"/>
					   <Label name="set_exp_name" textpadding="10,0,0,0" width="220" align="left" font="8" text="浏览器设置"/>
					</HorizontalLayout>

					<HorizontalLayout height="52" inset="0,0,0,0">
					   <Label name="set_exp_dec" padding="24,0,0,0" textpadding="0,6,0,0" width="220" align="left" font="8" textcolor="#FF6d6d6d" text="浏览器内核全局设置"/>
					</HorizontalLayout>

					<HorizontalLayout height="46">
					      <Label name="mainpageurll" padding="24,0,0,0" width="220" align="left" font="8" text="默认起始页"/>
					</HorizontalLayout>

          <HorizontalLayout height="84" width="500">
            <RichEdit name="mainpageurledit" padding="24,0,0,10" height="82" width="326" bordersize="1" bordercolor="#ffd3d3d3" borderround="7,7" font="0" textpadding="10,4,2,0" text="" menu="true" multiline="true" textcolor="ff666666" wanttab="false" vscrollbar="true" autovscroll="true" autohscroll="false" rich="false">
            </RichEdit>
            <Button name="mainpageurlbtn" padding="10,0,0,0" text="更改" align="center" bkcolor="#FFffffff" bordersize="1" bordercolor="#ffd3d3d3" borderround="5,5" hotbkcolor="#fff1f2f1" width="60" height="26"/>
          </HorizontalLayout>

          <HorizontalLayout height="16"></HorizontalLayout>

					<HorizontalLayout height="26" width="320">
						<Label name="GpuText" padding="24,4,0,0" height="20" width="320" align="left" font="8" text="浏览器GPU渲染开关：" />
					</HorizontalLayout>

					<HorizontalLayout height="8"></HorizontalLayout>

					<HorizontalLayout height="24" width="300">
						<!--<Label name="Gpu_edit" padding="35,2,0,10" height="24" width="52" killfocusimage="" bkimage="gpu_off.png" font="0" tipvaluecolor="ff333333" multiline="false" textcolor="ff666666" rich="false">
				    </Label>-->
            <CheckBox name="Gpu_switch" padding="24,0,0,10" width="52" height="24"  normalimage="gpu_off.png" selectedimage="gpu_on.png" disabledimage="gpu_off.png" />
            <!--<Button name="Gpubtn" padding="10,0,0,0" text="Switch" align="center" bkcolor="#FFffffff" bordersize="1" bordercolor="#ffd3d3d3" borderround="5,5" hotbkcolor="#fff1f2f1" width="60" height="26"/>-->
					</HorizontalLayout>

					<HorizontalLayout height="8"></HorizontalLayout>

					<HorizontalLayout height="22" width="500">
						<Label name="GpuInfo1" float="left" padding="24,2,0,0" width="460" height="20" textcolor="ff868686" align="left" texttooltip="true" endellipsis="true" text="为保证指纹安全，独立显卡硬件GPU渲染应保持关闭状态。" font="0"/>
					</HorizontalLayout>

					<HorizontalLayout height="22" width="500">
						<Label name="GpuInfo2" float="left" padding="24,2,0,0" width="460" height="20" textcolor="ff868686" align="left" texttooltip="true" endellipsis="true" text="如无需担心指纹安全因素，可打开此开关来加速浏览器页面渲染。" font="0"/>
					</HorizontalLayout>




					<HorizontalLayout height="16"></HorizontalLayout>

