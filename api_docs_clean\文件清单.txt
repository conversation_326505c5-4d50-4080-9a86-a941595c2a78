API文档清理后的文件清单
==================================================


## HTTP模式说明 (3 个文件)
- API_HTTP模式说明_1_以管理员身份运行CMD或者PowerShell，并确保终端在候鸟浏览器主目录打开，或已进入候鸟浏览器主路径.txt
- API_HTTP模式说明_2_启动成功过后在命令行工具可以看到API地址.txt
- API_HTTP模式说明_3_CLI命令行参数介绍.txt

## POSTMAN调试候鸟API接口 (2 个文件)
- API_POSTMAN调试候鸟API接口_1_仅支持客户端V3.9.2.114以上版本，请下载客户端最新版本.txt
- API_POSTMAN调试候鸟API接口_2_需要本地安装POSTMAN，见.txt

## 使用须知 (3 个文件)
- API_使用须知_1_获取API凭证.txt
- API_使用须知_2_查看API凭证.txt
- API_使用须知_2_获取环境ID.txt

## 分组管理 (4 个文件)
- API_分组管理_1_获取环境分组列表.txt
- API_分组管理_2_新建环境分组.txt
- API_分组管理_3_删除环境分组.txt
- API_分组管理_4_将指定环境从指定分组转移到另一个分组.txt

## 帐号登录 (1 个文件)
- API_帐号登录_2_仅支持客户端V3.9.2.114以上版本，请下载客户端最新版本.txt

## 常见问题 (3 个文件)
- API_常见问题_1_通过命令行启动客户端报错.txt
- API_常见问题_2_调用API是否可以同时手动打开客户端.txt
- API_常见问题_3_打开环境失败.txt

## 插件管理 (4 个文件)
- API_插件管理_1_列出当前帐户下所有已安装的插件(插件 ID，插件名称).txt
- API_插件管理_2_查询、列出指定环境中的所有插件(插件 ID，插件名称).txt
- API_插件管理_3_安装指定多个插件到指定的环境中.txt
- API_插件管理_4_删除指定环境插件.txt

## 环境开启关闭 (3 个文件)
- API_环境开启关闭_1_打开环境.txt
- API_环境开启关闭_2_关闭环境.txt
- API_环境开启关闭_3_强制终止环境.txt

## 环境管理 (16 个文件)
- API_环境管理_10_获取随机UA.txt
- API_环境管理_11_清除环境本地缓存.txt
- API_环境管理_12_查看环境运行状态.txt
- API_环境管理_13_查看环境网页自动运行信息.txt
- API_环境管理_14_添加环境自动运行网页地址.txt
- API_环境管理_15_更新环境某个自动运行网页地址.txt
- API_环境管理_16_删除环境某个自动运行网页地址.txt
- API_环境管理_1_获取环境列表.txt
- API_环境管理_2_查询指定环境ID的配置数据.txt
- API_环境管理_3_创建环境.txt
- API_环境管理_4_更新环境高级指纹参数.txt
- API_环境管理_5_更新环境.txt
- API_环境管理_6_更新环境代理.txt
- API_环境管理_7_删除环境.txt
- API_环境管理_8_导入Cookie.txt
- API_环境管理_9_导出Cookie.txt

## 脚本管理 (5 个文件)
- API_脚本管理_1_查询、列出指定环境中的所有脚本.txt
- API_脚本管理_2_切换指定环境已激活脚本.txt
- API_脚本管理_3_从我的脚本库中指派脚本到目标环境中.txt
- API_脚本管理_4_指定环境中的指定脚本设定为非激活状态.txt
- API_脚本管理_5_将未激活脚本从指定环境中移除.txt

## 获取成员列表 (1 个文件)
- API_获取成员列表_2_获取成员列表.txt

## 调试接口JSON数据官方更新下载 (2 个文件)
- API_调试接口JSON数据官方更新下载_1_POSTMAN如何导入JSON数据.txt
- API_调试接口JSON数据官方更新下载_2_POSTMAN如何导出JSON数据.txt

## 错误码对照表 (1 个文件)
- API_错误码对照表_9_错误码对照表.txt

## 附录 (1 个文件)
- API_附录_8_附录（国家码、时区、语言、系统和分辨率）.txt


总计: 49 个有效文件
