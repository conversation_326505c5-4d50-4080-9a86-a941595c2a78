# HTTP模式说明

## 描述
候鸟浏览器HTTP模式的详细启动说明，包括命令行参数、启动步骤和接口说明。

## 版本要求
- **最低版本**: V3.9.2.114以上版本
- **下载地址**: [下载候鸟浏览器最新版](https://www.mbbrowser.com/download.php)

## 准备工作
操作前，请先从控制台购买API凭证并获取帐号验证所需的信息。具体见[获取APP_ID、APP_KEY](/api/help)

## 1. 以管理员身份运行CMD或PowerShell

### 前置条件
- 以管理员身份运行CMD或者PowerShell
- 确保终端在候鸟浏览器主目录打开，或已进入候鸟浏览器主路径

### CMD启动命令
```bash
ApiServer.exe --port=8186 --account=XXXXX --app_id=XXXXX --app_key=XXXXX --return=on --logs=on
```

### PowerShell启动命令
```powershell
.\ApiServer.exe --port=8186 --account=XXXXX --app_id=XXXXX --app_key=XXXXX --return=on --logs=on
```

### 参数说明
将XXXXX替换为实际的值：
- **account**: 你的登录账号
- **app_id**: 从控制台获取的APP_ID
- **app_key**: 从控制台获取的APP_KEY

## 2. 启动成功确认

### 成功标志
启动成功后在命令行工具可以看到API地址，通常显示：
```
API Server started on: http://127.0.0.1:8186
```

### 登录状态确认
- **带参数启动**: 参数带入account、app_id、app_key，则在右下角的托盘菜单看见软件图标，标明已登入，可以调用接口运行脚本
- **不带参数启动**: 若未带入account、app_id、app_key参数，可在后续通过[登录帐号接口](/api/login)登录客户端

## 3. CLI命令行参数详解

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| --port | 可选 | 8186 | HTTP连接端口 |
| --account | 可选 | - | 登录账号 |
| --app_id | 可选 | - | 凭证APP_ID |
| --app_key | 可选 | - | 凭证APP_KEY |
| --return | 可选 | on | on: 所有返回数据显示到CONSOLE界面<br>off: 不在CONSOLE界面显示 |
| --logs | 可选 | on | on: 所有JSON数据写入API_LOG目录<br>off: 不写入LOG |
| --hide | 可选 | off | on: 支持客户端有头运行<br>off: 默认模式 |

### 完整启动示例
```bash
ApiServer.exe --port=8186 --account=<EMAIL> --app_id=7e147176e1d756eb03c0e18e7b640c23 --app_key=kwMTYxNG_MzlkZjhlZmM2YWNiY2M5MTkw --return=on --logs=on --hide=off
```

## 停止并退出APISERVER

### 方式一：托盘菜单退出
1. 在操作系统右下角TRAY区找到APISERVER图标
2. 右键点击图标唤出菜单
3. 点击"退出"完成退出APISERVER动作

### 方式二：任务管理器强制退出
1. 打开任务管理器
2. 找到ApiServer.exe进程
3. 结束进程

### 方式三：API接口退出
使用[退出APISERVER接口](/api/login)：
- **Path**: /api/v1/quit
- **Method**: POST

## 支持的接口列表

HTTP模式支持以下接口：

### 基础接口
- [**帐号登录**](/api/login)：启动客户端
- [**获取成员列表**](/api/members)：获取主帐号与子帐号数据

### 环境操作接口
- [**环境开启/关闭**](/api/browser)：打开环境、关闭环境、强制终止环境

### 环境管理接口
- [**环境管理**](/api/session)：
  - 获取环境列表
  - 查询指定环境的配置数据
  - 创建环境
  - 更新环境高级指纹参数
  - 更新环境
  - 更新环境代理
  - 删除环境
  - 导入Cookie
  - 导出Cookie
  - 获取随机UA
  - 清除环境本地缓存

### 分组管理接口
- [**分组管理**](/api/group)：
  - 获取环境分组列表
  - 新建环境分组
  - 删除环境分组
  - 转移环境分组

## 使用流程

1. **准备阶段**
   - 确保客户端版本V3.9.2.114以上
   - 获取API凭证（APP_ID和APP_KEY）
   - 以管理员身份打开命令行工具

2. **启动阶段**
   - 进入候鸟浏览器安装目录
   - 执行启动命令
   - 确认启动成功

3. **使用阶段**
   - 通过HTTP接口调用各种功能
   - 监控命令行输出和日志文件
   - 根据需要调整参数

4. **结束阶段**
   - 通过托盘菜单或API接口退出
   - 检查日志文件

## 常见问题

### Q: 启动时提示权限不足怎么办？
A: 确保以管理员身份运行CMD或PowerShell。

### Q: 端口8186被占用怎么办？
A: 使用--port参数指定其他端口，如--port=8187。

### Q: 启动后看不到托盘图标怎么办？
A: 检查是否正确传入了account、app_id、app_key参数。

### Q: 如何查看详细的运行日志？
A: 确保--logs=on，日志文件会保存在API_LOG目录中。

## 注意事项

1. **管理员权限**: 必须以管理员身份运行
2. **目录位置**: 确保在候鸟浏览器安装目录中执行命令
3. **参数格式**: 注意参数的正确格式，特别是--return不是--retrun
4. **端口占用**: 确保指定的端口未被其他程序占用
5. **网络防火墙**: 确保防火墙允许程序访问网络

## 相关链接

- [使用须知](/api/help)
- [帐号登录接口](/api/login)
- [环境管理接口](/api/session)
- [错误码对照表](/api/code)
- [常见问题](/api/question)
