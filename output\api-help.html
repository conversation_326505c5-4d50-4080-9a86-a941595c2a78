<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>/api/help</title>
  <style>
/* 基础重置与排版 */
body {
  margin: 0;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.7;
  color: #333;
  background-color: #fff;
  max-width: 960px;
  margin: 0 auto;
  box-sizing: border-box;
}

/* 段落 */
p {
  margin: 1em 0;
}

/* 标题 */
h1, h2, h3, h4, h5, h6 {
  margin: 1.5em 0 0.8em;
  color: #2c3e50;
  font-weight: 600;
  line-height: 1.3;
}

h1 { font-size: 2em; border-bottom: 1px solid #eee; padding-bottom: 0.5em; }
h2 { font-size: 1.6em; }
h3 { font-size: 1.4em; }
h4 { font-size: 1.2em; }

/* 列表 */
ul, ol {
  margin: 1em 0;
  padding-left: 2em;
}

li {
  margin: 0.4em 0;
}

/* 引用块 */
blockquote {
  margin: 1.5em 0;
  padding: 0.8em 1.5em;
  background-color: #f9f9f9;
  border-left: 4px solid #ddd;
  color: #666;
  font-style: italic;
  border-radius: 0 4px 4px 0;
}

/* 代码行内 */
code {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  background-color: #f3f4f6;
  color: #e9602d;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-size: 0.95em;
  white-space: nowrap;
}

/* 代码块 */
pre {
  margin: 1.5em 0;
  padding: 1.2em;
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  overflow-x: auto;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
}

pre code {
  background: none;
  color: inherit;
  padding: 0;
  font-size: inherit;
  white-space: pre;
  display: block;
}

/* 表格 */
table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5em 0;
  font-size: 14px;
  overflow: hidden;
  box-shadow: 0 0 0 1px #e0e0e0;
  border-radius: 6px;
}

th, td {
  padding: 10px 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

th {
  background-color: #f8f9fa;
  color: #2c3e50;
  font-weight: 600;
  white-space: nowrap;
}

tr:nth-child(even) {
  background-color: #f9f9fb;
}

tr:hover {
  background-color: #f0f5ff;
}

/* 链接 */
a {
  color: #1a73e8;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

/* 分隔线 */
hr {
  border: 0;
  height: 1px;
  background: #ddd;
  margin: 2em 0;
}

/* 图片 */
img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 1em 0;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
  </style>
</head>
<body>
  <h2>使用须知</h2> <ul><li><p>候鸟浏览器支持本地API的功能，帮助用户通过程序化的方式来启动和关闭浏览器等基础API功能，还可以配合Selenium和Puppeteer等自动化框架来实现浏览器操作的自动化。</p></li> <li><p>后续客户端 API将<code>采用HTTP模式连接</code>持续更新。</p></li></ul> <div class="ant-divider ant-divider-horizontal"></div> <p><strong>使用前请根据以下指引完成操作并获取信息，然后参照<a class=""><strong>【http模式说明】</strong></a>启动客户端并开始使用API</strong></p> <h3>1、获取API凭证</h3> <ul><li><p>API启动候鸟浏览器需要占用1个API凭证，即1个API凭证同一时间只能允许1台设备使用。<strong>使用前请确保账号至少有1个API凭证。</strong></p></li> <li><p>非团队版用户只能申请1个凭证，团队版仅限主账户申请，可申请的凭证数量等于团队成员总数</p></li></ul> <p><img></p> <h3>2、查看API凭证</h3> <ul><li><p>打开候鸟控制台，点击API-查看凭证获取 <strong><code>APP_ID</code></strong> 和 <strong><code>APP_KEY</code></strong></p></li></ul> <p><img></p> <p><img></p> <h3>2、获取环境ID</h3> <ul><li><p><span>API通过 <strong><code>Session_ID</code></strong>（环境ID）打开环境，环境ID如图，也可以通过“获取环境列表接口”</span>（<a class=""><code>Path：/api/v1/session/listid</code></a>）获取：</p></li></ul> <p><img></p> <p><a class="ant-btn ant-btn-primary">进入我的控制台</a></p>
</body>
</html>