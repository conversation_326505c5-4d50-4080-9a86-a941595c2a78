用户开启模板后，服务器端根据用户安装包版本号，在LOGIN的JSON返回中增加标志位： is_template =1   (1表示需要请求模板列表，0表示不需要)

用户开启模板后，服务器端根据用户安装包版本号，在LOGIN的JSON返回中增加标志位： is_template =1   (1表示需要请求模板列表，0表示不需要)

客户端启动的时侯根据login成功后返回的json中, is_template =1 或 0 判定是否需要进行一次模板列表请求。

第四节：心跳下发流程：

用户在控制台启用模板时，服务器通过心跳下发指令，客户端请求模板数据集合。

=====================================================================

*     模板列表心跳下发请求：connected|req_templatelist

客户端收到心跳指令，进行远程请求：

templatelist请求串：

（2）服务器端收到请求串，进行数据库查询，下发数据，返回Json格式：

成功：

{"msg":"操作成功","code":0,"data":{”TempID”:”11”,"TempName":"模板名称",”data”:”xxxxxxxxxxxxx”, ”createtime”:”创建时间”,”updatetime”:”修改时间”},

{"msg":"操作成功","code":0,"data":{”TempID”:”11”,"TempName":"模板名称",”data”:”xxxxxxxxxxxxx”, ”createtime”:”创建时间”,”updatetime”:”修改时间”},

{"msg":"操作成功","code":0,"data":{”TempID”:”11”,"TempName":"模板名称",”data”:”xxxxxxxxxxxxx”, ”createtime”:”创建时间”,”updatetime”:”修改时间”}}

失败：

{"msg":"没有可用数据包","code":-1,"data":""}

注：data数据hash码用来提供给客户端进行判定是否需要更新用户的本地template模板。只有当hash码不同时才需要更新。

（3）客户端通过获得的josn数据，保存到本地。

C:\MBData\LocalStorage\7zxjIqxxuQ28jnWdwVPQpA==\

文件名：Template.xml

本地模板 template.XML格式：

流程1：通过心跳流程，每次线程请求服务器获取到模板json数据后，全局覆盖本地此文件，并根据已有全局xml同步规则，此数据同步到服务器（遵循基础框架文档中第三章节：同步）。

流程2：对于用户已有环境ABC已使用了A模板，但用户在控制台将A模板停用后，客户端里此环境ABC的情况说明：如用户在控制台里停用了此环境ABC中原已加载的A模板，则此环境不再使用A模板，在环境配置面板中显示为原生模板列表即可。

详见第五节图示。

流程3：用户在控制台重新启用A模板，则仍需重新将ABC环境中的配置模板重新进行A模板指定操作。

流程4：模板所有者作为保留项，为后续迭代升级作为铺垫。在后续版本中，允许用户自建模板，首次版本不提供用户自建模板功能，只允许官方技术与客服进行官方模板提供（作为用户疑难页面处理解决方案的售后方式存在一段时间）。

第五节：客户端环境配置窗口模板分配说明

批量创建面板：

重要：如果用户未在控制台开启模板（未使用任何一个模板或停用了所有模板），则 上图中 请选择会话环境模板 项为 隐藏 状态。

第六节：ITEM环境数据结构与模板关系详述 / ITEM数据结构扩展说明

ITEM环境数据结构 新增约定

流程说明：

配置面板加载流程：

判断是否本地存在template.xml：

如果存在此文件，且文件不为空，获取XML文件中 <Template ID = "模板ID值"/> 并在template.xml中通过ID查找到对应的节点数据，并加载，同时将模板名称显示在配置面板中的下拉列表中。

如果不存在此文件，或文件存在但内容为空，不获取<Template ID = "模板ID值"/>

如果ID值在template.xml中未找到，则默认显示

存储流程：

用户给 IETM环境指定新模板：

用户在配置面板

选择其需要的模板项，同时点配置面板右下角确认项后，将ITEM.XML中的节点值

则将：

<Template ID = "模板ID值"/>

<Template NAME = "模板名称"/>

<Template FMASK = "模板FMASK值"/>

依据template.xml的最新数据进行赋值。

-----------------------------------------------------------------------------------------------

如果用户将上图中的项更换为 起始项, 同时点配置面板右下角确认项后，则将ITEM.XML中的节点值

<Template ID = "模板ID值"/> ID值置空。

<Template NAME = "模板名称"/> 值置空

<Template FMASK = "模板FMASK值"/>值置空

用户给item环境 修改/变更模板：

当template.xml 数据存在时：

则下拉列表里显示template所有列表内容 + 当前item包里的这三项值。

当template.xml数据不存在时：

则下拉列表里只有item包里的这三项值，在下拉列表里只显示一行的值。

如果item包里这三项值为空，则下拉列表控件不显示。

第七节：服务器端逻辑与客服补充说明：

客户使用的版本不达标准的，要推荐用户升级到最新版，用新不用旧的原则。

模板的服务端数据表中的记录，对应的记录ID不允许后期变更。（ID是带有主键的，数据库确保了所有记录的ID不重复，因此这个ID在表中要确保可以删，可以加记录，可以清空，不能truncate整个表，truncate了id就会重新生成）

模板启用和未启用的状态 都是会在列表中显示出来， 删除 约定为隐藏掉这个模板。

前期模板后台只提供给客服使用，后期增加到控制台提供用户自行添加使用。

客户端高度依赖服务器端传过来的ID，ITEM包会保存三项模板关键值。

客户端只能通过这个ID去判定各个模板的不同。

ITEM发给其它人进行导入后，保证导入完成即可使用的要求。（ITEM包内置了源始用户指定的模板信息，ITEM包中的xml里包含了模板数据，因此创始人将这个ITEM发给其它人，就能保证其它人导入(分享)完成即可用。）

模板如果用户在控制台改了，影响不到用户在之前就已经将这个ITEM导出后保存在硬盘中的那个包。属于用户自身去自行控制这些数据的范围了。

全局模板的特点就是：一旦添加了模板公开了模板，就不能去修改。

已创建的模板，如果用户已经在用的，一定不要再进行修改里面的参数。（例如： 模板A 已经有用户在用了，模板B是刚刚创建的没有人用，则模板A不能再去变动里面的开关，模板B就可以随意变动。）

第八节：WEB控制台 用户自定义模板

1、官方业务模板不变，保持原逻辑与界面均不变。

2、新增我的业务模板，如图：

[2022-02-04 新增]


================================================== 表格内容 ==================================================

参数名称 | 类型 | 必传 | 样例串/默认值 | 说明
Session_ID | string | 是 | 环境ID | 环境ID
IS_WEBRTC | bit | 否 | 1 or 0 | 1: WEBRTC FINGERPRINT设定有效
SystemOS | string | 否 | Windows | 环境所持操作系统
Session_Resolution | string | 否 | 2560x1440 | 环境分辨率
Session_UserAgent | string | 否 | Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.111 Safari/537.36 | 环境USER-AGENT
StaticIP_Type | bit | 否 | 1 or 0 | 1:静态IP
0：动态IP
Public_IP | string | 否 | xxx.xxx.xxx.xxx | 公网IP
Private_IP | string | 否 | xxx.xxx.xxx.xxx | 内网IP(可填随机IP)
TimeZone | string | 否 | US/Alaska -09:000 | 时区
CountryCode | string | 否 | US | 国家CODE
CityCode | string | 否 | 城市CODE
RegionCode | string | 否 | 州CODE
LanguageCode | string | 否 | En-US;en;q=0.9 | 环境默认语言
FingerPrint_Setting | string | 否 | 1,1,1,1 | 环境指纹设定：Lwav=1,FontCode=1,Dns=1,Canvas=1
1表示有效，0表示无效
Lock_Browser_windows_size | bit | 否 | 1 | 默认值：1，保持浏览器窗口大小锁定
Disable_video | bit | 否 | 0 | 视频限流 0关闭 1开启 默认关闭
Disable_img | bit | 否 | 0 | 图片限流 0关闭 1开启 默认关闭

{
"message": "Update Session adv_setting Success",
"code": 0,
"data": {
            “Session_Id” : 373808cb37bd63f5f7d92415e736e85f 	//环境ID
       }
}