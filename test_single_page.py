#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试单个页面抓取
"""

import requests
import re
import time
from urllib.parse import urljoin

def simple_html_to_text(html_content):
    """改进的HTML转文本处理"""
    try:
        # 移除script和style标签
        html_content = re.sub(r'<script[^>]*>.*?</script>', '', html_content, flags=re.DOTALL | re.IGNORECASE)
        html_content = re.sub(r'<style[^>]*>.*?</style>', '', html_content, flags=re.DOTALL | re.IGNORECASE)
        
        # 移除HTML注释
        html_content = re.sub(r'<!--.*?-->', '', html_content, flags=re.DOTALL)
        
        # 处理标题标签 - 保持层级
        html_content = re.sub(r'<h1[^>]*>(.*?)</h1>', r'\n# \1\n', html_content, flags=re.DOTALL | re.IGNORECASE)
        html_content = re.sub(r'<h2[^>]*>(.*?)</h2>', r'\n## \1\n', html_content, flags=re.DOTALL | re.IGNORECASE)
        html_content = re.sub(r'<h3[^>]*>(.*?)</h3>', r'\n### \1\n', html_content, flags=re.DOTALL | re.IGNORECASE)
        html_content = re.sub(r'<h4[^>]*>(.*?)</h4>', r'\n#### \1\n', html_content, flags=re.DOTALL | re.IGNORECASE)
        html_content = re.sub(r'<h5[^>]*>(.*?)</h5>', r'\n##### \1\n', html_content, flags=re.DOTALL | re.IGNORECASE)
        html_content = re.sub(r'<h6[^>]*>(.*?)</h6>', r'\n###### \1\n', html_content, flags=re.DOTALL | re.IGNORECASE)
        
        # 处理段落标签
        html_content = re.sub(r'<p[^>]*>(.*?)</p>', r'\1\n\n', html_content, flags=re.DOTALL | re.IGNORECASE)
        
        # 处理换行标签
        html_content = re.sub(r'<br[^>]*/?>', '\n', html_content, flags=re.IGNORECASE)
        
        # 处理列表项
        html_content = re.sub(r'<li[^>]*>(.*?)</li>', r'• \1\n', html_content, flags=re.DOTALL | re.IGNORECASE)
        
        # 处理代码块
        html_content = re.sub(r'<pre[^>]*>(.*?)</pre>', r'\n```\n\1\n```\n', html_content, flags=re.DOTALL | re.IGNORECASE)
        html_content = re.sub(r'<code[^>]*>(.*?)</code>', r'`\1`', html_content, flags=re.DOTALL | re.IGNORECASE)
        
        # 处理引用块
        html_content = re.sub(r'<blockquote[^>]*>(.*?)</blockquote>', r'\n> \1\n', html_content, flags=re.DOTALL | re.IGNORECASE)
        
        # 处理表格
        html_content = re.sub(r'<table[^>]*>(.*?)</table>', r'\n[表格内容]\n\1\n', html_content, flags=re.DOTALL | re.IGNORECASE)
        html_content = re.sub(r'<tr[^>]*>(.*?)</tr>', r'\1\n', html_content, flags=re.DOTALL | re.IGNORECASE)
        html_content = re.sub(r'<t[hd][^>]*>(.*?)</t[hd]>', r'\1 | ', html_content, flags=re.DOTALL | re.IGNORECASE)
        
        # 处理div标签 - 保留内容但添加换行
        html_content = re.sub(r'<div[^>]*>(.*?)</div>', r'\1\n', html_content, flags=re.DOTALL | re.IGNORECASE)
        
        # 移除所有剩余的HTML标签
        html_content = re.sub(r'<[^>]+>', '', html_content)
        
        # 解码HTML实体
        html_entities = {
            '&nbsp;': ' ',
            '&lt;': '<',
            '&gt;': '>',
            '&amp;': '&',
            '&quot;': '"',
            '&#39;': "'",
            '&hellip;': '...',
            '&mdash;': '—',
            '&ndash;': '–',
            '&copy;': '©',
            '&reg;': '®',
            '&trade;': '™'
        }
        
        for entity, char in html_entities.items():
            html_content = html_content.replace(entity, char)
        
        # 清理多余的空白和空行
        lines = html_content.split('\n')
        cleaned_lines = []
        prev_empty = False
        
        for line in lines:
            line = line.strip()
            if not line:
                if not prev_empty:
                    cleaned_lines.append('')
                prev_empty = True
            else:
                cleaned_lines.append(line)
                prev_empty = False
        
        # 移除开头和结尾的空行
        while cleaned_lines and not cleaned_lines[0]:
            cleaned_lines.pop(0)
        while cleaned_lines and not cleaned_lines[-1]:
            cleaned_lines.pop()
        
        return '\n'.join(cleaned_lines)
        
    except Exception as e:
        print(f"HTML转文本失败: {str(e)}")
        return "内容解析失败"

def test_page(url):
    """测试单个页面"""
    try:
        print(f"正在测试页面: {url}")
        
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        response = session.get(url, timeout=30)
        response.raise_for_status()
        response.encoding = 'utf-8'
        
        # 转换为文本
        text_content = simple_html_to_text(response.text)
        
        # 保存到文件
        filename = f"test_page_content.txt"
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"测试页面: {url}\n")
            f.write(f"抓取时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("=" * 60 + "\n\n")
            f.write(text_content)
        
        print(f"内容已保存到: {filename}")
        print(f"内容长度: {len(text_content)} 字符")
        
        # 显示前500字符预览
        print("\n内容预览:")
        print("-" * 40)
        print(text_content[:500])
        if len(text_content) > 500:
            print("...")
        print("-" * 40)
        
        return True
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    # 测试登录页面
    test_url = "https://www.mbbrowser.com/api/login"
    test_page(test_url)
