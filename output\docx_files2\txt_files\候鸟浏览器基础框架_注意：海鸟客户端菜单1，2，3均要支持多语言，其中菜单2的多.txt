注意：海鸟客户端菜单1，2，3均要支持多语言，其中菜单2的多语言由服务器提供(中文、英文)即可。1，3菜单项由客户端自有多语言架构控制。

注意：海鸟客户端菜单1，2，3均要支持多语言，其中菜单2的多语言由服务器提供(中文、英文)即可。1，3菜单项由客户端自有多语言架构控制。

2、点击 限量提取免费代理IP管理器 ：弹出代理IP管理器TAB2窗口，允许用户通过管理器面板选择需要的免费代理IP。

3、点击菜单中下方选项，快速选取指定地代理IP，整个菜单项分为菜单1，菜单2，菜单3。

2.3.2 海鸟客户端菜单项说明。

1、海鸟客户端 固定菜单项1：

2、菜单项2在客户端启动时通过线程请求服务器接口时一次性获取所有数据， 以保证显示菜单2时不出现卡顿。

A、客户端启动过程中，通过线程请求服务器，服务器将六大州区域所有代理IP项通过json格式返回。

B、 客户端获取json格式并置入内存，并显示在菜单2中。

C、国旗图标分为color/black两个文件夹，图标文件名使用名称从json获取，并使用本地文件夹中的国旗图标文件， 例: skin/color文件夹中图标，不可用的用black文件夹中图标。

D、每次点击菜单1中的某一菜单项，默认直接显示菜单2所有项，同时用线程在后端请求一次海鸟服务器获取这一项的更新数据，并更新内存（此时允许显示旧的菜单2所有数据），下次再显示则会自动显示更新的菜单项（内存已更新）。

3、菜单3各项说明及流程

添加到我的IP库：点击此按钮，将此IP及所有此IP的数据(包括启用IP的串)置入到用户的代理IP库(本地PROXY.XML)中，注意: 所有此IP数据均应来源于服务器 。

（重点）IP数据包含：地域，IP数据，asp请求串数据，asp请求后返回串数据，端口，写入时间。

注：重点：写入前需判断当前PROXY.XML是否已存在asp请求串(非返回串)数据端口是否已存在于PROXY中，如果已存在，说明有两种可能：1、服务器端逻辑有问题。2、用户在客户端上之前已操作过此条代理。

添加并一键指派到浏览器环境：

流程：进行完成 添加到我的IP库 所有流程 -> 弹出一键指派代理IP窗口，同时A区的焦点在此代理IP上。

即允许用户直接将此代理IP批量指派到多个环境中。

2.3.3 海鸟服务器端菜单项数据生成及下发说明。

1、 海鸟服务端代理IP数据预生成(库更新)流程详述：

代理IP数据预生成，用于使用不同的国家简写(如JP,US)，通过批量请求

判定哪些国家的代理IP有效并进行表更新。（后期如SSL服务器能返回支持国家区域列表功能则可进行二次调整）。

海鸟服务端提供接口，根据 州域 的不同返回州域下所有代理IP集合。

流程说明：
【输入】 
例，海鸟服务器提供接口：

参数：token,ist
    token = 客户端当前token

ist = NA
    North America: NA

South America: SA

Europe: EU

Africa: AF

Asia: AS

Oceania: OC

【输出】
   返回州际所有国家数据。

(重点)数据结构约定：

国家图片图标文件名: [flag_name] 根据black/color目录下所有图片文件名进行返回。

代理IP完整名称(中英文)：[ssl_proxy_name] 例：VIP-日本-1 – ***************
             代理状态: [proxy_status]  0 或 1  0表示空闲，1表示繁忙

调用串: [client_active_url]

注意：服务器表中所有代理记录的各调用串的端口不允许相同，必须为不同值（服务器在入库时可以根据当前PORT值进行累加，默认端口为5位数，起始值建议：10010），以此从源头来保证客户端进行调用串执行时不会出现启用失败情况。

4、海鸟服务端 客户端代理请求记录表与功能说明

流程说明：客户端每次请求需进行记录到日志表（用途为供运营方进行运营数据分析）

表内字段：ID，IST, ACCOUNT, PROXY_STRING, CLIENT_IP，SYSTEM，REQUEST_TIME，ISVALID

ID: 主键

ACCOUNT:帐户名称(邮箱)

IST：所在州名称
PROXY_STRING：请求串完整串
CLIENT_IP：客户端本地IP
SYSTEM：客户端操作系统
REQUEST_TIME：请求时间
ISVALID: 是否有效，默认为1有效

三、海鸟客户端自有配置、检测，使用SSL代理 详细流程说明

3.1 海鸟客户端环境配置界面说明，各界面元素说明及实现功能流程与约定。

3.1.1 如图，获取免费代理IP 按钮点击后，与OWERUI相同，仍旧显示菜单（共用）

区别如下：configpad窗口中的菜单新增显示一个菜单3的子菜单项 ->  “添加到此环境中”

放在菜单3的最上方，此菜单项点击后 进行 添加到我的IP库  -> 添加此代理IP到此环境  流程。

之后，无论成功或失败，添加到环境的事件添加到海鸟日志管理器中。
            注意：添加到此环境后，不要触发自动检测代理流程，检测代理仍旧交由用户手工进行。

3.1.2 点击 检测代理 按钮流程
以下重点描述仅针对SSL隧道代理IP的类型进行检测的流程说明：
  1、用户在完成SSL代理IP的选择并成功将代理IP自动填入环境面板中后

2、用户点击configpad.xml窗口中，检测代理按钮

3、客户端根据代理IP的类型（SSL标志位），线程方式，对此代理进行检测。

4、检测流程：

A、使用独立线程执行XML配置文件中的SSL URL串请求（必须项）。

B、使用独立线程执行SSL.EXE 串，打开本地隧道代理通道。

C、curllib库以加载本地127.0.0.1:xxxxxx SOCKS5代理方式运行

D、通过客户端内置的curlib请求服务器端在客户端登录时下发的JSON中的检测目标地址，判断是否可正常访问。

5、检测成功失败的显示样式与原流程相同，无变化。

6、原代理tractor检测窗口流程无变化。

7、检测代理的事件，无论成功或失败，添加到环境的事件到海鸟日志管理器中。

四、其它已知问题、情况的说明与安排计划：

在上述即定工作已完成的情况下，完善第四项的已知问题与流程。

SSL.EXE 默认弹出console的问题，将计划在所有流程处理完成后，通过修改SSL.EXE源码来支持后台运行。

关于已开启，已运行的SSL.EXE的隧道在客户操作系统休眠、唤醒后失效的问题，在最后集中解决。

客服按钮入口及客服平台的衔接

4.4  用户在客户端使用SSL代理IP的其它入口的添加与引流。

4.5  代理管理器中，批量检测代理IP针对SSL.EXE的流程完善与调试。

4.6  SSL.EXE运行冲突的检测与值守的二次完善。

4.7  admin.hainiao123.com在无法访问时的SSL.EXE隧道维持与串请求容错处理。

4.8  用户使用免费SSL代理IP和付费SSL代理IP的区分，付费模式设计。

候鸟/海鸟 研发部2025-06-15 编制


================================================== 表格内容 ==================================================

参数名称 | 类型 | 必传 | 样例串/默认值 | 说明
Session_ID | String | 是 | 3e8448bcfb9f35cbeaef5ac376771932, 3e8448bcfb9f35cbeaef5ac376771933 | 指定环境
(支持一次最多100个环境)
Actived_TYPE | int | 否 | 0,1,2 | 0:返回所有环境运行状态
1:返回已运行环境集合
2:返回未运行环境集合

{
" Session _ID":["7579a099e6fcee76fd1272ccdc30c1cc","c1f3f1b3d5072985581fe54343f1e524"]
}

{
"message "Session status return Success",
"code": 0,
"data": {
"listcontainer": [
        {
        "Session_Name": “商用业务环境一”
        "Session_ID": "914a9b97c6787a231ed2ab25e02ad5c9",
"session_actived": 0
} 
}
}

参数名称 | 类型 | 必传 | 说明
appId | string | 是 | 用户凭证appId
appSecret | string | 是 | 用户凭证appSecret