#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RAGFlow MCP Server 快速测试脚本
"""

import json
import uuid
import requests


def test_ragflow_mcp():
    """快速测试RAGFlow MCP Server"""
    
    # 配置
    base_url = "http://192.168.1.21:9382"
    api_key = "ragflow-mcp-2025"
    session_id = str(uuid.uuid4())
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }
    
    print("🚀 RAGFlow MCP Server 快速测试")
    print("=" * 40)
    print(f"服务器: {base_url}")
    print(f"会话ID: {session_id}")
    
    # 1. 测试连通性
    print(f"\n1️⃣ 测试服务器连通性...")
    try:
        response = requests.get(f"{base_url}/sse", timeout=5)
        print(f"✅ SSE端点响应: {response.status_code}")
    except Exception as e:
        print(f"❌ 连通性测试失败: {e}")
        return
    
    # 2. 初始化MCP连接
    print(f"\n2️⃣ 初始化MCP连接...")
    init_request = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "initialize",
        "params": {
            "protocolVersion": "2024-11-05",
            "capabilities": {"tools": {}},
            "clientInfo": {"name": "QuickTest", "version": "1.0"}
        }
    }
    
    try:
        url = f"{base_url}/messages/?session_id={session_id}"
        response = requests.post(url, headers=headers, json=init_request, timeout=10)
        print(f"✅ 初始化响应: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"   服务器信息: {result.get('result', {}).get('serverInfo', {}).get('name', 'Unknown')}")
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return
    
    # 3. 获取工具列表
    print(f"\n3️⃣ 获取可用工具...")
    tools_request = {
        "jsonrpc": "2.0",
        "id": 2,
        "method": "tools/list"
    }
    
    try:
        response = requests.post(url, headers=headers, json=tools_request, timeout=10)
        print(f"✅ 工具列表响应: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            tools = result.get('result', {}).get('tools', [])
            print(f"   可用工具数量: {len(tools)}")
            for tool in tools:
                print(f"   - {tool['name']}: {tool['description'][:50]}...")
    except Exception as e:
        print(f"❌ 获取工具失败: {e}")
        return
    
    # 4. 测试知识检索
    print(f"\n4️⃣ 测试知识检索...")
    
    # 测试问题
    test_question = "候鸟浏览器如何配置代理？"
    
    retrieval_request = {
        "jsonrpc": "2.0",
        "id": 3,
        "method": "tools/call",
        "params": {
            "name": "ragflow_retrieval",
            "arguments": {
                "dataset_ids": ["default"],  # 使用默认数据集
                "question": test_question
            }
        }
    }
    
    print(f"   问题: {test_question}")
    
    try:
        response = requests.post(url, headers=headers, json=retrieval_request, timeout=30)
        print(f"✅ 检索响应: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            if 'error' in result:
                print(f"❌ 检索错误: {result['error']}")
            else:
                content = result.get('result', {}).get('content', [])
                if content:
                    answer = content[0].get('text', '')
                    print(f"🤖 RAGFlow回答:")
                    if len(answer) > 200:
                        print(f"   {answer[:200]}...")
                        print(f"   [完整答案长度: {len(answer)} 字符]")
                    else:
                        print(f"   {answer}")
                else:
                    print(f"   ℹ️ 未找到相关内容")
        else:
            print(f"❌ 检索失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 检索异常: {e}")
    
    # 5. 测试其他问题
    print(f"\n5️⃣ 测试更多问题...")
    
    additional_questions = [
        "RAGFlow是什么？",
        "如何创建知识库？"
    ]
    
    for i, question in enumerate(additional_questions, 4):
        print(f"\n   问题 {i}: {question}")
        
        request = {
            "jsonrpc": "2.0",
            "id": i,
            "method": "tools/call",
            "params": {
                "name": "ragflow_retrieval",
                "arguments": {
                    "dataset_ids": ["default"],
                    "question": question
                }
            }
        }
        
        try:
            response = requests.post(url, headers=headers, json=request, timeout=20)
            if response.status_code == 200:
                result = response.json()
                if 'error' not in result:
                    content = result.get('result', {}).get('content', [])
                    if content:
                        answer = content[0].get('text', '')
                        print(f"   ✅ 获得答案 ({len(answer)} 字符)")
                    else:
                        print(f"   ℹ️ 未找到相关内容")
                else:
                    print(f"   ❌ 错误: {result['error']}")
            else:
                print(f"   ❌ HTTP错误: {response.status_code}")
        except Exception as e:
            print(f"   ❌ 异常: {e}")
    
    print(f"\n" + "=" * 40)
    print(f"🎉 快速测试完成！")


if __name__ == "__main__":
    test_ragflow_mcp()
