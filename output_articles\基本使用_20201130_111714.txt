标题: 基本使用
英文标题: Basic Usage
ID: 31
分类ID: 7
添加时间: 1606706234
更新时间: 1696734369
访问次数: 0
SEO标题: 候鸟浏览器基本使用流程
SEO关键词: 候鸟浏览器基本使用
SEO描述: 候鸟浏览器基本使用流程

================================================== 内容 ==================================================
1、安装完成后，双击桌面上的“候鸟浏览器”图标打开；

![](********************************/images/image_d986c20d6b6f.png)


2、在弹出登录框中输入您注册好的账户名称、密码信息，点击登录；

![](********************************/images/image_3d67ed6f9987.png)


3、登录后弹出候鸟浏览器的主界面，点击下面“新建环境配置”；

![](********************************/images/image_f718722a6998.png)


4、弹出新建环境窗口进行配置，步骤分别是填入配置名称、选择系统、选择分辨率、随机一个UA、选择一个代理方式、填入代理IP信息、检测代理（检查代理的同时时区、国家、语言等信息会根据您的IP所在地区自动匹配），在Webrtc指纹处勾选“自动识别代理IP”设置完成后点击“创建环境”；

![](********************************/images/image_e1d61730a65c.png)


5、创建完成后，主界面环境列表中会出现刚刚创建的环境，点击“运行”按钮运行环境；

![](********************************/images/image_fa6e6842757e.png)


6、环境运行后会弹出chrome浏览器，可以按“停止”按钮关闭环境；

![](********************************/images/image_534e566ef643.png)


7、在客户端右下角设置中，可以设置软件的快捷键，ctrl+H是隐藏和显示候鸟客户端，提高我们的工作效率。

![](********************************/images/image_aad8f2a739ea.png)

================================================== 英文内容 ==================================================
1、After the installation is complete, double-click the "Mbbrowser" icon on the desktop to open it;

![](********************************/images/image_b5e9d4123ce4.png)


2、Enter your registered account name and password in the pop-up login box and click Login;

![](********************************/images/image_c80b6f9f9f4c.png)


3、After login, the main interface of Mbbrowser will pop up. Click "New Environment Configuration" below;

<p><img src="********************************/images/image_28af219abbd6.png" width="460" /></p>


4、The new environment window pops up for configuration. The steps are to fill in the configuration name, select the system, select the resolution, randomly select a UA, select a proxy mode, fill in the proxy IP information, and detect the proxy (the simultaneous time zone, country, language and other information of the check proxy will automatically match according to the region where your IP is located). Select "Automatically identify proxy IP" at the Webrtc fingerprint and click "Create Environment" after the setting is complete;

![](********************************/images/image_2d3bd618e0b6.png)


5、After the creation, the newly created environment will appear in the environment list on the main interface. Click the "Run" button to run the environment;

<p><img src="********************************/images/image_9ae7ba793f9e.png" width="460" /></p>


6、Chrome browser will pop up after the environment is running. You can press the "Stop" button to close the environment;

![](********************************/images/image_16d1554eae7a.png)


7、In the Settings at the lower right corner of the client, you can set the shortcut key of the software, ctrl+H is to hide and display the Mbbrowser, improving our work efficiency.

![](********************************/images/image_b7300f19a749.png)