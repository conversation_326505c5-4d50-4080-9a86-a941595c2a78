﻿<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Window size="1260,590" sizebox="4,4,4,4" mininfo="1260,590" caption="0,0,0,50" roundcorner="5,5,5,5" showshadow="true" shadowsize="3" shadowposition="0,0" shadowcolor="#ff6272A4" fademode="true">
	<Include source="Default.xml" />

  <VerticalLayout bkcolor="#FF282A36">
    <HorizontalLayout height="37">

    	 <Button name="login" bkimage="icon.png" padding="8,6,0,0" width="28" height="28"/>
      <Label name="session_list_title" padding="6,4,0,0" text="网络代理服务器管理" width="180" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" align="left" font="8"></Label>

      <Control />
      <Button name="minbtn" width="28" height="26" tooltip="最小化" normalimage="minbtn.png" hotimage="minbtn_hover.png" pushedimage="minbtnpush.png" />
      <Button name="maxbtn" width="28" height="26" tooltip="最大化" normalimage="maxbtn.png" hotimage="maxbtn_hover.png" pushedimage="maxbtnpush.png" />
      <Button name="restorebtn" visible="false" width="28" height="26" tooltip="还原" normalimage="restorebtn.png" hotimage="restorebtn_hover.png" pushedimage="restorebtnpush.png" />
      <Button name="closebtn" width="28" height="26"  tooltip="关闭" normalimage="closebtn.png" hotimage="closebtn_hover.png" pushedimage="closebtnpush.png" />

    </HorizontalLayout>
  <HorizontalLayout name="bkground">

    <HorizontalLayout name="loading_data" bkcolor="#FF44475A" visible="true">

      <VerticalLayout>

        <HorizontalLayout height="240">
          <Control />
          <GifAnim name="data_loading" bkimage="dataloading.gif" height="200" width="200" padding="0,40,0,0" auto="true"/>
          <Control name="success" visible="false" padding="0,100,0,0"  bkimage="success.png" width="120" height="120" align="center" />
          <Control />
        </HorizontalLayout>


        <HorizontalLayout height="30" >
          <Control />
          <Label name="data_percent" text="0%" width="300" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" align="center" font="10"></Label>
          <Control />
        </HorizontalLayout>

        <HorizontalLayout height="60" >
          <Control />
          <Label name="process_description" text="代理服务器数据包 正在更新中.. 请稍侯.." textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" align="center" font="8"></Label>
          <Control />
        </HorizontalLayout>

        <HorizontalLayout height="40" >
        </HorizontalLayout>

        <HorizontalLayout name="backarea" height="60" visible="false">
          <Control />
          <Button text="返回" name="back" width="120" height="30" textcolor="#FFF8F8F2" disabledtextcolor="#FF6272A4" align="center" font="14"	bkcolor="#FF282A36" bordersize="1" bordercolor="#FFBD93F9" borderround="7,7" hotbkcolor="#FF44475A"/>
          <Control width="100" />
          <Button text="退出" name="closebtn" width="120" height="30" textcolor="#FFF8F8F2" disabledtextcolor="#FF6272A4" align="center" font="14"	bkcolor="#FF282A36" bordersize="1" bordercolor="#FFBD93F9" borderround="7,7" hotbkcolor="#FF44475A"/>
          <Control />
        </HorizontalLayout>

      </VerticalLayout>

    </HorizontalLayout>

		<VerticalLayout name="data" visible="false">


			<HorizontalLayout height="56" >
			    	 <VerticalLayout width="440">
               <Combo name="searchlist" reselect="true" dropboxsize="0,450" bordersize="0" padding="21,10,0,10" width="420" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="4,2,0,0" bkcolor="#ffdce1e7"
														normalimage="file='Profile\Setting_Combox_Normal_big_large.png' corner='5,5,30,5'" hotimage="file='Profile\Setting_Combox_Hover_large.png' corner='5,5,30,5'" pushedimage="file='Profile\Setting_Combox_Click_large.png' corner='5,5,25,10'"
												combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,30,5'"
												itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0">
               </Combo>
				         <RichEdit name="session_search" pos="20,10,0,10" height="36" width="378" tipvaluecolor="#FFF8F8F2" borderround="7,7" bkcolor="#FF44475A" font="8" textpadding="10,8,20,0" maxchar="400" tipvalue="查找代理服务器.." multiline="false" textcolor="#FFF8F8F2" rich="false" transparent="false" float="true">
				      </RichEdit>
				     </VerticalLayout>

			</HorizontalLayout>


      <HorizontalLayout inset="0,0,0,0" bkcolor="#FF282A36">
        	<List name="list_session_manager" vscrollbar="true" bordersize="1,1,1,1" itembkcolor="#FF282A36"  itemselectedbkcolor="#FF44475A" itemhotbkcolor="#FF44475A" bordercolor="#FF6272A4">
						<ListHeader height="36" bordersize="1" bordercolor="#FF6272A4" bkcolor="#FF44475A" textcolor="#FFF8F8F2">
              <ListHeaderItem text="操作" name="header_device_check" width="60" align="left" textpadding="20,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="类型" name="header_type" width="120" align="left" textpadding="20,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="连接IP地址" visible="false" name="header_device_name" width="140" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="代理服务器IP" name="header_ip" width="140" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="代理端口" name="header_port" width="100" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="登录名" name="header_lname" width="120" align="left" textpadding="15,0,0,0" endellipsis="true" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="登录密码" name="header_backup_datetime" width="120" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <!--<ListHeaderItem text="DNS" name="header_backup_datetime" width="60" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>-->
              <ListHeaderItem text="环境" name="header_backup_datetime" width="180" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="报告" name="header_checkstatus" width="110" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="操作" name="header_ope" width="140" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
						</ListHeader>

					</List>
				</HorizontalLayout>






		</VerticalLayout>


	</HorizontalLayout>
    <HorizontalLayout height="52" bkcolor="#FF44475A">
      <CheckBox name="opt_checkAll" text="" textpadding="57,1,0,0" selected="false"  visible="true" padding="23,16,0,0" height="18" width="16" normalimage="file='list_check_normal.png'" hotimage="list_check_hover.png" selectedimage="file='list_checked_actvice_normal.png'" ></CheckBox>
      <Control width="36"/>

      <VerticalLayout width="300">
        <Control />
        <HorizontalLayout height="20" >
          <Button name="checkproxys" enabled="false" padding="16,1,0,0" align="left" height="20" width="126" text="批量检测代理" font="23" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" />
          <Label name="checkproxysl" text="超时设定：" padding="4,0,0,0" width="66" align="left" font="5" textcolor="#FFF8F8F2"></Label>
          <VerticalLayout width="160">
            <Edit name="timeout" padding="0,0,0,0" text="5" align="center" height="20" width="52" tipvaluecolor="#FFF8F8F2" nativebkcolor="#FF282A36" bkcolor="#FF282A36" font="8" textpadding="0,0,0,0" tipvalue="5" maxchar="3" multiline="false" textcolor="#FFF8F8F2" rich="false" transparent="false" bordersize="1" bordercolor="#FF6272A4" borderround="7,7"/>
          </VerticalLayout>
          <Label name="checkproxysl2" text="秒" padding="10,1,0,0" width="12" align="left" font="5" textcolor="#FFF8F8F2"></Label>
        </HorizontalLayout>
        <Control />
      </VerticalLayout>
      <Control />
      <VerticalLayout visible="false" name="inputarea" width="140">
         <Control />
         <Button text="添加此项到当前配置" name="input" padding="10,2,0,0" width="120" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	bkcolor="#FFffffff" bordersize="1" bordercolor="#ffb3b3b3" borderround="11,11" hotbkcolor="#fff1f2f1"/>
         <Control />
      </VerticalLayout>
      <VerticalLayout width="140">
        <Control />
        <Button text="删除" name="deleteselected" enabled="false" tooltip="批量删除勾选的代理服务器"  padding="20,2,0,0" width="120" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	bkcolor="#FFffffff" bordersize="1" bordercolor="#ffb3b3b3" borderround="11,11" hotbkcolor="#fff1f2f1"/>
        <Control />
      </VerticalLayout>
      <VerticalLayout width="140">
      		<Control />
          <Button text="从文件中导入" name="archiveselected" tooltip="从本地文本文件中批量导入代理服务器数据集"  padding="20,2,0,0" texttooltip="true" endellipsis="true" textpadding="5,0,5,0" width="120" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	bkcolor="#FFffffff" bordersize="1" bordercolor="#ffb3b3b3" borderround="11,11" hotbkcolor="#fff1f2f1" />
          <Control />
       </VerticalLayout>
      <VerticalLayout width="140">
        <Control />
        <Button text="批量指派到环境" name="proxyip" tooltip="批量指派到环境"  padding="20,2,0,0" texttooltip="true" endellipsis="true" textpadding="5,0,5,0" width="120" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	bkcolor="#FFffffff" bordersize="1" bordercolor="#ffb3b3b3" borderround="11,11" hotbkcolor="#fff1f2f1" />
        <Control />
      </VerticalLayout>
       <VerticalLayout width="140">
      		<Control />
          <Button text="添加新代理" name="newproxy"   tooltip="手工添加一个全新的代理服务器" padding="20,2,0,0" texttooltip="true" endellipsis="true" textpadding="5,0,5,0" width="120" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	bkcolor="#FFfffae0" hotbkcolor="#fffff6c8" bordersize="1" bordercolor="#ffb3b3b3" borderround="11,11"/>
          <Control />
       </VerticalLayout>
        <VerticalLayout width="130">
      		<Control />
          <Button text="保存" name="btnok" tooltip=""  padding="20,2,0,0" width="110" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	bkcolor="#FFffffff" bordersize="1" bordercolor="#ffb3b3b3" borderround="11,11" hotbkcolor="#fff1f2f1"/>
          <Control />
       </VerticalLayout>

      <Control />
    </HorizontalLayout>
    <Control name="dragicon" float="true" width="14" height="16" bkimage="dragicon.png"/>
  </VerticalLayout>
</Window>
