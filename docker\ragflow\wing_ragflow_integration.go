package main

import (
    "bytes"
    "encoding/json"
    "fmt"
    "io"
    "net/http"
    "time"
)

// RAGFlowClient Wing 客户端的 RAGFlow 集成
type RAGFlowClient struct {
    BaseURL string
    APIKey  string
    Client  *http.Client
}

// NewRAGFlowClient 创建新的 RAGFlow 客户端
func NewRAGFlowClient() *RAGFlowClient {
    return &RAGFlowClient{
        BaseURL: "http://************:9380",
        APIKey:  "ragflow-UzYTlhMjIwNjI0YzExZjA4NzUyMDI0Mm",
        Client: &http.Client{
            Timeout: 60 * time.Second,
        },
    }
}

// Dataset 数据集结构
type Dataset struct {
    ID          string `json:"id"`
    Name        string `json:"name"`
    ChunkCount  int    `json:"chunk_count"`
    Description string `json:"description"`
}

// RetrievalRequest 检索请求
type RetrievalRequest struct {
    Question   string   `json:"question"`
    DatasetIDs []string `json:"dataset_ids"`
    TopK       int      `json:"top_k"`
}

// RetrievalResponse 检索响应
type RetrievalResponse struct {
    Code    int         `json:"code"`
    Data    interface{} `json:"data"`
    Message string      `json:"message"`
}

// GetDatasets 获取数据集列表
func (c *RAGFlowClient) GetDatasets() ([]Dataset, error) {
    url := fmt.Sprintf("%s/api/v1/datasets", c.BaseURL)
    
    req, err := http.NewRequest("GET", url, nil)
    if err != nil {
        return nil, err
    }
    
    req.Header.Set("Authorization", "Bearer "+c.APIKey)
    req.Header.Set("Content-Type", "application/json")
    
    resp, err := c.Client.Do(req)
    if err != nil {
        return nil, err
    }
    defer resp.Body.Close()
    
    body, err := io.ReadAll(resp.Body)
    if err != nil {
        return nil, err
    }
    
    var result struct {
        Code int       `json:"code"`
        Data []Dataset `json:"data"`
    }
    
    if err := json.Unmarshal(body, &result); err != nil {
        return nil, err
    }
    
    if result.Code != 0 {
        return nil, fmt.Errorf("API error: code %d", result.Code)
    }
    
    return result.Data, nil
}

// QueryRAGFlow 查询 RAGFlow 知识库
func (c *RAGFlowClient) QueryRAGFlow(question string) (string, error) {
    // 1. 获取数据集
    datasets, err := c.GetDatasets()
    if err != nil {
        return "", fmt.Errorf("获取数据集失败: %v", err)
    }
    
    if len(datasets) == 0 {
        return "", fmt.Errorf("没有可用的数据集")
    }
    
    // 2. 提取数据集ID
    var datasetIDs []string
    for _, dataset := range datasets {
        datasetIDs = append(datasetIDs, dataset.ID)
    }
    
    // 3. 执行检索查询
    url := fmt.Sprintf("%s/api/v1/retrieval", c.BaseURL)
    
    reqData := RetrievalRequest{
        Question:   question,
        DatasetIDs: datasetIDs,
        TopK:       5,
    }
    
    jsonData, err := json.Marshal(reqData)
    if err != nil {
        return "", err
    }
    
    req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
    if err != nil {
        return "", err
    }
    
    req.Header.Set("Authorization", "Bearer "+c.APIKey)
    req.Header.Set("Content-Type", "application/json")
    
    resp, err := c.Client.Do(req)
    if err != nil {
        return "", err
    }
    defer resp.Body.Close()
    
    body, err := io.ReadAll(resp.Body)
    if err != nil {
        return "", err
    }
    
    var result RetrievalResponse
    if err := json.Unmarshal(body, &result); err != nil {
        return "", err
    }
    
    if result.Code != 0 {
        return "", fmt.Errorf("查询失败: %s", result.Message)
    }
    
    // 4. 解析结果
    return fmt.Sprintf("查询成功: %v", result.Data), nil
}

// TestConnection 测试连接
func (c *RAGFlowClient) TestConnection() error {
    datasets, err := c.GetDatasets()
    if err != nil {
        return err
    }
    
    fmt.Printf("✅ RAGFlow 连接成功，找到 %d 个数据集\n", len(datasets))
    return nil
}

func main() {
    client := NewRAGFlowClient()
    
    // 测试连接
    if err := client.TestConnection(); err != nil {
        fmt.Printf("❌ 连接失败: %v\n", err)
        return
    }
    
    // 测试查询
    question := "候鸟浏览器如何配置代理？"
    result, err := client.QueryRAGFlow(question)
    if err != nil {
        fmt.Printf("❌ 查询失败: %v\n", err)
        return
    }
    
    fmt.Printf("🤖 查询结果: %s\n", result)
}