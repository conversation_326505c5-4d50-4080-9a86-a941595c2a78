# 错误码对照表

## 描述
候鸟浏览器API错误码对照表，包含CODE错误码、Status错误码和CURL代理检测错误码的详细说明。

## 1. CODE 错误码表

### 全局错误码

| 错误码 | 描述说明 |
|--------|----------|
| 0 | 成功 |
| -1 | 登录失败 / App_ID 或 App_KEY 非法 |
| -2 | 登录失败 / 连接候鸟服务器超时[curl 状态码] |
| -3 | 当前登录帐户套餐已过期 |
| -4 | Login_Expire |
| -5 | Login_ServerError / 服务器访问错误 |
| -6 | Login_MbSvrError / 启动 mbservice 失败 |
| -7 | Login_Uping / 还有上传在进行，需要等其完成才能登录 |
| -8 | Login_Occupy / 有其它实例已占用登录 |
| -9 | 数据同步失败:超时 |
| -10 | 数据处理失败：数据损坏导致无法加载 |
| -11 | 数据处理失败：当前目录没有写权限，目录位置 |
| -12 | 登录失败 / App_ID 处于在线状态 |
| -13 | 删除环境失败 |
| -14 | 导入 COOKIE 失败: COOKIE 格式不合法 |
| -15 | 导出 COOKIE 失败: COOKIE 内容为空 |

### 创建/更新环境错误码

| 错误码 | 描述说明 |
|--------|----------|
| -101 | 创建/更新环境失败: 环境名称超长 |
| -102 | 创建/更新环境失败: 环境描述超长 |
| -103 | 创建/更新环境失败: 分组不存在 |
| -104 | 创建/更新环境失败: 代理服务器填入值不合法 |
| -105 | 创建/更新环境失败: 代理检测失败,无效代理 |
| -106 | 创建/更新环境失败: 初始化时区失败 |
| -107 | 创建/更新环境失败: 操作系统名称填写错误 |
| -108 | 创建/更新环境失败: 环境分辨率填写错误 |
| -109 | 创建/更新环境失败: USERAGENT 未填入有效值 |
| -110 | 创建/更新环境失败: 环境默认语言未填入有效值 |
| -111 | 创建/更新环境失败: FingerPrint_Setting 填入值非法 |
| -112 | 创建/更新环境失败: 导入 COOKIE 文件内容不合法 |

### 其他错误码

| 错误码 | 描述说明 |
|--------|----------|
| -10000 | 未知异常 |

## 2. Status 错误码表

| 错误码 | 描述说明 |
|--------|----------|
| 0 | 成功 |
| -1 | 初始化数据失败 |
| -2 | 启动浏览器内核失败 |
| -3 | 当前浏览器环境：插件下载失败 |
| -4 | 当前浏览器环境：插件加载失败 |
| -5 | 当前浏览器环境：自动化脚本下载失败 |
| -6 | 当前浏览器环境：自动化脚本加载失败 |
| -7 | 当前浏览器环境：已经运行 |
| -8 | 当前浏览器环境：已经加入运行队列 |
| -9 | 当前浏览器环境：初始化 CDP 失败 |
| -10 | 当前浏览器环境：初始化 Service 失败 |
| -11 | 当前浏览器环境：CD 监听失败 |
| -12 | 当前浏览器环境：DP 退出 |
| -13 | 当前浏览器环境：连接失败 |
| -14 | 当前浏览器环境：初始化环境失败 |
| -15 | 当前浏览器环境：GetShortPathName 失败 |
| -16 | 当前浏览器环境：申请内存失败 |
| -17 | 当前浏览器环境：登录退出 |
| -18 | 当前浏览器环境：未收到响应信息 |
| -19 | 当前浏览器环境：关闭失败 |
| -20 | Headless 打开项无法用 stop 关闭，建议使用 kill 关闭 |
| -21 | 当前浏览器环境：强制关闭失败 |
| -22 | 未找到指定的环境SessionId |
| -5010 | POST串请求到API SERVER URL地址非法，或请求的目标API URL中有非法字符存在 |
| -10000 | 未知错误 |

## 使用说明

1. **CODE错误码**: 主要用于API请求的返回状态判断
2. **Status错误码**: 主要用于浏览器环境操作的状态判断
3. **错误处理**: 建议在代码中对常见错误码进行处理
4. **调试建议**: 遇到错误时，首先查看对应的错误码说明

## 常见错误处理建议

### 登录相关错误
- **-1**: 检查APP_ID和APP_KEY是否正确
- **-3**: 检查账户套餐是否过期
- **-8**: 确保没有其他实例占用登录

### 环境操作错误
- **-22**: 检查Session_ID是否存在
- **-105**: 检查代理服务器配置是否正确
- **-7**: 等待当前环境运行完成

### 网络相关错误
- **-2**: 检查网络连接和服务器状态
- **-5010**: 检查API URL格式是否正确

## 相关链接

- [POSTMAN调试工具](/api/postman-example)
- [使用须知](/api/help)
- [常见问题](/api/question)
