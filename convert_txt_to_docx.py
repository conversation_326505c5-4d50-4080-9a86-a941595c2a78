#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将TXT文件转换为DOCX格式
过滤掉导航和重复内容，只保留核心API文档内容
"""

import os
import re
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TxtToDocxConverter:
    def __init__(self, input_dir='api_docs_advanced', output_dir='api_docs_docx'):
        self.input_dir = input_dir
        self.output_dir = output_dir
        
        # 创建输出目录
        Path(self.output_dir).mkdir(exist_ok=True)
        
        # 需要过滤掉的导航内容
        self.filter_patterns = [
            r'API使用文档-候鸟防关联浏览器',
            r'首页.*?应用.*?价格.*?下载',
            r'登录&注册.*?简体中文',
            r'佣金计划.*?博客中心',
            r'使用教程.*?常见问题',
            r'候鸟浏览器API使用文档',
            r'API使用须知.*?简介',
            r'API接口文档.*?帐号登录',
            r'候鸟API接口实时调试工具',
            r'多种语言脚本示例',
            r'JSON在线格式化工具'
        ]
    
    def clean_content(self, content):
        """清理内容，移除导航和重复部分"""
        try:
            lines = content.split('\n')
            cleaned_lines = []
            skip_until_api = True
            
            for line in lines:
                line = line.strip()
                
                # 跳过空行
                if not line:
                    if not skip_until_api:
                        cleaned_lines.append('')
                    continue
                
                # 跳过分隔线
                if line.startswith('='):
                    skip_until_api = False
                    continue
                
                # 如果还在跳过阶段，检查是否到了API内容部分
                if skip_until_api:
                    if any(keyword in line.lower() for keyword in ['api', '接口', '环境', '管理', '登录', '获取']):
                        if len(line) > 10:  # 确保不是简单的导航项
                            skip_until_api = False
                            cleaned_lines.append(line)
                    continue
                
                # 过滤导航内容
                should_skip = False
                for pattern in self.filter_patterns:
                    if re.search(pattern, line, re.IGNORECASE):
                        should_skip = True
                        break
                
                # 过滤单字符或过短的行
                if len(line) <= 2:
                    should_skip = True
                
                # 过滤明显的导航项
                if line in ['首页', '应用', '价格', '下载', 'API', '使用教程', '常见问题', '佣金计划', '博客中心', '登录&注册', '简体中文']:
                    should_skip = True
                
                if not should_skip:
                    cleaned_lines.append(line)
            
            # 移除开头和结尾的空行
            while cleaned_lines and not cleaned_lines[0]:
                cleaned_lines.pop(0)
            while cleaned_lines and not cleaned_lines[-1]:
                cleaned_lines.pop()
            
            return '\n'.join(cleaned_lines)
            
        except Exception as e:
            logger.error(f"清理内容失败: {str(e)}")
            return content
    
    def create_simple_docx_content(self, title, content):
        """创建简单的DOCX内容（文本格式）"""
        try:
            # 创建文档内容
            doc_content = f"{title}\n"
            doc_content += "=" * len(title) + "\n\n"
            doc_content += content
            
            return doc_content
            
        except Exception as e:
            logger.error(f"创建DOCX内容失败: {str(e)}")
            return f"{title}\n\n{content}"
    
    def convert_file(self, txt_file):
        """转换单个TXT文件"""
        try:
            # 读取TXT文件
            with open(txt_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 提取标题
            lines = content.split('\n')
            title = "API文档"
            for line in lines[:5]:
                if line.startswith('API文档:'):
                    title = line.replace('API文档:', '').strip()
                    break
            
            # 清理内容
            cleaned_content = self.clean_content(content)
            
            # 如果清理后内容太少，跳过
            if len(cleaned_content.strip()) < 50:
                logger.warning(f"文件内容太少，跳过: {txt_file}")
                return False
            
            # 创建DOCX内容
            doc_content = self.create_simple_docx_content(title, cleaned_content)
            
            # 生成输出文件名
            base_name = os.path.splitext(os.path.basename(txt_file))[0]
            output_file = os.path.join(self.output_dir, f"{base_name}.txt")  # 暂时保存为TXT
            
            # 保存文件
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(doc_content)
            
            logger.info(f"已转换: {base_name}")
            return True
            
        except Exception as e:
            logger.error(f"转换文件失败: {txt_file}, 错误: {str(e)}")
            return False
    
    def convert_all_files(self):
        """转换所有TXT文件"""
        logger.info("开始转换TXT文件...")
        
        txt_files = list(Path(self.input_dir).glob('*.txt'))
        success_count = 0
        
        for txt_file in txt_files:
            if self.convert_file(txt_file):
                success_count += 1
        
        logger.info(f"转换完成! 总文件数: {len(txt_files)}, 成功: {success_count}")
        
        # 统计各类文档数量
        self.analyze_converted_files()
    
    def analyze_converted_files(self):
        """分析转换后的文件"""
        try:
            output_files = list(Path(self.output_dir).glob('*.txt'))
            
            # 按类别分组
            categories = {}
            for file_path in output_files:
                filename = file_path.name
                if '_' in filename:
                    category = filename.split('_')[1]  # 获取主要类别
                    if category not in categories:
                        categories[category] = []
                    categories[category].append(filename)
            
            logger.info("文档分类统计:")
            for category, files in categories.items():
                logger.info(f"  {category}: {len(files)} 个文件")
                
                # 显示该类别的子文档
                sub_docs = [f for f in files if len(f.split('_')) >= 4]
                if sub_docs:
                    logger.info(f"    子文档: {len(sub_docs)} 个")
            
        except Exception as e:
            logger.error(f"分析文件失败: {str(e)}")

def main():
    """主函数"""
    print("TXT转DOCX转换器启动...")
    print("="*50)
    
    converter = TxtToDocxConverter()
    converter.convert_all_files()
    
    print("="*50)
    print("转换完成!")
    print("- 输入目录: api_docs_advanced")
    print("- 输出目录: api_docs_docx")

if __name__ == "__main__":
    main()
