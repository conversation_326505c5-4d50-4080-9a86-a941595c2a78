API内容提取总结报告
==================================================

分析页面数: 10
发现API功能总数: 130
发现描述段落总数: 34

各页面详情:
------------------------------
页面: /api/
  API功能: 9 个
  描述段落: 2 个
  内容长度: 1025 字符

页面: /api/session
  API功能: 25 个
  描述段落: 2 个
  内容长度: 968 字符

页面: /api/login
  API功能: 11 个
  描述段落: 13 个
  内容长度: 3014 字符

页面: /api/browser
  API功能: 12 个
  描述段落: 6 个
  内容长度: 2841 字符

页面: /api/group
  API功能: 13 个
  描述段落: 1 个
  内容长度: 826 字符

页面: /api/script
  API功能: 14 个
  描述段落: 1 个
  内容长度: 866 字符

页面: /api/plugin
  API功能: 13 个
  描述段落: 1 个
  内容长度: 865 字符

页面: /api/members
  API功能: 9 个
  描述段落: 2 个
  内容长度: 1175 字符

页面: /api/help
  API功能: 12 个
  描述段落: 5 个
  内容长度: 1270 字符

页面: /api/question
  API功能: 12 个
  描述段落: 1 个
  内容长度: 1082 字符

