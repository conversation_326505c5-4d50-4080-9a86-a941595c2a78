#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
切分包含大量图片的DOCX文档
专门处理候鸟浏览器基础框架这种图文并茂的文档
"""

import os
import re
from pathlib import Path
from docx import Document
from docx.shared import Inches, Pt
from docx.oxml.ns import qn

def setup_document_styles(doc):
    """设置文档样式"""
    try:
        heading1 = doc.styles['Heading 1']
        heading1.font.size = Pt(18)
        heading1.font.bold = True
    except:
        pass

def copy_paragraph_completely(source_para, target_doc):
    """完整复制段落，包括图片和所有格式"""
    try:
        # 获取段落的XML元素
        source_element = source_para._element
        
        # 创建新段落
        new_para = target_doc.add_paragraph()
        target_element = new_para._element
        
        # 复制段落属性
        try:
            new_para.style = source_para.style
            new_para.alignment = source_para.alignment
        except:
            pass
        
        # 清空新段落的默认内容
        target_element.clear()
        
        # 复制源段落的所有子元素（包括文本、图片等）
        for child in source_element:
            target_element.append(child)
        
        return new_para
        
    except Exception as e:
        # 如果完整复制失败，尝试基本复制
        try:
            new_para = target_doc.add_paragraph()
            new_para.style = source_para.style
            new_para.alignment = source_para.alignment
            
            for run in source_para.runs:
                new_run = new_para.add_run(run.text)
                new_run.bold = run.bold
                new_run.italic = run.italic
                new_run.underline = run.underline
            
            return new_para
        except:
            # 最后的备选方案：只复制文本
            return target_doc.add_paragraph(source_para.text)

def copy_table_with_images(source_table, target_doc):
    """复制表格，包括其中的图片"""
    try:
        rows = len(source_table.rows)
        cols = len(source_table.columns) if source_table.rows else 0
        
        if rows == 0 or cols == 0:
            return None
        
        # 创建新表格
        new_table = target_doc.add_table(rows=rows, cols=cols)
        new_table.style = 'Table Grid'
        
        # 复制表格内容
        for i, row in enumerate(source_table.rows):
            for j, cell in enumerate(row.cells):
                if i < len(new_table.rows) and j < len(new_table.rows[i].cells):
                    target_cell = new_table.cell(i, j)
                    
                    # 清空目标单元格
                    target_cell.text = ""
                    
                    # 复制单元格的所有段落
                    for para_idx, paragraph in enumerate(cell.paragraphs):
                        if para_idx == 0:
                            # 使用第一个段落
                            target_para = target_cell.paragraphs[0]
                            target_para._element.clear()
                            
                            # 复制段落内容
                            for child in paragraph._element:
                                target_para._element.append(child)
                        else:
                            # 添加新段落并复制内容
                            target_para = target_cell.add_paragraph()
                            target_para._element.clear()
                            
                            for child in paragraph._element:
                                target_para._element.append(child)
        
        return new_table
        
    except Exception as e:
        print(f"⚠️  复制表格失败: {str(e)}")
        # 备选方案：复制表格文本
        try:
            new_table = target_doc.add_table(rows=rows, cols=cols)
            new_table.style = 'Table Grid'
            
            for i, row in enumerate(source_table.rows):
                for j, cell in enumerate(row.cells):
                    if i < len(new_table.rows) and j < len(new_table.rows[i].cells):
                        new_table.cell(i, j).text = cell.text
            
            return new_table
        except:
            return None

def find_logical_split_points(doc, target_parts=10):
    """找到逻辑切分点"""
    paragraphs_per_part = len(doc.paragraphs) // target_parts
    split_points = []
    
    # 添加起始点
    split_points.append(0)
    
    for i in range(1, target_parts):
        target_index = i * paragraphs_per_part
        
        # 在目标位置附近寻找合适的分割点
        best_split = target_index
        
        # 向前向后搜索50个段落，寻找标题或章节
        search_range = range(max(0, target_index - 50), 
                           min(len(doc.paragraphs), target_index + 50))
        
        for j in search_range:
            if j >= len(doc.paragraphs):
                continue
                
            paragraph = doc.paragraphs[j]
            text = paragraph.text.strip()
            
            # 优先选择标题样式的段落
            if paragraph.style.name.startswith('Heading'):
                best_split = j
                break
            
            # 其次选择看起来像标题的短文本
            elif (text and len(text) < 100 and 
                  (re.match(r'^(第[一二三四五六七八九十\d]+|[一二三四五六七八九十]、|\d+\.)', text) or
                   any(keyword in text for keyword in ['架构', '系统', '管理', '配置', '接口', '服务']))):
                best_split = j
                break
        
        split_points.append(best_split)
    
    # 添加结束点
    split_points.append(len(doc.paragraphs))
    
    return split_points

def create_image_rich_split_files(doc, split_points, output_dir, base_name="候鸟浏览器基础框架"):
    """创建包含图片的切分文件"""
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    output_files = []
    
    # 计算表格分配
    tables_per_part = len(doc.tables) // (len(split_points) - 1)
    table_index = 0
    
    for i in range(len(split_points) - 1):
        part_num = i + 1
        start_idx = split_points[i]
        end_idx = split_points[i + 1]
        
        print(f"📄 创建第 {part_num} 部分 (段落 {start_idx}-{end_idx})...")
        
        # 创建新文档
        new_doc = Document()
        setup_document_styles(new_doc)
        
        # 添加文档标题
        new_doc.add_heading(f'{base_name}({part_num})', 0)
        
        # 复制段落（包括图片）
        copied_paragraphs = 0
        image_count = 0
        
        for para_idx in range(start_idx, end_idx):
            if para_idx < len(doc.paragraphs):
                source_para = doc.paragraphs[para_idx]
                
                # 检查段落是否包含图片
                has_image = bool(source_para._element.xpath('.//w:drawing'))
                if has_image:
                    image_count += 1
                
                # 复制段落
                copy_paragraph_completely(source_para, new_doc)
                copied_paragraphs += 1
        
        # 分配表格
        tables_to_add = tables_per_part
        if part_num == len(split_points) - 1:  # 最后一部分包含剩余表格
            tables_to_add += len(doc.tables) % (len(split_points) - 1)
        
        copied_tables = 0
        for _ in range(tables_to_add):
            if table_index < len(doc.tables):
                if copy_table_with_images(doc.tables[table_index], new_doc):
                    copied_tables += 1
                table_index += 1
        
        # 保存文件
        filename = f"{base_name}({part_num}).docx"
        filepath = output_path / filename
        new_doc.save(str(filepath))
        output_files.append(str(filepath))
        
        # 统计信息
        file_size = filepath.stat().st_size / 1024
        print(f"✅ 创建文件: {filename}")
        print(f"   段落数: {copied_paragraphs}")
        print(f"   图片段落: {image_count}")
        print(f"   表格数: {copied_tables}")
        print(f"   文件大小: {file_size:.1f} KB")
        print()
    
    return output_files

def split_image_rich_document(source_file, output_dir, target_parts=10):
    """切分包含大量图片的文档"""
    try:
        print(f"📄 处理图文并茂文档: {Path(source_file).name}")
        
        # 加载文档
        doc = Document(source_file)
        
        file_size = Path(source_file).stat().st_size / 1024
        print(f"📊 文档信息:")
        print(f"   文件大小: {file_size:.1f} KB")
        print(f"   段落数: {len(doc.paragraphs)}")
        print(f"   表格数: {len(doc.tables)}")
        
        # 统计图片段落
        image_paragraphs = 0
        for paragraph in doc.paragraphs:
            if paragraph._element.xpath('.//w:drawing'):
                image_paragraphs += 1
        
        print(f"   图片段落: {image_paragraphs}")
        print(f"   图片密度: {image_paragraphs/len(doc.paragraphs)*100:.1f}%")
        print()
        
        # 根据文件大小调整切分数量
        if file_size > 20000:  # 大于20MB
            target_parts = max(10, int(file_size / 2000))  # 每个文件约2MB
        elif file_size > 10000:  # 大于10MB
            target_parts = max(8, int(file_size / 1500))   # 每个文件约1.5MB
        else:
            target_parts = max(5, int(file_size / 1000))   # 每个文件约1MB
        
        print(f"📊 切分计划: 切分为 {target_parts} 个文件")
        
        # 找到逻辑切分点
        split_points = find_logical_split_points(doc, target_parts)
        
        print(f"📊 实际切分点: {len(split_points)-1} 个部分")
        for i in range(len(split_points)-1):
            start = split_points[i]
            end = split_points[i+1]
            print(f"   第{i+1}部分: 段落 {start}-{end} ({end-start}个段落)")
        print()
        
        # 创建切分文件
        output_files = create_image_rich_split_files(doc, split_points, output_dir)
        
        return output_files
        
    except Exception as e:
        print(f"❌ 切分失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return []

if __name__ == "__main__":
    source_file = r"F:\augment\output\docx_files\候鸟浏览器基础框架第七十七版.docx"
    output_directory = r"F:\augment\output\docx_files"
    
    print("📄 图文并茂DOCX文档切分工具")
    print(f"📂 源文件: {source_file}")
    print(f"📂 输出目录: {output_directory}")
    print("=" * 60)
    
    if not Path(source_file).exists():
        print(f"❌ 源文件不存在: {source_file}")
        exit(1)
    
    # 删除之前的切分文件
    old_files = list(Path(output_directory).glob("候鸟浏览器基础框架(*.docx"))
    if old_files:
        print(f"🗑️  清理 {len(old_files)} 个旧文件...")
        for old_file in old_files:
            try:
                old_file.unlink()
            except:
                pass
        print()
    
    # 执行切分
    result_files = split_image_rich_document(source_file, output_directory)
    
    if result_files:
        print("=" * 60)
        print(f"🎉 切分完成! 生成了 {len(result_files)} 个文件")
        
        total_size = 0
        for file_path in result_files:
            file_size = Path(file_path).stat().st_size / 1024
            total_size += file_size
            print(f"   📄 {Path(file_path).name} ({file_size:.1f} KB)")
        
        original_size = 21523.5
        print(f"\n📊 切分统计:")
        print(f"   原文件大小: {original_size:.1f} KB")
        print(f"   切分后总大小: {total_size:.1f} KB")
        print(f"   内容保留率: {(total_size/original_size)*100:.1f}%")
        print(f"   平均文件大小: {total_size/len(result_files):.1f} KB")
        
        print("\n🎯 文件已准备好用于RAGFlow向量库！")
        print("💡 这些文件包含完整的图片和格式，适合全面的知识检索。")
    else:
        print("❌ 切分失败")
