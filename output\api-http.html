<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>/api/http</title>
  <style>
/* 基础重置与排版 */
body {
  margin: 0;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.7;
  color: #333;
  background-color: #fff;
  max-width: 960px;
  margin: 0 auto;
  box-sizing: border-box;
}

/* 段落 */
p {
  margin: 1em 0;
}

/* 标题 */
h1, h2, h3, h4, h5, h6 {
  margin: 1.5em 0 0.8em;
  color: #2c3e50;
  font-weight: 600;
  line-height: 1.3;
}

h1 { font-size: 2em; border-bottom: 1px solid #eee; padding-bottom: 0.5em; }
h2 { font-size: 1.6em; }
h3 { font-size: 1.4em; }
h4 { font-size: 1.2em; }

/* 列表 */
ul, ol {
  margin: 1em 0;
  padding-left: 2em;
}

li {
  margin: 0.4em 0;
}

/* 引用块 */
blockquote {
  margin: 1.5em 0;
  padding: 0.8em 1.5em;
  background-color: #f9f9f9;
  border-left: 4px solid #ddd;
  color: #666;
  font-style: italic;
  border-radius: 0 4px 4px 0;
}

/* 代码行内 */
code {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  background-color: #f3f4f6;
  color: #e9602d;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-size: 0.95em;
  white-space: nowrap;
}

/* 代码块 */
pre {
  margin: 1.5em 0;
  padding: 1.2em;
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  overflow-x: auto;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
}

pre code {
  background: none;
  color: inherit;
  padding: 0;
  font-size: inherit;
  white-space: pre;
  display: block;
}

/* 表格 */
table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5em 0;
  font-size: 14px;
  overflow: hidden;
  box-shadow: 0 0 0 1px #e0e0e0;
  border-radius: 6px;
}

th, td {
  padding: 10px 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

th {
  background-color: #f8f9fa;
  color: #2c3e50;
  font-weight: 600;
  white-space: nowrap;
}

tr:nth-child(even) {
  background-color: #f9f9fb;
}

tr:hover {
  background-color: #f0f5ff;
}

/* 链接 */
a {
  color: #1a73e8;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

/* 分隔线 */
hr {
  border: 0;
  height: 1px;
  background: #ddd;
  margin: 2em 0;
}

/* 图片 */
img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 1em 0;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
  </style>
</head>
<body>
  <h2>HTTP模式说明</h2> <ul><li><p>http模式仅支持V3.9.2.114以上版本，请下载客户端最新版本<a>【下载候鸟浏览器最新版】</a></p></li> <li><p>操作前，请先从控制台购买API凭证并获取帐号验证所需的信息。具体见<a class="">【获取APP_ID、APP_KEY】</a></p></li></ul> <div class="ant-divider ant-divider-horizontal"></div> <h3>命令行唤起候鸟浏览器</h3> <p><strong>1、以管理员身份运行CMD或者PowerShell，并确保终端在候鸟浏览器主目录打开，或已进入候鸟浏览器主路径</strong></p> <ul><li><p>cmd运行以下语句</p></li></ul> <p><strong><code>ApiServer.exe --port=8186 --account=XXXXX --app_id=XXXXX --app_key=XXXXX –-retrun=on –-logs=on</code></strong></p> <ul><li><p>PowerShell运行以下语句</p></li></ul> <p><strong><code>.\ApiServer.exe --port=8186 --account=XXXXX --app_id=XXXXX --app_key=XXXXX –retrun=on –logs=on</code></strong></p> <p><strong>2、启动成功过后在命令行工具可以看到API地址</strong></p> <p><img></p> <ul><li><p>参数带入account、app_id、app_key，则在右下角的托盘菜单看见软件图标，标明已登入，可以调用接口运行脚本</p></li> <li><p>若未带入account、app_id、app_key参数，可在后续通过登录帐号接口登录客户端。见接口文档。</p></li></ul> <p><strong>3、CLI命令行参数介绍</strong></p> <div class="code-view"><pre><code> --port [可选] http连接端口 (default 8186) --account [可选] 登录账号 --app_id [可选] 凭证APP_ID --app_key [可选] 凭证APP_KEY --retrun [on/off，default:on] APISERVER.EXE 所有需返回给用户的数据[JSON/其它]，所有状态信息，返回到 CONSOLE 界面。 --logs [on/off，default:on] APISERVER.EXE 所有需返回给用户的 JSON 数据，所有状态信息，明文写入 API_LOG 目录【非 LOG 目录】，LOG 文件前缀例：test@qq.com_API_log。 --hide [on/off，default:off] 2023-06-19 新增，支持客户端有头运行</code></pre> </div> <h3>停止并退出APISERVER</h3> <ul><li><p>在操作系统右下角TRAY区的APISERVER图标上点击右键，唤出菜单，点击退出并完成退出APISERVER动作。也可以通过任务管理器等强制退出。</p></li></ul> <h3>接口说明</h3> <ul><li><p><strong>支持以下接口</strong></p></li></ul> <blockquote><p><a class=""><strong>帐号登录</strong></a>：启动客户端</p></blockquote> <blockquote><p><a class=""><strong>获取成员列表</strong></a>：获取主帐号与子帐号数据</p></blockquote> <blockquote><p><a class=""><strong>环境开启/关闭</strong></a>：打开环境、关闭环境、强制终止环境</p></blockquote> <blockquote><p><a class=""><strong>环境管理</strong></a>：获取环境列表、查询指定环境的配置数据、创建环境、更新环境高级指纹参数、更新环境、更新环境代理、删除环境、导入Cookie、导出Cookie、获取随机UA、清除环境本地缓存</p></blockquote> <blockquote><p><a class=""><strong>分组管理</strong></a>：获取环境分组列表、新建环境分组、删除环境分组、转移环境分组</p></blockquote>
</body>
</html>