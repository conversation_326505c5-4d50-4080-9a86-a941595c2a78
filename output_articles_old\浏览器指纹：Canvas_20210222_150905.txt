标题: 浏览器指纹：Canvas
英文标题: Browser Fingerprint：Canvas
ID: 90
分类ID: 25
添加时间: 1613977745
更新时间: 1685607205
访问次数: 0
SEO标题: 浏览器指纹：Canvas
SEO关键词: 浏览器指纹：Canvas
SEO描述: 浏览器指纹：Canvas

================================================== 内容 ==================================================
Canvas是一种HTML5 API，用于在网页上绘出2D图像和动画。

除了上述功能之外，Canvas也可以作为浏览器指纹识别的附加熵。根据Englehardt和Narayanan在普林斯顿大学的一项研究（2016），超过5%的网站使用Canvas来进行指纹识别。

综上，Canvas通过命令浏览器绘制一个隐藏的Canvas图像来实现指纹识别。在不同的机器上，这张图片的绘制结果略有不同；但如果机器相同，则图像也相同。图像被绘出后，它会被转换成一个哈希字符串，被进一步用于身份验证的额外熵。

候鸟浏览器为Canvas指纹提供了噪声模拟。

当网站通过浏览器请求读取Canvas函数时，噪声模式下的Canvas屏蔽算法会中途拦截它，并向读出添加一个随机但始终会保持一致的噪声。为了更好地理解其工作原理，我们可以将其类比为一个“语音修正器”。当您使用一个有着特定预设的语音修饰器时，它会改变你的声音，使它与原来的声音有很大的区别，但随着时间的推移这种变化将保持一致。

================================================== 英文内容 ==================================================
Canvas is an HTML5 API for drawing 2D images and animations on web pages.

In addition to the above functions, Canvas can also be used as an additional entropy for browser fingerprint recognition. According to a study by Englehardt and Narayanan at Princeton University (2016), more than 5% of websites use Canvas for fingerprint recognition.

To sum up, Canvas realizes fingerprint recognition by commanding the browser to draw a hidden Canvas image. On different machines, this picture will produce slightly different results; But if the machine is the same, the image is the same. After the image is drawn, it is converted into a hash string, which is further used for additional entropy for authentication.

Migratory Bird Browser provides noise simulation for Canvas fingerprint.

When the website requests to read the Canvas function through the browser, the Canvas masking algorithm in noise mode will intercept it halfway and add a random but consistent noise to the read. To better understand how it works, we can think of it as a "speech modifier." When you use a speech modifier with a particular preset, it will change your voice so that it is very different from the original voice, but this change will remain consistent over time.