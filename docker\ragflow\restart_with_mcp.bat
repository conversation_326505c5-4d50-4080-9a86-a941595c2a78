@echo off
echo ========================================
echo RAGFlow MCP Server 启动脚本
echo ========================================
echo.

echo 🔄 停止现有的RAGFlow服务...
docker-compose down

echo.
echo 🚀 启动RAGFlow服务（已启用MCP Server）...
docker-compose up -d

echo.
echo ⏳ 等待服务启动完成...
timeout /t 30 /nobreak

echo.
echo 📊 检查服务状态...
docker-compose ps

echo.
echo 🔍 检查RAGFlow主服务日志...
docker logs ragflow-server --tail 20

echo.
echo 🌐 服务访问地址:
echo   RAGFlow Web界面: http://localhost:9380
echo   MCP Server端点: http://localhost:9382
echo.

echo 🧪 测试MCP Server连接...
curl -s -o nul -w "MCP Server状态码: %%{http_code}\n" http://localhost:9382/health 2>nul || echo MCP Server连接测试失败，可能还在启动中...

echo.
echo ✅ 启动完成！
echo.
echo 💡 使用说明:
echo   1. 访问 http://localhost:9380 使用RAGFlow Web界面
echo   2. MCP Server在端口9382上运行
echo   3. API密钥: ragflow-mcp-2025
echo   4. 查看日志: docker logs ragflow-server
echo.
pause
