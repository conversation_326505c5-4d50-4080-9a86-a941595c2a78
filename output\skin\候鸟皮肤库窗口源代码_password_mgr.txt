﻿<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Window size="600,590" caption="0,0,0,50" roundcorner="5,5,5,5" showshadow="true" shadowsize="3" shadowposition="0,0" shadowcolor="#ff6272A4" fademode="true">
	<Include source="Default.xml" />

  <VerticalLayout width="553" height="590" bkcolor="#FF282A36">
    <HorizontalLayout height="37">

    	 <Button name="login" bkimage="icon.png" padding="8,6,0,0" width="28" height="28"/>
      <Label name="passwordmgrtitle" padding="6,4,0,0" text="网页登录帐户管理器" width="260" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" align="left" font="8"></Label>

      <Control />
      <Button name="minbtn" width="28" height="26" tooltip="最小化" normalimage="minbtn.png" hotimage="minbtn_hover.png" pushedimage="minbtnpush.png" />
      <Button name="closebtn" width="28" height="26"  tooltip="关闭" normalimage="closebtn.png" hotimage="closebtn_hover.png" pushedimage="closebtnpush.png" />

    </HorizontalLayout>


   <HorizontalLayout name="loading_data" height="590" bkcolor="#ffe9e9e9" visible="true">

	    <VerticalLayout width="553" height="420">

					     <HorizontalLayout height="200" width="553">
					    	 <Control />
					    		<GifAnim name="data_loading" bkimage="dataloading.gif" height="200" width="200" padding="0,0,0,0" auto="true"/>
					    	 <Control />
					     </HorizontalLayout>


					     <HorizontalLayout width="553" height="30" >
					    	 <Control />
					    		  <Label name="data_percent" text="55%" width="300" textcolor="#FF616161" hottextcolor="#ff000000" align="center" font="10"></Label>
					    	 <Control />
					     </HorizontalLayout>

					     <HorizontalLayout width="553" height="60" >
					    	 <Control />
					    		  <Label name="process_description" text="密码数据正在加密备份中..   请稍侯.." width="300" textcolor="#FF616161" hottextcolor="#ff000000" align="center" font="8"></Label>
					    	 <Control />
					     </HorizontalLayout>

            <HorizontalLayout width="553" height="40" >
            </HorizontalLayout>

            <HorizontalLayout name="backarea" width="553" height="60" visible="false">
              <Control />
              <Button text="返回" name="back" width="120" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	normalimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
              <Control width="100" />
              <Button text="退出" name="closebtn" width="120" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	normalimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
              <Control />
            </HorizontalLayout>

      </VerticalLayout>

 </HorizontalLayout>

  <HorizontalLayout name="data" visible="false">


		<VerticalLayout width="953" height="603">


			<HorizontalLayout height="56" >
			    	 <VerticalLayout width="420">
				         <RichEdit name="session_search" padding="20,10,0,10" height="36" width="400" tipvaluecolor="#FF333333" borderround="7,7" bkcolor="#ffdce1e7" font="8" textpadding="10,8,20,0" maxchar="300" tipvalue="请输入关键字查找会话.." multiline="false" textcolor="#ff333333" rich="false" transparent="false">
				      </RichEdit>
				     </VerticalLayout>

			</HorizontalLayout>


      <HorizontalLayout inset="0,0,0,0" height="444" bkcolor="#FFffffff">
        	<List name="list_session_manager" vscrollbar="true" itembkcolor="#FFffffff" bordersize="1,1,1,1" itemselectedbkcolor="#FFe0e0e0" itemhotbkcolor="#FFf2f2f2" bordercolor="#FFD9DADB">
						<ListHeader height="36" bordersize="1" bordercolor="#FFD7D7D7" bkcolor="#FFF9F9FA">

							<ListHeaderItem text="帐号名称" name="header_device_title" width="120" align="left" textpadding="20,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="所属平台" name="header_device_choice" width="120" align="left" textpadding="20,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="网络站点" name="header_device_name" width="160" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="网站帐户" name="header_backup_datetime" width="120" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="登录密码" name="header_backup_datetime" width="80" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
						</ListHeader>

					</List>
				</HorizontalLayout>






		</VerticalLayout>


	</HorizontalLayout>
    <HorizontalLayout height="52" bkcolor="#ffe9e9e9">
         <Control />
      <VerticalLayout width="360">

       </VerticalLayout>
      <VerticalLayout width="140">
      		<Control />
          <Button text="保存" name="btnok"  padding="20,2,0,0" width="120" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	bkimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
          <Control />
       </VerticalLayout>


<Control />
    </HorizontalLayout>
  </VerticalLayout>
</Window>
