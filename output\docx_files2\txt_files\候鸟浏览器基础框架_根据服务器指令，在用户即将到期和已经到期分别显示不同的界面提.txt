根据服务器指令，在用户即将到期和已经到期分别显示不同的界面提示用户尽快进行续费充值。

候鸟客户端：

根据服务器指令，在用户即将到期和已经到期分别显示不同的界面提示用户尽快进行续费充值。

通过判断is_expire 值 显示不同的提示：

当且仅当is_expire = 1 时，表示此用户已接近到期时间，需在登录成功后，提示用户进行续费，并在下方按钮处显示：我知道了，继续登录。 用户再点击此按钮才可进入软体主界面。

登录成功时显示：

当且仅当is_expire = 2 时，表示此用户已达到到期时间，需在登录成功后，提示用户进行续费，并在下方按钮处显示：我知道了，继续登录。 用户再点击此按钮才可进入软体主界面。

登录成功时显示：

注：以上所有界面中的 续费字样按钮，点击后均直接打开浏览器跳到充值续费页面。

客户端登录成功后（包括用户在使用过程中）主面板在心跳接口中如实时收到：

connected|req_expire|1

显示：

登录成功后（包括用户在使用过程中）主面板在心跳接口中如收到：

connected|req_expire|2

显示：

重点：  在客户端在当前实例已启动的生命周期内收到

connected|req_expire|2

的运行状态下，用户点击运行按钮不再弹出浏览器。

第十六章 NVIDIA SDK 渲染优化 及 操作系统防火墙例外（白名单）优化

第十六章 NVIDIA SDK 渲染优化 及 操作系统防火墙例外（白名单）优化

第一节：基于 NVIDIA 显卡系统 GPU 硬件渲染优化

1、对于极少数用户出现候鸟浏览器黑屏的情况，依据内部多次讨论的结果，前期可先在候鸟官网文档中针对此情况提供解决方案 - 图文操作文档 - 供用户参考，即建议用户手动将mbbrowser和chrome应用程序添加到nvidia程序加速控制面板中来， 通过提供GPU硬解针对候鸟产品的性能提升来避免出现渲染问题导致黑屏问题。

2、后期考虑通过安装自动调用nvidia api接口来自动实现。

第二节：自动将候鸟浏览器添加操作系统防火墙信任菜单

NSIS脚本：

; 防火墙规则添加

ExecDos::exec 'netsh advfirewall firewall add rule name="xxxxx" dir=in program="$INSTDIR\${PRODUCT_NAME}.exe" action=allow'

ExecDos::exec 'netsh advfirewall firewall add rule name="xxxxx" dir=out program="$INSTDIR\${PRODUCT_NAME}.exe" action=allow'

具体位置参考：

注意，这里的xxxx要替换成应用程序的别名，最好和应用名区分开来，这是显示在白名单中的名字，而PRODUCT_NAME就是程序名的字符串。

"$INSTDIR\${PRODUCT_NAME}.exe"

删除防火墙规则

软件卸载后，需要删除防火墙规则。如下：

;防火墙规则删除

ExecDos::exec 'netsh advfirewall firewall delete rule name="xxxxx"'

注意，这里的xxxxx要和上面的名称保持一致，否则无法删除。

添加位置可以在程序目录被删除后执行。

[2021-02-19 新增]


================================================== 表格内容 ==================================================

错误码 | 错误码 | Message值 / 描述说明
0 | 全局 | 成功
-1 | 全局 | 登录失败/App_ID或App_KEY非法
-2 | 全局 | 登录失败/连接候鸟服务器超时[curl状态码]
-3 | 全局 | 当前登录帐户套餐已过期
-4 | 全局 | Login_Expire
-5 | 全局 | Login_ServerError//服务器访问错误
-6 | 全局 | Login_MbSvrError//启动mbservice失败
-7 | 全局 | Login_Uping,//还有上传在进行，需要等其完成才能登录
-8 | 全局 | Login_Occupy,//有其它实例已占用登录
-9 | 全局 | 数据同步失败:超时
-10 | 全局 | 数据处理失败：数据损坏导致无法加载
-11 | 全局 | 数据处理失败：当前目录没有写权限，目录位置：
-10 | 全局 | 登录失败/App_ID 处于在线状态
-12 | 全局 | 删除环境失败
-13 | 全局 | 导入COOKIE失败: COOKIE格式不合法
-14 | 全局 | 导出COOKIE失败: COOKIE内容为空
-101 | 创建/更新环境 | 创建/更新环境失败: 环境名称超长
-102 | 创建/更新环境 | 创建/更新环境失败: 环境描述超长
-103 | 创建/更新环境 | 创建/更新环境失败: 分组不存在
-104 | 创建/更新环境 | 创建/更新环境失败: 代理服务器填入值不合法
-105 | 创建/更新环境 | 创建/更新环境失败: 代理检测失败,无效代理
-106 | 创建/更新环境 | 创建/更新环境失败: 初始化时区失败
-107 | 创建/更新环境 | 创建/更新环境失败: 操作系统名称填写错误
-108 | 创建/更新环境 | 创建/更新环境失败: 环境分辨率填写错误
-109 | 创建/更新环境 | 创建/更新环境失败: USERAGENT未填入有效值
-110 | 创建/更新环境 | 创建/更新环境失败: 环境默认语言未填入有效值
-111 | 创建/更新环境 | 创建/更新环境失败: FingerPrint_Setting填入值非法
-112 | 创建/更新环境 | 创建/更新环境失败: 导入COOKIE文件内容不合法
-10000 | 全局 | 未知异常
-10001 | 全局
-10002 | 全局
-10003 | 全局

错误码 | 描述说明
0 | 成功
-1 | 初始化数据失败
-2 | 启动浏览器内核失败
-3 | 当前浏览器环境：插件下载失败
-4 | 当前浏览器环境：插件加载失败
-5 | 当前浏览器环境：自动化脚本下载失败
-6 | 当前浏览器环境：自动化脚本加载失败
-7 | 当前浏览器环境：已经运行
-8 | 当前浏览器环境：已经加入运行队列
-9 | 当前浏览器环境：初始化CDP失败
-10 | 当前浏览器环境：初始化Service失败
-11 | 当前浏览器环境：CD监听失败
-12 | 当前浏览器环境：DP 退出
-13 | 当前浏览器环境：连接失败
-14 | 当前浏览器环境：初始化环境失败
-15 | 当前浏览器环境：GetShortPathName失败
-16 | 当前浏览器环境：申请内存失败
-17 | 当前浏览器环境：登录退出
-18 | 当前浏览器环境：未收到响应信息
-19 | 当前浏览器环境：关闭失败
-20 | Headless打开项无法用stop关闭，建议使用kill关闭
-21 | 当前浏览器环境：强制关闭失败
-22 | 未找到指定的item ID
-10000 | 未知错误