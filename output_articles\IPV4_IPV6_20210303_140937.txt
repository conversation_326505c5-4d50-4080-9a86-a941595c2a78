标题: IPV4/IPV6
英文标题: IPV4/IPV6
ID: 96
分类ID: 25
添加时间: 1614751777
更新时间: 1685430005
访问次数: 0
SEO标题: IPV4,IPV6
SEO关键词: IPV4,IPV6
SEO描述: IPV4,IPV6

================================================== 内容 ==================================================
### IPV4

网际协议版本4（英语：Internet Protocol version 4，IPv4），又称互联网通信协议第四版，是网际协议开发过程中的第四个修订版本，也是此协议第一个被广泛部署的版本。IPv4是互联网的核心，也是使用最广泛的网际协议版本，其后继版本为IPv6，直到2011年，IANA IPv4位址完全用尽时，IPv6仍处在部署的初期。


IPv4在IETF于1981年9月发布的 RFC 791 中被描述，此RFC替换了于1980年1月发布的 RFC 760。


IPv4是一种无连接的协议，操作在使用分组交换的链路层（如以太网）上。此协议会尽最大努力交付数据包，意即它不保证任何数据包均能送达目的地，也不保证所有数据包均按照正确的顺序无重复地到达。这些方面是由上层的传输协议（如传输控制协议）处理的。


2019年11月26日，全球所有43亿个IPv4地址已分配完毕，这意味着没有更多的IPv4地址可以分配给ISP和其他大型网络基础设施提供商。

### IPV6

网际协议第6版（英语：Internet Protocol version 6，缩写：IPv6）是网际协议的最新版本，用作互联网的协议。用它来取代IPv4主要是为了解决IPv4地址枯竭问题，同时它也在其他方面对于IPv4有许多改进。


IPv6的设计目的是取代IPv4，然而长期以来IPv4在互联网流量中仍占据主要地位，IPv6的使用增长缓慢。在2019年12月，通过IPv6使用Google服务的用户百分率首次超过30%。

================================================== 英文内容 ==================================================
### IPV4

Internet Protocol version 4 (IPv4), also known as Internet Protocol Version 4, is the fourth revision of the Internet protocol in development and the first widely deployed version of the protocol. IPv4 is the core of the Internet and the most widely used version of the Internet protocol, followed by IPv6, which was still in the early stages of deployment when the IANA IPv4 address was exhausted in 2011.


IPv4 was described in the IETF's RFC 791 released in September 1981, which replaced the RFC 760 released in January 1980.


IPv4 is a connectionless protocol that operates on a link layer (such as Ethernet) that uses packet switching. This protocol does its best to deliver the packets, meaning that it does not guarantee that any packet will reach its destination, nor that all packets will arrive in the correct order without repetition. These aspects are handled by upper-layer transport protocols such as Transmission Control Protocol.


On November 26, 2019, all 4.3 billion IPv4 addresses in the world were allocated, meaning that there are no more IPv4 addresses to assign to ISPs and other large network infrastructure providers.

### IPV6

Internet Protocol version 6 (English: Internet Protocol version 6, abbreviated: IPv6) is the latest version of the Internet Protocol, used as the protocol for the Internet. It was used to replace IPv4 mainly to solve the IPv4 address exhaustion problem, but it also improved IPv4 in many other ways.


IPv6 was designed to replace IPv4, but while IPv4 has long dominated Internet traffic, its use has grown slowly. In December 2019, the percentage of users using Google services over IPv6 exceeded 30 percent for the first time.