前述：对于团队协作客户、及个人需要分享 ITEM 包给使用候鸟第三者用户，

前述：对于团队协作客户、及个人需要分享 ITEM 包给使用候鸟第三者用户，

提出需要对脚本代码进行加密行为的业务需求。在此章节中，将详细阐述并提

供完整业务逻辑和操作流程。

界面元素增加说明：

加解密约定：

a、 为保证用户的商用 脚本在我们服务器端可以进行采集，在用户加密

脚本之前，客户脚本必须发送到服务器端。

b、 用户对脚本进行加密统一使用用户自行设置的密码进行加解密。

c、 加解密菜单项和界面上加解密按钮无需跟此脚本的 OWNER 关联，

用户只要输入了正确的密码即可解密。

d、 客户端支持脚本的加密格式执行，也同时支持脚本的明文格式执

行。（需在 DRIVER 引擎中增加对加密脚本的支持 或 在内存中赋给 DRIVER 使用，此项需

讨论）

e、加密方式仍旧使用 AES_128 加密。

如图：

1、 C 区增加一列： 加密 显示此脚本文件当前加码状态。

说明：用户在 C 区点击右键唤出菜单，点击“加密脚本”后，弹出设

定密码对话框，用户填入密码后，加密完成。

2、 C 区菜单项：增加 加密脚本、解密脚本 2 项，点击 “解密脚本”后，

弹出解密对话框，用户填入密码后，客户端请求服务器接口进行此

AUTOSCRIPT 项的密码校验：

校验通过，进行解密

解密成功，将 C 区 加密 列中此项 由 “是” 变更为

“否”，解密完成。

解密失败，弹出提示窗。

校验失败，弹出提示窗，提示密码不对。

和 A 区一致，脚本名称要带有（P）（S）标志符。

CONFIGDATA.XML 中 关于 AUTOSCRIPTS_LIST 的节点参数新增，

AUTOSCRIPTS.XML 的新增，详见第十二章节。【点击跳入】

[2022-11-15立项]

【12-08二次增订】【12-18三次增订】

第三十二章

候鸟浏览器

产品API 1.0版生产及各项约定框架、工作流程详细说明

依据传统，具体工作步骤、顺序安排，人员分配在工作群里具体阐述。

前述：

框架流程：

自动化脚本（用户填入APP_ID,APP_KEY） ---> 候鸟产品集成API -- post--->  候鸟httpd(apiserver)进程 (127.0.0.1:8186)   --->  mbbrowser.exe/service.exe  <--->  正式服/测试服（验证Token,APP_ID,APP_KEY)  ---> 结果返回给MBBROWSER.exe -> httpd界面显示状态报告给用户。

候鸟API凭据购买与获取：

上图为HUBSTUDIO的起始标志。
---------------------------------------------------------------------------------------------------
候鸟约定一：  APP_ID 不变。 APP Secret 变更为：APP_KEY

API坐席 在候鸟里约定统称： API凭据

前题：用户必须购买API服务后，才能在控制台开通API凭据，使用产品集成API功能。

API_ID： 用户使用候鸟产品API功能的唯一有效ID。

APP_KEY: 用户使用候鸟产品API功能的唯一有效KEY。

约定二：以下所有功能, 标记（*）均为用户已购买 API凭据 后提供。

约定三：同一个 APP_ID/APP_KEY 不支持主帐号和子帐号同时使用。

约定四：API限流约定。 单用户APP_ID/APP_KEY 调用接口API频率默认为20次/秒，超过此频率APISERVER自动进行延迟处理。

API 限流 是限制用户在一定时间内 API 请求数量的过程。应用程序编程接口 (API) 充当用户和软件应用程序之间的网关。例如，当用户使用 候鸟的APISERVER进行脚本全自动化运行时，会触发 API 调用。此 API 与候鸟客户端应用程序及候鸟的网络服务器进行交互，并执行各类操作。此时，用户的自动化频率会对服务器硬件及服务器带宽带来巨额资源消耗和带宽占用。

API 限流还有助于抵御拒绝服务 (DoS) 攻击，在 DoS 攻击中，恶意用户发送大量请求以使网站或移动应用程序崩溃。随着在线用户数量的增加，企业需要实施 API 限流机制，以确保公平使用、数据安全并防止恶意攻击。

令牌桶算法例子：

令牌桶算法：

注：为确保服务器数据流通讯压力能控制在一个合法范围，需对客户端API进行限流处理，如APISERVER中的HTTPD核心自带有对限流的支持，可使用其核心自带的限流。

WEB控制台与客户端认定用户API凭据已开通依据。

A、登录环节：

WEB控制台与客户端通过登录接口返回的JSON指定字段节点【API_ID,APP_KEY】是否为空，来判定此用户是否具备使用API权限。

B、客户端在线实时激活API凭据环节：

用户实时购买并实时在WEB控制台开通API凭据后，服务器实时心跳下发API凭据给生效客户端， 如此时客户端已开启，客户端通过心跳接收指令，来更新本地标志位，更新用户当前API凭据服务功能。[此项暂时不做]

二、控制台/客户端 API服务 前期 基础步骤：

3、控制台环境ID的对外提供。标记（*）

说明：

  A、SESSION_ID在产品API对外提供后，用户以自有脚本中，依据SESSIOND_ID来进行环境运行、停止、修改配置、管理的唯一有效ID。

B、客户端与服务器端依据用户的APP_KEY、TOKEN、SESSIOD_ID，定位到用户要操作的各个ITEM及ITEM集合，并进行后续各分支流程。

C、控制台中的环境栏目中，所有列（环境列表，我分享的环境…） 均需显示出环境的唯一 UNQUE_SESSIOND_ID 。

、 产品客户端环境ID的对外提供。标记（*）

说明：在ID后提供复制按钮图标，点击ID或复制按钮图标，将ID粘帖入用户系统剪帖板。

三、候鸟客户端使用HTTP协议提供 集成API支撑

约定四：候鸟HTTPD进程名称约定：ApiServer.exe 版本号：1.0.1.1 中文名：候鸟API值守服务

约定五：所有返回数据默认使用JSON格式。

在CMD中运行此命令行【命令行参数不区分大小写】，可开启候鸟API模式：

C:\mbbrowser\ ApiServer.exe --port=8186  --app_id=202211291047058133354147840 --app_key= UHt3cCgYEAyF6r5F+exuzbOp1kcfaB8d4MVkEXM –retrun=on –logs=on –hide=on

参数说明：RETRUN: on / off

ON:  APISERVER.EXE 所有需返回给用户的数据[JSON/其它]，所有状态信息，返回到CONSOLE界面。（当用户未填写此参数时，默认为ON）

OFF: 所有返回给用户的数据[JSON/其它]、所有状态信息，不在CONSOLE界面显示。

参数说明：LOGS: on / off

ON:  APISERVER.EXE 所有需返回给用户的JSON数据，所有状态信息，明文写入API_LOG目录【非LOG目录】，LOG文件前缀例：test04@qq.com_API_log。（当用户未填写此参数时，默认为ON）

OFF: 所有返回给脚本的JSON数据、所有状态信息，不写入LOG。

约定六：当客户端使用API无头模式登录时，客户端请求的域名要变更为：【约定】API.MBBROWSER.COM 香港服为：API_HK.MBBROWSER.COM【香港域名此项经集体讨论后不做】，其它不变。测试服：APITEST.MBBROWSER.COM


================================================== 表格内容 ==================================================

参数名称 | 类型 | 必传 | 样例串/默认值 | 说明
Session_ID | array | 是 | 373808cb37bd63f5f7d92415e736e85f | 清除指定环境ID的浏览器缓存
Is_Clean_Cookies | bit | 否 | 1 | 清除指定环境ID的COOKIE/仅清除cookie，其它不清除

{
"message": "BrowserCache clean Success",
"code": 0,
"data": {
        "Clean_SessionID_Success": "373808cb37bd63f5f7d92415e736e85f",
        "Clean_SessionID_Failed": "f994d8e641ce7006acfa36c901829ff2"
    },
}