# 候鸟浏览器API文档总览

## 简介

候鸟浏览器API提供了完整的浏览器自动化功能，支持环境管理、脚本执行、插件管理等操作。

## API模块

### 帐号登录 (5个功能)

候鸟浏览器API登录认证

**功能列表**:
1. 候鸟浏览器支持本地API的功能，帮助用户通过程序化的方式来启动和关闭浏览器等基础API功能
2. 仅支持客户端V3.9.2.114以上版本
3. HTTP模式需配合使用CLI命令行启动客户端
4. 与APISERVER交互、请求地址详述
5. 实时切换账号并重新登录

### 环境管理 (16个功能)

管理浏览器环境的创建、更新、删除等操作

**功能列表**:
1. 获取环境列表
2. 查询指定环境ID的配置数据
3. 创建环境
4. 更新环境高级指纹参数
5. 更新环境
6. 更新环境代理
7. 删除环境
8. 导入Cookie
9. 导出Cookie
10. 获取随机UA
11. 清除环境本地缓存
12. 查看环境运行状态
13. 查看环境网页自动运行信息
14. 添加环境自动运行网页地址
15. 更新环境某个自动运行网页地址
16. 删除环境某个自动运行网页地址

### 环境开启关闭 (3个功能)

控制浏览器环境的启动和停止

**功能列表**:
1. 打开环境
2. 关闭环境
3. 强制终止环境

### 分组管理 (4个功能)

管理环境分组

**功能列表**:
1. 获取环境分组列表
2. 新建环境分组
3. 删除环境分组
4. 将指定环境从指定分组转移到另一个分组

### 插件管理 (4个功能)

管理浏览器插件

**功能列表**:
1. 列出当前帐户下所有已安装的插件(插件 ID，插件名称)
2. 查询、列出指定环境中的所有插件(插件 ID，插件名称)
3. 安装指定多个插件到指定的环境中
4. 删除指定环境插件

### 脚本管理 (5个功能)

管理自动化脚本

**功能列表**:
1. 查询、列出指定环境中的所有脚本
2. 切换指定环境已激活脚本
3. 从我的脚本库中指派脚本到目标环境中
4. 指定环境中的指定脚本设定为非激活状态
5. 将未激活脚本从指定环境中移除

## 统计信息

- **总模块数**: 6
- **总功能数**: 37
- **API服务器**: http://127.0.0.1:8186
- **支持格式**: JSON

## 快速开始

1. 下载并安装候鸟浏览器客户端 (V3.9.2.114+)
2. 获取APP_ID和APP_KEY
3. 启动API服务器
4. 调用登录接口进行认证
5. 使用其他API功能

## 文档说明

- 每个模块都有主文档和详细的功能文档
- 所有文档都包含请求示例和响应示例
- 建议使用POSTMAN进行接口调试
- 详细的错误码说明请参考错误码对照表
