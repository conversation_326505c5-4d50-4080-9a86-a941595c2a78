六、候鸟客户端 日志数据存储结构、逻辑流程约定：

六、候鸟客户端 日志数据存储结构、逻辑流程约定：

约定一：

所有日志数据采用SQLITE DB加密存储，加密KEY为候鸟官方私有KEY，不对外公开。每一类日志存入单独表中。

约定二：

为减少服务器压力，日志DB仅保存在用户本地，默认不同步到候鸟服务器。

约定三：

日志DB结构及存储位置，如图：

说明：
日志存储位置如下：
I:\MBData\Beta_LocalStorage\7zxjIqxxuQ28jnWdwVPQpA==\MainData\DBLogs 
DBLogs 文件夹大小写请严格按基础文档要求命名。

DB文件名：

登录日志： LOGIN.db

环境操作日志： SESSION.db

环境包导入日志：SESSION_IMPORT.db

环境包导出日志：SESSION_EXPORT.db

COOKIE导入日志：COOKIE_IMPORT.db

COOKIE导出日志：COOKIE_EXPORT.db

环境指纹变化日志：FINGER_PRINT.db

代理检测日志：PROXIES.db

各DB表名：main

约定四：

同MBDATA文件夹属性及原约定不变，DB文件默认不随客户端卸载而灭失（删除）。

约定五：

候鸟客户端启动和运行期间默认不加载各DB，仅在本地环境日志管理器窗体打开时，根据用户选择加载指定DB，如需时较长，则按惯例仍旧显示加载进度信息。关闭此窗体后，释放此DB，以减少客户机内存占用。

约定六：日志最大上限条数，默认各DB - main表记录：2000条，并在SETUP面板提供默认条数修改设定，面板中此项最大上限各DB不超过20000条。（后期根据实际运营情况进行调整）

约定七：各DB的写入仅以事件触发为条件，在不影响主进程的原则下，通过线程进行相应DB加载->表写入->DB释放。(之后重点测试是否存在有效率影响情况并根据实际情况调整约定七内容。)

约定八：在SETUP面板中，日志记录提供总开关项，开关位置：通用设置栏目中。

约定九：日志导出格式为EXCEL报表格式，将当前DB-main表中各字段以EXCEL格式导出。

七、候鸟服务器端 WEB控制台帐号登录日志：

与客户端不同，客户端仅记载用户所在的机器登录日志详单。

而候鸟服务器端 WEB控制台帐号登录日志记录所有机器（全局）登录同一个帐户的日志列表，供用户查询是否存在非法登录、登录IP【地域】的情况。

八：二期工程：增加插件、脚本的日志记录。

[x] 主面板搜索项增加下拉列表或菜单控件，显示最近搜索项。

[20230730]

第三十七章
代理服务器管理扩展：

一键代理IP批量指派到环境

依据传统，具体工作步骤、顺序安排，人员分配在工作群里具体阐述。

【图1】

【图2】

前述：

在批量创建环境的场景中，当前候鸟支持用户通过EXCEL批量导入自有环境和一键批量创建新环境两大场景。实际在商业运营中，部份用户并没有自行创建EXCEL，而是通过候鸟的一键批量创建新环境来进行首批的环境创建。在此过程中，用户希望将自已的多个代理服务器IP灵活的匹配到已经新创建的环境中，但当前候鸟产品并未提供这块支持，导致用户不得不在批量创建环境后，将自已的代理IP逐个逐个添加到环境里，用户端手工操作工作量十分庞大且没有必要，从而导致用户投诉后进而离场。为完善这一块的功能，现开辟单独的章节来进行此扩展的架构与具体逻辑实施。

详述：

图1、2 为通过主面板菜单进入代理服务器管理器的多个入口。

图3 为一键代理IP批量指派到环境功能主界面。

【图3】

【图4】

【图5】

流程说明一：

   依据传统的用户习惯：A、B、C三大操作步骤，现从上图4，图5进行说明。

箭头1：允许用户通过搜索代理关键字进行搜索，支持搜索列为：代理类型、代理IP、代理端口。三项。

箭头2：点刷新按钮，重新加载PROXY XML数据，更新列表。

箭头3：当用户直接进入到此界面时，默认显示 已有的代理 下拉项。

箭头4：此列显示代理类别，如果此环境不存在代理，则显示 N/A 色值：#ffcccccc

箭头5：C区列出已勾选的环境中的代理，删除代理功能将已勾选环境中的已勾选代理删除。

箭头7：随机指派模式 为默认模式：当用户从A区指派到B区时，将所有代理IP随机分配到各个环境中，这里有个前题，如果A区数量小于B区，必须保证A区的代理IP在B区的环境中全部都存在。

箭头8：顺序指派模式，当用户从A区指派到B区时，将所有代理IP，顺序分配到各个环境中，特点为：如果A区数量小于B区，则A区分配到B区后，B区还会有部份未被分配，此时拿 A区的IP继续分配B区剩余的部份。

箭头6：执行上述1-8的设定过程。

流程说明二：

【图6】

主面板右键菜单增加 更换环境代理IP项

1、允许用户直接通过此项跳转到【图3】，并进行单项或多项环境的代理IP指派。

2、在主面板上单选、多选后，进入更换环境代理IP窗口，此时B区的环境列表要自动显示为主面板上已单选或多选的环境列表。其它流程不变。

[20230919]


================================================== 表格内容 ==================================================

{
"message "Session Script List Success",
"code": 0,
"data": {
"listcontainer": [
        {
        "Session_Name": “商用业务环境一”
        "Session_ID": "373808cb37bd63f5f7d92415e736e85f",
"Group_Name": “default”
“Actived_script_id”:” O73808cb37bd63f5f7d92415e736e999”,
“Actiived_script_name”:”这是一个脚本例子”,
“Actiived_script_encode”:”true”,
"Script_Count": "4",
"UnActived_script_list": 
                    [{
                     "UnActived_script_encode" : "false",
                     "UnActived_script_Name" : "AAA",
                     "UnActived_script_ID" : "17c70e014d61b1fa43d3638ca5a1bc21"
                         },{
                     "UnActived_script_encode" : "false",
                     "UnActived_script_Name" : "BBB",
                     "UnActived_script_ID" : "17c70e014d61b1fa43d3638ca5a1bc22"
                         }],
"status": 0
}

}
}

参数名称 | 类型 | 必传 | 样例串/默认值 | 说明
Session_ID | string | 是 | 373808cb37bd63f5f7d92415e736e85f | 单个环境ID
Active_Script_ID | string | 是 | Script id | 环境当前已存在的SCRIPT ID