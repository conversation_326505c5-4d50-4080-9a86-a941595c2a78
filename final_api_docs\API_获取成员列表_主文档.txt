# 获取成员列表

## 功能描述
获取所有和此帐户相关联的主帐号与子帐号数据

## API信息

- **Path**: /members
- **Method**: POST
- **Content-Type**: application/json
- **最大请求频率**: 10次/分钟
- **服务器地址**: http://127.0.0.1:8186

## 请求参数

| 参数名称 | 类型 | 必传 | 样例串/默认值 | 说明 |
|---------|------|------|--------------|------|
| Account | string | 是 | <EMAIL> | 用户凭证 Account |

## 请求示例

```json
{
    "Account": "<EMAIL>"
}
```

## 响应示例

```json
{
    "msg": "Success",
    "status": 0,
    "data": "<EMAIL>:0",
    "account_expire_date": "2023-07-26 18:46:48",
    "account_reg_date": "2020-07-29 15:13:28"
}
```

## 响应参数说明

| 参数名称 | 说明 |
|---------|------|
| msg | 返回消息 |
| status | 状态码，0表示成功 |
| data | 账户信息，格式为"账户名:类型"<br>0表示主帐户，1表示子帐户 |
| account_expire_date | 账户过期时间 |
| account_reg_date | 账户注册时间 |

## 使用说明

1. 确保候鸟浏览器客户端已启动并登录
2. 使用有效的Account参数发送请求
3. 返回的data字段包含账户类型信息
4. 可用于验证账户状态和权限
5. 建议使用[POSTMAN调试工具](/api/postman-example)进行接口测试

## 注意事项

1. 请求频率限制：最大10次/分钟
2. 只需要Account参数，不需要APP_ID和APP_KEY
3. 返回的账户类型：0=主账户，1=子账户
4. 可获取账户的注册时间和过期时间信息

## 相关链接

- [POSTMAN调试工具](/api/postman-example)
- [错误码对照表](/api/code)
- [使用须知](/api/help)
