环境管理 - 4、环境管理
=============

API文档: 环境管理 - 4、环境管理
URL: https://www.mbbrowser.com/api/session
抓取时间: 2025-07-28 12:34:10






APInew












# API

• 使用须知
• HTTP模式说明
• 常见问题
• 2、获取成员列表
• 3、环境开启/关闭
• 4、环境管理
• 5、分组管理
• 6、脚本管理
• 7、插件管理
• 8、附录（国家码、时区、语言、系统和分辨率）
• 9、错误码对照表
• POSTMAN调试候鸟API接口
• 调试接口JSON数据官方更新、下载

## 环境管理
1、获取环境列表2、查询指定环境ID的配置数据3、创建环境4、更新环境高级指纹参数5、更新环境6、更新环境代理7、删除环境8、导入Cookie9、导出Cookie10、获取随机UA11、清除环境本地缓存12、查看环境运行状态13、查看环境网页自动运行信息14、添加环境自动运行网页地址15、更新环境某个自动运行网页地址16、删除环境某个自动运行网页地址 使用POSTMAN调试此接口

支持邮箱: <EMAIL>
©MBBROWSER @2025

京ICP备 2020047947号

本系统不提供代理IP服务，禁止用户使用本系统进行任何违法犯罪活动，用户使用本系统带来的任何责任由用户自行承担。

MBbrowser.com  All Rights Reserved. 候鸟防关联浏览器对网站内容拥有最终解释权。
工作日客服(微信)
工作日09-18点

夜间/周末客服(微信)

工作日 18-24点，周末全天

商务(微信)

mbbrowser_official

###### 全国咨询服务热线

400-112-6050
在线咨询

微信咨询

电话咨询

售后咨询