候鸟产品各种操作系统兼容，包括Windows（x64）版本7、8和10。

候鸟浏览器产品定位综述：

候鸟浏览器产品定位综述：

全球店铺商家、广告推广、商业群发及一些从事产品推广的互联网企业，常常试图绕过或禁用其互联网站安全机制的高效商业批量运营为目标，在某些情况下，他们使用模仿合法用户活动的工具来绕过或禁用其目标网站或网络平台安全机制。候鸟浏览器是一款反检测浏览器，是全球同类工具中流行的一款商业产品。

每个网络浏览器都有一个独特的“指纹”，供其他网站使用以验证其合法性。电子商务公司和网络服务平台，通常使用这种指纹来阻止来自以前被认为不安全或参与批量营销的浏览器商业行为。通常店铺商家使用各种虚拟机，代理和VPN服务器的做法并不那么有效，因为各网络平台的验证系统具有识别IP地址和虚拟机的功能。因此，候鸟产品使他们可以动态更改所有Web浏览器会话并生成无限数量的新配置，从而模仿正常用户的活动。

产品潜在用户群体：

渗透测试工作人员

社交媒体专业人士

基于关键字搜索来处理广告的专业人员

赏金操盘者，他们为在线博彩和游戏创建多个帐户，从而从组织者提供的特定交易中获得金钱奖金

隐私权倡导者

同时工作多个账户的人

总结：

根据产品的定位和用户群体，可得知，候鸟产品需要给用户一种极高的安全感，基于这样主打安全理念进行售卖的一款商业安全产品，同时产品不仅在用户和目标网站间具备不可追踪性，同样需要让用户和销售产品方之间，一并具备安全系数高，不可追踪性的安全环境，让用户放心购买，安心使用。

产品运行环境：

候鸟产品各种操作系统兼容，包括Windows（x64）版本7、8和10。

可以安装在本地或远程计算机上，也可以安装在虚拟机（例如VMWare或VirtualBox）上。PC系统的最低要求如下：2xCore 1.7GHz，2Gb RAM。候鸟产品可以下载并安装在多个设备上；但是，一次只允许一个帐户。用户数据存储在会话的云存储中，并且在卸载或重新安装候鸟之后也会保存。

语种支持：

候鸟产品与技术文档暂时仅提供英语和中文界面（语种支持自由实时切换），之后将提供更多的语种来配合商业运营需求。

技术规格（拟定）：

候鸟产品 基于Chromium网络浏览器；产品使用了浏览器核心源代码，并删除了Google启用的所有跟踪功能

在“AES-CBC(128) 加密消息传递”模式下运行

不使用任何隐藏的Google服务

使用AES-CBC（128位）算法加密所有保存的数据

通过各种协议连接到Internet，包括HTTP，SOCKS，SSH，TOR，TOR + SSH和DYNAMIC SOCKS

每个会话都会创建一个新配置，并且用户不需要多个虚拟机

允许同时在多线程模式下使用不同类型的连接

包括内置的专业反检测功能，可以定期更新用户代理的配置，扩展名，语言，地理位置和许多其他参数，这些参数可以实时更改

在每个会话之后保存指纹和cookie文件，从而允许多个用户使用已保存的会话，而无需在虚拟机之间切换

不需要特定设置即可主动，匿名和安全地开始工作

包含带有位置数据库GeoIP2 MaxMind的内置许可证，允许用户立即配置时间和地理位置

WebEmulator，是一种用于以自动操作网站的选项。此功能允许在使用新帐户之前自动在网站之间收集所需的cookie文件。

WebEmulator在多线程模式下在后台运行，允许设置用于访问网站的参数，例如访问页面的数量，花费在每个页面上的时间，暂停和两次访问之间的延迟。WebEmulator在任务完成后启用消息通知。

候鸟允许用户通过WebRTC故意泄漏假IP。该功能在整个会话过程中均已启用，并通过泄漏假IP地址并伪装成合法用户来误导目标组织。它还允许通过Proxifier，Bitvise和Plink进行连接。

当在后台模式下访问网站时复制文本时，WebEmulator提供触摸屏，移动设备，手动和自动输入仿真功能，可模仿真实用户的行为，并提高对此类帐户的信任度。

同行竞争对手产品优势：

林肯球优势：

Automator 一个更新的模块，允许Linken Sphere模仿人类行为绕过CAPTCHA，从而使开发人员可以自动注册新帐户。

保存和加密密码以便于访问的能力

存储和编辑Cookie文件的能力

一种新的本地备份算法，可将Cookie文件保存在活动计算机上，以防止由于互联网连接缓慢或不稳定而丢失。

一种新的人工文本输入仿真机制，使用智能计时算法来击败大多数能够识别自动输入的机器学习反欺诈检测系统。

一种新的用户界面，可在使用远程设备时提高Linken Sphere操作的速度。

会话同步：一种新功能，可创建一个虚拟办公室，使用任何连接的设备访问任何数据，使合作伙伴不仅可以相互之间传输所需的会话，还可以同时使用大量信息来初始化工作，从而显示这些会议的状态和最重要的信息。

候鸟产品网络连接特性：

支持通过“会话”， 将多个用户同时连接到Internet。每个会话可以单独命名。候鸟允许用户在多个设备上工作，并且不依赖于特定的硬件。用户可以在具有不同操作系统的不同设备上使用浏览器，但一次只能使用一个用户名和密码。云端存储访问，允许用户从任何设备启动候鸟产品。

可以通过以下方式分别配置每个会话连接：

无代理模式：直接连接到互联网

Tor：通过Tor浏览器连接

安全套接字外壳（SSH）隧道：通过SSH的Linken Sphere远程部分隧道

Tor + SSH隧道：通过Tor代理运行SSH（加密）隧道

动态SOCKS：通过SOCKS进行SSH动态端口转发不仅允许通过单个端口进行通信，还可以跨一系列端口进行通信。此选项将使SSH充当SOCKS代理服务器

SOCKS5：提供身份验证的互联网协议，因此只有授权用户才能访问同时支持TCP和UDP协议的服务器

超文本传输协议（HTTP）连接

指纹识别

候鸟浏览器支持修改以下指纹（初拟定）：

Canvas：HTML5 Canvas元素的一部分，用于在网页上显示图形，广泛用于根据用户的视频系统功能来识别用户

字体：可用于识别用户的另一个元素

插件：已安装和启用的插件有助于识别用户

音频（声学）指纹：音频指纹的作用是捕获声音的特征，从而使其与其他声音区分开

WebGL：Java API，可在Web浏览器中以3D图形形式工作，而无需使用插件

地理位置：反欺诈系统可以比较用户的IP地址及其实际地理位置

ClientRects：一种使用通过图像缩放获得的哈希来识别用户的方法

Ubercookie（s）：ClientRect和音频指纹的哈希值，可以识别设备

Web实时通信（WebRTC）（包括设备哈希）：一种技术，用于与媒体服务（例如麦克风或摄像机）的设备直接连接，并允许WebRTC绕过VPN和代理服务获得真实的IP地址；摄像头和麦克风有自己的位置指示器，也需要更改

级联样式表（Cascading Style Sheets，CSS）：一种使用CSS技术识别窗口的实际扩展的方法

触摸仿真：一种无需触摸鼠标即可进行触摸屏仿真的方法。将用户代理设置为便携式设备后，反欺诈系统仍可以检测到鼠标光标，因为它仍在显示器上

JS Navigator（JavaScript Windows Navigator）：包含时间，语言和扩展名；与程序信息一起发送的Web浏览器的参数

HTTP标头：主要的浏览器指纹之一，可以通过反欺诈防御来识别用户

域名系统（DNS）：能够为每个会话使用自己的DNS

本地IP地址：一个指示器，有助于显示用户的可能真实位置

上面列出的指纹取决于硬件。如果用户将同一指纹从一台机器转移到另一台机器，则最终指纹将不同。某些指纹（例如WebGL，字体和插件）包含在配置中，而其他指纹（例如Canvas，Audio和ClientRects）没有，但在创建会话时会生成。

同类竞品优势：

Linken Sphere包括大约50,000个设备指纹和一个配置生成器，用于创建其他自定义指纹。PRO和Premium许可证的用户可以在Tenebris Team论坛上访问大约150,000个指纹和13,000个用户代理，这些指纹会定期更新。

候鸟浏览器 产品服务端说明：

产品域名：

WEB 后台运行环境说明：

Linux centos 服务器 + lnmp 运行环境

全局监控平台：webmin

官方网站 /home/<USER>

Web 运营平台说明：

使用高效安全同时开源的Thinkphp完整商业库框架进行运营平台的基础架构与后期建设。

（以下摘录百度百科）

ThinkPHP是为了简化企业级应用开发和敏捷WEB应用开发而诞生的。最早诞生于2006年初，2007年元旦正式更名为ThinkPHP，并且遵循Apache2开源协议发布。ThinkPHP从诞生以来一直秉承简洁实用的设计原则，在保持出色的性能和至简的代码的同时，也注重易用性。并且拥有众多原创功能和特性，在社区团队的积极参与下，在易用性、扩展性和性能方面不断优化和改进。 [1]

ThinkPHP是一个快速、兼容而且简单的轻量级国产PHP开发框架，诞生于2006年初，原名FCS，2007年元旦正式更名为ThinkPHP，遵循Apache2开源协议发布，从Struts结构移植过来并做了改进和完善，同时也借鉴了国外很多优秀的框架和模式，使用面向对象的开发结构和MVC模式，融合了Struts的思想和TagLib（标签库）、RoR的ORM映射和ActiveRecord模式。

ThinkPHP可以支持windows/Unix/Linux等服务器环境，正式版需要PHP5.0以上版本支持，支持MySql、PgSQL、Sqlite多种数据库以及PDO扩展，ThinkPHP框架本身没有什么特别模块要求，具体的应用系统运行环境要求视开发所涉及的模块。

作为一个整体开发解决方案，ThinkPHP能够解决应用开发中的大多数需要，因为其自身包含了底层架构、兼容处理、基类库、数据库访问层、模板引擎、缓存机制、插件机制、角色认证、表单处理等常用的组件，并且对于跨版本、跨平台和跨数据库移植都比较方便。并且每个组件都是精心设计和完善的，应用开发过程仅仅需要关注您的业务逻辑。

（以上摘录百度百科）

候鸟产品流程

章节C/S交互部份详述：


================================================== 表格内容 ==================================================

域名 | 类型 | 备案状态 | 指向位置
MBbrowser.com | 主域名 | 已备案 | 暂定
EHouliao.com | 从域名 | 已备案 | 暂定
www.mbbrowser.com | 官方网站
Admin.MBbrowser.com
产品管理平台,其中包含有（未付费、已付费用户库、订单库、支付接口、客服平台、数据统计平台） | 运维

POST: 域名 + /api/mbbrowser/login

参数：
accounts 邮箱
pwd 密码

lang 语言（zh / en 及可能存在的其他语言）

返回：

{"msg":"登录成功","code":0,"data":{"id":"1","add_time":"2020-07-27 17:15:40","invitecode":"123A","token":"912c42cc8dd9496576e25caeed9cc231"}}
说明：
邮箱格式后端有校验，密码格式目前系统要求长度6-18之内