# API_环境开启关闭_02_关闭环境

## 功能描述
正常关闭指定的浏览器环境

## 所属模块
环境开启关闭

## API信息

- **Path**: /api/v1/browser/stop
- **Method**: POST
- **Content-Type**: application/json
- **服务器地址**: http://127.0.0.1:8186

## 请求参数

| 参数名称       | 类型    | 是否必传 | 示例/默认值                             | 说明       |
|------------|-------|------|-------------------------------------|----------|
| Session_ID | 字符串 | 是    | "373808cb37bd63f5f7d92415e736e85f"  | 环境ID     |

## 请求示例

```json
{
    "Session_ID": "373808cb37bd63f5f7d92415e736e85f"
}
```

## 成功响应

```json
{
    "message": "成功",
    "code": 0,
    "data": {
        "action": "停止环境ID",
        "status": 0
    }
}
```

## 使用说明

1. 确保要关闭的环境当前处于运行状态
2. 支持同时关闭多个环境
3. 正常关闭会保存当前环境状态
4. 如果正常关闭失败，可以使用强制终止功能
5. 建议使用[POSTMAN调试工具](/api/postman-example)进行接口测试

## 注意事项

1. 只能关闭当前正在运行的环境
2. 关闭过程可能需要几秒钟时间
3. 如果环境无响应，建议使用强制终止
4. 关闭后环境的数据会被保存

## 常见错误码

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| -19 | 当前浏览器环境：关闭失败 |
| -22 | 未找到指定的环境SessionId |

## 相关链接

- [强制终止环境](/api/browser/kill)
- [打开环境](/api/browser/start)
- [POSTMAN调试工具](/api/postman-example)
- [错误码对照表](/api/code)
