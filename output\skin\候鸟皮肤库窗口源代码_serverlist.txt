﻿<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Window size="600,400" caption="0,0,0,80" roundcorner="5,5,5,5" showshadow="true" shadowsize="3" shadowposition="0,0" shadowcolor="#ff6272A4" fademode="true">
	<Include source="Default.xml" />

  <VerticalLayout height="400" bkcolor="#FF282A36">
    <HorizontalLayout height="37">

    	 <Button name="login" bkimage="icon.png" padding="8,6,0,0" width="28" height="28"/>
      <Label name="session_list_title" padding="6,4,0,0" width="320" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" align="left" font="14"></Label>

      <Control />
      <!--<Button name="minbtn" width="28" height="26" tooltip="最小化" normalimage="minbtn.png" hotimage="minbtn_hover.png" pushedimage="minbtnpush.png" />-->
      <Button name="closebtn" width="28" height="26"  tooltip="关闭" normalimage="closebtn.png" hotimage="closebtn_hover.png" pushedimage="closebtnpush.png" />

    </HorizontalLayout>

    <HorizontalLayout name="loading_data" bkcolor="#ffe9e9e9" visible="true">

      <VerticalLayout >

        <HorizontalLayout height="200">
          <Control />
          <GifAnim bkimage="dataloading.gif" height="200" width="200" padding="0,0,0,0" auto="true"/>
          <Control />
        </HorizontalLayout>


        <HorizontalLayout height="30" >
          <Control />
          <Label name="data_percent" text="0%" width="300" textcolor="#FF616161" hottextcolor="#ff000000" align="center" font="10"></Label>
          <Control />
        </HorizontalLayout>
        <!--<HorizontalLayout width="553" height="30" >
                <Control />
                <Label name="file_percent" text="" width="300" textcolor="#FF616161" hottextcolor="#ff000000" align="center"></Label>
                <Control />
              </HorizontalLayout>-->
        <HorizontalLayout height="60" >
          <Control />
          <Label name="process_description" text="正在加载数据信息..   请稍侯.." width="550" textcolor="#FF616161" hottextcolor="#ff000000" align="center" font="8"></Label>
          <Control />
        </HorizontalLayout>

      </VerticalLayout>

    </HorizontalLayout>

    <HorizontalLayout name="data" visible="false">


		<VerticalLayout  height="360">


			<HorizontalLayout height="30" >
        <Label name="msg1" padding="20,0,0,0" text="请选择候鸟客户端连接最快的候鸟服务器节点" autocalcwidth="true" textcolor="#FFF8F8F2" font="4" hottextcolor="#FF8BE9FD"></Label>
        <Label name="msg2" text="候鸟客户端" autocalcwidth="true" textcolor="#FFBD93F9" font="4" visible="false"></Label>
        <Label name="msg3" text="连接最快的候鸟服务器节点" autocalcwidth="true" textcolor="#FFF8F8F2" font="4" visible="false" hottextcolor="#FF8BE9FD"></Label>
			</HorizontalLayout>


      <HorizontalLayout inset="0,0,0,0" bkcolor="#FF282A36">
        	<List name="list_session_manager" vscrollbar="true" itembkcolor="#FF282A36" itemselectedbkcolor="#FF44475A" itemhotbkcolor="#FF6272A4" bordercolor="#FF6272A4">
            <ListHeader height="36" bordersize="1" bordercolor="#FF6272A4" bkcolor="#FF44475A">

              <ListHeaderItem name="backupheadername1" text="线路名称" width="280" align="left"  texttooltip="true" endellipsis="true" textpadding="20,0,0,0" sepimage="split.png" sepwidth="2" font="7"></ListHeaderItem>
              <ListHeaderItem name="backupheadername2" text="线路速率" width="80" align="center" textpadding="0,0,0,0" sepimage="split.png" sepwidth="2" font="7"></ListHeaderItem>
              <ListHeaderItem name="backupheadername3" text="测速时间" width="150" align="left" textpadding="20,0,0,0" sepimage="split.png" sepwidth="2" font="7"></ListHeaderItem>
              <ListHeaderItem name="backupheadername4"  width="100" align="left" textpadding="20,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>

            </ListHeader>

          </List>
				</HorizontalLayout>

      <HorizontalLayout height="40" bkcolor="#ffe9e9e9" visible="false">
        <Label name="status" textcolor="#FF6e9bf6" align="center" font="18"></Label>
      </HorizontalLayout>

      <HorizontalLayout height="52" bkcolor="#ffe9e9e9">
        <!--<Control width="18"/>
        <VerticalLayout width="140">
          <Control />
          <Button text="测试速率" name="speedbtn" enabled="false" padding="0,2,0,0" width="120" height="30" texttooltip="true" endellipsis="true" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	bkcolor="#FFffffff" bordersize="1" bordercolor="#ffb3b3b3" borderround="8,8" hotbkcolor="#fff1f2f1"/>
          <Control />
        </VerticalLayout>
        <Control width="170"/>-->
        <Control />
        <VerticalLayout name="hostsarea" width="66">

          <CheckBox name="opt_checkhosts" text="HOSTS" textpadding="64,1,0,0" selected="false"  padding="0,20,0,0" height="18" width="16" normalimage="file='list_check_normal.png'" hotimage="list_check_hover.png" selectedimage="file='list_checked_actvice_normal.png'" ></CheckBox>

        </VerticalLayout>
        <VerticalLayout width="130">
          <Control />
          <Button text="确定" name="okbtn"  enabled="false" padding="10,2,0,0" width="120" height="30" texttooltip="true" endellipsis="true" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	bkcolor="#FFffffff" bordersize="1" bordercolor="#ffb3b3b3" borderround="8,8" hotbkcolor="#fff1f2f1"/>
          <Control />
        </VerticalLayout>
        <Control width="1"/>
        <VerticalLayout width="130">
          <Control />
          <Button text="取消" name="cancelbtn"  padding="10,2,0,0" width="120" height="30" texttooltip="true" endellipsis="true" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	bkcolor="#FFffffff" bordersize="1" bordercolor="#ffb3b3b3" borderround="8,8" hotbkcolor="#fff1f2f1" />
          <Control />
        </VerticalLayout>

        <Control width="12"/>
      </HorizontalLayout>

		</VerticalLayout>

	</HorizontalLayout>

  </VerticalLayout>
</Window>
