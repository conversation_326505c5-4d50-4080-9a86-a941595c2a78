标题: 团队协作版添加子账户
英文标题: Team Collaboration Edition Add Sub Account
ID: 61
分类ID: 7
添加时间: **********
更新时间: **********
访问次数: 0
SEO标题: 候鸟浏览器团队协作版添加子账户
SEO关键词: 候鸟浏览器团队协作版添加子账户
SEO描述: 候鸟浏览器团队协作版添加子账户

================================================== 内容 ==================================================
首先确认您购买的是套餐是《团队协作版》，如果不是请到购买页面（ https://www.mbbrowser.com/console/console/shop ）进行升级。

1、打开候鸟官网控制台（ https://www.mbbrowser.com/console/console ），点击左侧“子账户”链接，然后点击右侧“添加子账户”按钮；

![](6d7b2882624511f09a0d0242ac130006/images/image_f1b63d12a495.png)

2、在添加子账户窗口中填入子账户的账号（建议用采用邮箱格式，以便日后找回）和密码，点击“提交”按钮提交即添加完成；

![](6d7b2882624511f09a0d0242ac130006/images/image_dbef8dd20090.png)


3、添加完成后可以在列表中看到所有子账户的信息（ID、账户名、创建时间、环境数量等信息）；

![](6d7b2882624511f09a0d0242ac130006/images/image_0ecd4411a6f1.png)

4、主账户可以通过控制台对旗下子账户进行删除及环境查看；

![](6d7b2882624511f09a0d0242ac130006/images/image_857280b13ffa.png)

5、点击查看链接，可以看到该子账号创建的所有浏览器环境信息；

![](6d7b2882624511f09a0d0242ac130006/images/image_3001b0348cf4.png)

6、如果子账户的环境信息没有及时更新，请点击“刷新列表”按钮；

![](6d7b2882624511f09a0d0242ac130006/images/image_818f1572472a.png)

7、如要删除子账户，在子账户列表处点击“删除”链接，然后点击“确认”即可删除该子账户（注意：删除子账户可能将该子账户下浏览器环境一并删除，请及时转移备份）。![](6d7b2882624511f09a0d0242ac130006/images/image_8ab75822fc5d.png)

================================================== 英文内容 ==================================================
First to confirm you buy package is "Team Collaboration Version", if not, please upgrade to purchase page (https://www.mbbrowser.com/console/console/shop).

1. Open the mbbrowser website console (https://www.mbbrowser.com/console/console), click on "Sub Account" link on the left, then click "Add Account" button on the right side.

![](6d7b2882624511f09a0d0242ac130006/images/image_1dd4613ed87b.png)

2. In the window of adding sub-accounts, fill in the account number of sub-accounts (email format is recommended for later retrieval) and password, and click "Submit" button to complete the adding;

![](6d7b2882624511f09a0d0242ac130006/images/image_f00fa7bba505.png)


3. After the subaccount is added, you can view the information about all subaccounts (such as ID, account name, creation time, and number of environments) in the list.

![](6d7b2882624511f09a0d0242ac130006/images/image_794ef9f0904f.png)

4. The main account can delete its sub-accounts and view the environment through the console;

![](6d7b2882624511f09a0d0242ac130006/images/image_3e9338b797c7.png)

5. Click the link to view all the browser environment information created by the subaccount.

![](6d7b2882624511f09a0d0242ac130006/images/image_25a6a0a06689.png)

6. If the environment information of the sub-account is not updated in time, please click the "Refresh List" button;

![](6d7b2882624511f09a0d0242ac130006/images/image_0e11046faaff.png)

7、To delete a subaccount, click the "Delete" link in the subaccount list, and then click "OK" to delete the subaccount (note: Deleting a subaccount may delete the browser environment under the subaccount, please transfer the backup in time).

![](6d7b2882624511f09a0d0242ac130006/images/image_d022cb04ccdc.png)