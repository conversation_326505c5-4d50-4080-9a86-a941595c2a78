#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTML to DOCX Converter
将HTML文件批量转换为DOCX文件
"""

import os
import glob
from pathlib import Path
from bs4 import BeautifulSoup
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.style import WD_STYLE_TYPE
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
import re

def setup_document_styles(doc):
    """设置文档样式"""
    # 标题1样式
    try:
        heading1 = doc.styles['Heading 1']
        heading1.font.size = Pt(18)
        heading1.font.bold = True
    except:
        pass
    
    # 标题2样式
    try:
        heading2 = doc.styles['Heading 2']
        heading2.font.size = Pt(16)
        heading2.font.bold = True
    except:
        pass
    
    # 标题3样式
    try:
        heading3 = doc.styles['Heading 3']
        heading3.font.size = Pt(14)
        heading3.font.bold = True
    except:
        pass
    
    # 正文样式
    try:
        normal = doc.styles['Normal']
        normal.font.size = Pt(12)
    except:
        pass

def clean_text(text):
    """清理文本内容"""
    if not text:
        return ""
    
    # 移除多余的空白字符
    text = re.sub(r'\s+', ' ', text.strip())
    # 移除HTML实体
    text = text.replace('&nbsp;', ' ')
    text = text.replace('&lt;', '<')
    text = text.replace('&gt;', '>')
    text = text.replace('&amp;', '&')
    
    return text

def add_table_to_doc(doc, table_element):
    """将HTML表格转换为DOCX表格"""
    rows = table_element.find_all('tr')
    if not rows:
        return
    
    # 计算列数
    max_cols = 0
    for row in rows:
        cols = len(row.find_all(['td', 'th']))
        max_cols = max(max_cols, cols)
    
    if max_cols == 0:
        return
    
    # 创建表格
    docx_table = doc.add_table(rows=len(rows), cols=max_cols)
    docx_table.style = 'Table Grid'
    
    # 填充表格内容
    for i, row in enumerate(rows):
        cells = row.find_all(['td', 'th'])
        for j, cell in enumerate(cells):
            if j < max_cols:
                cell_text = clean_text(cell.get_text())
                docx_table.cell(i, j).text = cell_text
                
                # 如果是表头，设置粗体
                if cell.name == 'th':
                    for paragraph in docx_table.cell(i, j).paragraphs:
                        for run in paragraph.runs:
                            run.bold = True

def process_element(doc, element, level=0):
    """递归处理HTML元素"""
    if element.name is None:
        # 文本节点
        text = clean_text(element.string)
        if text:
            doc.add_paragraph(text)
        return
    
    tag_name = element.name.lower()
    
    # 处理标题
    if tag_name in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
        heading_level = int(tag_name[1])
        text = clean_text(element.get_text())
        if text:
            if heading_level <= 3:
                doc.add_heading(text, level=heading_level)
            else:
                p = doc.add_paragraph(text)
                p.style = 'Heading 3'
    
    # 处理段落
    elif tag_name == 'p':
        text = clean_text(element.get_text())
        if text:
            doc.add_paragraph(text)
    
    # 处理列表
    elif tag_name in ['ul', 'ol']:
        for li in element.find_all('li', recursive=False):
            text = clean_text(li.get_text())
            if text:
                p = doc.add_paragraph(text, style='List Bullet' if tag_name == 'ul' else 'List Number')
    
    # 处理表格
    elif tag_name == 'table':
        add_table_to_doc(doc, element)
    
    # 处理代码块
    elif tag_name in ['pre', 'code']:
        text = element.get_text()
        if text:
            p = doc.add_paragraph(text)
            # 设置等宽字体
            for run in p.runs:
                run.font.name = 'Courier New'
    
    # 处理div和其他容器
    elif tag_name in ['div', 'section', 'article', 'main']:
        for child in element.children:
            process_element(doc, child, level + 1)
    
    # 处理其他文本元素
    elif tag_name in ['span', 'strong', 'b', 'em', 'i']:
        text = clean_text(element.get_text())
        if text:
            p = doc.add_paragraph(text)
            if tag_name in ['strong', 'b']:
                for run in p.runs:
                    run.bold = True
            elif tag_name in ['em', 'i']:
                for run in p.runs:
                    run.italic = True

def convert_html_to_docx(html_file_path, output_dir):
    """将单个HTML文件转换为DOCX"""
    try:
        # 读取HTML文件
        with open(html_file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # 解析HTML
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 创建新的DOCX文档
        doc = Document()
        setup_document_styles(doc)
        
        # 添加文档标题
        title = soup.find('title')
        if title:
            doc.add_heading(clean_text(title.get_text()), 0)
        else:
            filename = Path(html_file_path).stem
            doc.add_heading(filename.replace('-', ' ').title(), 0)
        
        # 处理body内容
        body = soup.find('body')
        if body:
            for element in body.children:
                process_element(doc, element)
        else:
            # 如果没有body标签，处理整个文档
            for element in soup.children:
                process_element(doc, element)
        
        # 生成输出文件名
        input_filename = Path(html_file_path).stem
        output_filename = f"{input_filename}.docx"
        output_path = os.path.join(output_dir, output_filename)
        
        # 保存DOCX文件
        doc.save(output_path)
        print(f"✅ 转换完成: {html_file_path} -> {output_path}")
        return True
        
    except Exception as e:
        print(f"❌ 转换失败: {html_file_path} - 错误: {str(e)}")
        return False

def batch_convert_html_to_docx(input_dir, output_dir=None):
    """批量转换HTML文件为DOCX"""
    input_path = Path(input_dir)
    
    if output_dir is None:
        output_dir = input_path / "docx_output"
    else:
        output_dir = Path(output_dir)
    
    # 创建输出目录
    output_dir.mkdir(exist_ok=True)
    
    # 查找所有HTML文件
    html_files = list(input_path.glob("*.html"))
    
    if not html_files:
        print(f"❌ 在目录 {input_dir} 中没有找到HTML文件")
        return
    
    print(f"📁 找到 {len(html_files)} 个HTML文件")
    print(f"📁 输出目录: {output_dir}")
    print("-" * 50)
    
    success_count = 0
    total_count = len(html_files)
    
    # 逐个转换文件
    for html_file in html_files:
        if convert_html_to_docx(str(html_file), str(output_dir)):
            success_count += 1
    
    print("-" * 50)
    print(f"🎉 转换完成! 成功: {success_count}/{total_count}")
    
    if success_count < total_count:
        print(f"⚠️  有 {total_count - success_count} 个文件转换失败")

if __name__ == "__main__":
    # 设置输入和输出目录
    input_directory = r"F:\augment\output"
    output_directory = r"F:\augment\output\docx_files"
    
    print("🚀 开始HTML到DOCX批量转换...")
    print(f"📂 输入目录: {input_directory}")
    print(f"📂 输出目录: {output_directory}")
    print("=" * 60)
    
    batch_convert_html_to_docx(input_directory, output_directory)
