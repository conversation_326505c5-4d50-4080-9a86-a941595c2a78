2025-07-28 12:32:20,207 - INFO - 开始抓取API文档页面...
2025-07-28 12:32:20,208 - INFO - 正在处理第 1/18 个页面: 简介
2025-07-28 12:32:20,208 - INFO - 正在获取页面: https://www.mbbrowser.com/api/
2025-07-28 12:32:21,928 - INFO - 在页面 简介 中找到 9 个子章节
2025-07-28 12:32:21,929 - INFO - 页面 简介 包含 9 个子章节，将分别保存
2025-07-28 12:32:21,933 - INFO - 已保存TXT文件: API_简介_1_帐号登录.txt
2025-07-28 12:32:22,937 - INFO - 已保存TXT文件: API_简介_2_获取成员列表.txt
2025-07-28 12:32:23,941 - INFO - 已保存TXT文件: API_简介_3_环境开启_关闭.txt
2025-07-28 12:32:24,945 - INFO - 已保存TXT文件: API_简介_4_环境管理.txt
2025-07-28 12:32:25,950 - INFO - 已保存TXT文件: API_简介_5_分组管理.txt
2025-07-28 12:32:26,956 - INFO - 已保存TXT文件: API_简介_6_脚本管理.txt
2025-07-28 12:32:27,959 - INFO - 已保存TXT文件: API_简介_7_插件管理.txt
2025-07-28 12:32:28,963 - INFO - 已保存TXT文件: API_简介_8_附录（国家码、时区、语言、系统和分辨率）.txt
2025-07-28 12:32:29,966 - INFO - 已保存TXT文件: API_简介_9_错误码对照表.txt
2025-07-28 12:32:33,968 - INFO - 正在处理第 2/18 个页面: 使用须知
2025-07-28 12:32:33,969 - INFO - 正在获取页面: https://www.mbbrowser.com/api/help
2025-07-28 12:32:34,937 - INFO - 在页面 使用须知 中找到 12 个子章节
2025-07-28 12:32:34,937 - INFO - 页面 使用须知 包含 12 个子章节，将分别保存
2025-07-28 12:32:34,940 - INFO - 已保存TXT文件: API_使用须知_1_帐号登录.txt
2025-07-28 12:32:35,944 - INFO - 已保存TXT文件: API_使用须知_2_获取成员列表.txt
2025-07-28 12:32:36,950 - INFO - 已保存TXT文件: API_使用须知_3_环境开启_关闭.txt
2025-07-28 12:32:37,954 - INFO - 已保存TXT文件: API_使用须知_4_环境管理.txt
2025-07-28 12:32:38,957 - INFO - 已保存TXT文件: API_使用须知_5_分组管理.txt
2025-07-28 12:32:39,968 - INFO - 已保存TXT文件: API_使用须知_6_脚本管理.txt
2025-07-28 12:32:40,973 - INFO - 已保存TXT文件: API_使用须知_7_插件管理.txt
2025-07-28 12:32:41,977 - INFO - 已保存TXT文件: API_使用须知_8_附录（国家码、时区、语言、系统和分辨率）.txt
2025-07-28 12:32:42,981 - INFO - 已保存TXT文件: API_使用须知_9_错误码对照表.txt
2025-07-28 12:32:43,982 - INFO - 已保存TXT文件: API_使用须知_1_获取API凭证.txt
2025-07-28 12:32:44,984 - INFO - 已保存TXT文件: API_使用须知_2_查看API凭证.txt
2025-07-28 12:32:45,985 - INFO - 已保存TXT文件: API_使用须知_2_获取环境ID.txt
2025-07-28 12:32:49,987 - INFO - 正在处理第 3/18 个页面: HTTP模式说明
2025-07-28 12:32:49,988 - INFO - 正在获取页面: https://www.mbbrowser.com/api/http
2025-07-28 12:32:50,952 - INFO - 在页面 HTTP模式说明 中找到 12 个子章节
2025-07-28 12:32:50,952 - INFO - 页面 HTTP模式说明 包含 12 个子章节，将分别保存
2025-07-28 12:32:50,955 - INFO - 已保存TXT文件: API_HTTP模式说明_1_帐号登录.txt
2025-07-28 12:32:51,958 - INFO - 已保存TXT文件: API_HTTP模式说明_2_获取成员列表.txt
2025-07-28 12:32:52,963 - INFO - 已保存TXT文件: API_HTTP模式说明_3_环境开启_关闭.txt
2025-07-28 12:32:53,967 - INFO - 已保存TXT文件: API_HTTP模式说明_4_环境管理.txt
2025-07-28 12:32:54,973 - INFO - 已保存TXT文件: API_HTTP模式说明_5_分组管理.txt
2025-07-28 12:32:55,981 - INFO - 已保存TXT文件: API_HTTP模式说明_6_脚本管理.txt
2025-07-28 12:32:56,986 - INFO - 已保存TXT文件: API_HTTP模式说明_7_插件管理.txt
2025-07-28 12:32:57,990 - INFO - 已保存TXT文件: API_HTTP模式说明_8_附录（国家码、时区、语言、系统和分辨率）.txt
2025-07-28 12:32:58,992 - INFO - 已保存TXT文件: API_HTTP模式说明_9_错误码对照表.txt
2025-07-28 12:32:59,993 - INFO - 已保存TXT文件: API_HTTP模式说明_1_以管理员身份运行CMD或者PowerShell，并确保终端在候鸟浏览器主目录打开，或已进入候鸟浏览器主路径.txt
2025-07-28 12:33:00,994 - INFO - 已保存TXT文件: API_HTTP模式说明_2_启动成功过后在命令行工具可以看到API地址.txt
2025-07-28 12:33:01,996 - INFO - 已保存TXT文件: API_HTTP模式说明_3_CLI命令行参数介绍.txt
2025-07-28 12:33:05,998 - INFO - 正在处理第 4/18 个页面: 常见问题
2025-07-28 12:33:05,998 - INFO - 正在获取页面: https://www.mbbrowser.com/api/question
2025-07-28 12:33:06,959 - INFO - 在页面 常见问题 中找到 12 个子章节
2025-07-28 12:33:06,959 - INFO - 页面 常见问题 包含 12 个子章节，将分别保存
2025-07-28 12:33:06,962 - INFO - 已保存TXT文件: API_常见问题_1_帐号登录.txt
2025-07-28 12:33:07,968 - INFO - 已保存TXT文件: API_常见问题_2_获取成员列表.txt
2025-07-28 12:33:08,975 - INFO - 已保存TXT文件: API_常见问题_3_环境开启_关闭.txt
2025-07-28 12:33:09,982 - INFO - 已保存TXT文件: API_常见问题_4_环境管理.txt
2025-07-28 12:33:10,989 - INFO - 已保存TXT文件: API_常见问题_5_分组管理.txt
2025-07-28 12:33:11,996 - INFO - 已保存TXT文件: API_常见问题_6_脚本管理.txt
2025-07-28 12:33:13,004 - INFO - 已保存TXT文件: API_常见问题_7_插件管理.txt
2025-07-28 12:33:14,011 - INFO - 已保存TXT文件: API_常见问题_8_附录（国家码、时区、语言、系统和分辨率）.txt
2025-07-28 12:33:15,013 - INFO - 已保存TXT文件: API_常见问题_9_错误码对照表.txt
2025-07-28 12:33:16,015 - INFO - 已保存TXT文件: API_常见问题_1_通过命令行启动客户端报错.txt
2025-07-28 12:33:17,016 - INFO - 已保存TXT文件: API_常见问题_2_调用API是否可以同时手动打开客户端.txt
2025-07-28 12:33:18,019 - INFO - 已保存TXT文件: API_常见问题_3_打开环境失败.txt
2025-07-28 12:33:22,021 - INFO - 正在处理第 5/18 个页面: 帐号登录
2025-07-28 12:33:22,021 - INFO - 正在获取页面: https://www.mbbrowser.com/api/login
2025-07-28 12:33:22,993 - INFO - 在页面 帐号登录 中找到 11 个子章节
2025-07-28 12:33:22,994 - INFO - 页面 帐号登录 包含 11 个子章节，将分别保存
2025-07-28 12:33:23,000 - INFO - 已保存TXT文件: API_帐号登录_1_帐号登录.txt
2025-07-28 12:33:24,005 - INFO - 已保存TXT文件: API_帐号登录_2_获取成员列表.txt
2025-07-28 12:33:25,009 - INFO - 已保存TXT文件: API_帐号登录_3_环境开启_关闭.txt
2025-07-28 12:33:26,013 - INFO - 已保存TXT文件: API_帐号登录_4_环境管理.txt
2025-07-28 12:33:27,020 - INFO - 已保存TXT文件: API_帐号登录_5_分组管理.txt
2025-07-28 12:33:28,030 - INFO - 已保存TXT文件: API_帐号登录_6_脚本管理.txt
2025-07-28 12:33:29,034 - INFO - 已保存TXT文件: API_帐号登录_7_插件管理.txt
2025-07-28 12:33:30,041 - INFO - 已保存TXT文件: API_帐号登录_8_附录（国家码、时区、语言、系统和分辨率）.txt
2025-07-28 12:33:31,043 - INFO - 已保存TXT文件: API_帐号登录_9_错误码对照表.txt
2025-07-28 12:33:32,044 - INFO - 已保存TXT文件: API_帐号登录_1_候鸟浏览器支持本地API的功能，帮助用户通过程序化的方式来启动和关闭浏览器等基础API功能，还可以配合Selenium和Puppeteer等自动化框架来实现浏览器操作的自动化。.txt
2025-07-28 12:33:33,045 - INFO - 已保存TXT文件: API_帐号登录_2_仅支持客户端V3.9.2.114以上版本，请下载客户端最新版本.txt
2025-07-28 12:33:37,047 - INFO - 正在处理第 6/18 个页面: 获取成员列表
2025-07-28 12:33:37,047 - INFO - 正在获取页面: https://www.mbbrowser.com/api/members
2025-07-28 12:33:38,016 - INFO - 在页面 获取成员列表 中找到 9 个子章节
2025-07-28 12:33:38,016 - INFO - 页面 获取成员列表 包含 9 个子章节，将分别保存
2025-07-28 12:33:38,019 - INFO - 已保存TXT文件: API_获取成员列表_1_帐号登录.txt
2025-07-28 12:33:39,023 - INFO - 已保存TXT文件: API_获取成员列表_2_获取成员列表.txt
2025-07-28 12:33:40,030 - INFO - 已保存TXT文件: API_获取成员列表_3_环境开启_关闭.txt
2025-07-28 12:33:41,039 - INFO - 已保存TXT文件: API_获取成员列表_4_环境管理.txt
2025-07-28 12:33:42,047 - INFO - 已保存TXT文件: API_获取成员列表_5_分组管理.txt
2025-07-28 12:33:43,052 - INFO - 已保存TXT文件: API_获取成员列表_6_脚本管理.txt
2025-07-28 12:33:44,058 - INFO - 已保存TXT文件: API_获取成员列表_7_插件管理.txt
2025-07-28 12:33:45,062 - INFO - 已保存TXT文件: API_获取成员列表_8_附录（国家码、时区、语言、系统和分辨率）.txt
2025-07-28 12:33:46,064 - INFO - 已保存TXT文件: API_获取成员列表_9_错误码对照表.txt
2025-07-28 12:33:50,065 - INFO - 正在处理第 7/18 个页面: 环境开启关闭
2025-07-28 12:33:50,066 - INFO - 正在获取页面: https://www.mbbrowser.com/api/browser
2025-07-28 12:33:51,089 - INFO - 在页面 环境开启关闭 中找到 12 个子章节
2025-07-28 12:33:51,089 - INFO - 页面 环境开启关闭 包含 12 个子章节，将分别保存
2025-07-28 12:33:51,096 - INFO - 已保存TXT文件: API_环境开启关闭_1_帐号登录.txt
2025-07-28 12:33:52,104 - INFO - 已保存TXT文件: API_环境开启关闭_2_获取成员列表.txt
2025-07-28 12:33:53,111 - INFO - 已保存TXT文件: API_环境开启关闭_3_环境开启_关闭.txt
2025-07-28 12:33:54,119 - INFO - 已保存TXT文件: API_环境开启关闭_4_环境管理.txt
2025-07-28 12:33:55,127 - INFO - 已保存TXT文件: API_环境开启关闭_5_分组管理.txt
2025-07-28 12:33:56,130 - INFO - 已保存TXT文件: API_环境开启关闭_6_脚本管理.txt
2025-07-28 12:33:57,135 - INFO - 已保存TXT文件: API_环境开启关闭_7_插件管理.txt
2025-07-28 12:33:58,142 - INFO - 已保存TXT文件: API_环境开启关闭_8_附录（国家码、时区、语言、系统和分辨率）.txt
2025-07-28 12:33:59,145 - INFO - 已保存TXT文件: API_环境开启关闭_9_错误码对照表.txt
2025-07-28 12:34:00,150 - INFO - 已保存TXT文件: API_环境开启关闭_1_打开环境.txt
2025-07-28 12:34:01,154 - INFO - 已保存TXT文件: API_环境开启关闭_2_关闭环境.txt
2025-07-28 12:34:02,156 - INFO - 已保存TXT文件: API_环境开启关闭_3_强制终止环境.txt
2025-07-28 12:34:06,158 - INFO - 正在处理第 8/18 个页面: 环境管理
2025-07-28 12:34:06,159 - INFO - 正在获取页面: https://www.mbbrowser.com/api/session
2025-07-28 12:34:07,132 - INFO - 在页面 环境管理 中找到 25 个子章节
2025-07-28 12:34:07,133 - INFO - 页面 环境管理 包含 25 个子章节，将分别保存
2025-07-28 12:34:07,135 - INFO - 已保存TXT文件: API_环境管理_1_帐号登录.txt
2025-07-28 12:34:08,139 - INFO - 已保存TXT文件: API_环境管理_2_获取成员列表.txt
2025-07-28 12:34:09,144 - INFO - 已保存TXT文件: API_环境管理_3_环境开启_关闭.txt
2025-07-28 12:34:10,147 - INFO - 已保存TXT文件: API_环境管理_4_环境管理.txt
2025-07-28 12:34:11,150 - INFO - 已保存TXT文件: API_环境管理_5_分组管理.txt
2025-07-28 12:34:12,155 - INFO - 已保存TXT文件: API_环境管理_6_脚本管理.txt
2025-07-28 12:34:13,162 - INFO - 已保存TXT文件: API_环境管理_7_插件管理.txt
2025-07-28 12:34:14,166 - INFO - 已保存TXT文件: API_环境管理_8_附录（国家码、时区、语言、系统和分辨率）.txt
2025-07-28 12:34:15,168 - INFO - 已保存TXT文件: API_环境管理_9_错误码对照表.txt
2025-07-28 12:34:16,175 - INFO - 已保存TXT文件: API_环境管理_1_获取环境列表.txt
2025-07-28 12:34:17,179 - INFO - 已保存TXT文件: API_环境管理_2_查询指定环境ID的配置数据.txt
2025-07-28 12:34:18,184 - INFO - 已保存TXT文件: API_环境管理_3_创建环境.txt
2025-07-28 12:34:19,188 - INFO - 已保存TXT文件: API_环境管理_4_更新环境高级指纹参数.txt
2025-07-28 12:34:20,191 - INFO - 已保存TXT文件: API_环境管理_5_更新环境.txt
2025-07-28 12:34:21,196 - INFO - 已保存TXT文件: API_环境管理_6_更新环境代理.txt
2025-07-28 12:34:22,199 - INFO - 已保存TXT文件: API_环境管理_7_删除环境.txt
2025-07-28 12:34:23,204 - INFO - 已保存TXT文件: API_环境管理_8_导入Cookie.txt
2025-07-28 12:34:24,208 - INFO - 已保存TXT文件: API_环境管理_9_导出Cookie.txt
2025-07-28 12:34:25,212 - INFO - 已保存TXT文件: API_环境管理_10_获取随机UA.txt
2025-07-28 12:34:26,217 - INFO - 已保存TXT文件: API_环境管理_11_清除环境本地缓存.txt
2025-07-28 12:34:27,225 - INFO - 已保存TXT文件: API_环境管理_12_查看环境运行状态.txt
2025-07-28 12:34:28,232 - INFO - 已保存TXT文件: API_环境管理_13_查看环境网页自动运行信息.txt
2025-07-28 12:34:29,235 - INFO - 已保存TXT文件: API_环境管理_14_添加环境自动运行网页地址.txt
2025-07-28 12:34:30,243 - INFO - 已保存TXT文件: API_环境管理_15_更新环境某个自动运行网页地址.txt
2025-07-28 12:34:31,244 - INFO - 已保存TXT文件: API_环境管理_16_删除环境某个自动运行网页地址.txt
2025-07-28 12:34:35,246 - INFO - 正在处理第 9/18 个页面: 分组管理
2025-07-28 12:34:35,247 - INFO - 正在获取页面: https://www.mbbrowser.com/api/group
2025-07-28 12:34:36,217 - INFO - 在页面 分组管理 中找到 13 个子章节
2025-07-28 12:34:36,217 - INFO - 页面 分组管理 包含 13 个子章节，将分别保存
2025-07-28 12:34:36,223 - INFO - 已保存TXT文件: API_分组管理_1_帐号登录.txt
2025-07-28 12:34:37,226 - INFO - 已保存TXT文件: API_分组管理_2_获取成员列表.txt
2025-07-28 12:34:38,230 - INFO - 已保存TXT文件: API_分组管理_3_环境开启_关闭.txt
2025-07-28 12:34:39,235 - INFO - 已保存TXT文件: API_分组管理_4_环境管理.txt
2025-07-28 12:34:40,242 - INFO - 已保存TXT文件: API_分组管理_5_分组管理.txt
2025-07-28 12:34:41,245 - INFO - 已保存TXT文件: API_分组管理_6_脚本管理.txt
2025-07-28 12:34:42,249 - INFO - 已保存TXT文件: API_分组管理_7_插件管理.txt
2025-07-28 12:34:43,257 - INFO - 已保存TXT文件: API_分组管理_8_附录（国家码、时区、语言、系统和分辨率）.txt
2025-07-28 12:34:44,260 - INFO - 已保存TXT文件: API_分组管理_9_错误码对照表.txt
2025-07-28 12:34:45,266 - INFO - 已保存TXT文件: API_分组管理_1_获取环境分组列表.txt
2025-07-28 12:34:46,274 - INFO - 已保存TXT文件: API_分组管理_2_新建环境分组.txt
2025-07-28 12:34:47,277 - INFO - 已保存TXT文件: API_分组管理_3_删除环境分组.txt
2025-07-28 12:34:48,280 - INFO - 已保存TXT文件: API_分组管理_4_将指定环境从指定分组转移到另一个分组.txt
2025-07-28 12:34:52,282 - INFO - 正在处理第 10/18 个页面: 脚本管理
2025-07-28 12:34:52,283 - INFO - 正在获取页面: https://www.mbbrowser.com/api/script
2025-07-28 12:34:53,252 - INFO - 在页面 脚本管理 中找到 14 个子章节
2025-07-28 12:34:53,252 - INFO - 页面 脚本管理 包含 14 个子章节，将分别保存
2025-07-28 12:34:53,258 - INFO - 已保存TXT文件: API_脚本管理_1_帐号登录.txt
2025-07-28 12:34:54,265 - INFO - 已保存TXT文件: API_脚本管理_2_获取成员列表.txt
2025-07-28 12:34:55,271 - INFO - 已保存TXT文件: API_脚本管理_3_环境开启_关闭.txt
2025-07-28 12:34:56,278 - INFO - 已保存TXT文件: API_脚本管理_4_环境管理.txt
2025-07-28 12:34:57,285 - INFO - 已保存TXT文件: API_脚本管理_5_分组管理.txt
2025-07-28 12:34:58,292 - INFO - 已保存TXT文件: API_脚本管理_6_脚本管理.txt
2025-07-28 12:34:59,299 - INFO - 已保存TXT文件: API_脚本管理_7_插件管理.txt
2025-07-28 12:35:00,306 - INFO - 已保存TXT文件: API_脚本管理_8_附录（国家码、时区、语言、系统和分辨率）.txt
2025-07-28 12:35:01,309 - INFO - 已保存TXT文件: API_脚本管理_9_错误码对照表.txt
2025-07-28 12:35:02,316 - INFO - 已保存TXT文件: API_脚本管理_1_查询、列出指定环境中的所有脚本.txt
2025-07-28 12:35:03,322 - INFO - 已保存TXT文件: API_脚本管理_2_切换指定环境已激活脚本.txt
2025-07-28 12:35:04,326 - INFO - 已保存TXT文件: API_脚本管理_3_从我的脚本库中指派脚本到目标环境中.txt
2025-07-28 12:35:05,333 - INFO - 已保存TXT文件: API_脚本管理_4_指定环境中的指定脚本设定为非激活状态.txt
2025-07-28 12:35:06,336 - INFO - 已保存TXT文件: API_脚本管理_5_将未激活脚本从指定环境中移除.txt
2025-07-28 12:35:10,339 - INFO - 正在处理第 11/18 个页面: 插件管理
2025-07-28 12:35:10,340 - INFO - 正在获取页面: https://www.mbbrowser.com/api/plugin
2025-07-28 12:35:11,309 - INFO - 在页面 插件管理 中找到 13 个子章节
2025-07-28 12:35:11,309 - INFO - 页面 插件管理 包含 13 个子章节，将分别保存
2025-07-28 12:35:11,315 - INFO - 已保存TXT文件: API_插件管理_1_帐号登录.txt
2025-07-28 12:35:12,321 - INFO - 已保存TXT文件: API_插件管理_2_获取成员列表.txt
2025-07-28 12:35:13,328 - INFO - 已保存TXT文件: API_插件管理_3_环境开启_关闭.txt
2025-07-28 12:35:14,335 - INFO - 已保存TXT文件: API_插件管理_4_环境管理.txt
2025-07-28 12:35:15,342 - INFO - 已保存TXT文件: API_插件管理_5_分组管理.txt
2025-07-28 12:35:16,348 - INFO - 已保存TXT文件: API_插件管理_6_脚本管理.txt
2025-07-28 12:35:17,356 - INFO - 已保存TXT文件: API_插件管理_7_插件管理.txt
2025-07-28 12:35:18,365 - INFO - 已保存TXT文件: API_插件管理_8_附录（国家码、时区、语言、系统和分辨率）.txt
2025-07-28 12:35:19,368 - INFO - 已保存TXT文件: API_插件管理_9_错误码对照表.txt
2025-07-28 12:35:20,372 - INFO - 已保存TXT文件: API_插件管理_1_列出当前帐户下所有已安装的插件(插件 ID，插件名称).txt
2025-07-28 12:35:21,380 - INFO - 已保存TXT文件: API_插件管理_2_查询、列出指定环境中的所有插件(插件 ID，插件名称).txt
2025-07-28 12:35:22,386 - INFO - 已保存TXT文件: API_插件管理_3_安装指定多个插件到指定的环境中.txt
2025-07-28 12:35:23,388 - INFO - 已保存TXT文件: API_插件管理_4_删除指定环境插件.txt
2025-07-28 12:35:27,390 - INFO - 正在处理第 12/18 个页面: 附录
2025-07-28 12:35:27,390 - INFO - 正在获取页面: https://www.mbbrowser.com/api/appendix
2025-07-28 12:35:28,360 - INFO - 在页面 附录 中找到 9 个子章节
2025-07-28 12:35:28,360 - INFO - 页面 附录 包含 9 个子章节，将分别保存
2025-07-28 12:35:28,366 - INFO - 已保存TXT文件: API_附录_1_帐号登录.txt
2025-07-28 12:35:29,375 - INFO - 已保存TXT文件: API_附录_2_获取成员列表.txt
2025-07-28 12:35:30,382 - INFO - 已保存TXT文件: API_附录_3_环境开启_关闭.txt
2025-07-28 12:35:31,388 - INFO - 已保存TXT文件: API_附录_4_环境管理.txt
2025-07-28 12:35:32,395 - INFO - 已保存TXT文件: API_附录_5_分组管理.txt
2025-07-28 12:35:33,402 - INFO - 已保存TXT文件: API_附录_6_脚本管理.txt
2025-07-28 12:35:34,410 - INFO - 已保存TXT文件: API_附录_7_插件管理.txt
2025-07-28 12:35:35,417 - INFO - 已保存TXT文件: API_附录_8_附录（国家码、时区、语言、系统和分辨率）.txt
2025-07-28 12:35:36,419 - INFO - 已保存TXT文件: API_附录_9_错误码对照表.txt
2025-07-28 12:35:40,421 - INFO - 正在处理第 13/18 个页面: 错误码对照表
2025-07-28 12:35:40,421 - INFO - 正在获取页面: https://www.mbbrowser.com/api/code
2025-07-28 12:35:41,398 - INFO - 在页面 错误码对照表 中找到 9 个子章节
2025-07-28 12:35:41,398 - INFO - 页面 错误码对照表 包含 9 个子章节，将分别保存
2025-07-28 12:35:41,405 - INFO - 已保存TXT文件: API_错误码对照表_1_帐号登录.txt
2025-07-28 12:35:42,412 - INFO - 已保存TXT文件: API_错误码对照表_2_获取成员列表.txt
2025-07-28 12:35:43,420 - INFO - 已保存TXT文件: API_错误码对照表_3_环境开启_关闭.txt
2025-07-28 12:35:44,427 - INFO - 已保存TXT文件: API_错误码对照表_4_环境管理.txt
2025-07-28 12:35:45,435 - INFO - 已保存TXT文件: API_错误码对照表_5_分组管理.txt
2025-07-28 12:35:46,442 - INFO - 已保存TXT文件: API_错误码对照表_6_脚本管理.txt
2025-07-28 12:35:47,449 - INFO - 已保存TXT文件: API_错误码对照表_7_插件管理.txt
2025-07-28 12:35:48,456 - INFO - 已保存TXT文件: API_错误码对照表_8_附录（国家码、时区、语言、系统和分辨率）.txt
2025-07-28 12:35:49,461 - INFO - 已保存TXT文件: API_错误码对照表_9_错误码对照表.txt
2025-07-28 12:35:53,462 - INFO - 正在处理第 14/18 个页面: POSTMAN下载及安装
2025-07-28 12:35:53,463 - INFO - 正在获取页面: https://www.mbbrowser.com/api/postman
2025-07-28 12:35:54,427 - INFO - 在页面 POSTMAN下载及安装 中找到 9 个子章节
2025-07-28 12:35:54,427 - INFO - 页面 POSTMAN下载及安装 包含 9 个子章节，将分别保存
2025-07-28 12:35:54,430 - INFO - 已保存TXT文件: API_POSTMAN下载及安装_1_帐号登录.txt
2025-07-28 12:35:55,437 - INFO - 已保存TXT文件: API_POSTMAN下载及安装_2_获取成员列表.txt
2025-07-28 12:35:56,444 - INFO - 已保存TXT文件: API_POSTMAN下载及安装_3_环境开启_关闭.txt
2025-07-28 12:35:57,450 - INFO - 已保存TXT文件: API_POSTMAN下载及安装_4_环境管理.txt
2025-07-28 12:35:58,457 - INFO - 已保存TXT文件: API_POSTMAN下载及安装_5_分组管理.txt
2025-07-28 12:35:59,464 - INFO - 已保存TXT文件: API_POSTMAN下载及安装_6_脚本管理.txt
2025-07-28 12:36:00,471 - INFO - 已保存TXT文件: API_POSTMAN下载及安装_7_插件管理.txt
2025-07-28 12:36:01,478 - INFO - 已保存TXT文件: API_POSTMAN下载及安装_8_附录（国家码、时区、语言、系统和分辨率）.txt
2025-07-28 12:36:02,480 - INFO - 已保存TXT文件: API_POSTMAN下载及安装_9_错误码对照表.txt
2025-07-28 12:36:06,482 - INFO - 正在处理第 15/18 个页面: POSTMAN调试候鸟API接口
2025-07-28 12:36:06,482 - INFO - 正在获取页面: https://www.mbbrowser.com/api/postman-debug
2025-07-28 12:36:07,456 - INFO - 在页面 POSTMAN调试候鸟API接口 中找到 11 个子章节
2025-07-28 12:36:07,456 - INFO - 页面 POSTMAN调试候鸟API接口 包含 11 个子章节，将分别保存
2025-07-28 12:36:07,462 - INFO - 已保存TXT文件: API_POSTMAN调试候鸟API接口_1_帐号登录.txt
2025-07-28 12:36:08,466 - INFO - 已保存TXT文件: API_POSTMAN调试候鸟API接口_2_获取成员列表.txt
2025-07-28 12:36:09,469 - INFO - 已保存TXT文件: API_POSTMAN调试候鸟API接口_3_环境开启_关闭.txt
2025-07-28 12:36:10,474 - INFO - 已保存TXT文件: API_POSTMAN调试候鸟API接口_4_环境管理.txt
2025-07-28 12:36:11,478 - INFO - 已保存TXT文件: API_POSTMAN调试候鸟API接口_5_分组管理.txt
2025-07-28 12:36:12,482 - INFO - 已保存TXT文件: API_POSTMAN调试候鸟API接口_6_脚本管理.txt
2025-07-28 12:36:13,486 - INFO - 已保存TXT文件: API_POSTMAN调试候鸟API接口_7_插件管理.txt
2025-07-28 12:36:14,492 - INFO - 已保存TXT文件: API_POSTMAN调试候鸟API接口_8_附录（国家码、时区、语言、系统和分辨率）.txt
2025-07-28 12:36:15,494 - INFO - 已保存TXT文件: API_POSTMAN调试候鸟API接口_9_错误码对照表.txt
2025-07-28 12:36:16,498 - INFO - 已保存TXT文件: API_POSTMAN调试候鸟API接口_1_仅支持客户端V3.9.2.114以上版本，请下载客户端最新版本.txt
2025-07-28 12:36:17,499 - INFO - 已保存TXT文件: API_POSTMAN调试候鸟API接口_2_需要本地安装POSTMAN，见.txt
2025-07-28 12:36:21,505 - INFO - 正在处理第 16/18 个页面: 调试接口JSON数据官方更新下载
2025-07-28 12:36:21,505 - INFO - 正在获取页面: https://www.mbbrowser.com/api/postman-example
2025-07-28 12:36:22,490 - INFO - 在页面 调试接口JSON数据官方更新下载 中找到 11 个子章节
2025-07-28 12:36:22,491 - INFO - 页面 调试接口JSON数据官方更新下载 包含 11 个子章节，将分别保存
2025-07-28 12:36:22,495 - INFO - 已保存TXT文件: API_调试接口JSON数据官方更新下载_1_帐号登录.txt
2025-07-28 12:36:23,499 - INFO - 已保存TXT文件: API_调试接口JSON数据官方更新下载_2_获取成员列表.txt
2025-07-28 12:36:24,504 - INFO - 已保存TXT文件: API_调试接口JSON数据官方更新下载_3_环境开启_关闭.txt
2025-07-28 12:36:25,508 - INFO - 已保存TXT文件: API_调试接口JSON数据官方更新下载_4_环境管理.txt
2025-07-28 12:36:26,512 - INFO - 已保存TXT文件: API_调试接口JSON数据官方更新下载_5_分组管理.txt
2025-07-28 12:36:27,516 - INFO - 已保存TXT文件: API_调试接口JSON数据官方更新下载_6_脚本管理.txt
2025-07-28 12:36:28,520 - INFO - 已保存TXT文件: API_调试接口JSON数据官方更新下载_7_插件管理.txt
2025-07-28 12:36:29,524 - INFO - 已保存TXT文件: API_调试接口JSON数据官方更新下载_8_附录（国家码、时区、语言、系统和分辨率）.txt
2025-07-28 12:36:30,527 - INFO - 已保存TXT文件: API_调试接口JSON数据官方更新下载_9_错误码对照表.txt
2025-07-28 12:36:31,529 - INFO - 已保存TXT文件: API_调试接口JSON数据官方更新下载_1_POSTMAN如何导入JSON数据.txt
2025-07-28 12:36:32,530 - INFO - 已保存TXT文件: API_调试接口JSON数据官方更新下载_2_POSTMAN如何导出JSON数据.txt
2025-07-28 12:36:36,532 - INFO - 正在处理第 17/18 个页面: 多种语言脚本示例
2025-07-28 12:36:36,534 - INFO - 正在获取页面: https://www.mbbrowser.com/api/example
2025-07-28 12:36:37,502 - INFO - 在页面 多种语言脚本示例 中找到 9 个子章节
2025-07-28 12:36:37,502 - INFO - 页面 多种语言脚本示例 包含 9 个子章节，将分别保存
2025-07-28 12:36:37,510 - INFO - 已保存TXT文件: API_多种语言脚本示例_1_帐号登录.txt
2025-07-28 12:36:38,517 - INFO - 已保存TXT文件: API_多种语言脚本示例_2_获取成员列表.txt
2025-07-28 12:36:39,525 - INFO - 已保存TXT文件: API_多种语言脚本示例_3_环境开启_关闭.txt
2025-07-28 12:36:40,532 - INFO - 已保存TXT文件: API_多种语言脚本示例_4_环境管理.txt
2025-07-28 12:36:41,539 - INFO - 已保存TXT文件: API_多种语言脚本示例_5_分组管理.txt
2025-07-28 12:36:42,547 - INFO - 已保存TXT文件: API_多种语言脚本示例_6_脚本管理.txt
2025-07-28 12:36:43,554 - INFO - 已保存TXT文件: API_多种语言脚本示例_7_插件管理.txt
2025-07-28 12:36:44,558 - INFO - 已保存TXT文件: API_多种语言脚本示例_8_附录（国家码、时区、语言、系统和分辨率）.txt
2025-07-28 12:36:45,560 - INFO - 已保存TXT文件: API_多种语言脚本示例_9_错误码对照表.txt
2025-07-28 12:36:49,562 - INFO - 正在处理第 18/18 个页面: JSON在线格式化工具
2025-07-28 12:36:49,562 - INFO - 正在获取页面: https://www.mbbrowser.com/api/json
2025-07-28 12:36:50,534 - INFO - 在页面 JSON在线格式化工具 中找到 9 个子章节
2025-07-28 12:36:50,534 - INFO - 页面 JSON在线格式化工具 包含 9 个子章节，将分别保存
2025-07-28 12:36:50,540 - INFO - 已保存TXT文件: API_JSON在线格式化工具_1_帐号登录.txt
2025-07-28 12:36:51,547 - INFO - 已保存TXT文件: API_JSON在线格式化工具_2_获取成员列表.txt
2025-07-28 12:36:52,553 - INFO - 已保存TXT文件: API_JSON在线格式化工具_3_环境开启_关闭.txt
2025-07-28 12:36:53,561 - INFO - 已保存TXT文件: API_JSON在线格式化工具_4_环境管理.txt
2025-07-28 12:36:54,568 - INFO - 已保存TXT文件: API_JSON在线格式化工具_5_分组管理.txt
2025-07-28 12:36:55,574 - INFO - 已保存TXT文件: API_JSON在线格式化工具_6_脚本管理.txt
2025-07-28 12:36:56,582 - INFO - 已保存TXT文件: API_JSON在线格式化工具_7_插件管理.txt
2025-07-28 12:36:57,589 - INFO - 已保存TXT文件: API_JSON在线格式化工具_8_附录（国家码、时区、语言、系统和分辨率）.txt
2025-07-28 12:36:58,591 - INFO - 已保存TXT文件: API_JSON在线格式化工具_9_错误码对照表.txt
2025-07-28 12:37:02,593 - INFO - 抓取完成! 总页面数: 18, 成功: 18, 失败: 0
