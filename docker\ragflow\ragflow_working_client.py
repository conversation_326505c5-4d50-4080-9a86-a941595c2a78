#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RAGFlow 工作客户端
基于直接 API 调用的完整 RAGFlow 客户端实现
"""

import json
import requests
import time
from typing import Dict, Any, Optional, List

class RAGFlowWorkingClient:
    """RAGFlow 工作客户端 - 使用直接 API"""
    
    def __init__(self, 
                 ragflow_url: str = "http://************:9380",
                 api_key: str = "ragflow-UzYTlhMjIwNjI0YzExZjA4NzUyMDI0Mm"):
        self.ragflow_url = ragflow_url.rstrip('/')
        self.api_key = api_key
        
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}",
            "User-Agent": "RAGFlow-Working-Client/1.0",
            "Accept": "application/json"
        }
        
        self.datasets = []
        self.conversations = []
        
        print(f"🚀 RAGFlow 工作客户端初始化")
        print(f"   服务器: {self.ragflow_url}")
        print(f"   API密钥: {self.api_key[:20]}...")
    
    def get_datasets(self) -> List[Dict[str, Any]]:
        """获取数据集列表"""
        print(f"\n📚 获取数据集列表...")
        
        try:
            url = f"{self.ragflow_url}/api/v1/datasets"
            response = requests.get(url, headers=self.headers, timeout=10)
            
            print(f"   状态: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get("code") == 0 and "data" in result:
                    datasets = result["data"]
                    self.datasets = datasets
                    
                    print(f"✅ 获取到 {len(datasets)} 个数据集:")
                    
                    for i, dataset in enumerate(datasets):
                        dataset_id = dataset.get("id", "Unknown")
                        name = dataset.get("name", "Unnamed")
                        chunk_count = dataset.get("chunk_count", 0)
                        description = dataset.get("description", "No description")
                        
                        print(f"   {i+1}. ID: {dataset_id}")
                        print(f"      名称: {name}")
                        print(f"      块数: {chunk_count}")
                        print(f"      描述: {description}")
                        print()
                    
                    return datasets
                else:
                    print(f"❌ 数据集响应格式异常: {result}")
                    return []
            else:
                print(f"❌ 获取数据集失败: {response.status_code}")
                print(f"   响应: {response.text}")
                return []
                
        except Exception as e:
            print(f"❌ 获取数据集异常: {e}")
            return []
    
    def retrieval_query(self, question: str, dataset_ids: Optional[List[str]] = None, top_k: int = 5) -> Optional[Dict[str, Any]]:
        """检索查询"""
        print(f"\n🔍 检索查询: {question}")
        
        # 如果没有指定数据集ID，使用所有可用的数据集
        if dataset_ids is None:
            if not self.datasets:
                self.get_datasets()
            
            if self.datasets:
                dataset_ids = [dataset.get("id") for dataset in self.datasets if dataset.get("id")]
                print(f"   使用所有数据集: {dataset_ids}")
            else:
                print("❌ 没有可用的数据集")
                return None
        
        try:
            url = f"{self.ragflow_url}/api/v1/retrieval"
            
            request_data = {
                "question": question,
                "dataset_ids": dataset_ids,
                "top_k": top_k
            }
            
            print(f"   请求数据: {json.dumps(request_data, ensure_ascii=False, indent=2)}")
            
            response = requests.post(url, headers=self.headers, json=request_data, timeout=30)
            
            print(f"   状态: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"   响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
                
                if result.get("code") == 0:
                    print("✅ 检索查询成功")
                    return result
                else:
                    print(f"❌ 检索查询失败: {result.get('message', 'Unknown error')}")
                    return None
            else:
                print(f"❌ 检索请求失败: {response.status_code}")
                print(f"   响应: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ 检索查询异常: {e}")
            return None
    
    def chat_query(self, question: str, conversation_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """聊天查询"""
        print(f"\n💬 聊天查询: {question}")
        
        if conversation_id is None:
            conversation_id = f"wing-client-{int(time.time())}"
        
        try:
            url = f"{self.ragflow_url}/api/v1/chat"
            
            request_data = {
                "message": question,
                "conversation_id": conversation_id,
                "stream": False
            }
            
            print(f"   会话ID: {conversation_id}")
            print(f"   请求数据: {json.dumps(request_data, ensure_ascii=False, indent=2)}")
            
            response = requests.post(url, headers=self.headers, json=request_data, timeout=60)
            
            print(f"   状态: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"   响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
                
                if result.get("code") == 0:
                    print("✅ 聊天查询成功")
                    return result
                else:
                    print(f"❌ 聊天查询失败: {result.get('message', 'Unknown error')}")
                    return None
            else:
                print(f"❌ 聊天请求失败: {response.status_code}")
                print(f"   响应: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ 聊天查询异常: {e}")
            return None
    
    def test_comprehensive_query(self, question: str) -> Dict[str, Any]:
        """综合查询测试"""
        print(f"\n🧪 综合查询测试: {question}")
        print("=" * 60)
        
        results = {
            "question": question,
            "datasets": None,
            "retrieval_result": None,
            "chat_result": None,
            "success": False
        }
        
        try:
            # 1. 获取数据集
            datasets = self.get_datasets()
            results["datasets"] = datasets
            
            if not datasets:
                print("⚠️  没有可用的数据集，跳过检索测试")
            else:
                # 2. 检索查询
                retrieval_result = self.retrieval_query(question)
                results["retrieval_result"] = retrieval_result
            
            # 3. 聊天查询
            chat_result = self.chat_query(question)
            results["chat_result"] = chat_result
            
            # 4. 判断成功状态
            if retrieval_result or chat_result:
                results["success"] = True
                print(f"\n✅ 综合查询测试成功!")
            else:
                print(f"\n❌ 综合查询测试失败")
            
            return results
            
        except Exception as e:
            print(f"\n❌ 综合查询测试异常: {e}")
            results["error"] = str(e)
            return results
    
    def generate_wing_integration_code(self) -> str:
        """生成 Wing 客户端集成代码"""
        print(f"\n🔧 生成 Wing 客户端集成代码...")
        
        go_code = f'''package main

import (
    "bytes"
    "encoding/json"
    "fmt"
    "io"
    "net/http"
    "time"
)

// RAGFlowClient Wing 客户端的 RAGFlow 集成
type RAGFlowClient struct {{
    BaseURL string
    APIKey  string
    Client  *http.Client
}}

// NewRAGFlowClient 创建新的 RAGFlow 客户端
func NewRAGFlowClient() *RAGFlowClient {{
    return &RAGFlowClient{{
        BaseURL: "{self.ragflow_url}",
        APIKey:  "{self.api_key}",
        Client: &http.Client{{
            Timeout: 60 * time.Second,
        }},
    }}
}}

// Dataset 数据集结构
type Dataset struct {{
    ID          string `json:"id"`
    Name        string `json:"name"`
    ChunkCount  int    `json:"chunk_count"`
    Description string `json:"description"`
}}

// RetrievalRequest 检索请求
type RetrievalRequest struct {{
    Question   string   `json:"question"`
    DatasetIDs []string `json:"dataset_ids"`
    TopK       int      `json:"top_k"`
}}

// RetrievalResponse 检索响应
type RetrievalResponse struct {{
    Code    int         `json:"code"`
    Data    interface{{}} `json:"data"`
    Message string      `json:"message"`
}}

// GetDatasets 获取数据集列表
func (c *RAGFlowClient) GetDatasets() ([]Dataset, error) {{
    url := fmt.Sprintf("%s/api/v1/datasets", c.BaseURL)
    
    req, err := http.NewRequest("GET", url, nil)
    if err != nil {{
        return nil, err
    }}
    
    req.Header.Set("Authorization", "Bearer "+c.APIKey)
    req.Header.Set("Content-Type", "application/json")
    
    resp, err := c.Client.Do(req)
    if err != nil {{
        return nil, err
    }}
    defer resp.Body.Close()
    
    body, err := io.ReadAll(resp.Body)
    if err != nil {{
        return nil, err
    }}
    
    var result struct {{
        Code int       `json:"code"`
        Data []Dataset `json:"data"`
    }}
    
    if err := json.Unmarshal(body, &result); err != nil {{
        return nil, err
    }}
    
    if result.Code != 0 {{
        return nil, fmt.Errorf("API error: code %d", result.Code)
    }}
    
    return result.Data, nil
}}

// QueryRAGFlow 查询 RAGFlow 知识库
func (c *RAGFlowClient) QueryRAGFlow(question string) (string, error) {{
    // 1. 获取数据集
    datasets, err := c.GetDatasets()
    if err != nil {{
        return "", fmt.Errorf("获取数据集失败: %v", err)
    }}
    
    if len(datasets) == 0 {{
        return "", fmt.Errorf("没有可用的数据集")
    }}
    
    // 2. 提取数据集ID
    var datasetIDs []string
    for _, dataset := range datasets {{
        datasetIDs = append(datasetIDs, dataset.ID)
    }}
    
    // 3. 执行检索查询
    url := fmt.Sprintf("%s/api/v1/retrieval", c.BaseURL)
    
    reqData := RetrievalRequest{{
        Question:   question,
        DatasetIDs: datasetIDs,
        TopK:       5,
    }}
    
    jsonData, err := json.Marshal(reqData)
    if err != nil {{
        return "", err
    }}
    
    req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
    if err != nil {{
        return "", err
    }}
    
    req.Header.Set("Authorization", "Bearer "+c.APIKey)
    req.Header.Set("Content-Type", "application/json")
    
    resp, err := c.Client.Do(req)
    if err != nil {{
        return "", err
    }}
    defer resp.Body.Close()
    
    body, err := io.ReadAll(resp.Body)
    if err != nil {{
        return "", err
    }}
    
    var result RetrievalResponse
    if err := json.Unmarshal(body, &result); err != nil {{
        return "", err
    }}
    
    if result.Code != 0 {{
        return "", fmt.Errorf("查询失败: %s", result.Message)
    }}
    
    // 4. 解析结果
    return fmt.Sprintf("查询成功: %v", result.Data), nil
}}

// TestConnection 测试连接
func (c *RAGFlowClient) TestConnection() error {{
    datasets, err := c.GetDatasets()
    if err != nil {{
        return err
    }}
    
    fmt.Printf("✅ RAGFlow 连接成功，找到 %d 个数据集\\n", len(datasets))
    return nil
}}

func main() {{
    client := NewRAGFlowClient()
    
    // 测试连接
    if err := client.TestConnection(); err != nil {{
        fmt.Printf("❌ 连接失败: %v\\n", err)
        return
    }}
    
    // 测试查询
    question := "候鸟浏览器如何配置代理？"
    result, err := client.QueryRAGFlow(question)
    if err != nil {{
        fmt.Printf("❌ 查询失败: %v\\n", err)
        return
    }}
    
    fmt.Printf("🤖 查询结果: %s\\n", result)
}}'''
        
        # 保存代码到文件
        with open("wing_ragflow_integration.go", "w", encoding="utf-8") as f:
            f.write(go_code)
        
        print(f"✅ Wing 集成代码已生成: wing_ragflow_integration.go")
        return go_code


def main():
    """主函数"""
    print("🚀 RAGFlow 工作客户端测试")
    print("=" * 60)
    
    client = RAGFlowWorkingClient()
    
    try:
        # 测试问题
        test_questions = [
            "候鸟浏览器如何配置代理？",
            "RAGFlow是什么？",
            "如何创建知识库？"
        ]
        
        all_results = []
        
        for i, question in enumerate(test_questions, 1):
            print(f"\n{'='*20} 测试 {i}/{len(test_questions)} {'='*20}")
            result = client.test_comprehensive_query(question)
            all_results.append(result)
            
            if i < len(test_questions):
                time.sleep(2)  # 避免请求过快
        
        # 生成 Wing 集成代码
        client.generate_wing_integration_code()
        
        # 保存所有结果
        with open("ragflow_working_test_results.json", "w", encoding="utf-8") as f:
            json.dump(all_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n" + "=" * 60)
        print(f"🎉 RAGFlow 工作客户端测试完成！")
        print(f"💾 结果已保存到: ragflow_working_test_results.json")
        print(f"🔧 Wing 集成代码: wing_ragflow_integration.go")
        
    except KeyboardInterrupt:
        print(f"\n\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")


if __name__ == "__main__":
    main()
