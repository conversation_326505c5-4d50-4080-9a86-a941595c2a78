#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析API网站结构，尝试找到详细内容的访问方式
"""

import requests
import re
import time
import json
from urllib.parse import urljoin
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class APIStructureAnalyzer:
    def __init__(self, base_url='https://www.mbbrowser.com/api/'):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
    
    def fetch_page_source(self, url):
        """获取页面源码"""
        try:
            full_url = urljoin(self.base_url, url)
            logger.info(f"正在分析页面: {full_url}")
            
            response = self.session.get(full_url, timeout=30)
            response.raise_for_status()
            response.encoding = 'utf-8'
            
            return response.text
            
        except Exception as e:
            logger.error(f"获取页面失败: {url}, 错误: {str(e)}")
            return None
    
    def analyze_javascript_content(self, html_content):
        """分析页面中的JavaScript内容，寻找API数据"""
        try:
            # 查找可能包含API数据的JavaScript
            js_patterns = [
                r'<script[^>]*>(.*?)</script>',
                r'window\.__NUXT__\s*=\s*({.*?});',
                r'window\.__INITIAL_STATE__\s*=\s*({.*?});',
                r'apiData\s*=\s*({.*?});',
                r'sessionData\s*=\s*({.*?});'
            ]
            
            found_data = []
            
            for pattern in js_patterns:
                matches = re.findall(pattern, html_content, re.DOTALL | re.IGNORECASE)
                for match in matches:
                    if len(match) > 100:  # 只关注较长的内容
                        found_data.append({
                            'pattern': pattern,
                            'content': match[:500] + '...' if len(match) > 500 else match
                        })
            
            return found_data
            
        except Exception as e:
            logger.error(f"分析JavaScript失败: {str(e)}")
            return []
    
    def try_api_endpoints(self):
        """尝试可能的API端点"""
        possible_endpoints = [
            # 环境管理相关
            '/api/session/list',
            '/api/session/get',
            '/api/session/create',
            '/api/session/update',
            '/api/session/delete',
            
            # 可能的文档端点
            '/api/docs/session',
            '/api/help/session',
            '/api/session/docs',
            
            # 可能的JSON数据端点
            '/api/data/session.json',
            '/api/session.json',
            '/docs/session.json',
            
            # 其他可能的格式
            '/api/session?format=json',
            '/api/session?type=docs',
        ]
        
        results = []
        
        for endpoint in possible_endpoints:
            try:
                full_url = urljoin(self.base_url, endpoint)
                response = self.session.get(full_url, timeout=10)
                
                if response.status_code == 200:
                    content_type = response.headers.get('content-type', '').lower()
                    results.append({
                        'url': full_url,
                        'status': response.status_code,
                        'content_type': content_type,
                        'content_length': len(response.text),
                        'content_preview': response.text[:200] + '...' if len(response.text) > 200 else response.text
                    })
                    logger.info(f"找到有效端点: {full_url} ({response.status_code})")
                
                time.sleep(1)  # 避免请求过快
                
            except Exception as e:
                logger.debug(f"端点无效: {endpoint}, 错误: {str(e)}")
        
        return results
    
    def analyze_page_structure(self, url):
        """深度分析页面结构"""
        html_content = self.fetch_page_source(url)
        if not html_content:
            return None
        
        analysis = {
            'url': url,
            'content_length': len(html_content),
            'has_vue_app': 'vue' in html_content.lower() or '__nuxt__' in html_content.lower(),
            'has_react_app': 'react' in html_content.lower(),
            'has_angular_app': 'angular' in html_content.lower(),
            'javascript_data': self.analyze_javascript_content(html_content),
            'meta_tags': re.findall(r'<meta[^>]*>', html_content, re.IGNORECASE),
            'script_sources': re.findall(r'<script[^>]*src=["\']([^"\']+)["\']', html_content, re.IGNORECASE)
        }
        
        return analysis
    
    def search_for_api_content(self):
        """搜索API详细内容"""
        logger.info("开始分析API网站结构...")
        
        # 分析主要页面
        main_pages = [
            '/api/',
            '/api/session',
            '/api/login',
            '/api/browser',
            '/api/group'
        ]
        
        for page in main_pages:
            logger.info(f"\n分析页面: {page}")
            analysis = self.analyze_page_structure(page)
            
            if analysis:
                logger.info(f"页面类型: {'Vue.js应用' if analysis['has_vue_app'] else '静态页面'}")
                logger.info(f"内容长度: {analysis['content_length']} 字符")
                
                if analysis['javascript_data']:
                    logger.info(f"找到 {len(analysis['javascript_data'])} 个JavaScript数据块")
                    for i, data in enumerate(analysis['javascript_data'][:3]):  # 只显示前3个
                        logger.info(f"  数据块 {i+1}: {data['content'][:100]}...")
                
                if analysis['script_sources']:
                    logger.info(f"外部脚本: {len(analysis['script_sources'])} 个")
                    for script in analysis['script_sources'][:5]:  # 只显示前5个
                        logger.info(f"  - {script}")
        
        # 尝试API端点
        logger.info("\n尝试可能的API端点...")
        api_results = self.try_api_endpoints()
        
        if api_results:
            logger.info(f"找到 {len(api_results)} 个有效端点:")
            for result in api_results:
                logger.info(f"  {result['url']} - {result['content_type']} ({result['content_length']} 字符)")
                logger.info(f"    预览: {result['content_preview']}")
        else:
            logger.info("未找到有效的API端点")
        
        return api_results
    
    def generate_recommendations(self, api_results):
        """生成建议"""
        logger.info("\n=== 分析结果和建议 ===")
        
        if api_results:
            logger.info("✅ 找到了一些可能的API端点，建议进一步探索")
        else:
            logger.info("❌ 未找到直接的API端点")
        
        logger.info("\n可能的解决方案:")
        logger.info("1. 使用浏览器自动化工具 (Selenium/Playwright) 模拟用户操作")
        logger.info("2. 分析网络请求，查看是否有AJAX调用")
        logger.info("3. 检查页面的Vue.js组件和路由")
        logger.info("4. 尝试反向工程JavaScript代码")
        logger.info("5. 联系网站管理员获取API文档")

def main():
    """主函数"""
    print("API网站结构分析器启动...")
    print("="*50)
    
    analyzer = APIStructureAnalyzer()
    api_results = analyzer.search_for_api_content()
    analyzer.generate_recommendations(api_results)
    
    print("="*50)
    print("分析完成!")

if __name__ == "__main__":
    main()
