<?xml version="1.0" encoding="utf-8" standalone="yes" ?>
<Window size="500,400" caption="0,0,0,148" roundcorner="7,7,7,7" showshadow="true" shadowsize="7" shadowposition="0,0" shadowcorner="3,3,3,3" shadowcolor="#ff6272A4" shadowdarkness="30" fademode="true">
	<Font name="微软雅黑" id="0" size="14"  black="true" default="true"/>
	<Font name="微软雅黑" id="1" size="14"  black="true" />
	<Font name="微软雅黑" id="2" size="12"  black="true" />
	<Font name="微软雅黑" id="3" size="16"  black="true" />
	<Font name="arial" id="4" size="14" />
	<Font name="Microsoft YaHei" id="5" size="12" />
  <Default name="VScrollBar" value="width=&quot;14&quot; scroll_auto_hidden=&quot;true&quot; scrollbarfloat=&quot;true&quot; showbutton1=&quot;false&quot; showbutton2=&quot;false&quot; thumbnormalimage=&quot;file='common_scrollbar.png' source='0,0,7,9' corner='0,4,0,4'&quot; thumbhotimage=&quot;file='common_scrollbar.png' source='10,0,17,9' corner='0,4,0,4'&quot; thumbpushedimage=&quot;file='common_scrollbar.png' source='10,0,17,9' corner='0,4,0,4'&quot; bknormalimage=&quot;file='common_scrollbar.png' source='0,12,1,13' corner='0,0,0,0'&quot;" />
	<VerticalLayout bkcolor="#FFf5f7f8" bordersize="1" bordercolor="#FFdddddd" borderround="1,1" inset="0,0,0,0">
	 <HorizontalLayout height="37">

    	<Button name="login" bkimage="icon.png" padding="8,6,0,0" width="28" height="28"/>
      <Label name="session_list_title" padding="6,4,0,0" text="登录" width="180" textcolor="#FF616161" hottextcolor="#ff000000" align="left" font="8"></Label>

      <Control />

     <Button name="server" padding="0,0,0,0" width="28" height="26" tooltip="Switch Server" normalimage="mb_mutiline.png" hotimage="mb_mutiline_hover.png" pushedimage="mb_mutiline_push.png"/>

     <VerticalLayout width="48" height="30">
       <CheckBox name="security_switch" padding="3,1,0,0" width="42" height="24" tooltip="云端实时数据同步" selected="true" normalimage="Net_switch_off.png" selectedhotimage="Net_switch_on_hover.png" hotimage="Net_switch_off_hover.png" selectedimage="Net_switch_on.png" disabledimage="Net_switch_off.png" />
     </VerticalLayout>

      <Button name="login_language" padding="0,0,0,0" width="28" height="26" tooltip="Switch Language" normalimage="login_lng.png" hotimage="login_lng_hover.png"/>

      <Button name="minbtn" width="28" height="26" tooltip="最小化" normalimage="minbtn.png" hotimage="minbtn_hover.png" pushedimage="minbtnpush.png" />
      <Button name="closelogin" width="28" height="26"  tooltip="关闭" normalimage="closebtn.png" hotimage="closebtn_hover.png" pushedimage="closebtnpush.png" />

    </HorizontalLayout>

    <HorizontalLayout height="135" width="500" inset="0,0,0,0">
		    <VerticalLayout name="login_inset" width="100" height="100" padding="200,10,0,0" bkimage="file='logohead.png'"></VerticalLayout>
        <GifAnim name="logingif" visible="false" bkimage="anilogin.gif" height="134" width="134" padding="-117,-7,0,0" auto="true"/>
    </HorizontalLayout>



   <HorizontalLayout height="45" >
    	<Control />
		    <VerticalLayout width="320" height="45">

          <Combo name="usernamelist" bordersize="0" reselect="true" padding="0,10,0,10" width="320" height="36" borderround="5,5" textcolor="#FF000000" disabledtextcolor="#FF000000" itemalign="left" itemfont="0" textpadding="58,0,45,0"
						 normalimage="file='Profile\short_Combox_Normal_login.png' corner='5,5,30,10'" hotimage="file='Profile\short_Combox_Hover_login.png' corner='5,5,30,10'" pushedimage="file='Profile\short_Combox_Click_login.png' corner='5,5,30,10'"
						dropwndbordercolor="#FFcccccc" dropwndtransparent="280" itemtextcolor="#FF929292" itemhotbkcolor="#FFdddddd" itemselectedbkcolor="#FFcccccc" itemtextpadding="32,0,0,0" itemendellipsis="true" endellipsis="true">
          </Combo>

          <Edit name="username" mouseentermsg="true" mouseleavemsg="true" pos="3,10,0,10" bkimage="file='user.png' source='0,0,18,35' dest='0,0,18,35'" textpadding="30,3,0,0" tipvaluecolor="ff666666" textcolor="ff444444" width="286" nativebkcolor="#FFf5f7f8" bkcolor="#FFf5f7f8" height="35" float="true" />
		    	<!--<RichEdit name="username" padding="0,10,0,10" wanttab="false" height="35" width="340" inset="3,1,3,1" bkcolor="#FFf5f7f8" font="0" textpadding="2,6,20,0" tipvalue="用户名" text="" tipvaluecolor="ff333333" multiline="false" textcolor="ff666666" rich="false">
 				       </RichEdit>-->
		    </VerticalLayout>
      <Control />
    </HorizontalLayout>


   <HorizontalLayout height="2" >
    	<Control />
		    <VerticalLayout name="usernameline" width="320" height="1" bkcolor="#FFeaeaeb"></VerticalLayout>
      <Control />
    </HorizontalLayout>

   <HorizontalLayout height="45" >
    	<Control />
		    <VerticalLayout width="320" height="45">

		    	  <RichEdit name="password" mouseentermsg="true" mouseleavemsg="true" padding="0,10,0,10" wanttab="false" height="35" width="340" inset="3,1,3,1" normalimage="file='lock.png' source='0,0,24,35' dest='0,0,24,35'" maxchar="30" bkcolor="#FFf5f7f8" font="1" textpadding="32,8,20,0" text="" tipvaluecolor="ff333333" password="true" multiline="false" textcolor="ff666666" rich="false">
 				       </RichEdit>
          <Button name="viewpass" bkimage="eye_close.png" float="true" pos="290,20,0,0" font="0" width="24" height="12" text=""/>

		    </VerticalLayout>
      <Control />
    </HorizontalLayout>


  <HorizontalLayout height="2" >
    	<Control />
		    <VerticalLayout name="passworkline" width="320" height="1" bkcolor="#FFeaeaeb"></VerticalLayout>
      <Control />
    </HorizontalLayout>

<HorizontalLayout height="10">
</HorizontalLayout>
   <HorizontalLayout height="45">
 			<Control />
		    <VerticalLayout width="350" height="18">
		    	<HorizontalLayout height="18">
						<VerticalLayout width="42">
					      <CheckBox name="login_remenber" width="18" height="18"  valign="center" padding="18,0,0,0" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="true" />
					  </VerticalLayout>
					  <VerticalLayout width="188" height="18">
					      <Label name="remember" padding="0,-1,0,0" text="记住密码" valign="top" width="188" textcolor="#FF6f6f6f" font="29"></Label>
					  </VerticalLayout>
		        <VerticalLayout width="42">
					      <CheckBox name="login_auto" width="18" height="18"  valign="center" padding="20,0,0,0" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="false" />
					  </VerticalLayout>
					  <VerticalLayout width="100" height="18">
					      <Label name="auto" padding="2,-1,0,0" textpadding="0,0,0,0" valign="top" text="自动登录" width="300" textcolor="#FF6f6f6f" font="29"></Label>
					  </VerticalLayout>

					 </HorizontalLayout>
				</VerticalLayout>
			 <Control />
	</HorizontalLayout>

<HorizontalLayout height="15">
</HorizontalLayout>

    <HorizontalLayout name="regarea" textsetpos="true" height="20" width="500">
      <Control />
      <Label name="regmsg1" text="" autocalcwidth="true" align="center" font="2"></Label>
      <Control width="10"/>
      <Button name="regbtn" autocalcwidth="true" textpadding="0,1,0,0" text="" align="center" textcolor="#ff519cff" hottextcolor="#FF005ed3" font="2"/>
	  <Control width="10"/>
      <Label name="regmsg2" autocalcwidth="true" padding="0,0,0,0" text=""  align="center" font="2"></Label>
	  <Control width="10"/>
      <Button name="getpassbtn" autocalcwidth="true" textpadding="0,0,0,0" text="" align="center" textcolor="#ff519cff" hottextcolor="#FF005ed3" font="2"/>
      <Control />
    </HorizontalLayout>

<HorizontalLayout visible="false" name="loginarea" height="20">
	<!--<Control />-->
  <Label name="connectiontip" endellipsis="true" tooltip="" padding="7,0,0,0" text="" width="386" textcolor="#FF6f6f6f" align="left" font="2"></Label>
	<Control />
  <Label name="progresstext" padding="7,0,4,0" text="" width="100" textpadding="0,0,7,0" textcolor="#FF6f6f6f" align="right" font="4"></Label>
</HorizontalLayout>

<HorizontalLayout visible="false" textsetpos="true" name="expirearea1" height="20">
  <Label name="expiremsg11" padding="7,0,0,0" text="温馨提示：您的套餐" autocalcwidth="true" maxwidth="260" texttooltip="true" endellipsis="true" textcolor="#FF6f6f6f" font="2"></Label>
  <!--<Label name="expireviptype" text="体验版" width="60" textcolor="#ff519cff" align="center" font="2"></Label>-->
  <Label name="expiremsg13" padding="7,0,0,0" text="" textcolor="#ff519cff" autocalcwidth="true" maxwidth="48" texttooltip="true" endellipsis="true" font="2"></Label>
  <Label name="expiremsg14" padding="2,0,0,0" text="" autocalcwidth="true"  maxwidth="48" texttooltip="true" endellipsis="true" font="2"></Label>
  <Button name="neworder" padding="5,0,10,0" text="" autocalcwidth="true" maxwidth="128" texttooltip="true" endellipsis="true" textcolor="#ffff9600" hottextcolor="#FF0067ee" font="2"/>

</HorizontalLayout>

<HorizontalLayout visible="false"  textsetpos="true" name="expirearea2" height="20">
  <Label name="expiremsg21" padding="7,0,0,0" text="温馨提示：您的套餐" autocalcwidth="true" texttooltip="true" endellipsis="true" textcolor="#FF6f6f6f" font="2"></Label>
  <!--<Label name="expireviptype1" text="" width="60" textcolor="#ff519cff" align="center" font="2"></Label>-->
  <Label name="expiremsg23" padding="7,0,0,0" text="" width="48" texttooltip="true" endellipsis="true" textcolor="#ffff0000" align="left" font="2"></Label>
  <Label name="expiremsg24" padding="2,0,0,0" text="" autocalcwidth="true" font="2"></Label>
  <Button name="neworder1" autocalcwidth="true" maxwidth="128" padding="5,0,10,0" texttooltip="true" endellipsis="true" text="" textcolor="#ffff9600" hottextcolor="#FF0067ee" font="2"/>
</HorizontalLayout>

<HorizontalLayout height="4">
  <Progress name="ProgressBar" height="4" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" foreimage="file='progressbar1.png' corner='3,0,3,0'" min="0" max="100" value="0" hor="true" />
</HorizontalLayout>

    <HorizontalLayout height="52" bkcolor="#ff519cff" inset="0,0,0,0">
         <!--<Control />-->
					      <VerticalLayout>
					      		<!--<Control />-->
					             <Button name="loginbtn" height="52" padding="-2,-6,0,0" text="登 录" textpadding="0,-3,0,0" font="3" textcolor="#FFe6ebf2" disabledtextcolor="#FFFFFFFF" hottextcolor="#FFFFFFFF" />
					          <!--<Control />-->
					       </VerticalLayout>
				<!--<Control />-->
    </HorizontalLayout>

	</VerticalLayout>
</Window>