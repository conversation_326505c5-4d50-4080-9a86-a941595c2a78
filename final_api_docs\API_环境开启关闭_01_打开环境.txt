# API_环境开启关闭_01_打开环境

## 功能描述
启动指定的环境，成功后提供浏览器调试接口以供Selenium和Puppeteer自动化脚本使用

## 所属模块
环境开启关闭

## API信息

- **路径**: `/api/v1/browser/start`
- **方法**: POST
- **内容类型**: application/json
- **服务器地址**: http://127.0.0.1:8186

## 请求参数

| 参数名称    | 类型      | 是否必传 | 示例/默认值                                              | 说明       |
|---------|---------|------|-------------------------------------------------------|----------|
| Session_ID | 数组     | 是    | ["373808cb37bd63f5f7d92415e736e85f"]                   | 环境ID     |
| isHeadless | 布尔值   | 否    | true                                                  | True: 无头模式, False: 有头模式 |
| args    | 数组     | 否    | ["--disable-extensions", "--blink-settings=imagesEnabled=false"] | 启动参数   |

## 请求示例

```json
{
    "Session_ID": ["373808cb37bd63f5f7d92415e736e85f"],
    "isHeadless": true,
    "args": ["--disable-extensions", "--blink-settings=imagesEnabled=false"]
}
```

## 成功响应

```json
{
    "message": "成功",
    "code": 0,
    "data": {
        "listid": [{
            "Session_Name": "商用业务环境一",
            "Session_ID": "373808cb37bd63f5f7d92415e736e85f",
            "Group_Name": "默认",
            "Actived_script_id": "O73808cb37bd63f5f7d92415e736e999",
            "Actiived_script_name": "这是一个脚本例子",
            "Actiived_script_encode": "true",
            "Weblogin_Account_Count": "4",
            "Weblogin_Account_name": "<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>",
            "Plugins_Count": "4",
            "Plugin_Id": "jjbnhpnlakcdgfnnldamfeinfmahhdlm,jjbnhpnlakcdgfnnldamfeinfmahhdlm,jjbnhpnlakcdgfnnldamfeinfmahhdlm,jjbnhpnlakcdgfnnldamfeinfmahhdlm",
            "template_id": "123456",
            "template_name": "TikTok国际版",
            "browser_Path": "D:\\mbbrowser\\Chromium_x64\\chromium.exe",
            "browser_CDP_Port": 46973,
            "MBData_Path": "C:\\MBDATA\\xxxxxxxxxx\\xxxxxxxxxx\\xxxxxxxxxxx",
            "Public_ip": "************",
            "Internel_ip": "**************",
            "isDynamicIp": false,
            "StartPage": "about:blank",
            "proxyType": "socks5",
            "proxy_ip": "127.0.0.1",
            "proxy_port": "1080",
            "isHeadless": "true",
            "webdriver": "C:\\Users\\<USER>\\houniao\\Driver\\100\\chromedriver.exe",
            "status": 0
        }],
        "total": 1
    }
}
```

## 使用说明

1. 支持同时启动多个环境
2. 可配置无头模式和有头模式
3. 启动参数可用于优化性能（如禁用图片加载）
4. 返回的webdriver路径可直接用于selenium
5. CDP端口用于puppeteer等工具连接
6. 建议使用[POSTMAN调试工具](/api/postman-example)进行接口测试

## 相关链接

- [关闭环境](/api/browser/stop)
- [强制终止环境](/api/browser/kill)
- [POSTMAN调试工具](/api/postman-example)
- [错误码对照表](/api/code)
