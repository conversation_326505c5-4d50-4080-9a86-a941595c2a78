﻿<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Window size="1140,680" sizebox="4,4,4,4" caption="0,0,0,50" mininfo="1140,680" roundcorner="5,5,5,5" showshadow="true" shadowsize="3" shadowposition="0,0" shadowcolor="#ff6272A4" fademode="true">
  <Include source="Default.xml" />

  <VerticalLayout width="953" height="590" bkcolor="#FF282A36">
    <HorizontalLayout height="37">

    	 <Button name="login" bkimage="icon.png" padding="8,6,0,0" width="28" height="28"/>
      <Label name="pluginswndtitle" padding="6,4,0,0" text="环境登录帐户管理器" width="180" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" align="left" font="8"></Label>

      <Control />
      <Button name="minbtn" width="28" height="26" tooltip="最小化" normalimage="minbtn.png" hotimage="minbtn_hover.png" pushedimage="minbtnpush.png" />
      <Button name="maxbtn" width="28" height="26" tooltip="最大化" normalimage="maxbtn.png" hotimage="maxbtn_hover.png" pushedimage="maxbtnpush.png" />
      <Button name="restorebtn" visible="false" width="28" height="26" tooltip="还原" normalimage="restorebtn.png" hotimage="restorebtn_hover.png" pushedimage="restorebtnpush.png" />
      <Button name="closebtn" width="28" height="26"  tooltip="关闭" normalimage="closebtn.png" hotimage="closebtn_hover.png" pushedimage="closebtnpush.png" />

    </HorizontalLayout>
  <HorizontalLayout name="bkground">


 <HorizontalLayout name="loading_data" bkcolor="#ffe9e9e9" visible="true">

	    <VerticalLayout >

					     <HorizontalLayout name="loading_data" height="240">
					    	 <Control />
					    		<GifAnim name="data_loading" bkimage="dataloading.gif" height="200" width="200" padding="0,40,0,0" auto="true"/>
                 <Control name="success" visible="false" padding="0,100,0,0"  bkimage="success.png" width="120" height="120" align="center" />
					    	 <Control />
					     </HorizontalLayout>


					     <HorizontalLayout height="30" >
					    	 <Control />
					    		  <Label name="data_percent" text="0%" width="300" textcolor="#FF616161" hottextcolor="#ff000000" align="center" font="10"></Label>
					    	 <Control />
					     </HorizontalLayout>

					     <HorizontalLayout height="60" >
					    	 <Control />
					    		  <Label name="process_description" text="插件正在版本验证中，请稍侯.. " width="953" textcolor="#FF616161" hottextcolor="#ff000000" align="center" font="8"></Label>
					    	 <Control />
					     </HorizontalLayout>

              <HorizontalLayout height="40" >
              </HorizontalLayout>

              <HorizontalLayout name="backarea" width="953" height="60" visible="false">
                <Control />
                <Button text="返回" name="back" width="120" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	normalimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
                <Control width="100" />
                <Button text="退出" name="closewnd1" width="120" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	normalimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
                <Control />
              </HorizontalLayout>
      </VerticalLayout>

 </HorizontalLayout>

		<VerticalLayout name="data" visible="false">


			<HorizontalLayout height="56" >
        <Control width="20"/>
        <!--<VerticalLayout width="250">
          <Combo name="userplugin" bordersize="0" padding="0,10,0,10" width="250" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="8" bkcolor="#ffdce1e7"
              normalimage="file='Profile\Proxy_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\proxy_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\proxy_Combox_Click.png' corner='5,5,25,10'"
              combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
              itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0" textpadding="0,0,20,0" endellipsis="true">
          </Combo>
        </VerticalLayout>-->
        <VerticalLayout width="430">
          <Combo name="searchlist" reselect="true" dropboxsize="0,450" bordersize="0" padding="1,10,0,10" width="420" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="4,2,0,0" bkcolor="#ffdce1e7"
														normalimage="file='Profile\Setting_Combox_Normal_big_large.png' corner='5,5,30,5'" hotimage="file='Profile\Setting_Combox_Hover_large.png' corner='5,5,30,5'" pushedimage="file='Profile\Setting_Combox_Click_large.png' corner='5,5,25,10'"
												combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,30,5'"
												itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0">
          </Combo>
				         <RichEdit name="plugin_search" pos="0,10,0,10" height="36" width="378" tipvaluecolor="#FF333333" borderround="7,7" bkcolor="#ffdce1e7" font="8" textpadding="10,8,20,0" maxchar="300" tipvalue="请输入关键字查找插件.." multiline="false" textcolor="#ff333333" rich="false" transparent="false" float="true">
				      </RichEdit>
				</VerticalLayout>
        <Control />
        <VerticalLayout width="250">
          <Combo name="group" bordersize="0" padding="0,10,0,10" width="250" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="8" bkcolor="#ffdce1e7"
              normalimage="file='Profile\Proxy_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\proxy_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\proxy_Combox_Click.png' corner='5,5,25,10'"
              combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
              itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0" textpadding="0,0,20,0" endellipsis="true">
          </Combo>
        </VerticalLayout>
        <VerticalLayout width="196">
          <RichEdit name="session_search" padding="6,10,0,10" height="36" width="188" tipvaluecolor="#FF333333" borderround="7,7" bkcolor="#ffdce1e7" font="8" textpadding="10,8,20,0" maxchar="300" tipvalue="请输入关键字查找会话.." multiline="false" textcolor="#ff333333" rich="false" transparent="false">
          </RichEdit>
        </VerticalLayout>
        <Control width="20"/>
        <!--<VerticalLayout width="230">
              <Combo name="agent" bordersize="0" padding="21,0,0,10" width="200" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="4,2,0,0" bkcolor="#ffdce1e7"
                   normalimage="file='Profile\Setting_Combox_Normal_big_large.png' corner='5,5,25,10'" hotimage="file='Profile\Setting_Combox_Hover_large.png' corner='5,5,25,10'" pushedimage="file='Profile\Setting_Combox_Click_large.png' corner='5,5,25,10'"
               combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
               itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0">
              </Combo>
            </VerticalLayout>-->
      </HorizontalLayout>

			<HorizontalLayout height="4" bkcolor="#FFffffff">
	</HorizontalLayout>

      <HorizontalLayout bkcolor="#FFffffff">
        <Control width="20"/>
        <VerticalLayout>
          <List name="list_plugins_manager" inset="0,0,1,1" scrollwheel="true" bordersize="1" itembkcolor="#FFffffff" itemselectedbkcolor="#FFe0e0e0" itemhotbkcolor="#FFf2f2f2" bordercolor="#FFD9DADB" vscrollbar="true" floatscrollbar="true" scrollfbarFade="128">
						<ListHeader height="36" bordersize="1,1,0,1" bordercolor="#FFD7D7D7" bkcolor="#FFF9F9FA">
							<ListHeaderItem text="操作" name="header_device_choice" width="50" align="left" textpadding="10,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="名称" name="header_name" width="120" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="平台" name="header_platform" width="120" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="域名" name="header_domainurl" width="120" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="账户" name="header_user" width="110" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="密码" name="header_pass" width="110" align="left" textpadding="15,0,0,0"></ListHeaderItem>
            </ListHeader>
          </List>
          <HorizontalLayout height="30" >
            <Button name="selallplugins" padding="4,4,0,0" align="left" height="20" width="26" text="全选" font="5" hottextcolor="#FF005ed3" />
            <Button name="unselallplugins" padding="4,4,0,0" align="left" height="20" width="26" text="反选" font="5" hottextcolor="#FF005ed3" />
            <Button name="delplugins" enabled="false" padding="4,4,0,0" align="left" height="20" width="26" text="删除" font="5" hottextcolor="#FFff4444" />
            <Button name="clearplugins" visible="false" enabled="false" padding="4,4,0,0" align="left" height="20" width="50" text="清除插件" font="5" hottextcolor="#FFcf0000" />
            <Button name="refreshplugin" padding="4,4,0,0" align="left" height="20" width="26" text="刷新" font="5" hottextcolor="#FFff4444" />
            <Button name="viewpass" padding="4,4,0,0" align="left" height="20" width="96" text="显示密码" font="5" hottextcolor="#FFff4444" />
          </HorizontalLayout>
        </VerticalLayout>
        <Control width="20"/>
        <VerticalLayout width="446">
          <List name="list_session_manager" inset="0,0,1,1" scrollwheel="true" bordersize="1" itembkcolor="#FFffffff" itemselectedbkcolor="#FFe0e0e0" itemhotbkcolor="#FFf2f2f2" bordercolor="#FFD9DADB" vscrollbar="true" floatscrollbar="true" scrollfbarFade="128">
            <ListHeader height="36" bordersize="1,1,0,1" bordercolor="#FFD7D7D7" bkcolor="#FFF9F9FA">
              <ListHeaderItem text="操作" name="header_device_choice" width="50" align="left" textpadding="10,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="环境名称" name="header_name" width="192" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="创建时间" name="header_cdate" width="150" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="插件数" name="header_proxy" width="56" align="center" textpadding="0,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
            </ListHeader>
          </List>

          <HorizontalLayout height="30" >
            <Label name="lb_pluginsinst" text="已选中环境包含的帐户列表" padding="0,4,0,0" texttooltip="true" endellipsis="true" width="300" textcolor="#FF000000" hottextcolor="#ff000000" align="left" font="8"></Label>
            <Control />
            <Button name="selallsession" padding="4,4,0,0" align="left" height="20" width="26" text="全选" font="5" hottextcolor="#FF005ed3" />
            <Button name="unselallsession" padding="4,4,0,0" align="left" height="20" width="26" text="反选" font="5" hottextcolor="#FF005ed3" />
          </HorizontalLayout>

          <List name="list_plugins_items" inset="0,0,1,1" scrollwheel="true" bordersize="1" itembkcolor="#FFffffff" itemselectedbkcolor="#FFe0e0e0" itemhotbkcolor="#FFf2f2f2" bordercolor="#FFD9DADB" vscrollbar="true" floatscrollbar="true" scrollfbarFade="128">
            <ListHeader height="36" bordersize="1,1,0,1" bordercolor="#FFD7D7D7" bkcolor="#FFF9F9FA">
              <ListHeaderItem text="操作" name="header_device_choice" width="50" align="left" textpadding="10,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="账户名称" name="header_name" width="100" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="网站站点" name="header_domainurl" width="100" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="网站账户" name="header_domainurl" width="100" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="登录密码" name="header_domainurl" width="100" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
            </ListHeader>
          </List>
          <HorizontalLayout height="30" >
            <Button name="selallpluginsinst" padding="4,4,0,0" align="left" height="20" width="26" text="全选" font="5" hottextcolor="#FF005ed3" />
            <Button name="unselallpluginsinst" padding="4,4,0,0" align="left" height="20" width="26" text="反选" font="5" hottextcolor="#FF005ed3" />
            <Button name="delpluginsinst" enabled="false" padding="4,4,0,0" align="left" height="20" width="26" text="删除" font="5" hottextcolor="#FF005ed3" />
            <Button name="viewpass2" padding="4,4,0,0" align="left" height="20" width="56" text="显示密码" font="5" hottextcolor="#FFff4444" />
            <Button name="setstarturl" padding="4,4,0,0" align="left" height="20" width="120" text="设定为自动打开页面" font="5" hottextcolor="#FFff4444" />
          </HorizontalLayout>
        </VerticalLayout>
        <Control width="20"/>
				</HorizontalLayout>
		</VerticalLayout>

	</HorizontalLayout>
    <HorizontalLayout name="btnarea" height="52" bkcolor="#ffe9e9e9" inset="10,0,0,0">
         <!--<CheckBox name="opt_checkAll" text="全选" textpadding="57,1,0,0" selected="false"  visible="true" padding="3,16,0,0" height="18" width="16" normalimage="file='list_check_normal.png'" hotimage="list_check_hover.png" selectedimage="file='list_checked_actvice_normal.png'" ></CheckBox>-->
        <Label name="status" text="" padding="10,0,0,0" textcolor="#FF0006ff" hottextcolor="#ff000000" align="left" font="8"></Label>
         <Control width="10"/>
      <!--<Button name="help" padding="4,18,0,0" align="left" height="20" width="66" text="在线帮助" font="5" textcolor="#FF519cff" hottextcolor="#FF005ed3" />-->
      <VerticalLayout width="120">
        <Control />
        <Button text="添加新账户" name="newpassinfo" tooltip="添加新账户" padding="2,2,0,0" textpadding="5,0,5,0" texttooltip="true" endellipsis="true" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	bkcolor="#FFffffff" bordersize="1" bordercolor="#ffb3b3b3" borderround="7,7" hotbkcolor="#fff1f2f1"/>
        <Control />
      </VerticalLayout>
      <Control width="10"/>
      <VerticalLayout width="260">
      		<Control />
          <Button text="将选中的账户分配到勾选的环境中" enabled="false" name="instpassinfos" textpadding="10,0,10,0" texttooltip="true" endellipsis="true" padding="2,2,0,0" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	bkcolor="#FFffffff" bordersize="1" bordercolor="#ffb3b3b3" borderround="7,7" hotbkcolor="#fff1f2f1"/>
          <Control />
      </VerticalLayout>
      <Control  width="20"/>
    </HorizontalLayout>
    <Control name="dragicon" float="true" width="14" height="16" bkimage="dragicon.png"/>
  </VerticalLayout>
</Window>
