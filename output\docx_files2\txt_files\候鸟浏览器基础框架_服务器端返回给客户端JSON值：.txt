服务器端返回给客户端JSON值：

服务器端返回给客户端JSON值：

{

"msg": "Success",

"code": 0,

"data": {

"list": [{

"session_id": "9ad2d26aed624ad7e6ad93f53024687b",

"uid": "3",

"hsts": "999567",

"webgl": "3F8C71BFA45AEBB9|1",

"canvas": "-298475891|1",

"plugins": "02125A4589EC3B0B|1",

"audio": "19F2EC826DA99435|1",

"rects": "7CDA06DF8A5DDBFF|1",

"fonts": "A5CCA923FE75C966|1",

"security_percent": "100",

"clientinfo": {

"clientip": "************",

"host": "China Unicom",

"dns": "**************",

"countryCode": "CN",

"os": "Windows 10",

"webrtc_public": "************",

"browser": "Chrome *********",

"region_name": "Henan",

"latitude": "34.7584",

"longitude": "113.6485",

"timezone": "+08:00",

"timezone_text": "Asia/Shanghai",

"language": "zh-CN"

},

"add_time": "2023-10-18 14:55:06"

},

...

]

}

}

约定：

[20231030]

第四十章
候鸟常用数据设定

客户端环境面板常用数据管理

依据传统，具体工作步骤、顺序安排，人员分配在工作群里具体阐述。

综述：

用户在环境中选择需要的数据较为困难，因为当前的版本提供给用户的选择项过多，用户在寻找自已要的数据项时非常困难。此功能提供用户全局选择常用数据项，方便用户快速选择需要的项目。此功能作为强化用户使用候鸟习惯进行提供。

[20231120]

第四十一章
候鸟历史ITEM环境切换功能

客户端历史ITEM环境包数据管理

依据传统，具体工作步骤、顺序安排，人员分配在工作群里具体阐述。

综述：

用户对于同一个环境中，存在多个历史ITEM版本的功能一直缺失，在多内核、多业务、多个时间维度中，支持用户选择当前环境下，各个历史版本并进行切换，有利于用户对于环境配置数据的深入控制，强化用户对候鸟产品的使用粘性。

[20240507]

第四十二章
自动化脚本公共库限额使用说明
脚本公共库-客户端/服务器端

依据传统，具体工作步骤、顺序安排，人员分配在工作群里具体阐述。

综述：

为了增加用户对高资费套餐的购买，拉开各个套餐功能上的区别。同时为了候鸟官方自有官方脚本库不被外界滥用，抄袭，一锅端。进而提供自动化脚本公共库使用限额的约束规范。

一、规范原则：

原有约定不变：【体验版、个人自定义版不支持申请API凭证】

官方自动化脚本公共库仅针对已成功获取到API凭据的帐户开放（开放指的具体功能是：导出、修改[查看源代码]、指派到环境），非开放状态下的用户帐户，在客户端仍可以看到海量的自动化脚本公共库列表。

现根据当前候鸟套餐类别一一说明限额：

体验版

仅允许查看自动化脚本公共库列表（非开放状态）

个人自定义版

仅允许查看自动化脚本公共库列表（非开放状态）

个人无限版

每日10份公共库脚本导出、修改、指派到环境。

D、团队协作版

单帐户每日 10份 + 子帐户数*10 次 公共库脚本导出、修改、指派到环境。

3、对于公共库的脚本，在客户端上，原修改按钮点击后，运行的流程不能修改服务器公共库中的脚本（即只能有查看权限，不能有修改服务器上脚本的权力）。

二、购买区各套餐之功能列表增加限额区分说明。

服务新增：官方自动化脚本库数千套脚本，指派到我的商业海量环境

第一列不变。

第二列增加一行：官方商业脚本库导出到本地。10套脚本/天

第三列增加一行：官方商业脚本库导出到本地。10套脚本/天 * 子帐户数

三、实现流程说明：

1、服务器端用户表，根据UID、套餐增加子表，记录用户当天可用导出脚本库额度。

2、用户通过客户端行使上述导出等功能时，客户端请求服务器指定接口: xxx/script_permission，判定是否允许进行脚本的导出、指派、修改（查看）。

3、服务器端 xxx/script_permission 接口，通过UID，TOKEN，读取子表返回

script_permission约定值给客户端。

[20240708]


================================================== 表格内容 ==================================================

{
    "code": -12,
    "data": {
        "Delete_Plugin_Success": " ncennffkjdiamlpmcbajkmaiiiddgioo",
        "Delete_Plugin_Failed": "f994d8e641ce7006acfa36c901829ff2"
    },
    "message": "Plugin Delete Finished."
}

参数名称 | 类型 | 必传 | 样例串/默认值 | 说明