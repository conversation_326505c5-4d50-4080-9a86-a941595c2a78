标题: HSTS协议
英文标题: HSTS Protocol
ID: 100
分类ID: 25
添加时间: 1614752257
更新时间: 1685430155
访问次数: 0
SEO标题: HSTS协议
SEO关键词: HSTS协议
SEO描述: HSTS协议

================================================== 内容 ==================================================
国际互联网工程组织 IETE 正在推行一种新的 Web安全协议HTTP Strict Transport Security（HSTS）。采用 HSTS 协议的网站将保证浏览器始终连接到该网站的 HTTPS 加密版本，不需要用户手动在 URL 地址栏中输入加密地址。该协议将帮助网站采用全局加密，用户看到的就是该网站的安全版本。

HSTS 的作用是强制客户端（如浏览器）使用 HTTPS 与服务器创建连接。服务器开启 HSTS 的方法是，当客户端通过 HTTPS 发出请求时，在服务器返回的超文本传输协议响应头中包含 Strict-Transport-Security 字段。非加密传输时设置的 HSTS 字段无效。

================================================== 英文内容 ==================================================
Internet Engineering Organization IETE is promoting a new Web Security protocol, HTTP Strict Transport Security (HSTS). Websites using the HSTS protocol will ensure that the browser is always connected to the HTTPS encrypted version of the website, without requiring the user to manually enter the encrypted address in the URL address bar. The protocol will help websites adopt global encryption so that users see a secure version of the site.

The purpose of HSTS is to force a client (such as a browser) to create a connection to the server using HTTPS. The server enables HSTS by including the Strict-Transport-Security field in the hypertext Transfer protocol (TLS) response header returned by the server when the client makes a request over HTTPS. The HSTS field set during unencrypted transmission is invalid.