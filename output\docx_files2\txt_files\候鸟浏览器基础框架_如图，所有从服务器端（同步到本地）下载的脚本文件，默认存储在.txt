如图，所有从服务器端（同步到本地）下载的脚本文件，默认存储在此文件夹中。脚本根据类别分别存放到子文件夹中。

一、需求详述：

由于各个环境之间物理隔离之特性。因此，在业务开展中，候鸟历史VIP客户抱怨候鸟的产品从未支持过Sele与Pup的自动化脚本。在每个浏览器（环境中），VIP客户希望避免多个环境逐个安装、调试自动化脚本，希望自有脚本能安全存储在候鸟云端、支持脚本和环境能完整分享给同事，支持环境包含自动化脚本能完整的导出到本地存储。避免日常自动化脚本的管理过于繁琐和调试麻烦。需要候鸟提供在客户首次自行创建（导入）自动化脚本后，可以通过自行控制，指定其它环境批量支持相应自动化脚本（其它环境批量自动生效）。

初始约定：自动化脚本数据与管理，以用户为单位，物理隔离，即A用户用A脚本和B用户用A脚本，会在服务器的A、B用户的ITEM包中实际保存2份 A脚本。因此保证了各个用户间脚本版本的延伸性，并带来其业务稳定性。

二、数据结构说明：

基于帐户为单位，全局脚本列表存储： autoscripts.xml

存储位置：

Autoscripts 全局脚本存储位置：

Autoscripts.xml内容：

<?xml version="1.0" encoding="gb2312" ?>

<VERSION version="1" xmlversion="0">

<VER ID="2" AUTOSCRIPT_ID="17c70e014d61b1fa43d3638ca5a1bc25" SESSION_UNIQUE_ID="">

<NAME VALUE="D5E2CAC7D2BBB8F670757074656572B2E2CAD4BDC5B1BE" />

<AUTOSCRIPTVERSION VALUE="*******"/>

<SIZE VALUE="0"/>

<TYPE VALUE="0"/>

<CLASS VALUE="0"/>

<PASSWORD VALUE="aes(password)"/> [2022-11-11新增]

<MD5 VALUE="7c547fffac2430502f723b4ce76ca813"/>

<DES VALUE="D5E2CAC7D2BBB8F670757074656572B2E2CAD4BDC5B1BEA3ACD2BBB8F6B2E2CAD4C0FDD7D3A3ACD5E2CAC7D2BBB8F6C0FDD7D3C3E8CAF6A1A3"/>

<INSTTIME VALUE="1656453243"/>

</VER>

</VERSION>

说明如下：

如图，所有从服务器端（同步到本地）下载的脚本文件，默认存储在此文件夹中。脚本根据类别分别存放到子文件夹中。

Autoscripts.xml对应上图中的 A区列表，分成两个下拉项显示，PUP的脚本列单独显示，SELE的脚本列单独显示，官方脚本库的列表在后面详述。

ITEM包目录中，存储脚本管理器面板C区的autoscripts 脚本文件和ITEM包ConfigData.xml内容。

【客户端数据结构及数据存储详述】【补充 2022-06-24】【重点】

【存储架构支持：向后兼容支持 多脚本、多脚本顺序控制、多脚本顺序执行、多脚本间隔时间设定执行的数据存储结构，前期逻辑约定仅支持单脚本，不支持多个脚本同时运行，但此数据存储架构支持多脚本，向后兼容】

全局文件夹：AutoScripts

key_1文件夹

脚本.xml  --> 取出脚本代码-> 生成临时脚本文件供浏览器调用。

key.xml

key_2文件夹

脚本.xml

key.xml

->

key1脚本包.zip 【key_1文件夹的zip包】

key2脚本包.zip 【key_2文件夹的zip包】

上图为脚本.xml中内容，保存脚本的配置信息及代码段。

浏览器执行脚本时：将此xml中的脚本代码取出，存储为临时实际脚本文件，再调用执行。

【重点】Key.xml： 保存 脚本.xml文件的md5值。

通过key.xml ，客户端可以通知/控制服务器是否需要更新用户自有脚本表记录。

如果有节点值变化了，但不需要服务器更新表记录，则这类节点值放在key.xml中。
如果有节点值变化了，一定需要服务器更新表记录，则这类节点值放在 脚本.xml中。

例：用户更改了脚本的名称，并保存，则这个xml文件的md5值就变了，变了就是对的，这时服务器就知道要更新他的表记录。

autoscripts_desc ： 用户在脚本编辑器里，用户创建，编辑的脚本代码自行加的一个备注、脚本描述信息文本串。由客户端将这个文本串写入到脚本.xml节点中。

此参数 由客户端提供。

【客户端 - 服务器端 数据交换/通讯详述】

A、Full包同步说明：

1、autoscripts.xml 连同full.zip 包进行打包，并按原约定进行同步处理。

2、autoscripts.xml在任意时间节点产生变化，需触发full包进行版本校验，同步上传。

B、脚本包同步到服务器端说明：

1、用户在客户端安装(自行创建，自行导入)的脚本包，支持同步到服务器上。

2、服务器端通过api/autoscripts_upload 同步接口进行自动化脚本包的文件存储和脚本名称、文件名及md5值、upload时间，脚本文件大小，脚本key(唯一不重复ID,由客户端生成，算法等同于unique_session_id)，UID值存表（表名Autoscripts）。

3、脚本包key，md5值由客户端生成提交，服务器端进行key,md5验证，验证通过后本地存储并成功存表。

4、所有脚本文件包存放在/www/wwwroot/admin.mbbrowser.com/upload/Autoscripts 目录下，所有用户上传的脚本文件包均放在此目录下。（重点）

5、脚本包api/autoscripts_upload同步接口根据脚本包的脚本KEY的值进行唯一判定，对于/www/wwwroot/admin.mbbrowser.com/upload/ Autoscripts 目录下，仅且只允许存储唯一脚本KEY相同的包。

6、客户端在item.zip的configdata.xml中记录此环境下已分配的脚本包，允许多个脚本包，通过子节点记录多个脚本包的名称、文件名、MD5。

7、客户端请求获取脚本包下载时，通过提交key值来成功下载需要的包。

8、服务器端根据表autoscripts通过约定接口 api/ autoscripts _list返回给客户端已存在服务器上的所有脚本包列表。

9、客户端根据返回的list结果集合，分类成PUP和SELE两大类脚本列表。

三、自动化脚本管理窗口 数据来源于：autoscripts.xml

需实现的基础功能有：

1、列出用户已安装过的所有脚本包（不重复）【数据来源：autoscripts.xml】

2、列出脚本包的名称，脚本包的唯一标识（autoscripts key），安装时间，相关描述。

3、列出脚本包的唯一KEY。

扩展功能：

支持显示分组列表、环境列表。

支持单选，多选脚本包，指派给某个分组。（实际操作为：将item.xml文件中的autoscripts节点进行添加赋值）

支持单选多选脚本包，指派给某个环境。（实际操作为：将item.xml文件中的autoscripts节点进行添加赋值）

支持单选多选脚本包，指派给多个分组。（实际操作为：将item.xml文件中的autoscripts节点进行添加赋值）

支持单选多选脚本包，指派给多个环境。（实际操作为：将item.xml文件中的autoscripts节点进行添加赋值）

支持一键清空所有脚本包。（实际操作为：遍历所有item，并将item.xml文件中的autoscripts 相关节点删除）

支持一键清空指定分组下所有脚本包。（实际操作为：将item.xml文件中的autoscripts相关节点删除）

支持一键清空指定多个环境下所有脚本包。（实际操作为：将item.xml文件中的autoscripts相关节点删除）

四、浏览器内核与 脚本xml数据源关系说明：

客户端子进程在用户运行主面板环境时，获取一次当前chrome窗口已安装脚本的列表数据。

同时，客户端加载全局plugins.xml进行脚本包的KEY值和ITEM目录中脚本文件是否存在比对。

判定已安装的脚本中，是否有 脚本项 未存在于全局autoscripts.xml中，如果已存在，判断此chrome窗口对应的此item环境包的configdata.xml的autoscripts节点是否存在此脚本文件及ID,如果存在，则跳过。如果不存在，进行第5节点的动作。

判定如果未存在于全局autoscripts.xml中，将用户新安装的脚本的：脚本名称、脚本文件名、脚本文件的唯一标识（KEY），MD5, 安装时间新增模式添加到 全局autoscripts.xml中。

触发对此环境的item.zip包中的configdata.xml中的autoscripts节点进行新增赋值, 如ID相同，则进行此节点项更新，同时判断是否存在脚本文件在ITEM目录中，如果不存在，需要将脚本进行复制到ITEM环境目录中（如果本地不存在，则进行API请求下载过来，将脚本存 放到全局目录中，再存放到ITEM环境目录中）。

【原有即定流程】触发item包的版本验证与上传同步。

【原有即定流程】触发full包进行一次版本校验后进行full包的上传流程，将autoscripts.xml连同full包更新到服务器。

五、浏览器自动化脚本管理面板，窗体说明

浏览器自动化脚本管理器 窗口包含了以下功能：

1、用户自行管理（增加删除）已安装过的所有脚本。

2、用户自行添加官方提供的所有脚本。

3、支持用户快速查找需要的脚本。

4、所见即所得，一键查看不同分组，单个或多个环境下 已安装的脚本。

5、所见即所得，一键查找任意关键字环境下已安装的脚本。

6、支持一键 勾选多个脚本， 支持 安装到单个环境或多个环境中。

注：此窗口的长宽和会话管理器的窗口长宽严格一致。

A框列表框，请求服务器API获得用户自有脚本列表。

B框列表，列出分组下的各个环境

C框列表，每个环境item.zip中的configdata.xml中的autoscripts节点列表数据。

A区顶部下拉列表控件显示二个项:

已安装过的浏览器脚本（总数）

使用官方所有浏览器脚本（总数）

已安装过的浏览器脚本  = 用户full包中 account/autoscripts.xml

使用官方所有浏览器脚本 =  请求服务器获得的所有已在服务器存在的所有脚本包。

C区列表，显示用户已勾选环境的所有脚本，没勾选的环境的脚本不用列出来。

查找环境仍旧为全局查找，不是在分组内查找。

左下角的文字显示的是处理过程与处理结果。

XML数据结构附加节点

XML数据结构新增说明：

Item.zip 中 configdata.xml

<?xml version="1.0" encoding="gb2312" ?>

<VERSION version="4740" xmlversion="2">

<VER ID="6" VERSION="28" SESSION_UNIQUE_ID="a07db3145ad61566a392d04038671f39" FROMUID="0" SHARETIME="" FROM_ACCOUNT="" GROUP_ID="0" GROUP_NAME="" IR32="1">

<SESSION NAME="27" TOP="0" COLOR="#FFffffffff" SYSTEM="Win32" TEMPLATEID="11602" TEMPLATEFMASK="300" TEMPLATENAME="抖音国际版" PLUGINCOUNT="2"/>

<IS_ANONYMITY ANONYMITY = "0"/>

<COMMENT COMMENT_STRING=""/>

<SESSION_DEFINE_CODE SPHEREGL="0" ENABLE_LOCALHTML5_STORAGE="0" SAVE_LOCALHTML5_STORAGE="0" SAVE_INDEXDB_STORAGE="0"/>

<NETWORK TYPE="http" PMODE="2" IP="************" PORT="20000" USER="liantiao111" PASSWORD="liantiao111" PUBLIC_IP="" FAKEIP="***************" />

<NETWORK_CTR NA="0" FAKE_WRTC="0" SAME_IP="0" IPV6="0" WRTCOFF="0" DNS="" />

<USERAGENT_BROWSER CHROME="0" SAFARI="0" MSIE="0" OTHER="0" REGEN_CONFIG_USERAGENT="0" MAINURL="" />

<USERAGENT_STRING UA="Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/84.0.4147.89 Safari/537.36" UA_LNG="en-US" UA_LNG_STRING="[@en-US@,@US@]" />

<USERAGENT_CTR DISPOPUPS="0" ENABLE_SERVICE="0" BLOCKLNGINPUT="0"  />

<RESOLUTION WIGHT="1920" HEIGHT="1200" />

<RESOLUTION_CTR EMU_SCREEN="0" EMU_TOUCH="0" />

<POSITION LONGITUDE="0.000000" LATITUDE="0.000000" COUNTRY="AD" />

<TIMEZONE TIMEZONE_NAME="Africa/Abidjan (0)" ADD_VALUE=""/>

<FINGERPRINT_CODE AUDIO="1" CANVAS="1" FONTS="1" RETCS="1" DNS="1" AUTOIPCHECK="1" fpver="1"/>

<OTHER_SETTING PLUGINS_MIMETYPE="0" SAVE_ENCRYPT_COOKIES="0" ENABLE_FLASH="0" DYNAMIC_FINGERPRINTS="0" BLOCK_CANVAS_OUTPUT="0" />

<DYNAMIC_FINGERPRINTS_CTR D_AUDIO="0" D_CANVAS="0" D_FONTS="0" D_RETCS="0" D_MEDIA="0" D_WEBGL="0" D_MIME="0" D_PLUGINS="0" />

<PLUGIN_LIST NUM="2">

<PLUGIN ID="13b976d9c3305ebc26e5e0ae338a475d" NAME="setupvpn" VER="*******" MD5="**********"/>

<PLUGIN ID="bb32d8993a1fdc9757c4f1e8735baa53" NAME="setupvpn123" VER="*******" MD5="********************"/>

</PLUGIN_LIST>

<AUTOSCRIPTS_LIST NUM="2">

< AUTOSCRIPTS ID="13b976d9c3305ebc26e5e0ae338a475d" NAME="我的脚本1"  KEY=”UIERFIAHFDUAIIDSD”  TYPE ="0"  CLASS=”0” ENCRYPT =”aes(PASSWORD)” SIZE=”12312” MD5="**********" IS_VALID=”0” />

< AUTOSCRIPTS ID="bb32d8993a1fdc9757c4f1e8735baa53" NAME="我的脚本2"  KEY=”EIOADSKLFJADDDASD” TYPE="1"  CLASS=”0”  ENCRYPT =” aes(PASSWORD)” SIZE=”1232112” MD5="********************" IS_VALID=”1” />

</ AUTOSCRIPTS_LIST>

<ACCOUNT_LIST NUM="1">

<ACCOUNT DOMAIN="iuse.com.cn" PLATFORM="自定义类型" DOMAINURL="https://www.iuse.com.cn" LOGIN="111" ORGIN_PASSWORD="1111" ENCRYPT_PASSWORD="" PASSWORD2FA="" IS_VALID="1" action_url="" signon_realm="https://www.iuse.com.cn" BOOKMARK_URL="https://www.iuse.com.cn"  auto_open=”0” CREATE_TIME="" UPDATE_TIME=""/>

</ACCOUNT_LIST>

<ENV_CONFIG>

<EC_NOLOADIMG VALUE="0"/>

<EC_NOAUTOVIDEO VALUE="0"/>

<EC_NOSOUND VALUE="0"/>

<EC_NOTRANSLATE VALUE="0"/>

<EC_NOSAVEPWD VALUE="0"/>

<EC_NOMSGNOTIFY VALUE="0"/>

<EC_NOCLIPBOARD VALUE="0"/>

<EC_STOPITEM VALUE="0"/>

<EC_CAMERA VALUE="0"/>

<EC_DEVMODE VALUE="0"/>

<EC_CACHE_CLEAR VALUE="0"/>

<EC_LAST_PAGE VALUE="0"/>

<EC_COOKIES_BACKUP VALUE="0"/>

<EC_MOBILE_CURSOR_SWITCH VALUE="1"/>

</ENV_CONFIG>

<CACHE_CONFIG>

<CC_AUTODELETECACHE VALUE="0" DELETEDB="0" DISABLECLOUDSYNC="0"/>

<CC_CLEARCOOKIES VALUE="0" DISABLECLOUDSYNC="0"/>

<CC_CLEARHISTORY VALUE="0" CLEARHISTORYSYNC="0"/>

</CACHE_CONFIG>

<PERFORMANCE_CONFIG>

<PC_GPUACCELERATION VALUE="0"/>

<PC_RANDOMFINGERPRINT VALUE="0"/>

<PC_MEMORYSAVING VALUE="0"/>

</PERFORMANCE_CONFIG>

<IS_VALUED VALUED="0" />

<UPDATETIME VALUE="2022-03-21 18:06:58"/>

<CREATETIME VALUE="2021-03-05 18:34:28"/>

</VER>

</VERSION>

1、绿色区域为新增加部份，用来记录此用户此环境中所有已安装的脚本列表。

2、红色标记记录脚本的名称，为保证XML的格式合法，使用hex对此串进行加码。


================================================== 表格内容 ==================================================

{"message": "Success",
"status": 0,
"data": “
<EMAIL>:0, <EMAIL>:1, <EMAIL>:1”
}

{"message": "Success",
"status": 0,
"data": “<EMAIL>:0”
"account_reg_date": "2022-01-10 12:12:12",
" account_expire_date": "2023-12-12 12:12:12",
}