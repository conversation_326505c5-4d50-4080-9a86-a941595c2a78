<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>/api/members</title>
  <style>
/* 基础重置与排版 */
body {
  margin: 0;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.7;
  color: #333;
  background-color: #fff;
  max-width: 960px;
  margin: 0 auto;
  box-sizing: border-box;
}

/* 段落 */
p {
  margin: 1em 0;
}

/* 标题 */
h1, h2, h3, h4, h5, h6 {
  margin: 1.5em 0 0.8em;
  color: #2c3e50;
  font-weight: 600;
  line-height: 1.3;
}

h1 { font-size: 2em; border-bottom: 1px solid #eee; padding-bottom: 0.5em; }
h2 { font-size: 1.6em; }
h3 { font-size: 1.4em; }
h4 { font-size: 1.2em; }

/* 列表 */
ul, ol {
  margin: 1em 0;
  padding-left: 2em;
}

li {
  margin: 0.4em 0;
}

/* 引用块 */
blockquote {
  margin: 1.5em 0;
  padding: 0.8em 1.5em;
  background-color: #f9f9f9;
  border-left: 4px solid #ddd;
  color: #666;
  font-style: italic;
  border-radius: 0 4px 4px 0;
}

/* 代码行内 */
code {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  background-color: #f3f4f6;
  color: #e9602d;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-size: 0.95em;
  white-space: nowrap;
}

/* 代码块 */
pre {
  margin: 1.5em 0;
  padding: 1.2em;
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  overflow-x: auto;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
}

pre code {
  background: none;
  color: inherit;
  padding: 0;
  font-size: inherit;
  white-space: pre;
  display: block;
}

/* 表格 */
table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5em 0;
  font-size: 14px;
  overflow: hidden;
  box-shadow: 0 0 0 1px #e0e0e0;
  border-radius: 6px;
}

th, td {
  padding: 10px 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

th {
  background-color: #f8f9fa;
  color: #2c3e50;
  font-weight: 600;
  white-space: nowrap;
}

tr:nth-child(even) {
  background-color: #f9f9fb;
}

tr:hover {
  background-color: #f0f5ff;
}

/* 链接 */
a {
  color: #1a73e8;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

/* 分隔线 */
hr {
  border: 0;
  height: 1px;
  background: #ddd;
  margin: 2em 0;
}

/* 图片 */
img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 1em 0;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
  </style>
</head>
<body>
  <h2>获取成员列表</h2> <ul><li><p>获取所有和此帐户相关联的主帐号与子帐号数据。</p></li> <li><p><strong class="pcolor">【最大请求频率：10次/分钟】</strong></p></li> <li><p>Path：/members</p></li> <li><p>Method：POST</p></li> <li><p>Content-Type：application/json</p></li></ul> <blockquote><p>请求参数</p></blockquote> <div class="table"><table><thead><tr><td>参数名称</td> <td>类型</td> <td>必传</td> <td>样例串/默认值</td> <td>说明</td></tr></thead> <tbody><tr><td>Account</td> <td>string</td> <td>是</td> <td><EMAIL></td> <td>用户凭证 Account</td></tr></tbody></table></div> <blockquote><p>请求示例</p></blockquote> <div class="code-view"><pre><code>{ "Account": "<EMAIL>" }</code></pre> </div> <blockquote><p>返回参数</p></blockquote> <div class="code-view"><pre><code>{ "msg": "Success", "status": 0, "data": "<EMAIL>:0", "account_expire_date": "2023-07-26 18:46:48", "account_reg_date": "2020-07-29 15:13:28" }</code></pre> </div> <p><code>0</code> 表示主帐户，<code>1</code> 表示子帐户</p> <p><a class="ant-btn ant-btn-primary">使用POSTMAN调试此接口</a></p>
</body>
</html>