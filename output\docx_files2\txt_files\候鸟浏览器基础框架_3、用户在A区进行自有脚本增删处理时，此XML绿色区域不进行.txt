3、用户在A区进行自有脚本增删处理时，此XML绿色区域不进行数据变化。（仅全局autoscripts.xml full包中的有变化，同时服务器端有变化）

3、用户在A区进行自有脚本增删处理时，此XML绿色区域不进行数据变化。（仅全局autoscripts.xml full包中的有变化，同时服务器端有变化）

4、< AUTOSCRIPTS _LIST NUM="2"> 中的2表示总计已安装了有2个脚本包

5、TYPE=0表示为PUP类脚本，TYPE=1表示为SELE类脚本

6、IS_VALID=0 表示此脚本未使用。 IS_VALID=1表示此脚本已处理使用状态。【C区被勾选】

7、TYPE：0表示 0表示PUP脚本，1表示SELE脚本。

8、CLASS: 0：python  1：js   2:java

【2022-10-18 新增】
9、增加 ENCRYPT 参数， AES(PASSWORD) 有值表示加密，空值为明文。

【2024-11-11 新增】

10、ENV_CONFIG, CACHE_CONFIG，PERFORMANCE_CONFIG 节点。 新增ACCOUNT_LIST的auto_open架构。

七、在服务器端的数据库结构，表增加。

表一： （候鸟官方脚本库，全局库）表名：AUTOSCRIPTS

字段：

[主键]ID									   主键索引

Autoscripts_name    	(varchar)          	脚本包名称

Autoscripts _key   	   (varchar)			   脚本包唯一KEY值

Autoscripts _type   	   (int)			       脚本包类别：0表示PUP脚本，1表示SELE脚本。

Autoscripts _class   	   (int)			       脚本语言：0：python  1：js   2:java

Autoscripts _bussiness_type (int)            脚本商业用途类别代号

Autoscripts _function_type (int)            脚本功能用途类别代号

Autoscripts _owner  (int)            脚本创始者（首次上传）UID

Autoscripts_size      	(int)					脚本包大小使用字节数记录

Autoscripts _dec			(varchar)			脚本包说明，默认为空

Autoscripts _md5     	(varchar)			脚本包MD5,由客户端提供

Autoscripts _filename  	(varchar)			脚本包存储在服务器上文件名称

Create_Time 		(datetime)			   脚本包插入表中时间，字段属性默认填值

IS_VALID   			(bit)					   是否有效，默认为1（有效）

说明：此表为官方脚本库主表，记录所有用户通过客户端成功上传的脚本包。

条件为：根据 Autoscripts _key 保证唯一。

即当客户端通过自动化脚本管理器 或 通过本地导入的脚本进行安装新脚本到指定环境后，当客户端提交上传相同脚本KEY的脚本数据包时，在此表中只允许有一条记录、服务器全局脚本库目录中仅允许存放一份。

表二：（候鸟用户自有脚本集合，用户自有库）表名： USER_AUTOSCRIPTS

字段：

[主键]ID									主键索引

UID					(varchar)          	用户UID

Autoscripts _name    	(varchar)          	脚本包名称

Autoscripts _version   	(varchar)			脚本包版本

Autoscripts _size      	(int)					脚本包大小使用字节数记录

【2022-10-18】新增
Autoscripts _encrypt (int) 脚本包是否已加密 1 加密 0 未加密
Autoscripts _pass (varchar) 脚本包加密密码

Autoscripts _key       	(varchar)			脚本包KEY

Autoscripts _dec			(varchar)			脚本包说明，默认为空

Autoscripts _md5     	(varchar)			脚本包MD5,由客户端提供

Autoscripts _filename  	(varchar)			脚本包存储在服务器上文件名称

Create_Time 		(datetime)			脚本包插入表中时间，字段属性默认填值

Update_Time      (datetime)	       脚本包更新时间，用户更新脚本包的最近一次时间。

IS_VALID   			(bit)					是否有效，默认为1（有效）

注：此表为用户自有脚本包库表，记录用户已使用的所有脚本包。（A区列表）

条件为：根据 Autoscripts _key 保证唯一。

即当客户端通过脚本管理器 或或 通过本地导入的脚本进行安装新脚本到指定环境后，客户端 提交上传 脚本的KEY 和脚本目录的数据压缩包到服务器时，根据 Autoscripts _key 保证唯一。基于UID为条件 在此表中只允许有一条记录。

八、客户端与服务器端 流程说明：

【显示列表，新增脚本包】

1、用户自有脚本包列表显示 与 客户端获取流程：

自动化脚本管理器在打开后，使用线程请求服务器API/GET_USER_AUTOSCRIPTS 接口

请求参数：UID、TOKEN

2、服务器端通过此接口读取USER_AUTOSCRIPTS表中 UID所有记录并通过JSON格式进行返回给客户端。

客户端收到列表数据，显示在自动化脚本管理器的A区中。

2、用户安装 新脚本 显示与提交到服务器 流程：

用户通过自动化脚本管理器，手工创建新脚本，或通过 本地脚本导入 直接安装脚本时。

【客户端部份】

-> 客户端在用户成功安装（自创，导入）完成脚本后，（进行脚本包上传和提交到服务器端 触发） 【此行详细批注：候鸟客户端的 自动化脚本管理器窗口里，用户 打开脚本编辑器，自行编写脚本，或粘帖脚本代码到编辑器里，通过编辑器成功保存的行为（包括在编辑器里给脚本添加名称，脚本描述等动作），均视为用户自创脚本完成。 在保存后进行脚本包的上传触发。】

-> 客户端将脚本文件进行打包，打包成功后，请求服务器API/UPLOAD_AUTOSCRIPTS 接口，将脚本KEY生成出来，SIZE、MD5、用户UID、脚本文件包、脚本名称、脚本类别，提交给服务器。

-> 【如果存在用户将脚本指配到环境中】则客户端需将item.zip中的configdata.xml中autoscripts节点进行更新

客户端对此环境进行打包，获取服务器端版本号，增加版本号，更新上传此环境包到服务器。

【服务器端部份】

服务器接收到API/UPLOAD_AUTOSCIPRTS 接口，客户端上传上来的所有脚本包数据。

判断是否存在于表一和表二中，如果不存在，则进行入库，数据包存储到 PLUGINS目录处理。

如果存在记录时，则需要判断md5值是否相同，如果不相同，则要更新表中此项的md5值、NAME、DEC、SIEZ(根据key唯一update)。

返回 成功处理状态报告给客户端。

【客户端部份】通过使用候鸟官方自有，自动化脚本库 安装新脚本

用户点击 使用官方自有脚本库 下拉菜单项， 客户端使用线程请求服务器API/Get_AUTOSCRIPTS 接口

请求参数：UID、TOKEN

客户端脚本面板上A区列表显示所有官方脚本列表、名称、描述、创建时间

用户选择其中一个或多个脚本，并在B区勾选需要安装的环境（此时在C区用户看到勾选的环境已安装了哪些脚本包）

用户点击右下角按钮：将选中的脚本包安装到勾选的环境中

此时，客户端请求服务器端接口 API/GET_AUTOSCRIPTS_DOWNLOAD 发送参数：用户UID、脚本主表记录ID、TOKEN。

获得需要下载到本地并安装的脚本包URL地址。

客户端完成脚本安装。

脚本安装成功后，请求服务器API/UPLOAD_USER_AUTOSCRIPTS 接口，将脚本KEY、SIZE、MD5、用户UID、脚本包名称 提交给服务器。

客户端将已完成安装的环境下item.zip中的configdata.xml中autoscripts节点进行更新

客户端对此环境进行打包，获取服务器端版本号，增加版本号，更新上传此环境包到服务器。

【服务器端部份】通过使用官方自有浏览器自动化脚本库 安装新脚本包

服务器端通过 API/Get_AUTOSCRIPTS 接口 收到客户端请求参数：UID,TOKEN。

服务器端 返回JSON格式所有脚本数据：表记录ID、脚本KEY、SIZE、MD5、用户UID、脚本文件包、脚本名称。
-------------------------------------------------------------------------------------------------

服务器端通过 API/GET_AUTOSCRIPTS_DOWNLOAD 接口 收到客户端请求参数：用户UID、脚本主表记录ID、TOKEN。

服务器端通过 主表记录ID，查找主表：AUTOSCRIPTS，取出对应的记录，组合成下载地址，脚本KEY、SIZE、MD5、脚本文件包、脚本名称、通过JSON返回给客户端。

-------------------------------------------------------------------------------------------------

服务器端通过API/UPLOAD_USER_AUTOSCRIPTS 接口，收到客户端请求参数：TOKEN、脚本KEY、SIZE、MD5、用户UID、脚本名称（没有文件包）

服务器端根据脚本KEY，用户UID，判断是否存在于表二中，如果不存在，则进行入库。

九、在新机器上运行已有历史环境的客户端，自动化脚本包的逻辑描述：

运行环境A -> 获得环境A目录下的configdata.xml中的脚本列表，判断环境目录中是否存在这些脚本包。 如果不存在 -> 判断用户本地目录：/autoscripts/目录里是否存在此脚本  ->  如果不存在，下载服务器端此脚本包，存放到用户本地的/autoscripts/目录里。

     -> 将用户本地的/autoscripts/目录里 脚本包解压缩到 环境A目录下。

综述：当本地的某个ITEM没有这个脚本目录时，先从本地的/autoscripts/里拿,拿之前要与服务器进行MD5对比，如果相同，从本地拿，如果MD5不相同或本地没有此脚本包，从服务器下载到本地/autoscripts里。再从本地的autoscripts里拿。

注：严禁从首个安装过此脚本的ITEM下的目录里找脚本包数据源。

考虑到客服工作和用户对自动化脚本数据的本地需求层面，即：用户如果需要在本地找对应的脚本包，客服直接让用户去autoscripts目录里找就行，最好的办法是通过自动化脚本管理器里去找自有的脚本包集合。

十二、【删除脚本】

当A区列表显示的是官方提供的脚本包时，下方的删除脚本和清除脚本按钮置灰，处于不可点击状态。  当C区列表显示为空的时侯，下方的 删除选中脚本 按钮置灰，删除所有脚本置灰，处于不可点击状态。

十三、脚本文件名与KEY约定*重点

脚本数据以XML文件格式进行保存

脚本XML分为

头部KEY值区各节点：（头部的各节点及名称，目前KEY值区已知需要存放的有：脚本的KEY、脚本名称、MD5、脚本相关描述、脚本语言、脚本类别、脚本首次创建时间、脚本修改时间【其它的涉及到浏览器需要的节点HUA来进行补充】）

脚本区节点：存放脚本实际内容（脚本加码后存储）。脚本内容由候鸟的脚本编辑器在用户编辑完成后存放。

脚本文件名以session_unique_id的生成参数方式来进行生成。具体有:脚本id，脚本名称，随机数，生成脚本的日期时间，本机mac，本机磁盘id，本机cpuid，生成脚本KEY后查询本地所有脚本的KEY值,如果有重复的再次尝试生成KEY，直到KEY值唯一。

脚本KEY值【32位】 ：
为每个候鸟自动化脚本文件的唯一标识

服务器端与客户端通过KEY值对脚本的唯一性进行认定，服务器端与客户端通过KEY值是否相同进行分别存储还是覆盖性本地存储进行判定。

人工审核并移入到官方脚本库的过程，有人工加入脚本描述、去掉敏感信息、 完善脚本描述等人工行为，修改保存之后，以全新脚本文件的模式（KEY值不变），进入到官方脚本库文件夹中。因此和用户上传的源文件内容区别是比较大的。但KEY不能有变化，有了这个KEY，服务器端能知道源头在哪里，是哪个用户首次创建的脚本。

多个用户将同一个脚本多次上传到服务器，在人工审核中，如果此KEY值已存在官方脚本库，由于KEY值相同，则可以快速判定无需二次加入到官方脚本库。 因此：加入到官方脚本库的脚本KEY值应保持不变。

六、官方脚本库中的所有脚本，一定是经过了人工审核的，是能够给大众使用且是合法合规的。

这个不像插件，可以自动转入到官方库。这个是比较大的一个区别。

默认都是不公开的，只有后台设定过，才能变成公开

公开后，脚本文件的副本会存储在 官方脚本库的文件夹中。

七、对于用户自有脚本的上传，未涉及到转入到官方脚本库的，应存储在用户自有文件夹中。路径约定为：upload/autoscripts/aes(user)/脚本id.zip

十四、脚本的批量导入与导出

（1）脚本导出：

流程说明：


================================================== 表格内容 ==================================================

参数名称 | 类型 | 必传 | 样例串/默认值 | 说明
Session_ID | array | 是 | 373808cb37bd63f5f7d92415e736e85f, 705cc4c139e69b729a2fd277f30e1863 | 环境ID
isHeadless | boolean | 否 | true | true：默认浏览器器无头模式
false：浏览器有头模式
isWebDriverReadOnlyMode | boolean | 否 | true | 是否只读模式，默认true只读。（只读模式不会保存cookie等数据）
args | array | 否 | "args": [ "--disable-extensions", "--blink-settings=imagesEnabled=false" ] | 启动参数

{ 
   " Session_ID":["373808cb37bd63f5f7d92415e736e85f"],
    "args": [ 
        "--disable-extensions", 
        "--blink-settings=imagesEnabled=false" 
    ] 
}