<?xml version="1.0" encoding="utf-8" standalone="yes" ?>
<Window size="782,597" caption="0,0,0,50" roundcorner="5,5,5,5" showshadow="true" shadowsize="3" shadowposition="0,0" shadowcolor="#ff6272A4" fademode="true">
  <Default name="VScrollBar" value="width=&quot;7&quot; scroll_auto_hidden=&quot;true&quot; scrollbarfloat=&quot;false&quot; showbutton1=&quot;false&quot; showbutton2=&quot;false&quot; thumbnormalimage=&quot;file='common_scrollbar.png' source='0,0,7,9' corner='0,4,0,4'&quot; thumbhotimage=&quot;file='common_scrollbar.png' source='10,0,17,9' corner='0,4,0,4'&quot; thumbpushedimage=&quot;file='common_scrollbar.png' source='10,0,17,9' corner='0,4,0,4'&quot; bknormalimage=&quot;file='common_scrollbar.png' source='0,12,1,13' corner='0,0,0,0'&quot;" />
	<Font name="微软雅黑" id="0" size="15" default="true"/>
	<Font name="微软雅黑" id="1" size="14" />
	<VerticalLayout width="782" height="597" bkcolor="#FF282A36">
		<HorizontalLayout height="37" bkcolor="#FF44475A">
			<Button name="login" bkimage="icon.png" padding="8,6,0,0" width="28" height="28"/>
			<Label name="sessionadvancetitle" padding="6,6,10,0" text="环境高级设置" width="100"  textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD"/>
			<Label name="sessionname" padding="6,4,10,0" text="" width="200" align="left" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD"/>
			<Control />
			<Button name="minbtn" width="28" height="26" tooltip="最小化" normalimage="minbtn.png" hotimage="minbtn_hover.png" pushedimage="minbtnpush.png" />
      <Button name="closebtn" width="28" height="26"  tooltip="关闭" normalimage="closebtn.png" hotimage="closebtn_hover.png" pushedimage="closebtnpush.png" />
		</HorizontalLayout>

    <HorizontalLayout name="loading_data"  width="782" height="560" bkcolor="#ffe9e9e9" visible="true">
 			<Control />
	    <VerticalLayout width="553">
			 <Control />
					     <HorizontalLayout height="200" width="553">
					    	 <Control />
					    		<GifAnim bkimage="dataloading.gif" height="200" width="200" padding="0,0,0,0" auto="true"/>
					    	 <Control />
					     </HorizontalLayout>


					     <HorizontalLayout width="553" height="30" >
					    	 <Control />
					    		  <Label name="data_percent" text="0%" width="300" textcolor="#FF616161" hottextcolor="#ff000000" align="center" font="10"></Label>
					    	 <Control />
					     </HorizontalLayout>

					     <HorizontalLayout width="553" height="60" >
					    	 <Control />
					    		  <Label name="process_description" text="正在匹配ip信息..   请稍侯.." width="300" textcolor="#FF616161" hottextcolor="#ff000000" align="center" font="8"></Label>
					    	 <Control />
					     </HorizontalLayout>
					   <Control />
      </VerticalLayout>
				 <Control />
 		</HorizontalLayout>

	<VerticalLayout name="data" visible="false">
		<HorizontalLayout bkcolor="#ffffffff">

			<VerticalLayout width="120" bkcolor="#fff5f7f8">
				<Button name="session_set" text="会话设置" width="120"  padding="0,30,0,0" selected="true"  hottextcolor="ff519cff" pushedtextcolor="ff519cff"/>
				<Control height="5"/>
				<Button name="proxy_set" text="代理设置" width="120"  padding="0,0,0,0"  hottextcolor="ff519cff" pushedtextcolor="ff519cff"/>
				<Control height="5"/>
        <Button name="useragent_set" text="UserAgent" width="120"  padding="0,0,0,0" hottextcolor="ff519cff" pushedtextcolor="ff519cff"/>
				<Control height="5"/>
				<Button name="screen_set" text="屏幕设置" width="120"  padding="0,0,0,0" hottextcolor="ff519cff" pushedtextcolor="ff519cff"/>
				<Control height="5"/>
				<Button name="position_set" text="模拟位置" width="120"  padding="0,0,0,0" hottextcolor="ff519cff" pushedtextcolor="ff519cff"/>
				<Control height="5"/>
				<Button name="finger_set" text="指纹设置" width="120"  padding="0,0,0,0" hottextcolor="ff519cff" pushedtextcolor="ff519cff"/>
			</VerticalLayout>
			<TabLayout name="default_bk" selectedid="3">
				<VerticalLayout>
					            <Control height="55"/>

												<HorizontalLayout height="36" >
												    	 <VerticalLayout width="352">
													      <Combo name="session" bordersize="0" padding="21,0,0,10" width="321" height="36" borderround="3,3" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="0,0,45,0" bkcolor="#ffdce1e7"
																			normalimage="file='Profile\Setting_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\Setting_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\Setting_Combox_Click.png' corner='5,5,25,10'"
																			combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
																			itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0">
																				<!--<ListLabelElement height="36" pos="66,0,0,0" textpadding="10,0,0,0" text="使用历史会话创建新会话环境.." font="0" selected="true">
																					<Label name="textLab" pos="66,0,0,0" textpadding="10,0,0,0" text="使用历史会话创建新会话环境"  height="36" width="320" textcolor="#FF000000"/>
																				</ListLabelElement>-->

																			</Combo>
													     </VerticalLayout>
													     <VerticalLayout width="110" visible="false">
													      <Button name="color_set" bkimage="bk_quit_normal.png" hotimage="bk_quit_hot.png" pushedimage="bk_quit_hot.png" padding="8,6,0,0"  width="100" height="26" text="颜色设置"/>
													     </VerticalLayout>
													     <VerticalLayout width="70">
													      <Button name="copy"  bkimage="bk_quit_normal.png" hotimage="bk_quit_hot.png" pushedimage="bk_quit_hot.png" padding="8,6,0,0"  width="60" height="26" text="克隆"/>
													     </VerticalLayout>
													     <VerticalLayout width="110">
													      <Button name=""  bkimage="bk_quit_normal.png" hotimage="bk_quit_hot.png" pushedimage="bk_quit_hot.png" padding="8,6,0,0"  width="100" height="26" text="修改名称"/>
													     </VerticalLayout>

													    <Control />
												</HorizontalLayout>
					             <Control height="15"/>

										<HorizontalLayout height="36" >
										    	 <VerticalLayout width="342">
											     <RichEdit name="name" padding="21,0,0,10" height="36" width="342" tipvaluecolor="#FF333333" borderround="3,3" bkcolor="#ffdce1e7" font="8" textpadding="10,8,20,0" maxchar="300" tipvalue="请输入环境名称" multiline="false" textcolor="#ff333333" rich="false" transparent="false">
											      </RichEdit>
											     </VerticalLayout>
											    <VerticalLayout width="50">
													      <CheckBox name="WAV" width="18" height="18"  padding="22,12,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="true" />
													  </VerticalLayout>
													  <VerticalLayout width="180">
													      <Label name="lwav" padding="0,9,0,0" text="设定完成后自动验证" width="180" textcolor="#FF333333"  font="8"></Label>
													  </VerticalLayout>
										</HorizontalLayout>
										  <Control height="15"/>
						         <HorizontalLayout height="102">
										    	 <VerticalLayout width="342">
											     <RichEdit name="desc" padding="21,0,0,10" height="102" width="342" tipvaluecolor="#FF333333" borderround="3,3" bkcolor="#ffdce1e7" font="8" textpadding="10,8,20,0" maxchar="300" tipvalue="请输入此会话相关描述.." multiline="true" textcolor="#ff333333" rich="false" transparent="false">
											      </RichEdit>
											     </VerticalLayout>
										 </HorizontalLayout>

										   <Control height="15"/>

										 <HorizontalLayout height="32">
										  <Label name="loading_attach" padding="22,6,10,0" text="当前会话环境支持设定：" width="160"  textcolor="#FF000000" hottextcolor="#FF000000"/>
										 </HorizontalLayout>

										 <HorizontalLayout height="32" >
										        <VerticalLayout width="50">
													      <CheckBox name="WAV" width="18" height="18"  padding="22,12,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="true" />
													  </VerticalLayout>
													  <VerticalLayout width="180">
													      <Label name="lwav" padding="0,9,0,0" text="使用 SphereGL 模块" width="180" textcolor="#FF333333"  font="8"></Label>
													  </VerticalLayout>
										 </HorizontalLayout>

										 <HorizontalLayout height="32" >
										        <VerticalLayout width="50">
													      <CheckBox name="WAV" width="18" height="18"  padding="22,12,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="true" />
													  </VerticalLayout>
													  <VerticalLayout width="180">
													      <Label name="lwav" padding="0,9,0,0" text="启用html5本地存储" width="180" textcolor="#FF333333"  font="8"></Label>
													  </VerticalLayout>
										 </HorizontalLayout>
										 <HorizontalLayout height="32" >
										        <VerticalLayout width="50">
													      <CheckBox name="WAV" width="18" height="18"  padding="22,12,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="true" />
													  </VerticalLayout>
													  <VerticalLayout width="180">
													      <Label name="lwav" padding="0,9,0,0" text="保存html5本地存储数据" width="180" textcolor="#FF333333"  font="8"></Label>
													  </VerticalLayout>
										 </HorizontalLayout>
										 <HorizontalLayout height="32" >
										        <VerticalLayout width="50">
													      <CheckBox name="WAV" width="18" height="18"  padding="22,12,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="true" />
													  </VerticalLayout>
													  <VerticalLayout width="180">
													      <Label name="lwav" padding="0,9,0,0" text="保存XML本地存储数据" width="180" textcolor="#FF333333"  font="8"></Label>
													  </VerticalLayout>
										 </HorizontalLayout>

										  <Control height="72"/>

				</VerticalLayout>

				<VerticalLayout>
					 <Control height="55"/>
																	  <HorizontalLayout height="28">
																			<VerticalLayout width="160">
																		      <Label  name="proxyip" padding="22,0,0,0" text="代理服务器IP：" width="160" textcolor="#FF333333"  font="8"></Label>
																		  </VerticalLayout>
																		</HorizontalLayout>
																		<HorizontalLayout height="36" >

																			     <RichEdit name="proxyipe" padding="21,0,0,10" height="36" width="320" tipvaluecolor="#FF5f5f5f" borderround="3,3" bkcolor="#ffdce1e7" font="8" textpadding="10,8,20,0" maxchar="300" tipvalue="请输入代理IP或域名.." multiline="false" textcolor="#ff333333" rich="false" transparent="false">
																			      </RichEdit>
                                          <Button name="checkproxy"  bkimage="bk_quit_normal.png" hotimage="bk_quit_hot.png" pushedimage="bk_quit_hot.png" padding="10,5,0,0"  width="135" height="26" text="检测代理服务器"/>
                                           <Label  name="checkproxyInfo" padding="22,0,0,0" text="" width="160" textcolor="#FF333333"  font="8"></Label>
																		</HorizontalLayout>


																		<HorizontalLayout height="12">
																		</HorizontalLayout>
																		<HorizontalLayout height="28">
																			<VerticalLayout width="160">
																		      <Label  name="proxytype" padding="22,0,0,0" text="代理服务器类型：" width="160" textcolor="#FF333333"  font="8"></Label>
																		  </VerticalLayout>
																		</HorizontalLayout>
																		<HorizontalLayout height="36" width="540">
																		    	 <VerticalLayout width="300">
																			      <Combo name="proxytypec" bordersize="0" padding="21,0,0,10" dropboxsize="280,300" width="280" height="36" borderround="3,3" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="4,2,0,0" bkcolor="#ffdce1e7"
																									normalimage="file='Profile\Setting_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\Setting_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\Setting_Combox_Click.png' corner='5,5,25,10'"
																									combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
																									itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0">
																								<ListLabelElement height="26" pos="66,0,0,0" text="SOCKS5" font="0">
																									<Label name="textLab" float="true" text="SOCKS5" pos="0,0,0,0" height="36" width="100" textcolor="#FF000000"/>
																								</ListLabelElement>
																							<ListLabelElement height="36" pos="10,0,0,0" textpadding="10,0,0,0" text="No Proxy" font="0" selected="true">
																									<Label name="textLab" pos="10,0,0,0" textpadding="10,0,0,0" text="No Proxy"  height="36" width="100" textcolor="#FF000000"/>
																								</ListLabelElement>
																								<ListLabelElement height="36" pos="10,0,0,0" textpadding="10,0,0,0" text="HTTP" font="0" selected="true">
																									<Label name="textLab" pos="10,0,0,0" textpadding="10,0,0,0" text="HTTP"  height="36" width="100" textcolor="#FF000000"/>
																								</ListLabelElement>
																								<ListLabelElement height="36" pos="10,0,0,0" textpadding="10,0,0,0" text="HTTPS" font="0">
																									<Label name="textLab" pos="10,0,0,0" textpadding="10,0,0,0" text="HTTPS"  height="36" width="100" textcolor="#FF000000"/>
																								</ListLabelElement>
																								<ListLabelElement height="26" pos="66,0,0,0" text="SOCKS4" font="0">
																									<Label name="textLab" float="true" text="SOCKS4" pos="0,0,0,0" height="36" width="100" textcolor="#FF000000"/>
																								</ListLabelElement>
																								<ListLabelElement height="26" pos="66,0,0,0" text="SOCKS4A" font="0">
																									<Label name="textLab" float="true" text="SOCKS4A" pos="0,0,0,0" height="36" width="100" textcolor="#FF000000"/>
																								</ListLabelElement>



																									</Combo>
																			     </VerticalLayout>

																			      <VerticalLayout width="200">
																			      	   <RichEdit name="proxy_port" padding="12,0,0,10" height="36" width="120" tipvaluecolor="#FF5f5f5f" borderround="3,3" bkcolor="#ffdce1e7" font="8" textpadding="10,8,20,0" maxchar="300" tipvalue="代理端口" multiline="false" textcolor="#ff333333" rich="false" transparent="false"></RichEdit>
																			      </VerticalLayout>

																		</HorizontalLayout>

																		<HorizontalLayout height="12">
																		</HorizontalLayout>
																   <HorizontalLayout height="28">
																			<VerticalLayout width="160">
																		      <Label  name="ploginname" padding="22,0,0,0" text="登录名：" width="160" textcolor="#FF333333"  font="8"></Label>
																		  </VerticalLayout>
																		</HorizontalLayout>
																		<HorizontalLayout height="36" >
																		    	 <VerticalLayout>
																			     <RichEdit name="proxynameinput" padding="21,0,0,10" height="36" width="320" tipvaluecolor="#FF5f5f5f" borderround="3,3" bkcolor="#ffdce1e7" font="8" textpadding="10,8,20,0" maxchar="300" tipvalue="请输入登录用户名.." multiline="false" textcolor="#FF5f5f5f" rich="false" transparent="false">
																			      </RichEdit>
																			     </VerticalLayout>
																		</HorizontalLayout>

																	<HorizontalLayout height="12">
																		</HorizontalLayout>
																   <HorizontalLayout height="28">
																			<VerticalLayout width="160">
																		      <Label  name="ploginpass" padding="22,0,0,0" text="登录密码：" width="160" textcolor="#FF333333"  font="8"></Label>
																		  </VerticalLayout>
																		</HorizontalLayout>
																		<HorizontalLayout height="36" >
																		    	 <VerticalLayout>
																			     <RichEdit name="proxypassinput" padding="21,0,0,10" height="36" width="320" tipvaluecolor="#FF5f5f5f" borderround="3,3" bkcolor="#ffdce1e7" font="8" textpadding="10,8,20,0" maxchar="300" tipvalue="请输入登录密码.." multiline="false" textcolor="#FF5f5f5f" rich="false" transparent="false">
																			      </RichEdit>
																			     </VerticalLayout>
																		</HorizontalLayout>
																		 <Control height="12"/>

																			<HorizontalLayout height="36" >
																		    	 <VerticalLayout width="170">
																			       <RichEdit name="publicipinput" text="N/A" padding="21,0,0,0" height="36" width="170" tipvaluecolor="#FF5f5f5f" borderround="3,3" bkcolor="#ffdce1e7" font="8" textpadding="10,8,0,0" maxchar="300" tipvalue="PUBLIC IP.." multiline="false" textcolor="#FF5f5f5f" rich="false" transparent="false">
																			      </RichEdit>
																			     </VerticalLayout>
																			     <Control width="10" />
																		    	 <VerticalLayout width="162">
																			      <RichEdit name="localipinput" text="N/A" height="36" width="162" tipvaluecolor="#FF5f5f5f" borderround="3,3" bkcolor="#ffdce1e7" font="8" textpadding="10,8,0,0" maxchar="300" tipvalue="LOCAL IP.." multiline="false" textcolor="#FF5f5f5f" rich="false" transparent="false">
																			      </RichEdit>
																			     </VerticalLayout>
																			      	  <VerticalLayout width="50">
																							      <CheckBox name="NA" width="18" height="18"  padding="22,12,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="false" />
																							  </VerticalLayout>
																							  <VerticalLayout width="80">
																							      <Label name="NA_dec" padding="0,9,0,0" text="N/A" width="80" textcolor="#FF333333"  font="8"></Label>
																							  </VerticalLayout>
																			     <Control />
																		</HorizontalLayout>
																		 <Control height="12"/>
																		 	<HorizontalLayout height="50" >
																		    	  <VerticalLayout width="50">
																							      <CheckBox name="NA" width="18" height="18"  padding="22,12,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="false" />
																							  </VerticalLayout>
																							  <VerticalLayout width="100">
																							      <Label name="NA_dec" padding="0,9,0,0" text="使用虚拟wRTC" width="100" textcolor="#FF333333"  font="8"></Label>
																							  </VerticalLayout>
																							  <Control width="12"/>
																							   <VerticalLayout width="50">
																							      <CheckBox name="NA" width="18" height="18"  padding="22,12,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="false" />
																							  </VerticalLayout>
																							  <VerticalLayout width="130">
																							      <Label name="NA_dec" padding="0,9,0,0" text="连接IP和代理IP相同" width="130" textcolor="#FF333333"  font="8"></Label>
																							  </VerticalLayout>
																							   <VerticalLayout width="50">
																							      <CheckBox name="NA" width="18" height="18"  padding="22,12,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="false" />
																							  </VerticalLayout>
																							  <VerticalLayout width="40">
																							      <Label name="NA_dec" padding="0,9,0,0" text="ipv6" width="40" textcolor="#FF333333"  font="8"></Label>
																							  </VerticalLayout>
																							  <VerticalLayout width="50">
																							      <CheckBox name="NA" width="18" height="18"  padding="22,12,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="false" />
																							  </VerticalLayout>
																							  <VerticalLayout width="120">
																							      <Label name="NA_dec" padding="0,9,0,0" text="关闭 wRTC" width="120" textcolor="#FF333333"  font="8"></Label>
																							  </VerticalLayout>
																							  <Control />
																		</HorizontalLayout>


																		 	<HorizontalLayout height="36" >
																		    	  <VerticalLayout width="170">
                                             <RichEdit name="dnsinput" padding="21,0,0,0" height="36" width="170" tipvaluecolor="#FF5f5f5f" borderround="3,3" bkcolor="#ffdce1e7" font="8" textpadding="10,8,0,0" maxchar="300" tipvalue="Custom DNS" multiline="false" textcolor="#FF5f5f5f" rich="false" transparent="false">
																			        </RichEdit>
																						 </VerticalLayout>
																						<VerticalLayout width="146">
							        			 										<Button name="save"  bkimage="bk_quit_normal.png" hotimage="bk_quit_hot.png" pushedimage="bk_quit_hot.png" padding="10,5,0,0"  width="135" height="26" text="验证DNS"/>
																						</VerticalLayout>
																							  <Control />
																		</HorizontalLayout>

				</VerticalLayout>

				<VerticalLayout>
										  	 <Control height="55"/>
																						  <HorizontalLayout height="28">
																								<VerticalLayout width="280">
																							      <Label  name="UserAgent" padding="22,0,0,0" text="UserAgent 客户端设定" width="280" textcolor="#FF333333"  font="8"></Label>
																							  </VerticalLayout>
																							</HorizontalLayout>
																							<HorizontalLayout height="36" width="588">
																							    	 <VerticalLayout width="588">
																								      <Combo name="ComboUA" bordersize="0" padding="21,0,0,10" width="560" height="36" borderround="3,3" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="4,2,0,0" bkcolor="#ffdce1e7"
																														normalimage="file='Profile\Setting_Combox_Normal_large.png' corner='5,5,25,10'" hotimage="file='Profile\Setting_Combox_Hover_large.png' corner='5,5,25,10'" pushedimage="file='Profile\Setting_Combox_Click_large.png' corner='5,5,25,10'"
																														combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
																														itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0">


																														</Combo>
																								     </VerticalLayout>
																							</HorizontalLayout>



																							<HorizontalLayout height="32">
																							    	    <VerticalLayout width="50">
																												      <CheckBox name="NA" width="18" height="18"  padding="22,12,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="true" />
																												  </VerticalLayout>
																												  <VerticalLayout width="58">
																												      <Label name="NA_dec" padding="0,9,0,0" text="Chrome" width="58" textcolor="#FF333333"  font="8"></Label>
																												  </VerticalLayout>

																												   <VerticalLayout width="50">
																												      <CheckBox name="NA" width="18" height="18"  padding="22,12,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="true" />
																												  </VerticalLayout>
																												  <VerticalLayout width="50">
																												      <Label name="NA_dec" padding="0,9,0,0" text="Safari" width="50" textcolor="#FF333333"  font="8"></Label>
																												  </VerticalLayout>
																												   <VerticalLayout width="50">
																												      <CheckBox name="NA" width="18" height="18"  padding="22,12,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="true" />
																												  </VerticalLayout>
																												  <VerticalLayout width="40">
																												      <Label name="NA_dec" padding="0,9,0,0" text="MSIE" width="50" textcolor="#FF333333"  font="8"></Label>
																												  </VerticalLayout>
																												  <VerticalLayout width="50">
																												      <CheckBox name="NA" width="18" height="18"  padding="22,12,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="true" />
																												  </VerticalLayout>
																												  <VerticalLayout width="120">
																												      <Label name="NA_dec" padding="0,9,0,0" text="其它浏览器" width="120" textcolor="#FF333333" font="8"></Label>
																												  </VerticalLayout>
																												  <Control />
																							</HorizontalLayout>
																							<HorizontalLayout height="32">
																							    	    <VerticalLayout width="50">
																												      <CheckBox name="NA" width="18" height="18"  padding="22,12,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="true" />
																												  </VerticalLayout>
																												  <VerticalLayout width="258">
																												      <Label name="NA_dec" padding="0,9,0,0" text="更改UserAgent后重新生成配置" width="258" textcolor="#FF333333"  font="8"></Label>
																												  </VerticalLayout>
																												  <Control />
																							</HorizontalLayout>

																							<HorizontalLayout height="12">
																							</HorizontalLayout>


																					   <HorizontalLayout height="32">
																								<VerticalLayout width="180">