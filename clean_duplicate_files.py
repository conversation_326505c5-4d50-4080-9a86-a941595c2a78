#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理重复和无效的API文档文件
只保留真正有价值的独特内容
"""

import os
import re
from pathlib import Path
import logging
import shutil

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DuplicateFileCleaner:
    def __init__(self, input_dir='api_docs_advanced', output_dir='api_docs_clean'):
        self.input_dir = input_dir
        self.output_dir = output_dir
        
        # 创建输出目录
        Path(self.output_dir).mkdir(exist_ok=True)
        
        # 定义有价值的文件模式（真正的API功能）
        self.valuable_patterns = {
            # 环境管理的真实功能
            '环境管理': [
                r'API_环境管理_1_获取环境列表\.txt',
                r'API_环境管理_2_查询指定环境ID的配置数据\.txt',
                r'API_环境管理_3_创建环境\.txt',
                r'API_环境管理_4_更新环境高级指纹参数\.txt',
                r'API_环境管理_5_更新环境\.txt',
                r'API_环境管理_6_更新环境代理\.txt',
                r'API_环境管理_7_删除环境\.txt',
                r'API_环境管理_8_导入Cookie\.txt',
                r'API_环境管理_9_导出Cookie\.txt',
                r'API_环境管理_10_获取随机UA\.txt',
                r'API_环境管理_11_清除环境本地缓存\.txt',
                r'API_环境管理_12_查看环境运行状态\.txt',
                r'API_环境管理_13_查看环境网页自动运行信息\.txt',
                r'API_环境管理_14_添加环境自动运行网页地址\.txt',
                r'API_环境管理_15_更新环境某个自动运行网页地址\.txt',
                r'API_环境管理_16_删除环境某个自动运行网页地址\.txt'
            ],
            
            # 环境开启关闭的真实功能
            '环境开启关闭': [
                r'API_环境开启关闭_1_打开环境\.txt',
                r'API_环境开启关闭_2_关闭环境\.txt',
                r'API_环境开启关闭_3_强制终止环境\.txt'
            ],
            
            # 分组管理的真实功能
            '分组管理': [
                r'API_分组管理_1_获取环境分组列表\.txt',
                r'API_分组管理_2_新建环境分组\.txt',
                r'API_分组管理_3_删除环境分组\.txt',
                r'API_分组管理_4_将指定环境从指定分组转移到另一个分组\.txt'
            ],
            
            # 插件管理的真实功能
            '插件管理': [
                r'API_插件管理_1_列出当前帐户下所有已安装的插件\(插件 ID，插件名称\)\.txt',
                r'API_插件管理_2_查询、列出指定环境中的所有插件\(插件 ID，插件名称\)\.txt',
                r'API_插件管理_3_安装指定多个插件到指定的环境中\.txt',
                r'API_插件管理_4_删除指定环境插件\.txt'
            ],
            
            # 脚本管理的真实功能
            '脚本管理': [
                r'API_脚本管理_1_查询、列出指定环境中的所有脚本\.txt',
                r'API_脚本管理_2_切换指定环境已激活脚本\.txt',
                r'API_脚本管理_3_从我的脚本库中指派脚本到目标环境中\.txt',
                r'API_脚本管理_4_指定环境中的指定脚本设定为非激活状态\.txt',
                r'API_脚本管理_5_将未激活脚本从指定环境中移除\.txt'
            ],
            
            # 使用须知的真实功能
            '使用须知': [
                r'API_使用须知_1_获取API凭证\.txt',
                r'API_使用须知_2_查看API凭证\.txt',
                r'API_使用须知_2_获取环境ID\.txt'
            ],
            
            # HTTP模式说明的真实功能
            'HTTP模式说明': [
                r'API_HTTP模式说明_1_以管理员身份运行CMD或者PowerShell，并确保终端在候鸟浏览器主目录打开，或已进入候鸟浏览器主路径\.txt',
                r'API_HTTP模式说明_2_启动成功过后在命令行工具可以看到API地址\.txt',
                r'API_HTTP模式说明_3_CLI命令行参数介绍\.txt'
            ],
            
            # 常见问题的真实功能
            '常见问题': [
                r'API_常见问题_1_通过命令行启动客户端报错\.txt',
                r'API_常见问题_2_调用API是否可以同时手动打开客户端\.txt',
                r'API_常见问题_3_打开环境失败\.txt'
            ],
            
            # POSTMAN相关的真实功能
            'POSTMAN调试': [
                r'API_POSTMAN调试候鸟API接口_1_仅支持客户端V3\.9\.2\.114以上版本，请下载客户端最新版本\.txt',
                r'API_POSTMAN调试候鸟API接口_2_需要本地安装POSTMAN，见\.txt'
            ],
            
            # JSON数据相关的真实功能
            'JSON数据': [
                r'API_调试接口JSON数据官方更新下载_1_POSTMAN如何导入JSON数据\.txt',
                r'API_调试接口JSON数据官方更新下载_2_POSTMAN如何导出JSON数据\.txt'
            ],
            
            # 基础页面（每个只保留一个主要文件）
            '基础页面': [
                r'API_帐号登录_1_候鸟浏览器支持本地API的功能，帮助用户通过程序化的方式来启动和关闭浏览器等基础API功能，还可以配合Selenium和Puppeteer等自动化框架来实现浏览器操作的自动化\.txt',
                r'API_帐号登录_2_仅支持客户端V3\.9\.2\.114以上版本，请下载客户端最新版本\.txt',
                r'API_获取成员列表_2_获取成员列表\.txt',
                r'API_错误码对照表_9_错误码对照表\.txt',
                r'API_附录_8_附录（国家码、时区、语言、系统和分辨率）\.txt'
            ]
        }
        
        # 需要完全忽略的重复模式
        self.ignore_patterns = [
            r'API_.*_\d+_帐号登录\.txt$',
            r'API_.*_\d+_获取成员列表\.txt$',
            r'API_.*_\d+_环境开启_关闭\.txt$',
            r'API_.*_\d+_环境管理\.txt$',
            r'API_.*_\d+_分组管理\.txt$',
            r'API_.*_\d+_脚本管理\.txt$',
            r'API_.*_\d+_插件管理\.txt$',
            r'API_.*_\d+_附录（国家码、时区、语言、系统和分辨率）\.txt$',
            r'API_.*_\d+_错误码对照表\.txt$'
        ]
    
    def should_keep_file(self, filename):
        """判断文件是否应该保留"""
        # 首先检查是否在忽略列表中
        for ignore_pattern in self.ignore_patterns:
            if re.search(ignore_pattern, filename):
                # 但是要排除一些例外情况
                exceptions = [
                    'API_帐号登录_1_候鸟浏览器支持本地API的功能',
                    'API_帐号登录_2_仅支持客户端V3.9.2.114以上版本',
                    'API_获取成员列表_2_获取成员列表',
                    'API_错误码对照表_9_错误码对照表',
                    'API_附录_8_附录（国家码、时区、语言、系统和分辨率）'
                ]
                
                if any(exception in filename for exception in exceptions):
                    return True
                return False
        
        # 检查是否在有价值的模式中
        for category, patterns in self.valuable_patterns.items():
            for pattern in patterns:
                if re.search(pattern, filename):
                    return True
        
        return False
    
    def analyze_files(self):
        """分析文件并分类"""
        all_files = list(Path(self.input_dir).glob('*.txt'))
        
        keep_files = []
        ignore_files = []
        
        for file_path in all_files:
            filename = file_path.name
            if self.should_keep_file(filename):
                keep_files.append(file_path)
            else:
                ignore_files.append(file_path)
        
        logger.info(f"总文件数: {len(all_files)}")
        logger.info(f"保留文件数: {len(keep_files)}")
        logger.info(f"忽略文件数: {len(ignore_files)}")
        
        return keep_files, ignore_files
    
    def copy_valuable_files(self, keep_files):
        """复制有价值的文件到新目录"""
        success_count = 0
        
        for file_path in keep_files:
            try:
                dest_path = Path(self.output_dir) / file_path.name
                shutil.copy2(file_path, dest_path)
                logger.info(f"已复制: {file_path.name}")
                success_count += 1
            except Exception as e:
                logger.error(f"复制文件失败: {file_path.name}, 错误: {str(e)}")
        
        return success_count
    
    def create_summary_file(self, keep_files):
        """创建文件清单"""
        try:
            summary_content = "API文档清理后的文件清单\n"
            summary_content += "=" * 50 + "\n\n"
            
            # 按类别分组
            categories = {}
            for file_path in keep_files:
                filename = file_path.name
                # 提取主要类别
                if '_' in filename:
                    parts = filename.split('_')
                    if len(parts) >= 2:
                        category = parts[1]
                        if category not in categories:
                            categories[category] = []
                        categories[category].append(filename)
            
            for category, files in sorted(categories.items()):
                summary_content += f"\n## {category} ({len(files)} 个文件)\n"
                for filename in sorted(files):
                    summary_content += f"- {filename}\n"
            
            summary_content += f"\n\n总计: {len(keep_files)} 个有效文件\n"
            
            # 保存清单文件
            summary_path = Path(self.output_dir) / "文件清单.txt"
            with open(summary_path, 'w', encoding='utf-8') as f:
                f.write(summary_content)
            
            logger.info("已创建文件清单")
            
        except Exception as e:
            logger.error(f"创建清单失败: {str(e)}")
    
    def clean_files(self):
        """执行清理操作"""
        logger.info("开始分析和清理文件...")
        
        # 分析文件
        keep_files, ignore_files = self.analyze_files()
        
        # 复制有价值的文件
        success_count = self.copy_valuable_files(keep_files)
        
        # 创建清单
        self.create_summary_file(keep_files)
        
        logger.info(f"清理完成! 成功复制 {success_count} 个文件")
        
        # 显示一些被忽略的文件示例
        if ignore_files:
            logger.info("被忽略的文件示例:")
            for i, file_path in enumerate(ignore_files[:10]):
                logger.info(f"  - {file_path.name}")
            if len(ignore_files) > 10:
                logger.info(f"  ... 还有 {len(ignore_files) - 10} 个文件被忽略")

def main():
    """主函数"""
    print("API文档重复文件清理器启动...")
    print("="*50)
    
    cleaner = DuplicateFileCleaner()
    cleaner.clean_files()
    
    print("="*50)
    print("清理完成!")
    print("- 输入目录: api_docs_advanced")
    print("- 输出目录: api_docs_clean")
    print("- 查看文件清单: api_docs_clean/文件清单.txt")

if __name__ == "__main__":
    main()
