<?xml version="1.0" encoding="UTF-8"?>
<Window size="500,300" trans="true"  caption="0,0,0,100" roundcorner="9,9,9,9" showshadow="true" shadowsize="3" shadowposition="0,0" shadowcolor="#ff6272A4" fademode="true">
  <Include source="default.xml" />
    <Default name="Button" />
  <VerticalLayout bkcolor="#FF282A36">
    <HorizontalLayout height="26">
    	<Button bkimage="icon21px.png" padding="6,6,0,0" width="27" height="21"/>
    	<Label name="msessagetip" text="" align="left" width="180" padding="6,8,0,0" textcolor="#FFF8F8F2" font="8" />
      <Control />
      <Button name="closebtn" width="28" height="26"  tooltip="关闭" normalimage="closebtn.png" hotimage="closebtn_hover.png" pushedimage="closebtnpush.png" />
    </HorizontalLayout>
		<VerticalLayout>

			 <HorizontalLayout height="20" inset="30,10,20,0" >
      </HorizontalLayout>

      <HorizontalLayout height="170" inset="30,10,20,0" >
        <Label name="msessage" text="" align="left" textpadding="8,0,0,0" textcolor="#ff000000" font="8" />
      </HorizontalLayout>

      <HorizontalLayout height="20" inset="20,0,20,0" >
        <VerticalLayout>
        <Option name="dont" padding="20,0,0,0" texttooltip="true" endellipsis="true" normalimage="file='option_normal.png' dest='0,0,18,18'" hotimage="file='option_hot.png' dest='0,0,18,18'" selectedimage="file='option_pushed.png' dest='0,0,18,18'" align="left" height="18" text="下次不提示我" textpadding="28,0,0,0" selected="false"/>
        </VerticalLayout>
      </HorizontalLayout>

      <HorizontalLayout childpadding="2" inset="0,0,0,0">
           <Control />
				      <Button name="btnok" text="" width="140" bkcolor="#ffffffff" hotbkcolor="#fff1f2f1"  bordercolor="#ffcccccc" focusbordercolor="#ffaaaaaa" bordersize="1" borderround="2,2"  padding="0,4,0,0" float="left" font="0" height="26"/>
        <Control width="40"/>
              <Button name="cancelbtn" text="" width="140" bkcolor="#ffffffff" hotbkcolor="#fff1f2f1"  bordercolor="#ffcccccc" focusbordercolor="#ffaaaaaa" bordersize="1" borderround="2,2"  padding="0,4,0,0" float="left" font="0" height="26"/>
           <Control />
			   </HorizontalLayout>
		</VerticalLayout>
  </VerticalLayout>
</Window>