#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化候鸟浏览器基础框架文档切分
将小文件合并成更大的主题文件，适合RAGFlow使用
"""

import os
import re
from pathlib import Path
from docx import Document

def clean_and_merge_files(input_dir, output_dir):
    """清理并合并小文件成主题文件"""
    input_path = Path(input_dir)
    output_path = Path(output_dir)
    
    # 创建输出目录
    output_path.mkdir(exist_ok=True)
    
    # 查找所有候鸟浏览器基础框架文件
    framework_files = list(input_path.glob("候鸟浏览器基础框架_*.docx"))
    
    print(f"📁 找到 {len(framework_files)} 个框架文件")
    
    # 定义主题分类
    themes = {
        "基础架构": ["基础", "架构", "框架", "版本", "约定", "流程", "说明"],
        "API接口": ["API", "接口", "HTTP", "REST", "指令集", "凭据"],
        "环境管理": ["环境", "ITEM", "SESSION", "配置", "管理", "创建", "更新", "删除"],
        "代理服务": ["代理", "PROXY", "SSL", "IP", "服务器", "检测"],
        "插件管理": ["插件", "PLUGIN", "安装", "管理器"],
        "脚本管理": ["脚本", "SCRIPT", "自动化", "AUTOSCRIPTS"],
        "数据同步": ["同步", "上传", "下载", "FULL", "ZIP", "备份"],
        "用户认证": ["登录", "认证", "TOKEN", "密码", "验证"],
        "客户端": ["客户端", "PC端", "无头", "有头", "CONSOLE"],
        "服务器端": ["服务器端", "数据库", "存储", "处理"],
        "网络通信": ["网络", "通信", "心跳", "连接", "协议"],
        "安全机制": ["安全", "加密", "AES", "HASH", "校验"],
        "日志监控": ["日志", "LOG", "监控", "记录", "分析"],
        "业务模板": ["模板", "业务", "配置", "指纹"],
        "团队协作": ["团队", "协作", "分享", "权限", "子账户"],
        "系统优化": ["优化", "性能", "GPU", "内存", "缓存"]
    }
    
    # 为每个主题创建文件分组
    theme_files = {theme: [] for theme in themes.keys()}
    unclassified_files = []
    
    # 分类文件
    for file_path in framework_files:
        filename = file_path.name
        classified = False
        
        for theme, keywords in themes.items():
            if any(keyword in filename for keyword in keywords):
                theme_files[theme].append(file_path)
                classified = True
                break
        
        if not classified:
            unclassified_files.append(file_path)
    
    # 显示分类结果
    print("\n📊 文件分类结果:")
    for theme, files in theme_files.items():
        if files:
            print(f"  {theme}: {len(files)} 个文件")
    
    if unclassified_files:
        print(f"  未分类: {len(unclassified_files)} 个文件")
    
    # 合并每个主题的文件
    merged_count = 0
    for theme, files in theme_files.items():
        if not files:
            continue
        
        # 创建合并后的文档
        merged_doc = Document()
        merged_doc.add_heading(f'候鸟浏览器基础框架_{theme}', 0)
        
        content_added = False
        
        for file_path in sorted(files):
            try:
                doc = Document(str(file_path))
                
                # 添加子标题
                section_title = file_path.stem.replace('候鸟浏览器基础框架_', '')
                merged_doc.add_heading(section_title, 1)
                
                # 复制内容（跳过第一个标题）
                for i, paragraph in enumerate(doc.paragraphs):
                    if i == 0:  # 跳过文档标题
                        continue
                    
                    # 复制段落
                    new_para = merged_doc.add_paragraph()
                    new_para.style = paragraph.style
                    new_para.alignment = paragraph.alignment
                    
                    for run in paragraph.runs:
                        new_run = new_para.add_run(run.text)
                        new_run.bold = run.bold
                        new_run.italic = run.italic
                        new_run.underline = run.underline
                
                # 复制表格
                for table in doc.tables:
                    rows = len(table.rows)
                    cols = len(table.columns) if table.rows else 0
                    
                    if rows > 0 and cols > 0:
                        new_table = merged_doc.add_table(rows=rows, cols=cols)
                        new_table.style = 'Table Grid'
                        
                        for i, row in enumerate(table.rows):
                            for j, cell in enumerate(row.cells):
                                if i < len(new_table.rows) and j < len(new_table.rows[i].cells):
                                    new_table.cell(i, j).text = cell.text
                
                content_added = True
                
            except Exception as e:
                print(f"⚠️  处理文件失败: {file_path.name} - {str(e)}")
        
        # 保存合并后的文件
        if content_added:
            output_filename = f"候鸟浏览器基础框架_{theme}.docx"
            output_filepath = output_path / output_filename
            merged_doc.save(str(output_filepath))
            
            file_size = output_filepath.stat().st_size / 1024
            para_count = len(merged_doc.paragraphs)
            table_count = len(merged_doc.tables)
            
            print(f"✅ 创建主题文件: {output_filename}")
            print(f"   合并文件数: {len(files)}")
            print(f"   段落数: {para_count}")
            print(f"   表格数: {table_count}")
            print(f"   文件大小: {file_size:.1f} KB")
            print()
            
            merged_count += 1
    
    # 处理未分类文件
    if unclassified_files:
        merged_doc = Document()
        merged_doc.add_heading('候鸟浏览器基础框架_其他内容', 0)
        
        for file_path in sorted(unclassified_files[:20]):  # 限制数量
            try:
                doc = Document(str(file_path))
                
                section_title = file_path.stem.replace('候鸟浏览器基础框架_', '')
                merged_doc.add_heading(section_title, 1)
                
                for i, paragraph in enumerate(doc.paragraphs):
                    if i == 0:
                        continue
                    
                    new_para = merged_doc.add_paragraph()
                    new_para.style = paragraph.style
                    new_para.alignment = paragraph.alignment
                    
                    for run in paragraph.runs:
                        new_run = new_para.add_run(run.text)
                        new_run.bold = run.bold
                        new_run.italic = run.italic
                        new_run.underline = run.underline
                
            except Exception as e:
                print(f"⚠️  处理未分类文件失败: {file_path.name} - {str(e)}")
        
        # 保存未分类文件
        output_filename = "候鸟浏览器基础框架_其他内容.docx"
        output_filepath = output_path / output_filename
        merged_doc.save(str(output_filepath))
        
        file_size = output_filepath.stat().st_size / 1024
        print(f"✅ 创建其他内容文件: {output_filename} ({file_size:.1f} KB)")
        merged_count += 1
    
    return merged_count

def cleanup_small_files(input_dir):
    """清理小的框架文件"""
    input_path = Path(input_dir)
    framework_files = list(input_path.glob("候鸟浏览器基础框架_*.docx"))
    
    # 排除原始文件
    files_to_remove = [f for f in framework_files if f.name != "候鸟浏览器基础框架第七十七版.docx"]
    
    print(f"🗑️  清理 {len(files_to_remove)} 个小文件...")
    
    for file_path in files_to_remove:
        try:
            file_path.unlink()
        except Exception as e:
            print(f"⚠️  删除文件失败: {file_path.name} - {str(e)}")
    
    print("✅ 清理完成")

if __name__ == "__main__":
    input_directory = r"F:\augment\output\docx_files"
    output_directory = r"F:\augment\output\docx_files\framework_merged"
    
    print("🔄 候鸟浏览器基础框架文档优化工具")
    print(f"📂 输入目录: {input_directory}")
    print(f"📂 输出目录: {output_directory}")
    print()
    
    # 合并文件
    merged_count = clean_and_merge_files(input_directory, output_directory)
    
    print("=" * 60)
    print(f"🎉 优化完成! 生成了 {merged_count} 个主题文件")
    
    # 询问是否清理小文件
    print("\n是否清理原来的小文件? (y/n): ", end="")
    # 自动清理
    cleanup_small_files(input_directory)
    
    print("\n🎯 优化后的文件已准备好用于RAGFlow向量库！")
