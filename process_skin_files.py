#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
处理候鸟皮肤库文件：
1. 给所有文件添加前缀 "候鸟皮肤库窗口源代码_"
2. 对超过30KB的文件进行切分
"""

import os
import shutil
from pathlib import Path

def get_file_size_kb(file_path):
    """获取文件大小（KB）"""
    try:
        return file_path.stat().st_size / 1024
    except:
        return 0

def split_large_file(file_path, max_size_kb=30):
    """切分大文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except UnicodeDecodeError:
        # 尝试其他编码
        try:
            with open(file_path, 'r', encoding='gbk') as f:
                content = f.read()
        except:
            with open(file_path, 'r', encoding='latin-1') as f:
                content = f.read()
    
    lines = content.split('\n')
    total_lines = len(lines)
    
    # 估算每个部分的行数
    file_size_kb = get_file_size_kb(file_path)
    target_lines_per_part = int(total_lines * max_size_kb / file_size_kb)
    num_parts = max(2, (total_lines + target_lines_per_part - 1) // target_lines_per_part)
    
    lines_per_part = total_lines // num_parts
    
    base_name = file_path.stem
    extension = file_path.suffix
    parent_dir = file_path.parent
    
    split_files = []
    
    for part_num in range(num_parts):
        start_line = part_num * lines_per_part
        if part_num == num_parts - 1:
            end_line = total_lines
        else:
            end_line = (part_num + 1) * lines_per_part
        
        part_content = '\n'.join(lines[start_line:end_line])
        
        # 生成新文件名
        new_filename = f"{base_name}({part_num + 1}){extension}"
        new_filepath = parent_dir / new_filename
        
        # 保存文件
        with open(new_filepath, 'w', encoding='utf-8') as f:
            f.write(part_content)
        
        split_files.append(new_filepath)
        
        new_size = get_file_size_kb(new_filepath)
        print(f"   ✅ 生成: {new_filename} ({new_size:.1f} KB, {end_line-start_line}行)")
    
    return split_files

def process_skin_files(source_dir):
    """处理皮肤文件"""
    source_path = Path(source_dir)
    
    if not source_path.exists():
        print(f"❌ 目录不存在: {source_dir}")
        return
    
    # 获取所有文件
    all_files = [f for f in source_path.iterdir() if f.is_file()]
    
    print(f"📁 处理目录: {source_dir}")
    print(f"📊 找到 {len(all_files)} 个文件")
    print("=" * 80)
    
    processed_files = []
    split_files = []
    large_files = []
    
    # 第一步：分析文件大小
    for file_path in sorted(all_files):
        file_size = get_file_size_kb(file_path)
        
        if file_size > 30:
            large_files.append({
                'path': file_path,
                'size': file_size
            })
            print(f"🔴 需要切分: {file_path.name} ({file_size:.1f} KB)")
        else:
            print(f"✅ 大小合适: {file_path.name} ({file_size:.1f} KB)")
    
    print(f"\n📊 分析结果:")
    print(f"   总文件数: {len(all_files)}")
    print(f"   需要切分: {len(large_files)} 个文件")
    print(f"   大小合适: {len(all_files) - len(large_files)} 个文件")
    
    # 第二步：处理大文件切分
    if large_files:
        print(f"\n🔄 开始切分大文件...")
        
        for file_info in large_files:
            print(f"\n📄 切分文件: {file_info['path'].name} ({file_info['size']:.1f} KB)")
            
            try:
                # 切分文件
                parts = split_large_file(file_info['path'], max_size_kb=30)
                split_files.extend(parts)
                
                # 删除原文件
                file_info['path'].unlink()
                print(f"   🗑️  删除原文件: {file_info['path'].name}")
                
            except Exception as e:
                print(f"   ❌ 切分失败: {str(e)}")
    
    # 第三步：重新获取所有文件（包括切分后的）
    all_files = [f for f in source_path.iterdir() if f.is_file()]
    
    print(f"\n🔄 开始添加文件名前缀...")
    print("=" * 80)
    
    # 第四步：给所有文件添加前缀
    prefix = "候鸟皮肤库窗口源代码_"
    renamed_count = 0
    
    for file_path in sorted(all_files):
        old_name = file_path.name
        
        # 检查是否已经有前缀
        if old_name.startswith(prefix):
            print(f"⏭️  跳过（已有前缀）: {old_name}")
            continue
        
        # 生成新文件名
        new_name = prefix + old_name
        new_path = file_path.parent / new_name
        
        try:
            # 重命名文件
            file_path.rename(new_path)
            processed_files.append(new_path)
            renamed_count += 1
            
            file_size = get_file_size_kb(new_path)
            print(f"✅ 重命名: {old_name} -> {new_name} ({file_size:.1f} KB)")
            
        except Exception as e:
            print(f"❌ 重命名失败: {old_name} - {str(e)}")
    
    return {
        'total_files': len(all_files),
        'renamed_files': renamed_count,
        'split_files': len(split_files),
        'large_files': len(large_files),
        'processed_files': processed_files
    }

def analyze_final_result(source_dir):
    """分析最终结果"""
    source_path = Path(source_dir)
    all_files = [f for f in source_path.iterdir() if f.is_file()]
    
    print(f"\n📋 最终结果分析:")
    print("=" * 80)
    
    total_size = 0
    size_distribution = {
        'small': 0,    # < 10KB
        'medium': 0,   # 10-30KB
        'large': 0     # > 30KB
    }
    
    prefix_count = 0
    
    for file_path in sorted(all_files):
        file_size = get_file_size_kb(file_path)
        total_size += file_size
        
        # 检查前缀
        if file_path.name.startswith("候鸟皮肤库窗口源代码_"):
            prefix_count += 1
        
        # 大小分类
        if file_size < 10:
            size_distribution['small'] += 1
            status = "🟢"
        elif file_size <= 30:
            size_distribution['medium'] += 1
            status = "✅"
        else:
            size_distribution['large'] += 1
            status = "🔴"
        
        print(f"{status} {file_path.name} ({file_size:.1f} KB)")
    
    print("=" * 80)
    print(f"📊 最终统计:")
    print(f"   总文件数: {len(all_files)}")
    print(f"   总大小: {total_size:.1f} KB")
    print(f"   平均大小: {total_size/len(all_files):.1f} KB")
    print(f"   有前缀文件: {prefix_count} 个")
    print(f"   前缀覆盖率: {prefix_count/len(all_files)*100:.1f}%")
    
    print(f"\n📈 大小分布:")
    print(f"   🟢 小文件 (< 10KB): {size_distribution['small']} 个")
    print(f"   ✅ 中等文件 (10-30KB): {size_distribution['medium']} 个")
    print(f"   🔴 大文件 (> 30KB): {size_distribution['large']} 个")
    
    if size_distribution['large'] == 0:
        print(f"\n🎉 所有文件大小都符合要求！")
    else:
        print(f"\n⚠️  还有 {size_distribution['large']} 个文件超过30KB")

if __name__ == "__main__":
    source_directory = r"F:\augment\output\skin"
    
    print("📄 候鸟皮肤库文件处理工具")
    print("功能：1. 添加前缀 2. 切分大文件")
    print("=" * 80)
    
    # 处理文件
    result = process_skin_files(source_directory)
    
    if result:
        print("\n" + "=" * 80)
        print(f"🎉 处理完成!")
        print(f"   总文件数: {result['total_files']}")
        print(f"   重命名文件: {result['renamed_files']}")
        print(f"   切分的大文件: {result['large_files']}")
        print(f"   生成的切分文件: {result['split_files']}")
        
        # 分析最终结果
        analyze_final_result(source_directory)
        
        print(f"\n📁 处理完成的文件位置: {source_directory}")
        print("🎯 所有文件已准备就绪！")
    else:
        print("❌ 处理失败")
