标题: 浏览器指纹：媒体设备
英文标题: Browser Fingerprint：Media Device
ID: 89
分类ID: 25
添加时间: 1613977376
更新时间: 1685607188
访问次数: 0
SEO标题: 浏览器指纹：媒体设备
SEO关键词: 浏览器指纹：媒体设备
SEO描述: 浏览器指纹：媒体设备

================================================== 内容 ==================================================
WebRTC是一种浏览器插件，通过直接的P2P连接，促使网页内的音频和视频通话，从而无需安装额外插件或其他本地应用。为了使这个插件工作，WebRTC会连接到您的媒体设备，例如麦克风、摄像头和耳机。网站可通过以下两种方式利用这一追踪机制：

1. 设备枚举
2. 媒体设备ID追踪


您可以在Browser leaks test website中查看这两种身份验证方法。

### 设备枚举
这种方法依赖于检索用户已安装的麦克风、摄像头和耳机的完整列表来运作。虽然仅这个数字不足以明确地定位用户，但它仍然可以发挥一定作用。

在候鸟浏览器中，已经根据浏览器配置文件中不同媒体设备的数量进行了模拟。

### 媒体设备ID
为了使WebRTC正常工作，网站不仅需要知道您拥有的设备数量和类型。为了建立完善的实时通讯，唯一的设备标识符也是必须的。您可以联想一下您的设备地址。当然，浏览器不会允许网站得知你设备的完整型号名称，它们会用哈希值来替代，这就是设备ID。与此同时，网站也可以使用这些值用于用户识别。

由于媒体设备ID对每一个用户来说都是唯一的，因此它在浏览器指纹识别中是一种特别有效的技术。

在候鸟浏览器中，每一个设备的真实设备ID都会得到掩蔽。

================================================== 英文内容 ==================================================
WebRTC is a browser plug-in that enables audio and video calls within web pages via a direct P2P connection, eliminating the need to install additional plug-ins or other native applications. To make this plug-in work, WebRTC connects to your media devices, such as microphones, cameras, and headphones. Websites can take advantage of this tracking mechanism in two ways:

1. Device enumeration
2. Media device ID tracing


You can view both authentication methods on the Browser leaks test website.

### Device enumeration
The method relies on retrieving a complete list of microphones, cameras and headphones that the user has installed. While this number alone is not enough to explicitly target users, it can still make a difference.

In the Migratory Bird browser, simulation has been done based on the number of different media devices in the browser profile.

### Media device ID
For WebRTC to work properly, websites need to know more than just the number and type of devices you have. In order to establish perfect real-time communication, unique device identifiers are also necessary. You can think of your device address. Of course, browsers won't let websites know the full model name of your device; instead, they'll use a hash value called the device ID. At the same time, websites can also use these values for user identification.

Because the media device ID is unique to each user, it is a particularly effective technique for browser fingerprinting.

In the Migratory Bird Browser, the real device ID of each device is masked.