﻿<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Window size="600,400" caption="0,0,0,40" roundcorner="5,5,5,5" showshadow="true" shadowsize="3" shadowposition="0,0" shadowcolor="#ff6272A4" fademode="true">
	<Include source="Default.xml" />

  <VerticalLayout width="953" height="590" bkcolor="#FF282A36">

    <HorizontalLayout height="37">

    	 <Button name="login" bkimage="icon.png" padding="8,6,0,0" width="28" height="28"/>
      <Label name="Wndtitle" padding="6,4,0,0" text="环境Cookie" endellipsis="true" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" align="left" font="8"></Label>

      <Control width="10"/>
      <Button name="minbtn" width="28" height="26" tooltip="最小化" normalimage="minbtn.png" hotimage="minbtn_hover.png" pushedimage="minbtnpush.png" />
      <Button name="closebtn" width="28" height="26"  tooltip="关闭" normalimage="closebtn.png" hotimage="closebtn_hover.png" pushedimage="closebtnpush.png" />

    </HorizontalLayout>

  <HorizontalLayout name="bkground" visible="true">

		<VerticalLayout height="603">


			<HorizontalLayout height="246" padding="16,20,16,2">

			    	 <VerticalLayout >
				         <RichEdit name="msg" vscrollbar="true" padding="0,10,0,2" height="232" tipvaluecolor="#FF333333" borderround="3,3" bkcolor="#ffdce1e7" font="8" textpadding="10,6,4,0" textcolor="#ff333333" wantreturn="true" rich="false" transparent="false">
				      </RichEdit>
				     </VerticalLayout>

			</HorizontalLayout>

      <HorizontalLayout height="30">
      <VerticalLayout >
        <Control />
        <HorizontalLayout height="26">
        <!--<Label name="cookiemgrInfo" height="26" padding="20,0,0,0" text="" align="left" textcolor="#FF333333" hottextcolor="ffFF0000" font="8"></Label>
          <Control width="2"/>
        <Button text="换算COOKIE到期时间" padding="0,2,0,0" name="expiration" width="150" height="24" textcolor="#FF757f91" disabledtextcolor="#FFB3B3B3" align="center" font="21" endellipsis="true" />-->
        </HorizontalLayout>
        <Control />
      </VerticalLayout>
      </HorizontalLayout>

      <HorizontalLayout inset="0,0,0,0" height="403">

				</HorizontalLayout>






		</VerticalLayout>


	</HorizontalLayout>




    <HorizontalLayout height="52" bkcolor="#ffe9e9e9">
         <Control />
      <!--<VerticalLayout width="420">

       </VerticalLayout>-->
        <VerticalLayout width="140">
      		<Control />
          <Button text="确定" name="btnok"   padding="16,2,0,0" width="120" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	bkimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
          <Control />
       </VerticalLayout>

<Control />
    </HorizontalLayout>
  </VerticalLayout>
</Window>
