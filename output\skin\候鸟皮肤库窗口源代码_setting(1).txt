<?xml version="1.0" encoding="utf-8" standalone="yes" ?>
<Window size="690,560" sizebox="4,4,4,4" mininfo="690,560" caption="0,0,0,100" showshadow="true" shadowimage="shadow.png" shadowcorner="23,13,23,33" shadowcolor="#ff6272A4" fademode="true">
	<Include source="Default.xml" />
	<VerticalLayout bkcolor="#FF282A36">
		<HorizontalLayout height="37">
			<VerticalLayout width="204" bkcolor="#FF44475A">
						<Label name="set" padding="20,14,0,0" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" font="8" text="设置" />
		  </VerticalLayout>
		  <VerticalLayout bkcolor="#FF282A36" width="486">

		  	    <HorizontalLayout>
		  	    	<Control width="430"/>
								<Button name="minbtn" width="28" height="26" tooltip="最小化" normalimage="minbtn.png" hotimage="minbtn_hover.png" pushedimage="minbtnpush.png" />
			      		<Button name="closewin" width="28" height="26"  tooltip="关闭" normalimage="closebtn.png" hotimage="closebtn_hover.png" pushedimage="closebtnpush.png" />
			      </HorizontalLayout>
		  </VerticalLayout>
		</HorizontalLayout>
		<HorizontalLayout bkcolor="ffffffff">

			<VerticalLayout width="204" bkcolor="#ffebebeb">
				<HorizontalLayout height="23">
				</HorizontalLayout>
				<HorizontalLayout height="33">
           <Button name="login" bkimage="Setting/setting_logo.png" padding="20,0,0,0" width="32" height="32"/>
           <Label name="setting_account" padding="8,0,0,0" width="130" texttooltip="true" endellipsis="true" textcolor="#FF000000" hottextcolor="ffFF0000" font="8" text="ihouniao" />
				</HorizontalLayout>

				<Button name="account_set" text="账号信息" font="8" width="177" height="32" textpadding="31,6,0,0" texttooltip="true" endellipsis="true" padding="12,18,0,0" align="left" selected="true" bkimage="\Setting\btn\account_btn_normal.png" hotimage="\Setting\btn\account_btn_hot.png" focusedimage="\Setting\btn\account_btn_selected.png" textcolor="ff505050" hottextcolor="ff505050" pushedtextcolor="ff0089ff" focusedtextcolor="ff0089ff"/>
				<Control height="7"/>
				<Button name="generic_set"  text="通用设置" font="8" width="177" height="32"  textpadding="31,6,0,0" texttooltip="true" endellipsis="true" padding="12,0,0,0"  align="left" bkimage="\Setting\btn\setup_btn_normal.png" hotimage="\Setting\btn\setup_btn_hot.png" focusedimage="\Setting\btn\setup_btn_selected.png" textcolor="ff505050" hottextcolor="ff505050" pushedtextcolor="ff0089ff" focusedtextcolor="ff0089ff"/>
				<Control height="7"/>
        <Button name="shortcut_set" text="快捷按键" font="8" width="177" height="32"  textpadding="31,6,0,0" texttooltip="true" endellipsis="true" padding="12,0,0,0" align="left" bkimage="\Setting\btn\shortcut_btn_normal.png" hotimage="\Setting\btn\shortcut_btn_hot.png" focusedimage="\Setting\btn\shortcut_btn_selected.png" textcolor="ff505050" hottextcolor="ff505050" pushedtextcolor="ff0089ff" focusedtextcolor="ff0089ff"/>
				<Control height="7"/>
				<Button name="backup_set" text="通讯设置" font="8" width="177" height="32"  textpadding="31,6,0,0" texttooltip="true" endellipsis="true" padding="12,0,0,0" align="left" bkimage="\Setting\btn\sync_btn_normal.png" hotimage="\Setting\btn\sync_btn_hot.png" focusedimage="\Setting\btn\sync_btn_selected.png" textcolor="ff505050" hottextcolor="ff505050" pushedtextcolor="ff0089ff" focusedtextcolor="ff0089ff"/>
				<Control height="7"/>
        <Button name="item_set" text="环境面板" font="8" width="177" height="32"  textpadding="31,6,0,0" texttooltip="true" endellipsis="true" padding="12,0,0,0" align="left" bkimage="\Setting\btn\platform_btn_normal.png" hotimage="\Setting\btn\platform_btn_hot.png" focusedimage="\Setting\btn\platform_btn_selected.png" textcolor="ff505050" hottextcolor="ff505050" pushedtextcolor="ff0089ff" focusedtextcolor="ff0089ff"/>
        <Control height="7"/>
        <Button name="timezone_set" text="时区检测" font="8" width="177" height="32"  textpadding="31,6,0,0" texttooltip="true" endellipsis="true" padding="12,0,0,0" align="left" bkimage="\Setting\btn\time_btn_normal.png" hotimage="\Setting\btn\time_btn_hot.png" focusedimage="\Setting\btn\time_btn_selected.png" textcolor="ff505050" hottextcolor="ff505050" pushedtextcolor="ff0089ff" focusedtextcolor="ff0089ff"/>
        <Control height="7"/>
        <Button name="browse_set" text="浏览器设置" font="8" width="177" height="32"  textpadding="31,6,0,0" texttooltip="true" endellipsis="true" padding="12,0,0,0" align="left" bkimage="\Setting\btn\tool_btn_normal.png" hotimage="\Setting\btn\tool_btn_hot.png" focusedimage="\Setting\btn\tool_btn_selected.png" textcolor="ff505050" hottextcolor="ff505050" pushedtextcolor="ff0089ff" focusedtextcolor="ff0089ff"/>
				<Control height="7"/>
        <Button name="security_set" text="安全设置" font="8" width="177" height="32"  textpadding="31,6,0,0" texttooltip="true" endellipsis="true" padding="12,0,0,0" align="left" bkimage="\Setting\btn\secure_btn_normal.png" hotimage="\Setting\btn\secure_btn_hot.png" focusedimage="\Setting\btn\secure_btn_selected.png" textcolor="ff505050" hottextcolor="ff505050" pushedtextcolor="ff0089ff" focusedtextcolor="ff0089ff"/>
        <Control height="7"/>
				<Button name="about_set" text="关于产品" font="8" width="177" height="32"  textpadding="31,6,0,0" texttooltip="true" endellipsis="true" padding="12,0,0,0" align="left" bkimage="\Setting\btn\light_btn_normal.png" hotimage="\Setting\btn\light_btn_hot.png" focusedimage="\Setting\btn\light_btn_selected.png" textcolor="ff505050" hottextcolor="ff505050" pushedtextcolor="ff0089ff" focusedtextcolor="ff0089ff"/>
        <Control height="7"/>
        <Button name="test" visible="false" font="8" text="测试栏目" width="177" height="32"  textpadding="31,6,0,0" texttooltip="true" endellipsis="true"   padding="12,0,0,0" align="left" bkimage="\Setting\btn\setup_btn_normal.png" hotimage="\Setting\btn\setup_btn_hot.png" focusedimage="\Setting\btn\setup_btn_selected.png" textcolor="ff505050" hottextcolor="ff505050" pushedtextcolor="ff0089ff" focusedtextcolor="ff0089ff"/>
        <Control height="7"/>
			  <Button name="generic1_set" visible="false" text="字体颜色" font="8" width="177" height="32"  textpadding="31,6,0,0" texttooltip="true" endellipsis="true" padding="12,0,0,0"  align="left" bkimage="\Setting\btn\account_btn_normal.png" hotimage="\Setting\btn\account_btn_hot.png" focusedimage="\Setting\btn\account_btn_selected.png" textcolor="ff505050" hottextcolor="ff505050" pushedtextcolor="ff0089ff" focusedtextcolor="ff0089ff"/>
			</VerticalLayout>

			<TabLayout name="default_bk" selectedid="0">
				<VerticalLayout inset="0,6,0,0"  vscrollbar="true">
					<HorizontalLayout height="32" inset="0,0,0,0">
					   <Button padding="30,0,0,0" height="32" width="32" bkimage="Setting\title\user.png"/>
					   <Label name="set_account_name" textpadding="10,0,0,0" width="220" align="left" font="8" text="帐户信息"/>
					</HorizontalLayout>

					<HorizontalLayout height="52" inset="0,0,0,0">
					   <Label name="set_account_dec" padding="24,0,0,0" textpadding="0,6,0,0" width="220" align="left" font="8" textcolor="#FF6d6d6d" text="当前帐户信息"/>
					</HorizontalLayout>

					<HorizontalLayout height="42">
					      <Label name="about_name_title" padding="24,0,0,0" width="220" align="left" font="8" text="候鸟帐户ID"/>
					</HorizontalLayout>

					<HorizontalLayout height="24">
					       <Button name="login_id" padding="24,0,0,0"  autocalcwidth="true" maxwidth="320" height="22" align="left" font="8" endellipsis="true" textcolor="#FF6d6d6d" hottextcolor="#ff00ceb8" pushedtextcolor="ffFF0000" text="<EMAIL>"/>
					</HorizontalLayout>

					<HorizontalLayout height="8"></HorizontalLayout>

					<HorizontalLayout height="30">
                 <Button name="change_login" bkcolor="#FFffffff" bordersize="1" bordercolor="#ffd3d3d3" borderround="11,11" hotbkcolor="#fff1f2f1" padding="24,0,0,0" float="left" width="86" height="26" text="切换帐户"/>
					</HorizontalLayout>

					<HorizontalLayout height="18"></HorizontalLayout>

					<HorizontalLayout height="30">
					      <Label name="cloud_version_title" padding="24,0,0,0" width="220" align="left" font="8" text="云端数据版本"/>
					</HorizontalLayout>

					<HorizontalLayout height="30">
					      <Button name="about_name" padding="24,3,0,0" autocalcwidth="true" align="left" font="8" textcolor="#FF6d6d6d" hottextcolor="ff00ceb8" pushedtextcolor="ffFF0000" text="数据版本"/>
					</HorizontalLayout>

         <HorizontalLayout height="2"></HorizontalLayout>

					<HorizontalLayout height="30">
                 <Button name="cloud_version" bkcolor="#FFffffff" bordersize="1" bordercolor="#ffd3d3d3" borderround="11,11" hotbkcolor="#fff1f2f1" padding="24,0,0,0" float="left" width="106" height="26" text="数据版本验证"/>
					</HorizontalLayout>


					<HorizontalLayout height="16"></HorizontalLayout>

					<HorizontalLayout height="30">
					      <Label name="lastlogintimel" padding="24,0,0,0" width="220" align="left" font="8" text="最近登录时间节点"/>
					</HorizontalLayout>

					<HorizontalLayout height="30">
					      <Button name="lastlogintime" padding="24,3,0,0" autocalcwidth="true" align="left" font="8" textcolor="#FF6d6d6d" hottextcolor="ff00ceb8" pushedtextcolor="ffFF0000" text="最近登录时间节点"/>
					</HorizontalLayout>


					<HorizontalLayout height="16"></HorizontalLayout>

					<HorizontalLayout height="30">
					      <Label name="expiretimel" padding="24,0,0,0" width="220" align="left" font="8" text="授权到期时间"/>

					</HorizontalLayout>

					<HorizontalLayout height="30">
					      <Button name="expiretime" padding="24,3,0,0" autocalcwidth="true" align="left" font="8" textcolor="#FF6d6d6d" hottextcolor="ff00ceb8" pushedtextcolor="ffFF0000" text="授权到期时间"/>
					</HorizontalLayout>


					<HorizontalLayout height="16"></HorizontalLayout>

					<HorizontalLayout height="30">
					      <Label name="LicenseTypel" padding="24,0,0,0" width="220" align="left" font="8" text="授权套餐类型"/>
					</HorizontalLayout>

					<HorizontalLayout height="30">
					      <Button name="LicenseType" padding="24,3,0,0" autocalcwidth="true" align="left" font="8" textcolor="#FF6d6d6d" hottextcolor="ff00ceb8" pushedtextcolor="ffFF0000" text="授权套餐类型"/>
					</HorizontalLayout>

					<HorizontalLayout height="4"></HorizontalLayout>

					<HorizontalLayout height="30" >
              <Button name="neworder" padding="24,0,0,0" bordersize="1" bordercolor="#ffd3d3d3" borderround="11,11" hotbkcolor="#fff1f2f1" width="120" height="26" font="5" text="续费"/>
					</HorizontalLayout>



					<HorizontalLayout height="16"></HorizontalLayout>

					<HorizontalLayout height="30">
					      <Label name="ChangePassword_title" padding="24,0,0,0" width="220" align="left" font="8" text="更换帐户密码"/>
					</HorizontalLayout>
	<HorizontalLayout height="4"></HorizontalLayout>
					<HorizontalLayout height="30" >
              <Button name="ChangePassword" padding="24,0,0,0" bordersize="1" bordercolor="#ffd3d3d3" borderround="11,11" hotbkcolor="#fff1f2f1" width="120" height="26" font="5" text="修改密码"/>
					</HorizontalLayout>

					<HorizontalLayout height="16"></HorizontalLayout>

					<HorizontalLayout height="30">
					      <Label name="quitlogin_title" padding="24,0,0,0" width="220" align="left" font="8" text="安全退出登录"/>
					</HorizontalLayout>
	<HorizontalLayout height="4"></HorizontalLayout>
					<HorizontalLayout height="30" >
              <Button name="quitlogin" padding="24,0,0,0" bordersize="1" bordercolor="#ffd3d3d3" borderround="11,11" hotbkcolor="#fff1f2f1" width="120" height="26" font="5" text="退出登录"/>
					</HorizontalLayout>

          <HorizontalLayout height="50"></HorizontalLayout>


          <!--<HorizontalLayout height="30" width="520" inset="0,0,0,0">
              <Control />
              <Button name="clearlogininfo" padding="10,0,0,0" float="left" width="180" font="0"  textcolor="#FF555555"  text="清除本地账户历史登录痕迹"/>
              <Control />
          </HorizontalLayout>-->

				</VerticalLayout>

				<VerticalLayout inset="0,6,0,0" vscrollbar="true">
					<HorizontalLayout height="32" inset="0,0,0,0">
					   <Button padding="30,0,0,0" height="32" width="32" bkimage="Setting\title\setting.png"/>
					   <Label name="set_common_name" textpadding="10,0,0,0" width="220" align="left" font="8" text="通用设置"/>
					</HorizontalLayout>

					<HorizontalLayout height="52" inset="0,0,0,0">
					   <Label name="set_common_dec" padding="24,0,0,0" textpadding="0,6,0,0" width="220" align="left" font="8" textcolor="#FF6d6d6d" text="软件全局设置"/>
					</HorizontalLayout>

					<HorizontalLayout height="46">
					      <Label name="cfglanguagel" padding="24,0,0,0" width="220" align="left" font="8" text="全局默认语言设置"/>
					</HorizontalLayout>

					<HorizontalLayout height="35">
					       <Combo name="cfglanguage" scrollbarwidth="7" padding="24,0,0,0" textpadding="0,0,50,0" itemalign="center" bkimage="file='combo_normal_large.png' corner='0,0,25,0'" hotimage="file='combo_hot_large.png' corner='0,0,25,0'" pushedimage="file='combo_pushed_large.png' corner='0,0,25,0'" width="185" height="25"  itemhotbkcolor="fff1f1f1" itemselectedbkcolor="ffffffff" itemtextpadding="-25,0,0,0">
                            <!--<ListLabelElement name="cfgencrypt" text="简体中文" selected="true"/>
                            <ListLabelElement name="cfgencryptnot" text="英文" />-->
                         </Combo>
					</HorizontalLayout>

					<HorizontalLayout height="16"></HorizontalLayout>

					<HorizontalLayout height="30">
                 <Label name="location" padding="24,0,0,0" width="400" align="left" font="8" text="默认配置文件存储位置"/>
					</HorizontalLayout>

					<HorizontalLayout height="8"></HorizontalLayout>

					<HorizontalLayout height="68" width="500">
						<RichEdit name="location_edit" padding="24,0,0,10" height="66" width="276" killfocusimage=""  bordersize="1" bordercolor="#ffd3d3d3" focusbordercolor="#ff9a9a9b" borderround="7,7" bkimage="file='file_setup_location_normal.png' dest='0,0,276,26'" font="20" textpadding="30,6,20,0" text="" tipvaluecolor="ff333333" multiline="true" textcolor="ff666666" rich="true" vscrollbar="true">
				    </RichEdit>
				    <Button name="locationeditbtn" padding="10,0,0,0" text="更改" align="center" bkcolor="#FFffffff" bordersize="1" bordercolor="#ffd3d3d3" focusbordercolor="#ff9a9a9b" borderround="7,7" hotbkcolor="#fff1f2f1" width="60" height="26"/>
					</HorizontalLayout>
					<HorizontalLayout height="20" width="500">
					<Label name="locationdefault" float="left" padding="24,2,0,0" width="160" height="20" textcolor="ffa6a6a6" texttooltip="true" endellipsis="true" text="配置数据文件的默认存储位置" font="0"/><Button name="gotofolder" float="left" align="left" padding="10,3,0,0" text="查看本地存储数据" texttooltip="true" endellipsis="true" font="30" width="160" height="20" textcolor="ff2ba704"/>
					</HorizontalLayout>

          <HorizontalLayout height="20" width="500">
            <Label name="locationdes" float="left" padding="24,-4,0,0" height="20" texttooltip="true" endellipsis="true" textcolor="ffa6a6a6" text="建议选用NVME或SSD高速磁盘存储" font="0"/>
          </HorizontalLayout>

            	<HorizontalLayout height="10"></HorizontalLayout>


							<HorizontalLayout height="16"></HorizontalLayout>

            	<HorizontalLayout height="30">
                 <Label name="commonl" padding="24,0,0,0" width="400" texttooltip="true" endellipsis="true" align="left" font="8" text="通用选项"/>
					    </HorizontalLayout>

            	<HorizontalLayout height="8"></HorizontalLayout>

            	<HorizontalLayout height="200">
								<VerticalLayout padding="24,4,0,0">
		              				<Option name="commwnd" font="8" padding="0,0,0,0" normalimage="file='option_normal.png' dest='0,0,18,18'" hotimage="file='option_hot.png' dest='0,0,18,18'" selectedimage="file='option_pushed.png' dest='0,0,18,18'"  align="left" width="380" height="18" text="连接中断，在桌面右下角提示我" textpadding="28,-1,6,0"  texttooltip="true" endellipsis="true" selected="true"/>
		              				<Control height="6" />
		              				<Option name="updatewnd" font="8" padding="0,4,0,0" normalimage="file='option_normal.png' dest='0,0,18,18'" hotimage="file='option_hot.png' dest='0,0,18,18'" selectedimage="file='option_pushed.png' dest='0,0,18,18'" align="left" width="380" height="18" text="有更新时,自动提示我升级" textpadding="28,-1,6,0"  texttooltip="true" endellipsis="true" selected="true" />
													<Control height="6" />
													<Option name="autorun" font="8" padding="0,4,0,0" normalimage="file='option_normal.png' dest='0,0,18,18'" hotimage="file='option_hot.png' dest='0,0,18,18'" selectedimage="file='option_pushed.png' dest='0,0,18,18'"  align="left" width="380" height="18" text="开机时自动启动候鸟浏览器" textpadding="28,-1,6,0"  texttooltip="true" endellipsis="true" />
													<Control height="6" />
													<Option name="upbackup" font="8" padding="0,4,0,0" normalimage="file='option_normal.png' dest='0,0,18,18'" hotimage="file='option_hot.png' dest='0,0,18,18'" selectedimage="file='option_pushed.png' dest='0,0,18,18'" align="left" width="380" height="18" text="退出时自动同步环境会话数据" textpadding="28,-1,6,0"  texttooltip="true" endellipsis="true" selected="true"/>
		              				<Control height="6" />
		              				<Option name="newfullzip" font="8" padding="0,4,0,0" normalimage="file='option_normal.png' dest='0,0,18,18'" hotimage="file='option_hot.png' dest='0,0,18,18'" selectedimage="file='option_pushed.png' dest='0,0,18,18'" align="left" width="380" height="18" text="使用精简数据同步模式" textpadding="28,-1,6,0"  texttooltip="true" endellipsis="true" selected="true"/>
                  				<Control height="6" />
                  				<Option name="FingerCheck" font="8" padding="0,4,0,0" normalimage="file='option_normal.png' dest='0,0,18,18'" hotimage="file='option_hot.png' dest='0,0,18,18'" selectedimage="file='option_pushed.png' dest='0,0,18,18'" align="left" width="380" height="18" text="每次运行环境自动进行指纹检测" textpadding="28,-1,6,0"  texttooltip="true" endellipsis="true" />
                          <Control height="6" />
                          <Option name="rightmenubtn" font="8" padding="0,4,0,0" normalimage="file='option_normal.png' dest='0,0,18,18'" hotimage="file='option_hot.png' dest='0,0,18,18'" selectedimage="file='option_pushed.png' dest='0,0,18,18'" align="left" width="380" height="18" text="主面板上显示右键菜单按钮" textpadding="28,-1,6,0"  texttooltip="true" endellipsis="true" selected="true"/>
                </VerticalLayout>
							</HorizontalLayout>

		      		<HorizontalLayout height="50"></HorizontalLayout>

				</VerticalLayout>

				<VerticalLayout inset="0,6,0,0" vscrollbar="true">

         <HorizontalLayout height="32" inset="0,0,0,0">
					   <Button padding="30,0,0,0" height="32" width="32" bkimage="Setting\title\hotkey.png"/>
					   <Label name="set_shortcut_name" textpadding="10,0,0,0" width="220" align="left" font="8" text="快捷按键"/>
					</HorizontalLayout>

					<HorizontalLayout height="52" inset="0,0,0,0">
					   <Label name="set_shortcut_dec" padding="24,0,0,0" textpadding="0,6,0,0" width="220" align="left" font="8" textcolor="#FF6d6d6d" text="自定义候鸟快捷按键"/>
					</HorizontalLayout>

          <HorizontalLayout height="46">
					      <Label name="about_shortcut_title" padding="24,0,0,0" width="220" align="left" font="8" text="开启/关闭快捷键"/>
					</HorizontalLayout>


          <HorizontalLayout height="24">
            <CheckBox name="Hot_switch" padding="24,0,0,10" width="52" height="24"  normalimage="gpu_on.png" selectedimage="gpu_off.png" disabledimage="gpu_on.png" />
          </HorizontalLayout>

					<HorizontalLayout height="8"></HorizontalLayout>

					 <HorizontalLayout height="24">
					      <Label name="hotl" padding="24,0,0,0" height="20" font="5" textcolor="ffa6a6a6" text="启用/禁用快捷键"/>
					 </HorizontalLayout>

					<HorizontalLayout height="16"></HorizontalLayout>

					<HorizontalLayout height="30">
             <Label name="closeallexplorerl" font="8" align="left" padding="24,0,0,0" height="20" width="200" text="关闭所有浏览器"/>
					</HorizontalLayout>

          <HorizontalLayout height="40">
						<Combo name="closeallexplorer" padding="24,0,0,0"  textpadding="0,0,50,0" itemtextpadding="-25,0,0,0" itemalign="center" borderround="3,3" bkimage="combo_normal_large.png" hotimage="combo_hot_large.png" pushedimage="combo_pushed_large.png" width="155" height="25"  itemhotbkcolor="fff1f1f1" itemselectedbkcolor="ffffffff" >
                            <ListLabelElement  text="Ctrl + Alt + Q" selected="true"/>
                            <ListLabelElement  text="Ctrl + Q" />
                         </Combo>
					</HorizontalLayout>

					<HorizontalLayout height="8"></HorizontalLayout>

					<HorizontalLayout height="30">
             <Label name="showallexplorerl" font="8" align="left" padding="24,0,0,0" height="20" width="200" text="显示所有浏览器"/>
					</HorizontalLayout>

          <HorizontalLayout height="40">
						<HotKey name="showallexplorer" padding="24,0,0,0" text="Ctrl + Alt + G" align="center" borderround="5,5" bkimage="screen_normal.png" hotimage="screen_hot.png" pushedimage="screen_hot.png" width="155" height="26"/>
					</HorizontalLayout>

					<HorizontalLayout height="8"></HorizontalLayout>

					<HorizontalLayout height="30">
             <Label name="hideallexplorerl" font="8" align="left" padding="24,0,0,0" height="20" width="200" text="隐藏所有浏览器"/>
					</HorizontalLayout>

          <HorizontalLayout height="40">
						<HotKey name="hideallexplorer" padding="24,0,0,0" text="Ctrl + Alt + H" align="center" borderround="5,5" bkimage="screen_normal.png" hotimage="screen_hot.png" pushedimage="screen_hot.png" width="155" height="26"/>
					</HorizontalLayout>

					<HorizontalLayout height="8"></HorizontalLayout>

					<HorizontalLayout height="30">
             <Label name="hideappl" font="8" align="left" padding="24,0,0,0" height="20" width="200" text="隐藏候鸟"/>
					</HorizontalLayout>

          <HorizontalLayout height="40">
						<HotKey name="hideapp" padding="24,0,0,0" text="Ctrl + H" align="center" borderround="5,5" bkimage="screen_normal.png" hotimage="screen_hot.png" pushedimage="screen_hot.png" width="155" height="26"/>
					</HorizontalLayout>

					<HorizontalLayout height="8"></HorizontalLayout>

					<HorizontalLayout height="30">
             <Label name="closeappl" font="8" align="left" padding="24,0,0,0" height="20" width="200" text="关闭候鸟"/>
					</HorizontalLayout>

          <HorizontalLayout height="40">
						<HotKey name="closeapp" padding="24,0,0,0" text="Ctrl + Alt + W" align="center" borderround="5,5" bkimage="screen_normal.png" hotimage="screen_hot.png" pushedimage="screen_hot.png" width="155" height="26"/>
					</HorizontalLayout>

					<HorizontalLayout height="8"></HorizontalLayout>
