@echo off
echo ========================================
echo RAGFlow MCP Server 诊断和修复工具
echo ========================================
echo.

echo 🔍 这个工具将帮助你:
echo   1. 诊断 MCP Server 连接问题
echo   2. 修复配置问题
echo   3. 测试连接
echo   4. 生成客户端配置
echo.

echo 📋 选择操作:
echo   1. 运行完整诊断
echo   2. 修复 MCP Server 配置
echo   3. 仅测试连接
echo   4. 生成客户端配置
echo   5. 查看服务状态
echo.

set /p choice="请选择操作 (1-5): "

if "%choice%"=="1" (
    echo.
    echo 🔍 运行完整诊断...
    python diagnose_mcp_connection.py
    echo.
    echo 📋 诊断完成！查看上面的结果和建议。
) else if "%choice%"=="2" (
    echo.
    echo 🔧 修复 MCP Server 配置...
    python fix_mcp_server.py
    echo.
    echo 📋 修复完成！
) else if "%choice%"=="3" (
    echo.
    echo 🧪 测试连接...
    python quick_test_mcp.py
) else if "%choice%"=="4" (
    echo.
    echo 📝 生成客户端配置...
    echo {
    echo   "ragflow_mcp": {
    echo     "server_url": "http://58.49.146.17:9382",
    echo     "api_key": "ragflow-UzYTlhMjIwNjI0YzExZjA4NzUyMDI0Mm",
    echo     "timeout": 30
    echo   }
    echo } > wing_client_config.json
    echo ✅ 客户端配置已生成: wing_client_config.json
) else if "%choice%"=="5" (
    echo.
    echo 📊 查看服务状态...
    echo.
    echo Docker 容器状态:
    docker ps | findstr ragflow
    echo.
    echo 端口监听状态:
    netstat -an | findstr ":9380\|:9382"
    echo.
    echo 测试连接:
    curl -s -o nul -w "RAGFlow主服务 (9380): %%{http_code}\n" http://localhost:9380/ 2>nul || echo RAGFlow主服务: 连接失败
    curl -s -o nul -w "MCP服务 (9382): %%{http_code}\n" http://localhost:9382/sse 2>nul || echo MCP服务: 连接失败
) else (
    echo.
    echo ❌ 无效选择，请重新运行脚本
)

echo.
echo ========================================
echo 💡 使用提示:
echo   - 如果是首次设置，建议先运行选项 1 (完整诊断)
echo   - 如果诊断发现问题，运行选项 2 (修复配置)
echo   - 修复后运行选项 3 (测试连接) 验证
echo   - 最后使用选项 4 生成的配置文件配置 Wing 客户端
echo ========================================
echo.

echo 📚 相关文档:
echo   - MCP Server 配置说明: MCP_Server_配置说明.md
echo   - 诊断结果文件: mcp_diagnostic_results.json
echo   - 客户端配置文件: wing_client_config.json
echo.

pause
