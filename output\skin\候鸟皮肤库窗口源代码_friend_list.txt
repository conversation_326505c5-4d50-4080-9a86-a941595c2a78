<?xml version="1.0" encoding="UTF-8"?>
<Window>
	<ListContainerElement padding="1,0,1,0" bkcolor="#FF282A36" selectedbkcolor="#FF44475A">
		<VerticalLayout height="79" >
			<HorizontalLayout height="76">
				<!--<VerticalLayout name="logo_container" width="60" inset="10,10,0,0" visible="false" >
					<Button name="logo" width="42" height="26"  padding ="0,8,0,0" mouse="false"/>
					<Button name="MsgTip" float="true" pos="42,5,0,0" width="16" height="16" bkimage="message.png" textpadding="0,0,0,2" text="0" textcolor="ffffffff" visible="false"/>
				</VerticalLayout>-->

				<VerticalLayout inset="10,14,0,0">
					<HorizontalLayout height="30">
            <!--<Control name="mark" width="15" height="15" padding ="0,6,0,0" bkimage="file='mark.png' source='0,0,15,15'" mouse="false"/>-->
            <Control name="top" width="15" height="15" bkimage="top.png" padding="2,6,6,0" visible="false" />
            <Control name="share" width="18" height="17" bkimage="share.png" padding="2,6,6,0" visible="false" />
            <Label name="nickname" endellipsis="true" textpadding="0,0,0,0" font="6" height="30" textcolor="#ff383a3e"/>
					</HorizontalLayout>

          <HorizontalLayout height="4">
          </HorizontalLayout>

					<HorizontalLayout height="20" width="186">

					  <Label name="status1" width="56" padding="0,0,0,0" text="最近活动: " textcolor="#ff888888"/>
            <Label name="status" width="100" align="left" padding="0,0,0,0" text="" textcolor="#ffa6a6a6"/>

					</HorizontalLayout>

					<!--<Label name="Buffer_Text" width="160" endellipsis="true" textcolor="#ffc5c5c5"/>-->
				</VerticalLayout>

        <VerticalLayout inset="0,14,0,0" width="158">
          <HorizontalLayout height="30">

            <Control name="statusimg" padding ="0,7,0,0" width="11" height="11" mouse="false"/>
            <Label name="statustxt" width="50" height="30" textpadding="6,-4,0,0" font="19" text="" textcolor="#ff00BD7F"/>
            <!--<GifAnim name="loading" visible="false" autoplay="false" float="true" bkimage="Rolling-0.6s-18px.gif" height="18" width="18" pos="40,5,0,0" padding="0,0,6,0"/>-->
            <ProgressRing name="loading" visible="false" float="true" pos="40,5,0,0" width="16" height="16"/>
            <Button name="start"  width="62" height="26" padding="12,0,0,0" bkimage="btn_start.png" hotimage="btn_start_hover.png" pushedimage="btn_start_click.png" align="left" textcolor="#ff6389e2" hottextcolor="#ff365bbe" pushedtextcolor="#ffff9833" font="19" textpadding="27,5,0,0" text="运行"/>
            <Button name="stop" visible="false" width="62" height="26" padding="12,0,0,0" bkimage="btn_stop.png" hotimage="btn_stop_hover.png" pushedimage="btn_stop_click.png" align="left" textcolor="#ffcb8c8c" hottextcolor="#fff06666" pushedtextcolor="#ffff7357" font="19" textpadding="27,5,0,0" text="停止"/>
            <Button name="rightmenu_btn" visible="true" width="9" height="18" padding="3,4,0,0" normalimage="rightmenu_btn_normal.png" hotimage="rightmenu_btn_hover.png" pushedimage="rightmenu_btn_hover.png" align="left"/>
          </HorizontalLayout>


          <HorizontalLayout height="6">
          </HorizontalLayout>

          <HorizontalLayout inset="0,0,0,0" height="26" width="156">

            <!--VerticalLayout width="150"-->
              <Label name="description" padding="0,-13,0,0" align="right" textpadding="0,0,17,0"  text="" textcolor="#FF8BE9FD"/>
            <!--/VerticalLayout-->
 					  <Button name="btnRetryUp" padding="0,-2,0,0" align="left" width="60" height="26" textpadding="0,0,0,2" text="重试" textcolor="#FF8BE9FD" visible="false"/>
      		  <Button name="closeDes" visible="false" padding="0,-6,0,0" width="28" height="26"  tooltip="清除" normalimage="closebtn.png" hotimage="closebtn_hover.png" pushedimage="closebtnpush.png" />

          </HorizontalLayout>

        </VerticalLayout>

			</HorizontalLayout>

      <!--<HorizontalLayout inset="1,0,0,0" height="1">
        <Control height="1" bkcolor="#ffdbdee3"/>
      </HorizontalLayout>-->
      <HorizontalLayout name="ProgressBarArea" visible="false" height="2">
        <Progress name="ProgressBar" height="2" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" forecolor="#FF3488ec" min="0" max="100" value="0" hor="true" />
      </HorizontalLayout>

    </VerticalLayout>
	</ListContainerElement>
</Window>