标题: 浏览器指纹：时区
英文标题: Browser Fingerprint：TimeZone
ID: 58
分类ID: 25
添加时间: 1612515967
更新时间: 1685607088
访问次数: 0
SEO标题: 浏览器指纹：时区
SEO关键词: 浏览器指纹：时区
SEO描述: 浏览器指纹：时区

================================================== 内容 ==================================================
网站可通过两种方式获取您的时区。一种是在Ip数据库中匹配到你的IP地址。第二种则是使用JavaScript函数，通过浏览器的API从操作系统的区域设置中获取时区。网站会核对两种方式获取的结果是否一致。如不一致，它会认为您可能使用了位于另一时区的代理服务器。

因此，确保JavaScript获取的系统时区和代理所在地时区相一致，这对与您的隐私保护来说是至关重要的。在候鸟浏览器中，可以在新建环境配置中根据代理IP自动匹配时区。

### 自动选择时区
在运行浏览器配置文件之前，候鸟浏览器首先会通过您在新建环境配置设置的链接连到候鸟浏览器自身的服务器。连网成功后，后端就能看到您真实的外部IP地址，即其他网站也能读出的地址。

接下来，候鸟浏览器会为该IP地址从不断更新的Ip数据库检索与之相对应的时区，并在浏览器配置文件中设置好。一旦该浏览器配置文件被启动，这个与IP相匹配的时区随即被应用。

![候鸟浏览器时区设置](https://admin.mbbrowser.com/static/upload/images/article/2021/02/05/1612515398709235.png "候鸟浏览器时区设置")

### 手动选择时区
您也可以手动设置时区。您就可以访问一个手动配置下拉列表。您可以从这个标准化Unix时区值列表中选择您需要的时区。

![候鸟浏览器手动调整时区](https://admin.mbbrowser.com/static/upload/images/article/2021/02/05/1612516157393165.png "候鸟浏览器手动调整时区")

================================================== 英文内容 ==================================================
The website can get your time zone in two ways. One is to match your Ip address to the IP database. The second is to use JavaScript functions to get the time zone from the operating system's locale Settings through the browser's API. The site checks whether the two methods are the same. If not, it assumes that you may be using a proxy server located in another locale.

Therefore, it is important for your privacy to ensure that the system time zone obtained by JavaScript is consistent with the time zone where the agent is located. In the Migrating Bird browser, you can automatically match the time zone based on the proxy IP address in the new environment configuration.

### Automatic TimeZone Selection
Before running the browser profile, Migratory Bird first connects to Migratory Bird's own server through the link you set up in the new environment configuration. Once connected, the back end can see your real external IP address, which other websites can read as well.

The Migrating Bird browser then retrieves the corresponding time zone for that IP address from the constantly updated Ip database and sets it in the browser profile. Once the browser profile is launched, the time zone matching the IP is applied.

![](6d7b2882624511f09a0d0242ac130006/images/image_d9d38c7dc532.png)

### Manual TimeZone Selection
You can also manually set the time zone. You can access a manual configuration drop-down list. You can select the time zone you want from this list of normalized Unix time zone values.

![](6d7b2882624511f09a0d0242ac130006/images/image_01b82c2c69fd.png)