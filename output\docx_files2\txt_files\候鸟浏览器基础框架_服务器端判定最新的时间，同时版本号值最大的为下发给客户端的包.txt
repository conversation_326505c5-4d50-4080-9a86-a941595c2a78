服务器端判定最新的时间，同时版本号值最大的为下发给客户端的包。

服务器端判定最新的时间，同时版本号值最大的为下发给客户端的包。

建议，不select表，直接从文件夹进行判定。这样效率最高。

上传下发日志表和LOG文件：

上传写表： upload_log表 记录 用户上传的token,时间,包名,位置和类型。同时写upload_log日志。

下发写表： export_log表 记录 用户下载的token,时间，包名,位置和类型。同时写export_log日志。

上传数据串版本校验：

约定：PC端每次请求服务器端数据上传之前，先行请求服务器返回待下载包的版本号并进行对比。

A、对于本地版本号小于或等于服务器版本号情况下：PC端在内存中主动将本地数据包版本号在服务器返回版本号上+1，并上传最新版本包到服务器。

B、对于本地版本号大于服务器版本号情况下：PC端上传本地最新版本包到服务器，不加1。

（一）版本请求：

PC端：

服务器端：

Json格式：

{"hash":"xxxxxxxx","version":"xx"}

【2020-08-11第五版】实际接口：问题：只针对了full使用场景，未针对子zip使用场景。（【8-16号】此问题已在第八版后续描述中解决）

如果该用户没有上传过，同样返回成功，version为0。只需检测version即可判断是否需要上传或下载

接口地址：

/api/mbbrowser/datainfo

参数：token

返回信息：其中的version为最新版本号

{"msg":"操作成功","code":0,"data":{"version":11}}

（二）上传请求：

约定：PC端每次请求服务器端数据上传之前，先行版本请求服务器，获知服务器中已存储的最新版本号并进行对比。

对于本地客户端内存中版本号大于服务器版本号情况下：PC端主动上传最新版本包到服务器。（通常此情况不存在，仅且仅有一种可能即服务器端数据丢失。）

B、对于本地版本号小于或等于服务器版本号情况下：PC端自动将本地数据包版本在服务器版本号基础上+1并上传到服务器。

数据上传：

采用post表单方式上传文件。文件超过5M分片上传，每次上传一个分片，可支持多线程同时上传多个分片（需测试是否影响性能）。每个分片上传完都会检测是否所有分片都上传完成（可不按顺序上传），全部上传完成，服务器自行合并，并校验md5(hash码)。

上传提交数据使用：

两个字段：
file：文件。

String：是其他参数AES加码后的字符串

例：token=12ee986e60878ef20942c24c53fd033e&chunkid=0&chunknum=1&version=1&hash=5218c8b585264ff3d9ba5590fd592315&datatype=full

Token： 登录后返回的token

Chunkid： 分片id，从0开始

Chunknum： 分片数量，如3（此时chunkid最大为2），为1则表示不分片

Version： 数据包版本

Hash： 完整数据包的md5值

Datatype： 数据包类型

=============================================

同步前分片校验与同步过程跳过同HASH片数据之优化逻辑：

（以下逻辑前期暂时不做，后期根据压力测试，对于巨型FULL包【大于20M】增加此逻辑）

【2020-8-16】

约定增加部份：

Chunkid分片ID 增加 分片HASH

分片HASH列表数据结构如下：

token=12ee986e60878ef20942c24c53fd033e&chunkid=0&chunknum=1&version=1&hash=5218c8b585264ff3d9ba5590fd592315&datatype=full&Chunkhash=1|xxxxxxxxxxx;2|xxxxxxxxxxxx;3|xxxxxxxxxx

对于FULL包大于20M的

服务器保存分片原始数据和full包数据。对于分片相同的包，不上传。

客户端根据分片原始数据和本地数据进行HASH对比，对于分片相同的包，不下载。以此减少服务器传输压力。

=============================================

全量full上传数据包格式说明（附加说明）：

full包（zip包）： FULL包根据PC端本地存储分类压缩有多个zip小包的集合。

具体为：

Full包完整文件名

例：   1_EISDJHF234DKSKE_FULL.ZIP

2_DAFADKADLKFAD_FULL.ZIP

服务器解压缩full包后：

GOLBAL.ZIP （各XML数据包集合）

1_ SESSION_UNIQUE_ID（ITEM1）.zip

C:\Users\<USER>\AppData\Roaming\MBbrowser\LocalStorage\sxEwjM8XllTeWJqEBHvWrQ==\MainData\ChromeData下各SESSION_UNIQUE_ID文件夹 数据，每个SESSION_UNIQUE_ID文件夹数据对应一套完整ITEM。

1_ SESSION_UNIQUE_ID（ITEM2）.zip

1_ SESSION_UNIQUE_ID（ITEM3）.zip

……

……

即：服务器解压缩full包后不仅要单独保存full包，还要分别保存zip小包。这些zip小包由PC端已登录用户的各ITEM集合组成。

定义：要求full包，包含所有zip小包，即完整包。 Zip小包为同版本下的PC端已登录用户的各ITEM集合组成。 Full包可替代zip小包，但zip小包不能顶替full包。

Full包和各小包分别存储。存储位置示例：

/mnt/data/username(用户登录成功的名称，加码的)/storage/ 1_EISDJHF234DKSKE_FULL.ZIP

/mnt/data/username(用户登录成功的名称，加码的)/storage /1_SESSION_UNIQUE_ID1.ZIP

/mnt/data/username(用户登录成功的名称，加码的)/storage /1_SESSION_UNIQUE_ID2.ZIP

上传分片数据临时存放位置示例：

/mnt/data/username(用户登录成功的名称，加码的)/uploadTemp/1_EISDJHF234DKSKE_full_1.zip

/mnt/data/username(用户登录成功的名称，加码的)/uploadTemp/1_EISDJHF234DKSKE_full_2.zip

…

上传完成，服务器端获取到上传完成的状态后，进行合并，成功合并完整数据包 并 同时校验数据hash合法正确无误后，应实时清理对应目录下的分片数据。

用户历史数据清理说明：

1、当前已存储数据包不低于最近7天的，超出的清理掉

详述：

保留最近7天的包

比如： 2019年1月份有7天的包是这个用户最近的包

那么2019年1月份那7天就是最近7天的包

这些包要保留下来。

然后2020年9月份用户又用了1天，那么就是清理掉 2019年1月份的最旧的那一天的包，保留下来的是 2019年1月份6天的包和2020年9月份那1天的包

以此类推

以天为单位。

A、此逻辑支持后面业务有新需求：可以改成15天或保留更久一点

B、清理数据是异步流程，不实时进行。

C、要有 create_time 和update_time 和is_valid 基础3个字段。

(6) 定时备份机制：

/mnt/data/username(用户登录成功的名称，加码的)/业务类型/用户zip

/mnt/data下所有数据要每24小时完整覆盖式备份到另一台机器（可临时备份到另一个文件夹。）

服务器端下发（PC端下载）流程详述：

约定：PC端每次请求服务器端数据下载之前，先行请求服务器返回待下载包的版本号并进行对比。

A、对于本地版本号大于服务器版本号情况下：PC端主动上传最新版本包到服务器。

B、对于本地版本号小于服务器版本号情况下：PC端主动下载最新版本包到服务器。

（一）版本请求：

服务器端：

Json格式：

{"hash":"xxxxxxxx","version":"xx"}

（二）下载请求：

当PC端请求过来，请求串格式：

? aes(token=xxxxxxx&type=full&act=download&msg=request)

服务器端返回动态url

服务器进行AES解码，判定token是否合法。

如合法：

http://domain/download?aes(filename=1_xxxxxxxx_full.zip&size=xxxxxx&version=xx)

3、 PC端收到串进行解码串，数据校验（文件名，版本，大小），并在PC端显示进度条。

4、PC端 进行下载

5、PC端下载成功。

下载失败：进行三次重试，如下载仍旧失败，进入第二种下载机制进行。

成功返回给服务器串：

http://domain/?aes(token=xxxx&act=download&msg=success&time=当前时间)

6、服务器收到串

进行解码，解码成功如果act=download&msg=success则将此URL失效处理。

7、下载超时说明：如果1小时内未收到act=download&msg=success强制将此下载url失效处理。


================================================== 表格内容 ==================================================

参数名称 | 数据类型 | 说明 | 摘要
VERSION | varchar | Xml 版本号 | (此XML数据格式版本号)
ID | Number | 会话序号 | (会话列表显示/管理顺序)
LOGIN_ACCOUNT | varchar | 用户登录名 | 用户登录帐户(购买帐户)
SESSION_UNIQUE_ID | Number | 会话ID | 会话唯一不重复标识ID
SESSION NAME | Number | 会话名称 | 会话名称
SITE NAME | varchar | 网站域名 | 不带前缀www，只显示根域名
LOGIN NAME | varchar | 网站登录用户名
PASSWORD STRING | varchar | 网站登录密码
IS_VALUED | bit | 是否有效 | 预留项
UPDATETIME | DateTime | 2021-01-12 12:22:34 | 记录更新时间
UPDATETIME | DateTime | 2021-01-12 12:22:34 | 记录创建时间

<?xml version="1.0" encoding="utf-8" ?>
<VERSION version="11">
<VER ID="1" LOGIN_ACCOUNT="" SESSION_UNIQUE_ID="">
	<SESSION NAME="<EMAIL>"/>
	<SITE NAME = "21cn.com"/>
	<LOGIN NAME="sztengli"/>
	<PASSWORD NAME="sztengli"/>
	<IS_VALID VALUE="1" />
	<UPDATETIME VALUE ="2021-01-12 12:22:34"/>
	<CREATETIME VALUE ="2021-01-12 12:22:34"/>
</VER>
</VERSION>