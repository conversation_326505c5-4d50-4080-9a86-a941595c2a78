第四十三章
候鸟服务器线路切换-客户端/服务器端完整流程

第四十三章
候鸟服务器线路切换-客户端/服务器端完整流程

依据传统，具体工作步骤、顺序安排，人员分配在工作群里具体阐述。

前述：

由于客户群体属于跨境电商，对于VPN翻墙是刚性需求，过半的客户群体均使用VPN、V2RAY等工具进行局部、全局翻墙，使自已的本地线路进入国际互联网。

对于国内无法访问国外网站的客观事实基础下，国外的用户也无法访问国内的网站，这是由中国的国家防火墙决定的，属于不可抗因素。因此用户在VPN后，无法使用候鸟原网络节点（国内节点）进行客户端登录变成了一种对于小白客户群非常困难的技术门槛。（大部份小白用户不懂得修改HOSTS来连入候鸟海外服务器，也不知道海外服务器的IP地址）

约定：
服务器端 基础线路XML文件，文件名约定： serverlist.xml

文件格式约定：utf-8

流程约定：
    通过服务器端在SHOPXO里提供完整的针对SERVERLIST.XML的一套生成管理后台，内嵌入到SHOPXO的后台中，维护人员通过添加删除候鸟服务器节点，并实时生成SERVERLIST.XML 供客户端更新到本地。

（见图43.1）

一、客户端通过将本地Serverlist.xml中的version节点值提交给服务器，通过服务器的返回来判断是否要下载Serverlist.xml并更新本地。

<VERSION version="101" xmlversion="17"><VER>

43.1.1 客户端与服务器端通过请求串，并根据服务器端的请求返回，作为下载到本地依据，根据下载回来的XML中的version节点值大于本地值，作为更新本地文件并重新加载此文件到内存之依据。

    即 当本地Serverlist.xml的version值小于服务器端，客户端即触发下载Serverlist.xml并更新到本地。

线路 ID 为 客户端识别线路的唯一标志位。

43.2.1 客户端通过在base.dat中写入明文串：ServerIndex = 2 来记录用户选定的线路，此 “2” 值，应为线路ID值，即服务器端在数据库中的ID字段的对应ID值。

43.2.2 服务器端在生成XML时，此ID值必须使用数据库中表记录的ID字段值。

服务器端生成serverlist.xml的IP填入说明：

   43.3.1 服务器端 在serverlist.xml中填入的IP为官方在域名解析时填入的IP，此IP由服务器端在管理后台中通过XML中已给定的域名进行自动解析，并显示在编辑页面中，维护人员可在后台编辑页面中人工修改此IP。

43.3.2 维护人员，在SHOPXO后台，点击并更新serverlist.xml时，服务器端根据当前表单，将各个节点的IP值，一并写入到serverlist.xml各节点的IP参数中。

服务器端生成的 SERVERLIST.XML 全文格式如下：

注意，商用环境下，此XML实际生成的是密文。

<?xml version="1.0" encoding="utf-8"?>

<VERSION version="373" xmlversion="17">

<VER>

<NAME VALUE="全球网络主线路节点(默认)"/>

<MAINPAGEURL VALUE="http://www.yalala.com/?wd=" IP="*************"/>

<MAINURL VALUE="http://admin.mbbrowser.com" IP=""/>

<MAINURLHEADLESS VALUE="http://api.mbbrowser.com" IP=""/>

<MAINBAKAUPURL VALUE="http://admin-hzbk.mbbrowser.com" IP=""/>

<MAINBAKURL VALUE="http://v2.admin.mbbrowser.com" IP=""/>

<MAINURLWS VALUE="ws://admin.mbbrowser.com:3234" IP=""/>

<MAINURLHEADLESSWS VALUE="ws://api.mbbrowser.com:3234" IP=""/>

</VER>

<VER>

<NAME VALUE="华东区BGP电信骨干网节点"/>

<MAINPAGEURL VALUE="http://www.yalala.com/?wd=" IP=""/>

<MAINURL VALUE="http://hangzhou.ehouniao.com" IP="**************"/>

<MAINURLHEADLESS VALUE="http://hangzhou.api.ehouniao.com" IP="**************"/>

<MAINBAKAUPURL VALUE="http://admin-hzbk.mbbrowser.com" IP=""/>

<MAINBAKURL VALUE="http://v2.admin.mbbrowser.com" IP=""/>

<MAINURLWS VALUE="ws://hangzhou.ehouniao.com:3234" IP="**************"/>

<MAINURLHEADLESSWS VALUE="ws://hangzhou.api.ehouniao.com:3234" IP="**************"/>

</VER>

</VERSION>

43.4.1、绿色部份为默认节点，为整个XML文件的核心节点，无论用户是否重装软件，还是首次使用，或者是从未进入到此线路窗口时，客户端默认使用的节点，一定是绿色部份的默认节点（基础约定）（此时base.dat里一定无 ServerIndex 串，或者无base.dat文件）。

43.4.2、服务器端发送过来的serverlist.xml中，默认节点的各子节点参数中IP值一定为空，这是因为服务器端的admin.mbbrowser.com的默认节点本身就是多个IP共用同一个域名，多台反代IP同时指向一个域名的客观结果。所以，默认节点无IP表示：客户端使用的admin.mbbrowser.com中的某一台反代服务器由国内、国际DNS服务器来决定。

43.4.3、关于 serverlist.xml中的IP值的读取，写入和使用约束。

       无论是默认节点（绿色），还是其它节点（黄色），XML中的IP值(来自服务器端写入的ip 值)，均不默认作为客户端的PING 数据用途。客户端的PING方式，应严格用于PING 节点中的域名，而非IP，同时在PING域名的过程中，会得到这个域名对应的IP，如未得到IP（ping域名失败[用户本地未设置DNS，DNS失效，DNS污染等因素]），此时，可以将XML中的IP作为基准值。

      如在PING流程中得到了域名的IP，则将得到的IP写入（更新）到XML中，再将IP作为基准值（在HOSTS用途中详细描述）。

五、客户端请求服务器，下载，更新、加载serverlist.xml、弹线路窗体，之流程详细阐述：

43.5.1 候鸟客户端在用户进行首次登录时，在本地存在serverlist.xml时，会自动使用默认节点进行连接候鸟服务器，如果在连接候鸟服务器失败时，会显示以下信息 (因断网的多因素性，因此不仅限于服务器端此信息)：

43.5.2 客户端在判断到服务器已返回登录失败时，应自动第一时间弹出线路窗口，供用户选择/切换速率最快的线路。

43.5.3 注：如果用户之前已经有设置过指定线路 (base.dat中有值)，则此处要以base.dat中的值为准，如之前已勾选了HOSTS，则仍旧要默认勾选HOSTS（hosts中存在对应串，则默认必勾选）。

43.5.4 请求服务器获取最新版本serverlist.xml 超时最大时间设定：3秒

说明：对于用户的耐心而言，3秒已经是极限值，因此无论是不是服务器在3秒后是否有返回，均应认定服务器超时无返回，此时应直接调用本地serverlist.xml并进行显示。

43.5.5 关于HOSTS 与 “连接” 按钮的关系和逻辑流程详细说明：

         ********
         HOSTS用途说明：

由于用户的DNS解析和DNS失败的因素影响，同时用户的线路已进入国际无法使用默认节点（绿色部份）时，HOSTS在此时起到决定用户最终是否能连入候鸟服务器的关键之处。

通过写入HOSTS文件，候鸟客户端在本质上此时已经绕过了
        国际DNS服务器，并能保证客户端顺利连上候鸟服务器。

********

关于HOSTS 与 “连接” 按钮的关系和逻辑流程详细说明

根据43.3.1和43.3.2的章节：

客户端已明确得知serverlist.xml中的IP使用具体方法，即在PING的流程中，将实际的IP内容更新到本地serverlist.xml中（不包含默认节点），来确保各线路对应的xml中各节点的ip值存在于本地serverlist.xml中。

约定：默认节点的ID，客户端可以用当前表中(服务器返回的serverlist.xml)已设定的默认节点ID作为最终ID值，以后此ID不允许变化，也不允许变更，即终生不变。

      接上图：

      ********.1
          对于默认节点，不允许将IP值写入到此线路中各节点值中。

      ********.2
          对于默认节点，HOSTS的CHECKBOX应置灰，不允许用户勾选。
                (以此来保证用户随时可以回到初始默认线路状态下)





      ********.3
       对于用户在勾选HOSTS时，客户端须判断当前已联接节点，是否为默  
       认节点（绿色部份），如果不是默认节点，则表示在其它线路节点上
       已有联接，因此，客户端可在用户仅勾选HOSTS CHECKBOX动作后 
      （未点击连接），进行HOSTS写入。

********.4

对于用户点击连接按钮（点击连接按钮已成功操作完成），则无论是否有勾选HOSTS，都必须自动将HOSTS的CHECKBOX由勾选状态变更为未勾选状态，同时要清空原HOSTS记录的一切客户端原写入的数据。

[20240808]

第四十四章
候鸟服务器API-IP提取-客户端/服务器端完整流程

依据传统，具体工作步骤、顺序安排，人员分配在工作群里具体阐述。

44.1 前述：

    对于已使用同行产品，过来使用候鸟产品的熟练客户，通常有30%的机率会向候鸟官方询问如何通过候鸟客户端一键提取各IP平台商的IP接口中的IP数据。
    优点如下：

1、无需访问IP平台商的官网，直接通过API-IP-URL 进行商用代理IP数据的提取。
    2、可以通过候鸟客户端来将提取的代理IP自动置入候鸟代理IP库，并方便的管理自已的IP资源。

44.2同行软件界面参考：

44.3 候鸟客户端代理服务器XML代码示例：

<?xml version="1.0" encoding="utf-8"?>

<VERSION version="363" xmlversion="7">

<VER>

<PROXY_INDEX VALUE="0"/>

<PROXY_ID VALUE="3"/>

<NAME VALUE="No Proxy"/>

<PROXY_LOGO_ID VALUE=""/>

<PROXYTYPE VALUE="0"/>

<DATETIME VALUE="2024-07-25 16:41:11"/>

</VER>

<VER>

<PROXY_INDEX VALUE="1"/>

<PROXY_ID VALUE="4"/>

<NAME VALUE="SOCKS5"/>

<PROXY_LOGO_ID VALUE="ipfly.png"/>

<PROXYTYPE VALUE="1"/>

<DATETIME VALUE="2024-07-26 10:06:58"/>

</VER>

<VER>

<PROXY_INDEX VALUE="9"/>

<PROXY_ID VALUE="12"/>

<NAME VALUE="luminati_HTTPS"/>

<PROXY_LOGO_ID VALUE=""/>

<PROXYTYPE VALUE="9"/>

<DATETIME VALUE="2024-07-26 00:00:00"/>

</VER>

</VERSION>

44.4 候鸟客户端集成一键提取IP平台商之代理IP界面说明：

功能区在右上角：

第一个下拉列表：列出所有合作方的代理名称。

第二个下拉列表：列出支持的代理协议

一键提取IP平台商代理IP：点击后弹窗显示已提取到的IP数据列表，并进行列出。见后图。

CONFIG按钮：点击后允许客户设置/填入提取代理IP的接口URL。见后图


================================================== 表格内容 ==================================================

{
"message "Golbal Installed Plugin List Success",
"code": 0,
"data": {
"listcontainer": [
        {
"Plugins_Count": "4",
"Plugin_list": 
                    [{
                     "Plugin_Name" : "AAA",
                     "Plugin_Id" : " jjbnhpnlakcdgfnnldamfeinfmahhdlm"，
 "Plugin_Ver" : "*******"
                         },
{
                     "Plugin_Name" : "BBB",
                     "Plugin_Id" : " jjbnhpnlakcdgfnnldamfeinfmahhdlm "，
 "Plugin_Ver" : "*******"
                         }],
"status": 0
}

}
}

参数名称 | 类型 | 必传 | 样例串/默认值 | 说明
Plugin_ID | array | 是 | ncennffkjdiamlpmcbajkmaiiiddgioo | 指定安装插件ID（支持多个）
Session_ID | string | 是 | 373808cb37bd63f5f7d92415e736e85f | 指定环境ID
Plugin_ver | string | 否 | 1.0.0 | 插件版本号，可选