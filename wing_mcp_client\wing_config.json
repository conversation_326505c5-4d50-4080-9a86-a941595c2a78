{"mcp_servers": {"ragflow": {"name": "RAGFlow Knowledge Base", "description": "RAGFlow MCP Server for enterprise document retrieval", "server_url": "http://************:9382", "api_key": "ragflow-UzYTlhMjIwNjI0YzExZjA4NzUyMDI0Mm", "timeout": 30, "retry_count": 3, "enabled": true, "endpoints": {"sse": "/sse", "messages": "/messages/", "health": "/health"}, "capabilities": {"tools": true, "resources": false, "prompts": false}, "tools": [{"name": "ragflow_retrieval", "description": "Search and retrieve information from RAGFlow knowledge base", "parameters": {"question": {"type": "string", "description": "The question to search for", "required": true}, "dataset_ids": {"type": "array", "description": "Optional list of dataset IDs to search in", "required": false}, "document_ids": {"type": "array", "description": "Optional list of document IDs to search in", "required": false}}}]}}, "client_config": {"name": "Wing AI IDE", "version": "1.0.0", "protocol_version": "2024-11-05", "user_agent": "Wing-MCP-Client/1.0", "default_timeout": 30, "max_retries": 3, "connection_pool_size": 5}, "logging": {"level": "INFO", "file": "wing_mcp.log", "max_size": "10MB", "backup_count": 5}, "security": {"verify_ssl": true, "allowed_hosts": ["************", "localhost", "127.0.0.1"]}}