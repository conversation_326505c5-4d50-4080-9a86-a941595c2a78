﻿<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Window size="340,500" caption="0,0,0,80" roundcorner="5,5,5,5" showshadow="true" shadowsize="3" shadowposition="0,0" shadowcolor="#ff6272A4" fademode="true">
	<Include source="Default.xml" />

  <VerticalLayout bkcolor="#FF282A36">

    <HorizontalLayout height="37">

      <Control bkimage="icon.png" padding="8,6,0,0" width="28" height="28"/>
      <Label name="title" padding="6,4,0,0" autocalcwidth="true" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" align="left" font="8"></Label>
      <Button name="recheck" visible="false" padding="10,10,0,0" height="24" width="80" text="重新检测" textpadding="10,0,10,0" texttooltip="true" endellipsis="true" borderround="1,1" textcolor="#FFF8F8F2" hottextcolor="#FFFFFFFF" bkcolor="#FF6272A4" hotbkcolor="#FF8BE9FD"	/>

      <Control />
      <!--<Button name="minbtn" width="28" height="26" tooltip="最小化" normalimage="minbtn.png" hotimage="minbtn_hover.png" pushedimage="minbtnpush.png" />-->
      <Button name="closebtn2" width="28" height="26"  tooltip="关闭" normalimage="closebtn.png" hotimage="closebtn_hover.png" pushedimage="closebtnpush.png" />

    </HorizontalLayout>

    <HorizontalLayout height="10">

    </HorizontalLayout>

    <HorizontalLayout padding="10,0,10,0" height="2">
      <Control height="2" bkcolor="#FF6272A4"/>
    </HorizontalLayout>

  <HorizontalLayout name="bkground" visible="true">

    <VerticalLayout inset="10,0,10,10" >
      <RichEdit name="Info" readonly="true" vscrollbar="true" disabledtextcolor="#FF6272A4" text="" padding="0,10,0,2" tipvaluecolor="#FFF8F8F2" borderround="7,7" bkcolor="#FF44475A" font="17" textpadding="10,10,4,0" wantreturn="true" rich="true" transparent="false" textcolor="#FFF8F8F2">
      </RichEdit>
    </VerticalLayout>

	</HorizontalLayout>

    <!--<HorizontalLayout height="52" bkcolor="#ffe9e9e9">
    </HorizontalLayout>-->

    <HorizontalLayout padding="10,0,10,0" height="2">
      <Control height="2" bkcolor="#FF6272A4"/>
    </HorizontalLayout>

    <VerticalLayout height="80">
      <Control />
      <HorizontalLayout height="50">
        <Control />
      <Button name="proxy_tip" padding="0,0,0,0" height="50" width="200" text="配置多级代理教程" textpadding="10,0,10,0" texttooltip="true" endellipsis="true" font="11" borderround="7,7" textcolor="#FFF8F8F2" hottextcolor="#FFFFFFFF" bkcolor="#FF6272A4" hotbkcolor="#FF8BE9FD"	/>
        <Control />
      </HorizontalLayout>
      <Control />
    </VerticalLayout>

  </VerticalLayout>

</Window>
