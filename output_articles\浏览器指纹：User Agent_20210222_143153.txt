标题: 浏览器指纹：User Agent
英文标题: Browser Fingerprint：User Agent
ID: 85
分类ID: 25
添加时间: 1613975513
更新时间: 1685607123
访问次数: 0
SEO标题: 浏览器指纹：User Agent
SEO关键词: 浏览器指纹：用户代理（User Agent）
SEO描述: 浏览器指纹：用户代理（User Agent）

================================================== 内容 ==================================================
用户代理（User Agent）是一种浏览器的原生短字符串。通过读取用户代理字符串，网站可以识别您的浏览器及操作系统的版本。

下面是一个用户代理值的示例

Mozilla/5.0 (Windows NT 6.3; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/62.0.2785.8Safari/537.36


在这个例子中，网站将推测用户使用的是Windows 8.1和Chrome 62。“NT 6.3”是Windows发行的不同版本。您可以在Wikipedia article维基百科中查看其他已发行的版本。

创建浏览器配置文件时，遵循您在概览页面的关于操作系统过滤器的选择，用户代理值从候鸟浏览器的指纹数据库被获取。您可以在导航栏页面查看浏览器配置文件的用户代理值。

================================================== 英文内容 ==================================================
The User Agent is a browser's native short string. By reading the user agent string, the website can identify the version of your browser and operating system.

Here is an example of a user agent value

Mozilla/5.0 (Windows NT 6.3; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/62.0.2785.8Safari/537.36


In this example, the site will assume that the user is using Windows 8.1 and Chrome 62. "NT 6.3" is a different release of Windows. You can view other released versions at Wikipedia article.

When creating the browser profile, following your selection of operating system filters on the overview page, the user agent values are retrieved from the Migratory Bird Browser fingerprint database. You can view the user agent value of the browser configuration file on the navigation bar.