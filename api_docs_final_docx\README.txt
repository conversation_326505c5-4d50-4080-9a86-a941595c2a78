候鸟浏览器API文档 - 最终版本
==================================================

总文件数: 49 个

文档分类:
1. 环境管理 (16个子功能) - 核心API功能
2. 分组管理 (4个子功能)
3. 插件管理 (4个子功能)
4. 脚本管理 (5个子功能)
5. 环境开启关闭 (3个子功能)
6. HTTP模式说明 (3个子功能)
7. 使用须知 (3个子功能)
8. 常见问题 (3个子功能)
9. POSTMAN调试 (2个子功能)
10. JSON数据处理 (2个子功能)
11. 其他基础功能 (4个)

特点:
- 已去除重复和导航内容
- 每个文件对应一个具体的API功能
- 文件名格式: API_功能模块_序号_具体功能名
- 内容已清理，适合直接使用

使用说明:
这些文档可以直接用于:
- RAGFlow知识库构建
- API开发参考
- 技术文档整理
- 自动化脚本开发
