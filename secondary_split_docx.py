#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对过大的DOCX文件进行二次拆分
确保RAGFlow能够成功解析
"""

import os
import re
from pathlib import Path
from docx import Document

def analyze_file_sizes(directory):
    """分析目录中DOCX文件的大小"""
    docx_path = Path(directory)
    docx_files = list(docx_path.glob("*.docx"))
    
    print(f"📁 分析目录: {directory}")
    print(f"📊 找到 {len(docx_files)} 个DOCX文件")
    print("=" * 80)
    
    file_info = []
    total_size = 0
    
    for file_path in sorted(docx_files):
        try:
            file_size = file_path.stat().st_size / 1024  # KB
            total_size += file_size
            
            # 加载文档获取段落数
            doc = Document(str(file_path))
            para_count = len(doc.paragraphs)
            table_count = len(doc.tables)
            
            file_info.append({
                'path': file_path,
                'name': file_path.name,
                'size_kb': file_size,
                'paragraphs': para_count,
                'tables': table_count,
                'needs_split': file_size > 100  # 大于100KB需要拆分
            })
            
            status = "🔴 需要拆分" if file_size > 100 else "✅ 大小合适"
            print(f"{status} {file_path.name}")
            print(f"   大小: {file_size:.1f} KB, 段落: {para_count}, 表格: {table_count}")
            print()
            
        except Exception as e:
            print(f"❌ 分析失败: {file_path.name} - {str(e)}")
            print()
    
    print("=" * 80)
    print(f"📊 统计信息:")
    print(f"   总文件数: {len(file_info)}")
    print(f"   总大小: {total_size:.1f} KB")
    print(f"   平均大小: {total_size/len(file_info):.1f} KB")
    
    large_files = [f for f in file_info if f['needs_split']]
    print(f"   需要拆分: {len(large_files)} 个文件")
    print(f"   合适大小: {len(file_info) - len(large_files)} 个文件")
    
    return file_info

def extract_base_name(filename):
    """提取文件的基础名称（去掉.docx后缀）"""
    return filename.replace('.docx', '')

def split_large_docx(file_info, output_dir, max_size_kb=80):
    """拆分过大的DOCX文件"""
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    split_files = []
    
    for info in file_info:
        if not info['needs_split']:
            # 文件大小合适，直接复制
            base_name = extract_base_name(info['name'])
            new_filename = f"{base_name}.docx"
            new_filepath = output_path / new_filename
            
            # 复制文件
            try:
                doc = Document(str(info['path']))
                doc.save(str(new_filepath))
                split_files.append(str(new_filepath))
                
                print(f"✅ 保持原样: {new_filename} ({info['size_kb']:.1f} KB)")
            except Exception as e:
                print(f"❌ 复制失败: {info['name']} - {str(e)}")
            
            continue
        
        # 需要拆分的大文件
        print(f"🔄 拆分大文件: {info['name']} ({info['size_kb']:.1f} KB)")
        
        try:
            doc = Document(str(info['path']))
            
            # 计算需要拆分的部分数
            target_paragraphs_per_part = int(info['paragraphs'] * max_size_kb / info['size_kb'])
            num_parts = max(2, (info['paragraphs'] + target_paragraphs_per_part - 1) // target_paragraphs_per_part)
            
            print(f"   计划拆分为 {num_parts} 个部分，每部分约 {target_paragraphs_per_part} 个段落")
            
            paragraphs_per_part = info['paragraphs'] // num_parts
            tables_per_part = info['tables'] // num_parts
            
            base_name = extract_base_name(info['name'])
            
            for part_num in range(num_parts):
                # 计算段落范围
                start_para = part_num * paragraphs_per_part
                if part_num == num_parts - 1:
                    end_para = info['paragraphs']  # 最后一部分包含剩余段落
                else:
                    end_para = (part_num + 1) * paragraphs_per_part
                
                # 创建新文档
                new_doc = Document()
                
                # 添加标题
                if num_parts > 1:
                    title = f"{base_name}({part_num + 1})"
                else:
                    title = base_name
                
                new_doc.add_heading(title, 0)
                
                # 复制段落
                copied_paras = 0
                for para_idx in range(start_para, min(end_para, len(doc.paragraphs))):
                    try:
                        source_para = doc.paragraphs[para_idx]
                        new_para = new_doc.add_paragraph()
                        
                        # 复制段落样式
                        try:
                            new_para.style = source_para.style
                            new_para.alignment = source_para.alignment
                        except:
                            pass
                        
                        # 复制段落内容
                        for run in source_para.runs:
                            new_run = new_para.add_run(run.text)
                            new_run.bold = run.bold
                            new_run.italic = run.italic
                            new_run.underline = run.underline
                        
                        copied_paras += 1
                        
                    except Exception as e:
                        # 备选方案：只复制文本
                        new_doc.add_paragraph(doc.paragraphs[para_idx].text)
                        copied_paras += 1
                
                # 分配表格（只在第一部分添加表格）
                if part_num == 0 and info['tables'] > 0:
                    for table_idx in range(min(tables_per_part, len(doc.tables))):
                        try:
                            source_table = doc.tables[table_idx]
                            rows = len(source_table.rows)
                            cols = len(source_table.columns) if source_table.rows else 0
                            
                            if rows > 0 and cols > 0:
                                new_table = new_doc.add_table(rows=rows, cols=cols)
                                new_table.style = 'Table Grid'
                                
                                for r, row in enumerate(source_table.rows):
                                    for c, cell in enumerate(row.cells):
                                        if r < len(new_table.rows) and c < len(new_table.rows[r].cells):
                                            new_table.cell(r, c).text = cell.text
                        except Exception as e:
                            print(f"   ⚠️  表格复制失败: {str(e)}")
                
                # 保存文件
                if num_parts > 1:
                    new_filename = f"{base_name}({part_num + 1}).docx"
                else:
                    new_filename = f"{base_name}.docx"
                
                new_filepath = output_path / new_filename
                new_doc.save(str(new_filepath))
                split_files.append(str(new_filepath))
                
                # 检查生成文件的大小
                new_size = new_filepath.stat().st_size / 1024
                print(f"   ✅ 生成: {new_filename} ({new_size:.1f} KB, {copied_paras}段落)")
        
        except Exception as e:
            print(f"   ❌ 拆分失败: {str(e)}")
    
    return split_files

def clean_old_files(directory):
    """清理原始文件"""
    docx_path = Path(directory)
    docx_files = list(docx_path.glob("*.docx"))
    
    print(f"🗑️  清理原始文件...")
    for file_path in docx_files:
        try:
            file_path.unlink()
        except Exception as e:
            print(f"⚠️  删除失败: {file_path.name} - {str(e)}")
    
    print(f"✅ 清理完成")

def secondary_split_docx_files(source_dir, output_dir=None, max_size_kb=80):
    """对DOCX文件进行二次拆分"""
    if output_dir is None:
        output_dir = source_dir
    
    print("📄 DOCX文件二次拆分工具")
    print(f"📂 源目录: {source_dir}")
    print(f"📂 输出目录: {output_dir}")
    print(f"🎯 目标大小: {max_size_kb} KB")
    print("=" * 80)
    
    # 分析文件大小
    file_info = analyze_file_sizes(source_dir)
    
    if not file_info:
        print("❌ 没有找到DOCX文件")
        return []
    
    # 拆分过大的文件
    print("\n🔄 开始拆分过大的文件...")
    split_files = split_large_docx(file_info, output_dir, max_size_kb)
    
    return split_files

if __name__ == "__main__":
    source_directory = r"F:\augment\output\docx_files2"
    output_directory = r"F:\augment\output\docx_files2_split"
    
    if not Path(source_directory).exists():
        print(f"❌ 源目录不存在: {source_directory}")
        exit(1)
    
    # 执行二次拆分
    result_files = secondary_split_docx_files(source_directory, output_directory, max_size_kb=80)
    
    if result_files:
        print("\n" + "=" * 80)
        print(f"🎉 二次拆分完成! 生成了 {len(result_files)} 个文件")
        
        total_size = 0
        large_files = 0
        
        print(f"\n📋 生成的文件:")
        for file_path in result_files:
            file_size = Path(file_path).stat().st_size / 1024
            total_size += file_size
            
            if file_size > 100:
                large_files += 1
                status = "🔴"
            elif file_size > 80:
                status = "🟡"
            else:
                status = "✅"
            
            print(f"   {status} {Path(file_path).name} ({file_size:.1f} KB)")
        
        print(f"\n📊 拆分统计:")
        print(f"   总文件数: {len(result_files)}")
        print(f"   总大小: {total_size:.1f} KB")
        print(f"   平均大小: {total_size/len(result_files):.1f} KB")
        print(f"   仍需拆分: {large_files} 个文件")
        print(f"   适合RAGFlow: {len(result_files) - large_files} 个文件")
        
        print("\n🎯 文件已准备好用于RAGFlow！")
        
        if large_files > 0:
            print(f"💡 建议: 还有 {large_files} 个文件可能需要进一步拆分")
    else:
        print("❌ 拆分失败")
