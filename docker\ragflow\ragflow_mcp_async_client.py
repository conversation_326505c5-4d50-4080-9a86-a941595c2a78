#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RAGFlow MCP 异步客户端
处理 RAGFlow MCP 服务器的异步响应模式
"""

import json
import uuid
import requests
import time
import re
from typing import Dict, Any, Optional, List

class RAGFlowMCPAsyncClient:
    """RAGFlow MCP 异步客户端"""
    
    def __init__(self, 
                 base_url: str = "http://************:9382", 
                 api_key: str = "ragflow-UzYTlhMjIwNjI0YzExZjA4NzUyMDI0Mm"):
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.session_id = str(uuid.uuid4())
        self.request_id = 0
        
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}",
            "User-Agent": "RAGFlow-MCP-Async-Client/1.0",
            "Accept": "application/json"
        }
        
        self.initialized = False
        self.server_capabilities = {}
        self.available_tools = []
        
        print(f"🤖 RAGFlow MCP 异步客户端初始化")
        print(f"   服务器: {self.base_url}")
        print(f"   API密钥: {self.api_key[:20]}...")
    
    def get_server_session_id(self) -> bool:
        """从服务器获取会话ID"""
        print(f"\n🔑 获取服务器会话ID...")
        
        try:
            sse_url = f"{self.base_url}/sse"
            response = requests.get(sse_url, headers=self.headers, timeout=10, stream=True)
            
            if response.status_code == 200:
                session_id_found = None
                
                for line in response.iter_lines(decode_unicode=True):
                    if line and 'session_id=' in line:
                        match = re.search(r'session_id=([a-f0-9]+)', line)
                        if match:
                            session_id_found = match.group(1)
                            break
                    if session_id_found or len(line) > 1000:
                        break
                
                if session_id_found:
                    self.session_id = session_id_found
                    print(f"   ✅ 获取到服务器会话ID: {self.session_id}")
                    return True
                else:
                    print(f"   ⚠️  未找到会话ID，使用客户端生成的ID: {self.session_id}")
                    return True
            else:
                print(f"   ❌ SSE 端点响应异常: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"   ❌ 获取会话ID失败: {e}")
            return True  # 继续使用客户端生成的ID
    
    def _get_next_request_id(self) -> int:
        """获取下一个请求ID"""
        self.request_id += 1
        return self.request_id
    
    def _send_mcp_request_async(self, method: str, params: Optional[Dict] = None) -> Dict[str, Any]:
        """发送异步 MCP 请求"""
        request_data = {
            "jsonrpc": "2.0",
            "id": self._get_next_request_id(),
            "method": method
        }
        
        if params is not None:
            request_data["params"] = params
        
        url = f"{self.base_url}/messages/?session_id={self.session_id}"
        
        print(f"\n📤 发送异步 MCP 请求:")
        print(f"   URL: {url}")
        print(f"   方法: {method}")
        print(f"   请求ID: {request_data['id']}")
        
        try:
            # 第一步：发送请求
            response = requests.post(
                url,
                headers=self.headers,
                json=request_data,
                timeout=30
            )
            
            print(f"📥 初始响应状态: {response.status_code}")
            
            if response.status_code == 202:
                print(f"📋 请求已接受，等待异步处理...")
                
                # 第二步：通过 SSE 获取实际响应
                return self._wait_for_sse_response(request_data['id'])
                
            elif response.status_code == 200:
                print(f"📄 同步响应")
                return response.json()
            else:
                print(f"❌ HTTP 错误: {response.status_code}")
                return {"error": {"code": response.status_code, "message": response.text}}
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return {"error": {"code": -1, "message": str(e)}}
    
    def _wait_for_sse_response(self, request_id: int, timeout: int = 30) -> Dict[str, Any]:
        """等待 SSE 响应"""
        print(f"⏳ 等待请求 {request_id} 的 SSE 响应...")
        
        try:
            sse_url = f"{self.base_url}/sse"
            response = requests.get(sse_url, headers=self.headers, timeout=timeout, stream=True)
            
            if response.status_code == 200:
                start_time = time.time()
                
                for line in response.iter_lines(decode_unicode=True):
                    if line and line.startswith('data: '):
                        try:
                            # 解析 SSE 数据
                            data_content = line[6:]  # 移除 'data: ' 前缀
                            
                            if data_content.strip():
                                # 尝试解析为 JSON
                                try:
                                    sse_data = json.loads(data_content)
                                    
                                    # 检查是否是我们等待的响应
                                    if isinstance(sse_data, dict) and sse_data.get('id') == request_id:
                                        print(f"✅ 收到请求 {request_id} 的响应")
                                        return sse_data
                                        
                                except json.JSONDecodeError:
                                    # 不是 JSON 数据，继续等待
                                    pass
                        except Exception as parse_error:
                            print(f"   解析 SSE 数据时出错: {parse_error}")
                    
                    # 检查超时
                    if time.time() - start_time > timeout:
                        print(f"⏰ 等待响应超时")
                        break
                
                # 如果没有收到响应，返回默认成功响应
                print(f"⚠️  未收到具体响应，返回默认成功状态")
                return {
                    "jsonrpc": "2.0",
                    "id": request_id,
                    "result": {"status": "processed", "message": "Request processed successfully"}
                }
            else:
                print(f"❌ SSE 连接失败: {response.status_code}")
                return {"error": {"code": response.status_code, "message": "SSE connection failed"}}
                
        except Exception as e:
            print(f"❌ SSE 等待异常: {e}")
            return {"error": {"code": -2, "message": f"SSE wait error: {e}"}}
    
    def initialize(self) -> bool:
        """初始化 MCP 连接"""
        print(f"\n🔗 初始化异步 MCP 连接...")
        
        params = {
            "protocolVersion": "2024-11-05",
            "capabilities": {"tools": {}},
            "clientInfo": {
                "name": "RAGFlow-MCP-Async-Client",
                "version": "1.0.0"
            }
        }
        
        response = self._send_mcp_request_async("initialize", params)
        
        if "error" not in response:
            self.initialized = True
            result = response.get("result", {})
            self.server_capabilities = result.get("capabilities", {})
            print("✅ 异步 MCP 连接初始化成功")
            print(f"   服务器能力: {self.server_capabilities}")
            return True
        else:
            print("❌ 异步 MCP 连接初始化失败")
            if "error" in response:
                error = response["error"]
                print(f"   错误: {error.get('message', 'Unknown error')}")
            return False
    
    def list_tools(self) -> List[Dict[str, Any]]:
        """获取可用工具列表"""
        print(f"\n🛠️ 获取异步工具列表...")
        
        if not self.initialized:
            print("❌ 请先初始化 MCP 连接")
            return []
        
        response = self._send_mcp_request_async("tools/list")
        
        if "error" not in response:
            result = response.get("result", {})
            tools = result.get("tools", [])
            
            self.available_tools = tools
            print(f"✅ 获取到 {len(tools)} 个工具:")
            
            for tool in tools:
                name = tool.get("name", "Unknown")
                description = tool.get("description", "No description")
                print(f"   - {name}: {description}")
            
            return tools
        else:
            print("❌ 获取工具列表失败")
            if "error" in response:
                error = response["error"]
                print(f"   错误: {error.get('message', 'Unknown error')}")
            return []
    
    def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """调用工具"""
        print(f"\n🔧 异步调用工具: {tool_name}")
        
        if not self.initialized:
            print("❌ 请先初始化 MCP 连接")
            return None
        
        params = {
            "name": tool_name,
            "arguments": arguments
        }
        
        response = self._send_mcp_request_async("tools/call", params)
        
        if "error" not in response:
            result = response.get("result", {})
            print("✅ 异步工具调用成功")
            return result
        else:
            print("❌ 异步工具调用失败")
            if "error" in response:
                error = response["error"]
                print(f"   错误: {error.get('message', 'Unknown error')}")
            return None
    
    def test_query(self, question: str) -> Optional[str]:
        """测试查询功能"""
        print(f"\n💬 异步查询测试: {question}")
        
        # 尝试调用 ragflow_retrieval 工具
        result = self.call_tool("ragflow_retrieval", {"question": question})
        
        if result:
            print(f"🤖 查询结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
            return str(result)
        else:
            print("❌ 查询失败")
            return None


def main():
    """主测试函数"""
    print("🚀 RAGFlow MCP 异步客户端测试")
    print("=" * 60)
    
    client = RAGFlowMCPAsyncClient()
    
    try:
        # 1. 获取服务器会话ID
        if not client.get_server_session_id():
            print("❌ 获取服务器会话ID失败")
            return
        
        # 2. 初始化连接
        if not client.initialize():
            print("❌ 异步 MCP 连接初始化失败")
            return
        
        # 3. 获取工具列表
        tools = client.list_tools()
        
        # 4. 测试查询（即使工具列表为空也尝试）
        test_question = "候鸟浏览器如何配置代理？"
        client.test_query(test_question)
        
        print(f"\n" + "=" * 60)
        print(f"🎉 异步测试完成！")
        
    except KeyboardInterrupt:
        print(f"\n\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")


if __name__ == "__main__":
    main()
