﻿<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Window size="1200,590" caption="0,0,0,50" sizebox="4,4,4,4" mininfo="960,590" roundcorner="5,5,5,5" showshadow="true" shadowsize="3" shadowposition="0,0" shadowcolor="#ff6272A4" fademode="true">
	<Include source="Default.xml" />

  <VerticalLayout width="953" height="590" bkcolor="#FF282A36">
    <HorizontalLayout height="37">

    	 <Button name="login" bkimage="icon.png" padding="8,6,0,0" width="28" height="28"/>
      <Label name="session_list_title" padding="6,4,0,0" text="会话环境管理器" width="300" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" align="left" font="8"></Label>

      <Control />
      <Button name="minbtn" width="28" height="26" tooltip="最小化" normalimage="minbtn.png" hotimage="minbtn_hover.png" pushedimage="minbtnpush.png" />
      <Button name="closebtn" width="28" height="26"  tooltip="关闭" normalimage="closebtn.png" hotimage="closebtn_hover.png" pushedimage="closebtnpush.png" />

    </HorizontalLayout>
  <HorizontalLayout name="bkground">


 <HorizontalLayout name="loading_data" height="603" bkcolor="#ffe9e9e9" visible="true">

	    <VerticalLayout width="953" height="420">

					     <HorizontalLayout name="loading_data" height="240" width="953">
					    	 <Control />
					    		<GifAnim name="data_loading" bkimage="dataloading.gif" height="200" width="200" padding="0,40,0,0" auto="true"/>
					    	 <Control />
					     </HorizontalLayout>


					     <HorizontalLayout width="953" height="30" >
					    	 <Control />
					    		  <Label name="data_percent" text="55%" width="300" textcolor="#FF616161" hottextcolor="#ff000000" align="center" font="10"></Label>
					    	 <Control />
					     </HorizontalLayout>

					     <HorizontalLayout width="953" height="60" >
					    	 <Control />
					    		  <Label name="process_description" text="会话环境正在版本验证中，请稍侯.. " width="953" textcolor="#FF616161" hottextcolor="#ff000000" align="center" font="8"></Label>
					    	 <Control />
					     </HorizontalLayout>

              <HorizontalLayout width="953" height="40" >
              </HorizontalLayout>

              <HorizontalLayout name="backarea" width="953" height="60" visible="false">
                <Control />
                <Button text="返回" name="back" width="120" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	normalimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
                <Control width="100" />
                <Button text="退出" name="closewnd1" width="120" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	normalimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
                <Control />
              </HorizontalLayout>
      </VerticalLayout>

 </HorizontalLayout>

		<VerticalLayout name="data" visible="false">


			<HorizontalLayout height="56" >
        <VerticalLayout width="260">
          <Combo name="group" bordersize="0" padding="10,12,0,10" width="250" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" bkcolor="#ffdce1e7"
              normalimage="file='Profile\Proxy_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\proxy_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\proxy_Combox_Click.png' corner='5,5,25,10'"
              combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
              itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0" textpadding="0,0,20,0" endellipsis="true">
          </Combo>
        </VerticalLayout>

        <VerticalLayout width="240">
          <Control />
          <Button text="创建新分组" name="createnew" tooltip="创建新分组" padding="20,10,0,0" textpadding="26,-1,20,0" width="200" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" texttooltip="true" endellipsis="true" font="14"	bordersize="1" bordercolor="#ffb3b3b3" borderround="3,3" bkimage="file='common/btn_new_group_normal.png' corner='5,10,5,10'" hotimage="file='common/btn_new_group_hot.png' corner='5,10,5,10'" pushedimage="file='common/btn_new_group_push.png' corner='5,10,5,10'" disabledimage="file='common/btn_del_group_disable.png' corner='5,10,5,10'"/>
          <Control />
        </VerticalLayout>

        <VerticalLayout width="220">
          <Control />
          <Button text="删除分组" name="delete" tooltip="删除分组" enabled="false" padding="0,10,0,0" textpadding="26,-1,20,0" width="200" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" texttooltip="true" endellipsis="true" font="14"	bordersize="1" bordercolor="#ffb3b3b3" borderround="3,3" bkimage="file='common/btn_del_group_normal.png' corner='5,10,5,10'" hotimage="file='common/btn_del_group_hot.png' corner='5,10,5,10'" pushedimage="file='common/btn_del_group_push.png' corner='5,10,5,10'" disabledimage="file='common/btn_del_group_disable.png' corner='5,10,5,10'"/>
          <Control />
        </VerticalLayout>

        <VerticalLayout width="240">
          <Control />
          <Button text="重命名分组" name="rename" tooltip="重命名分组" enabled="false" padding="0,10,0,0" textpadding="26,-1,20,0" width="200" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" texttooltip="true" endellipsis="true" font="14"	bordersize="1" bordercolor="#ffb3b3b3" borderround="3,3" bkimage="file='common/btn_rename_group_normal.png' corner='5,10,5,10'" hotimage="file='common/btn_rename_group_hot.png' corner='5,10,5,10'" pushedimage="file='common/btn_rename_group_push.png' corner='5,10,5,10'" disabledimage="file='common/btn_rename_group_disable.png' corner='5,10,5,10'"/>
          <Control />
        </VerticalLayout>
        <!--<VerticalLayout width="440">
				         <RichEdit name="session_search" padding="20,10,0,10" height="36" width="420" tipvaluecolor="#FF333333" borderround="3,3" bkcolor="#ffdce1e7" font="8" textpadding="10,8,20,0" maxchar="300" tipvalue="请输入关键字查找会话.." multiline="false" textcolor="#ff333333" rich="false" transparent="false">
				      </RichEdit>
				     </VerticalLayout>
				     <VerticalLayout width="30">
				        <CheckBox name="opt_ie_cache" selected="false"  visible="true" padding="10,26,0,0" height="18" width="16" normalimage="file='list_check_normal.png'" hotimage="list_check_hover.png" selectedimage="file='list_checked_actvice_normal.png'" ></CheckBox>
				     </VerticalLayout>
				     <VerticalLayout width="180">
				        <Label name="searchl" padding="4,25,0,0" text="搜索包含在会话中的关键词" width="180" textcolor="#FF616161" hottextcolor="#ff000000" align="left" font="16"></Label>
				     </VerticalLayout>
        <Control width="40"/>-->
      </HorizontalLayout>


      <HorizontalLayout bkcolor="#FFffffff">
        	<List name="list_session_manager" reselect="true" bordersize="1,1,1,1" itembkcolor="#FFffffff" itemselectedbkcolor="#FFe0e0e0" itemhotbkcolor="#FFf2f2f2" bordercolor="#FFD9DADB" vscrollbar="true">
						<ListHeader height="36" bordersize="1" bordercolor="#FFD7D7D7" bkcolor="#FFF9F9FA">
							<ListHeaderItem text="操作" name="header_device_choice" width="60" align="left" textpadding="20,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="会话名称" name="header_name" width="156" align="left" textpadding="20,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="操作平台" name="header_system" width="100" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="代理" name="header_proxy" width="120" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="注释" name="header_backup_datetime" width="214" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="创建时间" name="header_ctime" width="150" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="最近一次使用时间" name="header_utime" width="146" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="" name="header_sessionid" width="240" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
						</ListHeader>

          </List>
				</HorizontalLayout>
		</VerticalLayout>


	</HorizontalLayout>
    <HorizontalLayout height="52" bkcolor="#ffe9e9e9" inset="20,0,0,0">
         <CheckBox name="opt_checkAll" text="" textpadding="57,1,0,0" selected="false"  visible="true" padding="3,16,0,0" height="18" width="16" normalimage="file='list_check_normal.png'" hotimage="list_check_hover.png" selectedimage="file='list_checked_actvice_normal.png'" ></CheckBox>

         <Control />


      <VerticalLayout width="260">
        <Combo name="togroup" bordersize="0" padding="10,8,0,10" width="250" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="0,0,0,0" bkcolor="#ffdce1e7"
            normalimage="file='Profile\Proxy_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\proxy_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\proxy_Combox_Click.png' corner='5,5,25,10'"
            combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
            itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0" >
        </Combo>
      </VerticalLayout>

      <VerticalLayout width="260">
        <Control />
        <Button text="选中转移到指定分组" name="selectedtogroup" tooltip="选中转移到指定分组" padding="20,2,0,0" textpadding="10,-1,10,0" width="240" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	bkimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
        <Control />
      </VerticalLayout>

    <Control width="30"/>
    </HorizontalLayout>
    <Control name="dragicon" float="true" width="14" height="16" bkimage="dragicon.png"/>
  </VerticalLayout>
</Window>
