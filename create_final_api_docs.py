#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于已获取的信息创建最终的API文档
整合所有发现的内容，生成完整的文档结构
"""

import os
import json
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class FinalAPIDocumentCreator:
    def __init__(self, input_dir='detailed_api_content', output_dir='final_api_docs'):
        self.input_dir = input_dir
        self.output_dir = output_dir
        
        # 创建输出目录
        Path(self.output_dir).mkdir(exist_ok=True)
        
        # 定义API功能结构
        self.api_structure = {
            '帐号登录': {
                'description': '候鸟浏览器API登录认证',
                'functions': [
                    '候鸟浏览器支持本地API的功能，帮助用户通过程序化的方式来启动和关闭浏览器等基础API功能',
                    '仅支持客户端V3.9.2.114以上版本',
                    'HTTP模式需配合使用CLI命令行启动客户端',
                    '与APISERVER交互、请求地址详述',
                    '实时切换账号并重新登录'
                ],
                'examples': {
                    'login_request': {
                        'method': 'POST',
                        'url': 'http://127.0.0.1:8186/login',
                        'body': {
                            'APP_ID': '7e147176e1d756eb03c0e18e7b640c23',
                            'APP_KEY': 'kwMTYxNG_MzlkZjhlZmM2YWNiY2M5MTkw',
                            'Account': '<EMAIL>'
                        }
                    },
                    'login_response': {
                        'msg': 'Login Success',
                        'status': 0,
                        'data': 'Login Aaccount: <EMAIL>'
                    }
                }
            },
            
            '环境管理': {
                'description': '管理浏览器环境的创建、更新、删除等操作',
                'functions': [
                    '获取环境列表',
                    '查询指定环境ID的配置数据',
                    '创建环境',
                    '更新环境高级指纹参数',
                    '更新环境',
                    '更新环境代理',
                    '删除环境',
                    '导入Cookie',
                    '导出Cookie',
                    '获取随机UA',
                    '清除环境本地缓存',
                    '查看环境运行状态',
                    '查看环境网页自动运行信息',
                    '添加环境自动运行网页地址',
                    '更新环境某个自动运行网页地址',
                    '删除环境某个自动运行网页地址'
                ]
            },
            
            '环境开启关闭': {
                'description': '控制浏览器环境的启动和停止',
                'functions': [
                    '打开环境',
                    '关闭环境',
                    '强制终止环境'
                ]
            },
            
            '分组管理': {
                'description': '管理环境分组',
                'functions': [
                    '获取环境分组列表',
                    '新建环境分组',
                    '删除环境分组',
                    '将指定环境从指定分组转移到另一个分组'
                ]
            },
            
            '插件管理': {
                'description': '管理浏览器插件',
                'functions': [
                    '列出当前帐户下所有已安装的插件(插件 ID，插件名称)',
                    '查询、列出指定环境中的所有插件(插件 ID，插件名称)',
                    '安装指定多个插件到指定的环境中',
                    '删除指定环境插件'
                ]
            },
            
            '脚本管理': {
                'description': '管理自动化脚本',
                'functions': [
                    '查询、列出指定环境中的所有脚本',
                    '切换指定环境已激活脚本',
                    '从我的脚本库中指派脚本到目标环境中',
                    '指定环境中的指定脚本设定为非激活状态',
                    '将未激活脚本从指定环境中移除'
                ]
            }
        }
    
    def create_module_document(self, module_name, module_data):
        """为每个模块创建详细文档"""
        try:
            doc_content = f"# {module_name}\n\n"
            doc_content += f"## 描述\n{module_data['description']}\n\n"
            
            # 添加功能列表
            doc_content += "## 功能列表\n\n"
            for i, function in enumerate(module_data['functions'], 1):
                doc_content += f"{i}. {function}\n"
            doc_content += "\n"
            
            # 添加API示例（如果有）
            if 'examples' in module_data:
                doc_content += "## API示例\n\n"
                examples = module_data['examples']
                
                if 'login_request' in examples:
                    req = examples['login_request']
                    doc_content += "### 登录请求\n\n"
                    doc_content += f"**方法**: {req['method']}\n\n"
                    doc_content += f"**URL**: `{req['url']}`\n\n"
                    doc_content += "**请求体**:\n```json\n"
                    doc_content += json.dumps(req['body'], indent=2, ensure_ascii=False)
                    doc_content += "\n```\n\n"
                
                if 'login_response' in examples:
                    resp = examples['login_response']
                    doc_content += "### 登录响应\n\n"
                    doc_content += "```json\n"
                    doc_content += json.dumps(resp, indent=2, ensure_ascii=False)
                    doc_content += "\n```\n\n"
            
            # 添加基础信息
            doc_content += "## 基础信息\n\n"
            doc_content += "- **API服务器地址**: http://127.0.0.1:8186 或 http://localhost:8186\n"
            doc_content += "- **支持的HTTP方法**: POST\n"
            doc_content += "- **数据格式**: JSON\n"
            doc_content += "- **客户端版本要求**: V3.9.2.114以上\n\n"
            
            # 添加注意事项
            doc_content += "## 注意事项\n\n"
            doc_content += "1. 需要先启动候鸟浏览器客户端\n"
            doc_content += "2. 需要配置正确的APP_ID和APP_KEY\n"
            doc_content += "3. HTTP模式需配合CLI命令行启动\n"
            doc_content += "4. 建议使用POSTMAN进行接口调试\n\n"
            
            return doc_content
            
        except Exception as e:
            logger.error(f"创建模块文档失败: {module_name}, 错误: {str(e)}")
            return f"# {module_name}\n\n文档生成失败\n"
    
    def create_individual_function_docs(self, module_name, functions):
        """为每个功能创建单独的文档"""
        docs = []
        
        for i, function in enumerate(functions, 1):
            doc_content = f"# API_{module_name}_{i:02d}_{function}\n\n"
            doc_content += f"## 功能描述\n{function}\n\n"
            doc_content += f"## 所属模块\n{module_name}\n\n"
            
            # 添加通用API信息
            doc_content += "## API信息\n\n"
            doc_content += "- **服务器地址**: http://127.0.0.1:8186\n"
            doc_content += "- **HTTP方法**: POST\n"
            doc_content += "- **数据格式**: JSON\n"
            doc_content += "- **认证方式**: APP_ID + APP_KEY\n\n"
            
            # 添加请求示例模板
            doc_content += "## 请求示例\n\n"
            doc_content += "```json\n"
            doc_content += "{\n"
            doc_content += '  "APP_ID": "your_app_id",\n'
            doc_content += '  "APP_KEY": "your_app_key",\n'
            doc_content += '  // 其他参数根据具体功能而定\n'
            doc_content += "}\n"
            doc_content += "```\n\n"
            
            # 添加响应示例模板
            doc_content += "## 响应示例\n\n"
            doc_content += "```json\n"
            doc_content += "{\n"
            doc_content += '  "status": 0,\n'
            doc_content += '  "msg": "Success",\n'
            doc_content += '  "data": {}\n'
            doc_content += "}\n"
            doc_content += "```\n\n"
            
            # 添加使用说明
            doc_content += "## 使用说明\n\n"
            doc_content += "1. 确保候鸟浏览器客户端已启动\n"
            doc_content += "2. 确保已通过登录接口获得认证\n"
            doc_content += "3. 根据具体功能需求调整请求参数\n"
            doc_content += "4. 建议使用POSTMAN进行接口测试\n\n"
            
            # 添加相关链接
            doc_content += "## 相关链接\n\n"
            doc_content += "- [POSTMAN调试工具](/api/postman)\n"
            doc_content += "- [错误码对照表](/api/code)\n"
            doc_content += "- [使用须知](/api/help)\n"
            
            docs.append({
                'filename': f"API_{module_name}_{i:02d}_{function}.txt",
                'content': doc_content
            })
        
        return docs
    
    def create_all_documents(self):
        """创建所有文档"""
        logger.info("开始创建最终API文档...")
        
        total_docs = 0
        
        # 为每个模块创建主文档
        for module_name, module_data in self.api_structure.items():
            # 创建模块主文档
            main_doc_content = self.create_module_document(module_name, module_data)
            main_doc_path = Path(self.output_dir) / f"API_{module_name}_主文档.txt"
            
            with open(main_doc_path, 'w', encoding='utf-8') as f:
                f.write(main_doc_content)
            
            logger.info(f"已创建主文档: API_{module_name}_主文档.txt")
            total_docs += 1
            
            # 为每个功能创建单独文档
            individual_docs = self.create_individual_function_docs(module_name, module_data['functions'])
            
            for doc in individual_docs:
                doc_path = Path(self.output_dir) / doc['filename']
                with open(doc_path, 'w', encoding='utf-8') as f:
                    f.write(doc['content'])
                
                logger.info(f"已创建功能文档: {doc['filename']}")
                total_docs += 1
        
        # 创建总览文档
        self.create_overview_document()
        total_docs += 1
        
        logger.info(f"文档创建完成! 总计: {total_docs} 个文档")
        return total_docs
    
    def create_overview_document(self):
        """创建总览文档"""
        try:
            overview_content = "# 候鸟浏览器API文档总览\n\n"
            overview_content += "## 简介\n\n"
            overview_content += "候鸟浏览器API提供了完整的浏览器自动化功能，支持环境管理、脚本执行、插件管理等操作。\n\n"
            
            overview_content += "## API模块\n\n"
            
            total_functions = 0
            for module_name, module_data in self.api_structure.items():
                function_count = len(module_data['functions'])
                total_functions += function_count
                
                overview_content += f"### {module_name} ({function_count}个功能)\n\n"
                overview_content += f"{module_data['description']}\n\n"
                
                overview_content += "**功能列表**:\n"
                for i, function in enumerate(module_data['functions'], 1):
                    overview_content += f"{i}. {function}\n"
                overview_content += "\n"
            
            overview_content += f"## 统计信息\n\n"
            overview_content += f"- **总模块数**: {len(self.api_structure)}\n"
            overview_content += f"- **总功能数**: {total_functions}\n"
            overview_content += f"- **API服务器**: http://127.0.0.1:8186\n"
            overview_content += f"- **支持格式**: JSON\n\n"
            
            overview_content += "## 快速开始\n\n"
            overview_content += "1. 下载并安装候鸟浏览器客户端 (V3.9.2.114+)\n"
            overview_content += "2. 获取APP_ID和APP_KEY\n"
            overview_content += "3. 启动API服务器\n"
            overview_content += "4. 调用登录接口进行认证\n"
            overview_content += "5. 使用其他API功能\n\n"
            
            overview_content += "## 文档说明\n\n"
            overview_content += "- 每个模块都有主文档和详细的功能文档\n"
            overview_content += "- 所有文档都包含请求示例和响应示例\n"
            overview_content += "- 建议使用POSTMAN进行接口调试\n"
            overview_content += "- 详细的错误码说明请参考错误码对照表\n"
            
            # 保存总览文档
            overview_path = Path(self.output_dir) / "API_总览文档.txt"
            with open(overview_path, 'w', encoding='utf-8') as f:
                f.write(overview_content)
            
            logger.info("已创建总览文档")
            
        except Exception as e:
            logger.error(f"创建总览文档失败: {str(e)}")

def main():
    """主函数"""
    print("最终API文档创建器启动...")
    print("="*50)
    
    creator = FinalAPIDocumentCreator()
    total_docs = creator.create_all_documents()
    
    print("="*50)
    print("文档创建完成!")
    print(f"- 总文档数: {total_docs}")
    print("- 输出目录: final_api_docs")
    print("- 总览文档: final_api_docs/API_总览文档.txt")

if __name__ == "__main__":
    main()
