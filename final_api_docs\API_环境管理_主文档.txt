# 环境管理

## 描述
管理浏览器环境的创建、更新、删除等操作

## 功能列表

1. 获取环境列表
2. 查询指定环境ID的配置数据
3. 创建环境
4. 更新环境高级指纹参数
5. 更新环境
6. 更新环境代理
7. 删除环境
8. 导入Cookie
9. 导出Cookie
10. 获取随机UA
11. 清除环境本地缓存
12. 查看环境运行状态
13. 查看环境网页自动运行信息
14. 添加环境自动运行网页地址
15. 更新环境某个自动运行网页地址
16. 删除环境某个自动运行网页地址

## 基础信息

- **API服务器地址**: http://127.0.0.1:8186 或 http://localhost:8186
- **支持的HTTP方法**: POST
- **数据格式**: JSON
- **客户端版本要求**: V3.9.2.114以上

## 注意事项

1. 需要先启动候鸟浏览器客户端
2. 需要配置正确的APP_ID和APP_KEY
3. HTTP模式需配合CLI命令行启动
4. 建议使用POSTMAN进行接口调试

