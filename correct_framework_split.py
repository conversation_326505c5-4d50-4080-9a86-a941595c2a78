#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的候鸟浏览器基础框架文档切分脚本
确保不丢失任何内容
"""

import os
import re
from pathlib import Path
from docx import Document
from docx.shared import Inches, Pt

def setup_document_styles(doc):
    """设置文档样式"""
    try:
        heading1 = doc.styles['Heading 1']
        heading1.font.size = Pt(18)
        heading1.font.bold = True
    except:
        pass
    
    try:
        heading2 = doc.styles['Heading 2']
        heading2.font.size = Pt(16)
        heading2.font.bold = True
    except:
        pass
    
    try:
        normal = doc.styles['Normal']
        normal.font.size = Pt(12)
    except:
        pass

def copy_paragraph_with_style(source_para, target_doc):
    """完整复制段落及其样式"""
    try:
        new_para = target_doc.add_paragraph()
        
        # 复制段落样式
        try:
            new_para.style = source_para.style
        except:
            pass
        
        # 复制段落对齐方式
        new_para.alignment = source_para.alignment
        
        # 复制所有runs及其格式
        for run in source_para.runs:
            new_run = new_para.add_run(run.text)
            new_run.bold = run.bold
            new_run.italic = run.italic
            new_run.underline = run.underline
            
            try:
                if run.font.size:
                    new_run.font.size = run.font.size
                if run.font.name:
                    new_run.font.name = run.font.name
                if run.font.color.rgb:
                    new_run.font.color.rgb = run.font.color.rgb
            except:
                pass
        
        return new_para
    except Exception as e:
        # 如果复制失败，至少保存文本
        return target_doc.add_paragraph(source_para.text)

def copy_table_completely(source_table, target_doc):
    """完整复制表格"""
    try:
        rows = len(source_table.rows)
        cols = len(source_table.columns) if source_table.rows else 0
        
        if rows == 0 or cols == 0:
            return None
        
        # 创建新表格
        new_table = target_doc.add_table(rows=rows, cols=cols)
        new_table.style = 'Table Grid'
        
        # 复制每个单元格的内容和格式
        for i, row in enumerate(source_table.rows):
            for j, cell in enumerate(row.cells):
                if i < len(new_table.rows) and j < len(new_table.rows[i].cells):
                    target_cell = new_table.cell(i, j)
                    
                    # 清空目标单元格
                    target_cell.text = ""
                    
                    # 复制所有段落
                    for para_idx, paragraph in enumerate(cell.paragraphs):
                        if para_idx == 0:
                            # 使用第一个段落
                            target_para = target_cell.paragraphs[0]
                        else:
                            # 添加新段落
                            target_para = target_cell.add_paragraph()
                        
                        # 复制段落内容和格式
                        for run in paragraph.runs:
                            new_run = target_para.add_run(run.text)
                            new_run.bold = run.bold
                            new_run.italic = run.italic
                            new_run.underline = run.underline
        
        return new_table
    except Exception as e:
        print(f"⚠️  复制表格失败: {str(e)}")
        return None

def find_major_sections(doc):
    """找到主要章节分割点"""
    sections = []
    
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        if not text:
            continue
        
        # 识别主要章节标题
        is_major_section = False
        
        # 模式1: 明确的章节标记
        if re.match(r'^(第[一二三四五六七八九十\d]+章|第[一二三四五六七八九十\d]+部分|[一二三四五六七八九十]、|\d+\.|第\d+节)', text):
            is_major_section = True
        
        # 模式2: Heading 1 样式
        elif paragraph.style.name == 'Heading 1':
            is_major_section = True
        
        # 模式3: 包含关键主题词的短标题
        elif (len(text) < 80 and 
              any(keyword in text for keyword in [
                  '产品定位', '技术规格', '架构设计', '系统架构', '数据库设计', 
                  'API接口', '环境管理', '代理服务', '插件管理', '脚本管理',
                  '用户认证', '安全机制', '网络通信', '日志系统', '性能优化',
                  '客户端', '服务器端', '数据同步', '业务流程', '团队协作'
              ])):
            is_major_section = True
        
        if is_major_section:
            sections.append({
                'index': i,
                'title': text,
                'style': paragraph.style.name
            })
    
    return sections

def split_by_content_size(doc, target_size_mb=2):
    """按内容大小切分文档"""
    target_paragraphs = int(len(doc.paragraphs) * target_size_mb / 21)  # 基于原文件21MB估算
    target_tables = int(len(doc.tables) * target_size_mb / 21)
    
    print(f"📊 切分参数:")
    print(f"   目标文件大小: {target_size_mb}MB")
    print(f"   每文件段落数: ~{target_paragraphs}")
    print(f"   每文件表格数: ~{target_tables}")
    
    parts = []
    current_start = 0
    part_num = 1
    
    while current_start < len(doc.paragraphs):
        current_end = min(current_start + target_paragraphs, len(doc.paragraphs))
        
        # 寻找合适的分割点（避免在段落中间分割）
        if current_end < len(doc.paragraphs):
            # 向后寻找章节标题作为分割点
            for i in range(current_end, min(current_end + 50, len(doc.paragraphs))):
                text = doc.paragraphs[i].text.strip()
                if (text and len(text) < 100 and 
                    (doc.paragraphs[i].style.name.startswith('Heading') or
                     re.match(r'^(第[一二三四五六七八九十\d]+|[一二三四五六七八九十]、|\d+\.)', text))):
                    current_end = i
                    break
        
        parts.append({
            'start': current_start,
            'end': current_end,
            'part_num': part_num
        })
        
        current_start = current_end
        part_num += 1
    
    return parts

def create_split_files(doc, parts, output_dir, base_name="候鸟浏览器基础框架"):
    """创建切分后的文件"""
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    output_files = []
    table_index = 0
    tables_per_part = len(doc.tables) // len(parts)
    
    for part in parts:
        # 创建新文档
        new_doc = Document()
        setup_document_styles(new_doc)
        
        # 添加文档标题
        new_doc.add_heading(f'{base_name}({part["part_num"]})', 0)
        
        # 复制段落
        paragraph_count = 0
        for para_idx in range(part['start'], part['end']):
            if para_idx < len(doc.paragraphs):
                copy_paragraph_with_style(doc.paragraphs[para_idx], new_doc)
                paragraph_count += 1
        
        # 分配表格
        tables_to_add = tables_per_part
        if part['part_num'] == len(parts):  # 最后一部分包含剩余表格
            tables_to_add += len(doc.tables) % len(parts)
        
        table_count = 0
        for _ in range(tables_to_add):
            if table_index < len(doc.tables):
                if copy_table_completely(doc.tables[table_index], new_doc):
                    table_count += 1
                table_index += 1
        
        # 保存文件
        filename = f"{base_name}({part['part_num']}).docx"
        filepath = output_path / filename
        new_doc.save(str(filepath))
        output_files.append(str(filepath))
        
        # 统计信息
        file_size = filepath.stat().st_size / 1024
        print(f"✅ 创建文件: {filename}")
        print(f"   段落范围: {part['start']}-{part['end']} ({paragraph_count}个段落)")
        print(f"   表格数: {table_count}")
        print(f"   文件大小: {file_size:.1f} KB")
        print()
    
    return output_files

def split_framework_document_correctly(source_file, output_dir, target_size_mb=2):
    """正确切分候鸟浏览器基础框架文档"""
    try:
        print(f"📄 开始处理: {Path(source_file).name}")
        
        # 加载文档
        doc = Document(source_file)
        
        print(f"📊 文档信息:")
        print(f"   段落数: {len(doc.paragraphs)}")
        print(f"   表格数: {len(doc.tables)}")
        
        file_size = Path(source_file).stat().st_size / 1024
        print(f"   文件大小: {file_size:.1f} KB")
        print()
        
        # 计算需要切分的部分数
        num_parts = max(1, int(file_size / (target_size_mb * 1024)))
        print(f"📊 切分计划: 切分为 {num_parts} 个文件")
        
        # 按内容大小切分
        parts = split_by_content_size(doc, target_size_mb)
        
        print(f"📊 实际切分: {len(parts)} 个部分")
        print()
        
        # 创建切分文件
        output_files = create_split_files(doc, parts, output_dir)
        
        return output_files
        
    except Exception as e:
        print(f"❌ 切分失败: {str(e)}")
        return []

if __name__ == "__main__":
    source_file = r"F:\augment\output\docx_files\候鸟浏览器基础框架第七十七版.docx"
    output_directory = r"F:\augment\output\docx_files"
    
    print("📄 候鸟浏览器基础框架文档正确切分工具")
    print(f"📂 源文件: {source_file}")
    print(f"📂 输出目录: {output_directory}")
    print()
    
    if not Path(source_file).exists():
        print(f"❌ 源文件不存在: {source_file}")
        exit(1)
    
    # 删除之前错误的切分文件
    old_files = list(Path(output_directory).glob("候鸟浏览器基础框架_*.docx"))
    if old_files:
        print(f"🗑️  清理 {len(old_files)} 个旧文件...")
        for old_file in old_files:
            try:
                old_file.unlink()
            except:
                pass
        print()
    
    # 执行正确的切分
    result_files = split_framework_document_correctly(source_file, output_directory, target_size_mb=2)
    
    if result_files:
        print("=" * 60)
        print(f"🎉 切分完成! 生成了 {len(result_files)} 个文件")
        
        total_size = 0
        for file_path in result_files:
            file_size = Path(file_path).stat().st_size / 1024
            total_size += file_size
            print(f"   📄 {Path(file_path).name} ({file_size:.1f} KB)")
        
        print(f"\n📊 总大小: {total_size:.1f} KB")
        print(f"📊 平均大小: {total_size/len(result_files):.1f} KB")
        print(f"📊 原文件大小: 21523.5 KB")
        print(f"📊 内容保留率: {(total_size/21523.5)*100:.1f}%")
        
        print("\n🎯 文件已准备好用于RAGFlow向量库！")
    else:
        print("❌ 切分失败")
