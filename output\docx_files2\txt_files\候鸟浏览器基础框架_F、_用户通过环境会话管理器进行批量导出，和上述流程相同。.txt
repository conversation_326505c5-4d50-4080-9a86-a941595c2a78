F、  用户通过环境会话管理器进行批量导出，和上述流程相同。

客户端界面调整与流程说明：

界面调整：（如图）

1、选定的存档 按钮 名称改为： 导出到本地

2、在导出到本地按钮后 增加 一个按钮，名称为：导入本地环境包

3、主面板右键菜单增加 导出到本地、 导入此环境 项

流程说明：

1、单个或批量导出流程：

用户在主面板上对单ITEM项点右键菜单后，从菜单项选择 导出会话环境

弹出下面窗口。

窗口里显示单个item项即可。

用户输入导出密码，选择导出目录。（目录位置需默认记住，下次导出仍旧为最近一次导出成功的目录位置）

用户点击导出按钮。

此时客户端流程：

将此ITEM的 配置数据、浏览器数据 严格按 14版基础框架文档约定数据格式进行打包，打包密码为用户设置密码，不再是产品内核压缩密码，文件名仍旧要带有版本号，SESSION_UNIQUE_ID, ZIP方式存储，文件名串格式不变。

在此窗口左下角显示导出状态。其它任何导出失败需详细显示失败信息（如：导出失败：指定文件夹路径不存在等）。

导出成功显示文字： 您的环境已成功导出到本地！查看文件夹 用户点击 查看文件夹，打开导出的目标文件夹。

F、  用户通过环境会话管理器进行批量导出，和上述流程相同。

G、  单项导出与批量导出使用同一个窗口，区别为批量导出时，窗体lable控件里显示的是多个item项名称，导出到本地的为多个文件。

2、单个和批量导入流程：

用户在主面板上对单ITEM项点右键菜单后，从菜单项选择 导入会话环境

弹出如图窗口。

程序通过用户点击菜单项可知此时用户为单项导入，因此将窗口中的“选择目录”按钮置灰。仅允许用户选择单个数据包文件。在label控件中的会话环境项仅允许为1个。

密码判断说明：导入的密码如不正确，导入时，在左下角提示。

用户在环境会话管理器里进行的导入，无需将“选择目录”置灰。

F、  对于本地导入的item，此item版本号要无条件更新为高于当前服务器端版本号,并采用线程方式，基于单ITEM同步接口，自动同步到服务器端。

[2021-03-13 新增]

第十九章 候鸟客户端本地环境item包子数据

COOKIES数据集接口、服务器端存储、管理功能与详细逻辑流程。

第十九章 候鸟客户端本地环境item包子数据 COOKIES数据集接口、服务器端存储、管理功能与详细逻辑流程。[2021-03-13 新增]

前述：由于候鸟客户端对于cookies属于一个交付给chromium内核的中间环节。

人为误操作、 Chromium本身和第三方插件、网站、Win32应用工具，对cookies的修改、删除、更新、动作都具备完全的权限，从而导致客观上在实际商用环境中，存在涉及到COOKIES被人为误操作（误退出登录）、外界第三方工具修改、清空浏览器COOKIES后导致付费用户业务异常等各项不确定因素。

在chrome本身自带清理、各种第三方浏览器清理插件、第三方页面自动脚本、用户本地机器上的各种PC端第三方工具，杀毒软件、系统瘦身工具(CCLEAN)、系统优化精简工具(360、鲁大师、QQPC管家等)，人为的无意识状态下，错误的实施了自动化修改、删除，清空用户通过候鸟导入的 原有效cookies 的行为。

因此专门开辟此章节针对此类情况进行候鸟基础框架逻辑的补充与完善。

即，需考虑在用户导入cookies的环节中，将cookies数据作为一个独立子数据 (仍需使用SESSION_UNIQUE_ID与ITEM关联)保存在服务器中，而不仅仅是保存在chromium的db中（不能仅仅只依赖用户的 服务器端/本地 候鸟chrome内核中的cookies db单一文件作为唯一凭据），以避免在长期使用中，用户随意的安装使用上述各类工具，导致用户本地浏览器db被其它第三方应用清空后无法恢复而造成各类投诉和已付费用户流失。

【以下流程兼容 候鸟2.0/1.0接口协议规范 】

服务器端 新数据表结构：

Item-cookies.tbl

表结构字段说明：

CookieID:  数据类型参照主表      主键、唯一不重复。

Userid：        -----            （用户ID）长度同主表长度。

Item-sessionid：    -----       (ITEM SESSION_UNQUE_ID)

CookiesName：     -----       （COOKIES文件名称）

CookiesPath:       -----       （COOKIES文件存储路径）

CreateTime：       -----       (COOKIES创建时间，客户端导入后时间)

Is_valid:           -----       此记录是否有效

客户端与服务器端交互流程说明：

用户在上图界面中，进行cookies导入，在导入cookies成功后，Post方式，将cookies数据发送到服务器端指定接口（接口详见本章第二节）。

在用户使用浏览器登录成功目标网站后，客户端检测到浏览器数据变化时，要判断cookies DB是否有变化，如果有变化需POST。（需集体讨论）

Cookies文件名约定：

Cookies_session_unque_id_时间戳.txt

Cookies服务器端存储位置约定：

/Userdata/uid/CookiesStorage/Cookies_session_unque_id_时间戳.txt

服务器端接口：

遵循1.0/2.0规范，使用full包、item包上传接口规范，支持cookies文件上传。

客户端请求服务器端接口：

在cookies导入成功后，将cookies文件、 session_unque_id、数据包类别：cookie、用户token、hash AES POST到服务器端。

（注：cookies没有版本校验流程，但有HASH校验流程）

2、控制台页面界面完善说明：(重点)

一、新增cookies图标。

对于有cookies文件的ITEM，在列表中显示cookies图标。

二、新增管理按钮。

下载环境包到本地：

用户点击此按钮，将服务器端的环境包下载地址发送到用户的浏览器，供用户将此ITEM下载到本地。

下载COOKIES到本地：

将此ITEM下所有COOKIES进行打包，将包文件发送到用户的浏览器，供用户将COOKIES zip包下载到本地。

操作部份：

查看/编辑：  在弹窗里，显示WEB RICHEDIT控件，将cookies 文本加载到控件中，并使用控件自带的功能支持用户查看，编辑。

下载到本地： 支持用户将此cookies单文件下载到本地。

综述：第十八章COOKIES客户端、服务器端逻辑可彻底解决上述各方面因素导致用户COOKEIS丢失所带来的各种业务问题。

[2021-04-08 新增]

第二十章 候鸟浏览器客户端

用户海量环境-本地分组

完整数据架构与详细图文逻辑流程及约定。

第二十章 候鸟浏览器客户端 用户海量环境-本地分组 完整数据架构与详细图文逻辑流程及约定。[2021-04-08 新增]

前述：

基于用户普遍添加大量ITEM会话环境，为方便用户依据业务的不同、会话类别归属的不同、常用会话集合的设定与冷门会话等，不同各种业务与功能支持批量处理等需求，现约定并新增 候鸟客户端“本地分组”完整功能与图文流程、数据架构与详细逻辑。

约定：

ConfigData数据结构与增加部份：(增加内容以红色标注)

----configdata.xml ----

<VER ID="8" VERSION="0" SESSION_UNIQUE_ID="9bcc1ce2723465bdcd540e2e3280e4a1" FROMUID="110524" SHARETIME="2021-01-25 15:44:23" FROM_ACCOUNT=”<EMAIL>” GROUP_ID=”0” GROUP_NAME=”default”>

<SESSION NAME="790790" TOP="0" COLOR="#FFffffffff" SYSTEM="Win32"/>

<IS_ANONYMITY ANONYMITY = "0"/>

<COMMENT COMMENT_STRING=""/>

<SESSION_DEFINE_CODE SPHEREGL="0" ENABLE_LOCALHTML5_STORAGE="0" SAVE_LOCALHTML5_STORAGE="0" SAVE_INDEXDB_STORAGE="0"/>

<NETWORK TYPE="noproxy" PMODE="0" IP="N/A" PORT="0" USER="" PASSWORD="" PUBLIC_IP="*************" FAKEIP="**************" />

<NETWORK_CTR NA="0" FAKE_WRTC="0" SAME_IP="0" IPV6="0" WRTCOFF="0" DNS="" />

<USERAGENT_BROWSER CHROME="0" SAFARI="0" MSIE="0" OTHER="0" REGEN_CONFIG_USERAGENT="0" />

<USERAGENT_STRING UA="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36" UA_LNG="en-US" UA_LNG_STRING="[@en-US@,@US@]" />

<USERAGENT_CTR DISPOPUPS="0" ENABLE_SERVICE="0" BLOCKLNGINPUT="0"  />

<RESOLUTION WIGHT="721" HEIGHT="1280" />

<RESOLUTION_CTR EMU_SCREEN="0" EMU_TOUCH="0" />

<POSITION LONGITUDE="0.000000" LATITUDE="0.000000" COUNTRY="AD" />

<TIMEZONE TIMEZONE_NAME="Africa/Abidjan (0)" ADD_VALUE=""/>

<FINGERPRINT_CODE AUDIO="1" CANVAS="1" FONTS="1" RETCS="1" DNS="1" AUTOIPCHECK="1"/>

<OTHER_SETTING PLUGINS_MIMETYPE="0" SAVE_ENCRYPT_COOKIES="0" ENABLE_FLASH="0" DYNAMIC_FINGERPRINTS="0" BLOCK_CANVAS_OUTPUT="0" />

<DYNAMIC_FINGERPRINTS_CTR D_AUDIO="0" D_CANVAS="0" D_FONTS="0" D_RETCS="0" D_MEDIA="0" D_WEBGL="0" D_MIME="0" D_PLUGINS="0" />

<IS_VALUED VALUED="0" />

<UPDATETIME VALUE="2021-01-25 11:39:22"/>

<CREATETIME VALUE="2021-01-25 11:39:22"/>

</VER>

----configdata.xml ----

参数与流程说明：

FROM_ACCOUNT ：from_account为分享发起者帐户名称。


================================================== 表格内容 ==================================================

参数名称 | 类型 | 必传 | 样例串/默认值 | 说明
Session_ID | string | 是 | 373808cb37bd63f5f7d92415e736e85f | 环境ID

{"message": "Successfully terminated",
"code": 0,
"data": {
        "action": "StopSession_ID",
        "status": 0
    }