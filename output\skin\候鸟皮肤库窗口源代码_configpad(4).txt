      	<Button name="finger_recorder_btn" padding="8,11,0,0" text="指纹轨迹" height="22" textpadding="10,0,10,0" texttooltip="true" endellipsis="true" bkcolor="#FF006fdf" hotbkcolor="#ff0055ab"	 borderround="7,7" autocalcwidth="true" maxwidth="200" textcolor="#FFe6ebf2" hottextcolor="#FFFFFFFF" pushedtextcolor="FFe6ebf2" align="center" font="5"></Button>

        <Button name="his_version_btn" padding="8,11,0,0" text="历史版本" height="22" textpadding="10,0,10,0" texttooltip="true" endellipsis="true" bkcolor="#FF006fdf" hotbkcolor="#ff0055ab"	 borderround="7,7" autocalcwidth="true" maxwidth="200" textcolor="#FFe6ebf2" hottextcolor="#FFFFFFFF" pushedtextcolor="FFe6ebf2" align="center" font="5"></Button>

      	<Button name="api_proxyip_btn" padding="8,11,0,0" text="API提取代理IP" height="22" textpadding="10,0,10,0" texttooltip="true" endellipsis="true" bkcolor="#FF006fdf" hotbkcolor="#ff0055ab"	 borderround="7,7" autocalcwidth="true" maxwidth="200" textcolor="#FFe6ebf2" hottextcolor="#FFFFFFFF" pushedtextcolor="FFe6ebf2" align="center" font="5"></Button>
      </HorizontalLayout>




      <HorizontalLayout inset="20,-1,22,0" height="2">
        <Control height="2" bkcolor="#FF006fdf"/>
      </HorizontalLayout>

        <HorizontalLayout inset="20,0,22,0" height="10">
      </HorizontalLayout>

      <HorizontalLayout inset="20,0,22,0" height="8">
      </HorizontalLayout>

      	<TabLayout name="more_bk" selectedid="0">

      	   <VerticalLayout inset="22,0,22,0">
      	      <HorizontalLayout height="60">

					        <VerticalLayout  width="390">
					          <HorizontalLayout height="28">
					          <Label name="itemnamel"  padding="0,10,0,0" width="100" textpadding="0,0,0,0" texttooltip="true" endellipsis="true"  text="当前环境：" align="right" font="5" textcolor="#ff000000" hottextcolor="#FFadadad"></Label>
					          <Label name="itemname" padding="0,10,0,0" width="280" textpadding="6,0,0,0" texttooltip="true" endellipsis="true"  text="" align="left" font="5" textcolor="#FF4a4a4a" hottextcolor="#FFadadad"></Label>
					          </HorizontalLayout>
					          <HorizontalLayout height="22">
					            <Label name="itemidl" padding="0,0,0,0" height="22" width="100" textpadding="0,1,0,0" texttooltip="true" endellipsis="true"  text="环境ID：" align="right" font="5" textcolor="#ff000000" hottextcolor="#FFadadad"></Label>
					            <Button name="itemid" padding="4,4,0,0" height="22" textpadding="2,0,0,0" align="left" textcolor="#FF005ed3" tooltip="copy" autocalcwidth="true" font="5" hottextcolor="#ff00224d" />
					          </HorizontalLayout>
					        </VerticalLayout>

					        <Control />
					        <VerticalLayout width="160">
					          <Button name="getwebrtcbtn" padding="0,8,0,0" height="40" width="150" text="检测环境指纹" textpadding="2,6,0,6" texttooltip="true" endellipsis="true" align="center" font="8" borderround="7,7" textcolor="#FFe6ebf2" hottextcolor="#FFFFFFFF" pushedtextcolor="FFe6ebf2" disabledtextcolor="#ffcccccc" bkcolor="#FF006fdf" hotbkcolor="#ff0055ab"	/>
					        </VerticalLayout>

			        </HorizontalLayout>

			      <HorizontalLayout height="28" >
			        <Label name="webrtclistl" padding="0,0,0,0" width="288" textpadding="0,0,0,0" endellipsis="true"  texttooltip="true" text="指纹安全轨迹报表" align="left" font="8" textcolor="#ff000000" hottextcolor="#FFadadad"></Label>
			        <Control />
			        <Control width="20" height="20" padding="0,7,0,0" bkimage="fgreen.png"/>
			        <Label name="webrtcstatusl1" padding="0,5,0,0" width="90" textpadding="4,2,0,0" endellipsis="true" texttooltip="true" text="指纹持续稳定中" align="left" font="5" textcolor="#FF31AE5F" hottextcolor="#FFadadad"></Label>
			        <Control width="8" />
			        <Control width="19" padding="0,6,0,0" height="19" bkimage="fwarning.png"/>
			        <Label name="webrtcstatusl2" padding="0,5,0,0" width="90" textpadding="4,2,0,0" endellipsis="true"  texttooltip="true" text="捕获到指纹变化" align="right" font="5" textcolor="#ffff5b5b" hottextcolor="#FFadadad"></Label>
			      </HorizontalLayout>

			      <VerticalLayout height="320" inset="0,0,0,0">
					      <List name="list_webts" inset="0,0,1,1" scrollwheel="true" bordersize="1" bkcolor="#FFFFFFFF" itembkcolor="#FFffffff" itemselectedbkcolor="#FFe0e0e0" itemhotbkcolor="#FFf2f2f2" bordercolor="#FFD9DADB" rich="false" vscrollbar="true">
					        <ListHeader height="36" bordersize="1,1,0,1" bordercolor="#FFD7D7D7" bkcolor="#FFF9F9FA">
					          <ListHeaderItem text="操作" name="header_device_choice" width="42" align="center" textpadding="0,0,0,0" sepimage="split.png" sepwidth="1"></ListHeaderItem>
					          <ListHeaderItem text="HSTS" name="header_name" width="56" align="left" textpadding="13,0,0,0" sepimage="split.png" sepwidth="1" font="26"></ListHeaderItem>
					          <ListHeaderItem text="WEBGL" name="header_name" width="56" align="left" textpadding="10,0,0,0" sepimage="split.png" sepwidth="1" font="26"></ListHeaderItem>
					          <ListHeaderItem text="CANVAS" name="header_name" width="56" align="left" textpadding="8,0,0,0" sepimage="split.png" sepwidth="1" font="26"></ListHeaderItem>
					          <ListHeaderItem text="PLUGINS" name="header_name" width="56" align="left" textpadding="5,0,0,0" sepimage="split.png" sepwidth="1" font="26"></ListHeaderItem>
					          <ListHeaderItem text="AUDIO" name="header_name" width="56" align="left" textpadding="12,0,0,0" sepimage="split.png" sepwidth="1" font="26"></ListHeaderItem>
					          <ListHeaderItem text="RECTS" name="header_name" width="56" align="left" textpadding="10,0,0,0" sepimage="split.png" sepwidth="1" font="26"></ListHeaderItem>
					          <ListHeaderItem text="FONTS" name="header_name" width="56" align="left" textpadding="10,0,0,0" sepimage="split.png" sepwidth="1" font="26"></ListHeaderItem>
					          <ListHeaderItem text="安全" name="header_esti" width="38" align="left"  textpadding="8,0,0,0" sepimage="split.png" sepwidth="1"></ListHeaderItem>
					           <ListHeaderItem text="上次检测时间" name="header_date" width="150" align="left" textpadding="5,0,0,0" sepimage="split.png" sepwidth="1"></ListHeaderItem>
					        </ListHeader>
					      </List>
			        <HorizontalLayout height="30" padding="0,0,0,0">
			          <Button name="selall" padding="0,4,0,0" align="left" autocalcwidth="true" text="全选" font="5" hottextcolor="#FF005ed3" />
			          <Control width="10"/>
			          <Button name="unselall" padding="0,4,0,0" align="left" autocalcwidth="true" text="反选" font="5" hottextcolor="#FF005ed3" />
			          <Control width="10"/>
			          <Button name="freshwebrtc" padding="0,4,0,0" textcolor="#ff2E894E" align="left" autocalcwidth="true" text="报表刷新" font="5" hottextcolor="#FF005ed3" />
			          <Control />
			          <Label name="webrtcmsg1" padding="0,0,0,0"  textpadding="2,4,0,10" texttooltip="true" width="280" endellipsis="true" text="候鸟每次将捕获到的异常记录下来并实时反馈入轨迹报表" align="center" font="5" textcolor="#ffaeaeae" hottextcolor="#FF005ed3"></Label>
			          <Control />
			          <Button name="del" padding="0,4,0,0" align="right" autocalcwidth="true" text="删除" font="5" hottextcolor="#FF005ed3" />
			          <Control width="10"/>
			          <Button name="clear" textpadding="0,0,0,0" padding="0,4,0,0" align="right" autocalcwidth="true" text="清空报表" font="5" hottextcolor="#FF005ed3" />
			        </HorizontalLayout>
			      </VerticalLayout>

			      <HorizontalLayout inset="0,0,0,0" height="1">
			        <Control height="2" bkcolor="#ffc6c9cd"/>
			      </HorizontalLayout>

			      <VerticalLayout inset="0,0,0,0" height="200">
			        <RichEdit name="msg" bordersize="1" bordercolor="#ffe1d8d8" vscrollbar="true" disabledtextcolor="#ff000000" text="" padding="0,10,0,2" tipvaluecolor="#ffffffff" borderround="7,7" bkcolor="#ffffffff" font="20" textpadding="10,10,4,0" wantreturn="true" rich="true" transparent="false">
			        </RichEdit>
			      </VerticalLayout>

			      <HorizontalLayout inset="0,0,0,0" height="24" >
			        <Control />
			        <Button name="copy" padding="0,4,0,0" align="right" autocalcwidth="true" text="11" font="5" hottextcolor="#FF005ed3" />
			      </HorizontalLayout>



			      <HorizontalLayout inset="0,0,0,0" height="60" >
			        <Control />
			      </HorizontalLayout>

					 </VerticalLayout>




			    <!--历史版本-->

			       <VerticalLayout inset="22,0,22,0">
					      <HorizontalLayout height="60">

					        <VerticalLayout  width="390">
					          <HorizontalLayout height="28">
					            <Label name="itemnamel2" padding="0,10,0,0" width="100" textpadding="0,0,0,0" texttooltip="true" endellipsis="true"  text="当前环境：" align="right" font="5" textcolor="#FF000000" hottextcolor="#FFadadad"></Label>
					            <Label name="itemname2" padding="0,10,0,0" width="280" textpadding="6,0,0,0" texttooltip="true" endellipsis="true"  text="" align="left" font="5" textcolor="#FF4a4a4a" hottextcolor="#FFadadad"></Label>
					          </HorizontalLayout>
					          <HorizontalLayout height="22">
					            <Label name="itemidl2" padding="0,0,0,0" width="100" textpadding="0,1,0,0" texttooltip="true" endellipsis="true"  text="环境ID：" align="right" font="5" textcolor="#FF000000" hottextcolor="#FFadadad"></Label>
					            <Button name="itemid2" padding="4,4,0,0" textpadding="2,0,0,0" align="left" textcolor="#FF005ed3" tooltip="copy" autocalcwidth="true" font="5" hottextcolor="#ff00224d" />
					          </HorizontalLayout>
					        </VerticalLayout>

					        <Control />
					        <VerticalLayout width="160">
					          <Button name="gethisverbtn" enabled="false" padding="0,8,0,0" height="40" width="150" text="切换指定版本" textpadding="2,6,0,6" endellipsis="true" align="center" font="8" borderround="7,7" textcolor="#FFe6ebf2" hottextcolor="#FFFFFFFF" pushedtextcolor="FFe6ebf2" disabledtextcolor="#ffcccccc" bkcolor="#FF006fdf" hotbkcolor="#ff0055ab"	/>
					        </VerticalLayout>

					      </HorizontalLayout>

					      <HorizontalLayout height="28" >
					        <Label name="hisverlistl" padding="0,0,0,0" width="288" textpadding="0,0,0,0" endellipsis="true"  texttooltip="true" text="当前环境历史版本一览" align="left" font="8" textcolor="#ff000000" hottextcolor="#FFadadad"></Label>
					      </HorizontalLayout>

					      <VerticalLayout height="320" inset="0,0,0,0">
					        <List name="list_vers" inset="0,0,1,1" scrollwheel="true" bordersize="1" bkcolor="#FFFFFFFF" itembkcolor="#FFffffff" itemselectedbkcolor="#FFe0e0e0" itemhotbkcolor="#FFf2f2f2" bordercolor="#FFD9DADB" rich="false" vscrollbar="true">
					          <ListHeader height="36" bordersize="1,1,0,1" bordercolor="#FFD7D7D7" bkcolor="#FFF9F9FA">
					            <ListHeaderItem text="操作" name="header_device_choice" width="42" align="center" textpadding="0,0,0,0" sepimage="split.png" sepwidth="1"></ListHeaderItem>
					            <ListHeaderItem text="当前环境包历史版本" name="header_ver" width="310" align="left"  textpadding="8,0,0,0" sepimage="split.png" sepwidth="1"></ListHeaderItem>
					            <ListHeaderItem text="数据包大小" name="header_size" width="100" align="left"  textpadding="8,0,0,0" sepimage="split.png" sepwidth="1"></ListHeaderItem>
					            <ListHeaderItem text="时间" name="header_date" width="150" align="left" textpadding="5,0,0,0" sepimage="split.png" sepwidth="1"></ListHeaderItem>
					          </ListHeader>
					        </List>
					        <HorizontalLayout height="30" padding="0,0,0,0">
					          <!--<Button name="selall2" padding="0,4,0,0" align="left" autocalcwidth="true" text="全选" font="5" hottextcolor="#FF005ed3" />
					          <Control width="10"/>
					          <Button name="unselall2" padding="0,4,0,0" align="left" autocalcwidth="true" text="反选" font="5" hottextcolor="#FF005ed3" />
					          <Control width="10"/>-->
					          <Button name="freshhisver" padding="0,4,0,0" textcolor="#ff2E894E" align="left" autocalcwidth="true" text="刷新" font="5" hottextcolor="#FF005ed3" />
					          <Control />
					          <Label name="hisvermsg1" padding="0,0,0,0"  textpadding="2,4,0,10" texttooltip="true" width="280" endellipsis="true" text="候鸟支持您切换当前环境到不同的历史版本" align="center" font="5" textcolor="#ffaeaeae" hottextcolor="#FF005ed3"></Label>
					          <Control />
					          <Button name="del2" enabled="false" padding="0,4,0,0" align="right" autocalcwidth="true" text="删除" font="5" hottextcolor="#FF005ed3" />
					          <Control width="10"/>
					          <Button name="clear2" textpadding="0,0,0,0" padding="0,4,0,0" align="right" autocalcwidth="true" text="清空" font="5" hottextcolor="#FF005ed3" />
					        </HorizontalLayout>
					      </VerticalLayout>

					      <HorizontalLayout inset="0,0,0,0" height="2">
					        <Control height="2" bkcolor="#ffc6c9cd"/>
					      </HorizontalLayout>

					      <VerticalLayout inset="0,0,0,0" height="200">
					        <RichEdit name="msg2" bordersize="1" bordercolor="#ffe1d8d8" vscrollbar="true" disabledtextcolor="#ff000000" text="" padding="0,10,0,2" tipvaluecolor="#ffffffff" borderround="7,7" bkcolor="#ffffffff" font="20" textpadding="10,10,4,0" wantreturn="true" rich="true" transparent="false">
					        </RichEdit>
					      </VerticalLayout>

					      <HorizontalLayout inset="0,0,0,0" height="24" >
					        <!--<Label name="hisvermsg2" padding="0,4,0,0" autocalcwidth="true" textpadding="0,0,0,0" texttooltip="true" endellipsis="true"  text="点击查看已勾历史版本环境配置信息" align="left" font="8" textcolor="#FFadadad" hottextcolor="#FFadadad"></Label>-->
					        <Control />
					        <Button name="copy2" padding="0,4,0,0" align="right" autocalcwidth="true" text="11" font="5" hottextcolor="#FF005ed3" />
					      </HorizontalLayout>

							</VerticalLayout>


        <!--API提取代理IP-->

			  <VerticalLayout sepheight="1" header="hidden">
    	    <!--高级设置top1-->

            <HorizontalLayout height="28">
              <CheckBox name="check_apiprovider" padding="26,0,0,10" width="52" height="24"  normalimage="key_off.png" selectedimage="key_on.png" disabledimage="key_off.png" />
              <Label name="default_apiprovider_dec" padding="10,-5,0,0" textpadding="0,0,0,0" text="未启用" texttooltip="true" endellipsis="true" width="500" textcolor="#FF9da1a3" font="5"></Label>
              <!--<Button name="firstpage_help" padding="8,6,0,0" height="15" width="15" tooltip="Auto running webpages.." normalimage="file='helpbtn.png'" hotimage="file='helpbtn_hover.png'" pushedimage="file='helpbtn_push.png'"/>-->
            </HorizontalLayout>

						<HorizontalLayout height="32">
						    <Label name="ip_provider_name" padding="26,0,0,0" textpadding="0,0,0,0" text="服务商" texttooltip="true" endellipsis="true" autocalcwidth="true" width="160" textcolor="#FF333333" hottextcolor="ffFF0000" font="8"></Label>
                <Control width="70"/>
                <Button name="ip_provider_fresh" padding="10,9,0,0" textpadding="4,0,4,0" text="刷新" texttooltip="true" endellipsis="true" align="center" width="80" borderround="7,7" bkcolor="#FFdce2e7" hotbkcolor="#ff3996f4" textcolor="#FF686b6e" hottextcolor="#ffffffff" pushedtextcolor="#FF26292d" font="5"></Button>
                <Control width="26" />
						</HorizontalLayout>

						<HorizontalLayout height="38">
						    <VerticalLayout width="360">
						        <Combo name="ip_provider_list" reselect="true" dropboxsize="0,320" bordersize="0" padding="26,2,0,10" width="320" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="4,2,0,0" bkcolor="#ffdce1e7"
						            normalimage="file='Profile\Setting_Combox_Normal_big_large.png' corner='25,5,32,7'" hotimage="file='Profile\Setting_Combox_Hover_large.png' corner='25,5,32,7'" pushedimage="file='Profile\Setting_Combox_Click_large.png' corner='25,5,32,7'"
						            combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='25,5,32,7'"
						            itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0">
						        </Combo>
						    </VerticalLayout>
						    <VerticalLayout width="300">
                  <Button name="ip_provider_web" padding="10,9,0,0" textpadding="0,2,0,0" text="服务商官网" texttooltip="true" endellipsis="true" align="left" width="300" textcolor="#FF333333" hottextcolor="ff0055ab" font="5"></Button>
						    </VerticalLayout>
						</HorizontalLayout>

						<HorizontalLayout height="10"></HorizontalLayout>

						<HorizontalLayout height="32">
						    <Label name="ip_provider_typel" padding="26,0,0,0" textpadding="0,0,0,0" text="代理类型" texttooltip="true" endellipsis="true" autocalcwidth="true" maxwidth="500" textcolor="#FF333333" hottextcolor="ffFF0000" font="8"></Label>
						</HorizontalLayout>

            <HorizontalLayout height="38">
						    <VerticalLayout width="360">
						        <Combo name="ip_provider_type" reselect="true" dropboxsize="0,320" bordersize="0" padding="26,2,0,10" width="320" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="4,2,0,0" bkcolor="#ffdce1e7"
						            normalimage="file='Profile\Setting_Combox_Normal_big_large.png' corner='25,5,32,7'" hotimage="file='Profile\Setting_Combox_Hover_large.png' corner='25,5,32,7'" pushedimage="file='Profile\Setting_Combox_Click_large.png' corner='25,5,32,7'"
						            combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='25,5,32,7'"
						            itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0">
						        </Combo>
						    </VerticalLayout>
						</HorizontalLayout>

						<HorizontalLayout height="10"></HorizontalLayout>

						<HorizontalLayout height="32">
						    <Label name="ip_provider_typel2" padding="26,0,0,0" textpadding="0,0,0,0" text="提取方式" texttooltip="true" endellipsis="true" autocalcwidth="true" maxwidth="500" textcolor="#FF333333" hottextcolor="ffFF0000" font="8"></Label>
						</HorizontalLayout>

            <HorizontalLayout height="38">
						    <VerticalLayout width="500">
						        <Combo name="ip_provider_type2" reselect="true" dropboxsize="0,460" bordersize="0" padding="26,2,0,10" width="460" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="4,2,0,0" bkcolor="#ffdce1e7"
						            normalimage="file='Profile\Setting_Combox_Normal_big_large.png' corner='25,5,32,7'" hotimage="file='Profile\Setting_Combox_Hover_large.png' corner='25,5,32,7'" pushedimage="file='Profile\Setting_Combox_Click_large.png' corner='25,5,32,7'"
						            combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='25,5,32,7'"
						            itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0">
						        </Combo>
						    </VerticalLayout>
						</HorizontalLayout>

						<HorizontalLayout height="10"></HorizontalLayout>

						<HorizontalLayout height="32">
						    <Label name="api_links_name" padding="26,0,0,0" textpadding="0,0,0,0" text="提取链接" texttooltip="true" endellipsis="true" autocalcwidth="true" maxwidth="500" textcolor="#FF333333" hottextcolor="ffFF0000" font="8"></Label>
						    <Button name="api_links_help" padding="8,9,0,0" height="15" width="15" tooltip="By calling the API link interface provided by the IP platform provider, the IP address information is parsed and extracted from the returned data." normalimage="file='helpbtn.png'" hotimage="file='helpbtn_hover.png'" pushedimage="file='helpbtn_push.png'"/>
						</HorizontalLayout>

            <HorizontalLayout height="150">
						    <VerticalLayout width="500">
				         <RichEdit name="api_links" padding="26,0,0,0" height="140" width="460" tipvaluecolor="#FF333333" borderround="7,7" bkcolor="#ffdce1e7" font="0" textpadding="8,8,20,0" maxchar="300" multiline="true" textcolor="#ff333333" rich="false" transparent="false">
				         </RichEdit>
				        </VerticalLayout>

						</HorizontalLayout>


						<HorizontalLayout height="26">
							  <CheckBox name="check_api_proxy_ip" width="18" height="18"  padding="26,0,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="true" />
						    <Label name="check_api_proxy_ip_label" padding="6,-6,0,0" textpadding="0,0,0,0" text="重复代理IP校验" texttooltip="true" endellipsis="true" autocalcwidth="true" maxwidth="200" textcolor="#FF333333" hottextcolor="ffFF0000" font="8"></Label>
						    <Label name="check_api_proxy_ip_detail" padding="6,-6,0,0" textpadding="0,2,0,0" text="(运行环境时,自动检测代理IP是否已使用,如已使用过则尝试重新提取代理IP)" texttooltip="true" endellipsis="true" autocalcwidth="true" maxwidth="320" textcolor="#FF666666" hottextcolor="ffFF0000" font="5"></Label>
						</HorizontalLayout>

						<HorizontalLayout height="22">
						    <Label name="check_api_proxy_ip_numl" padding="51,0,0,0" textpadding="0,0,0,0" text="最多提取次数" texttooltip="true" endellipsis="true" autocalcwidth="true" maxwidth="200" textcolor="#FF333333" hottextcolor="ffFF0000" font="5"></Label>
						    <RichEdit name="api_max_fetch_num" padding="6,0,0,10" height="20" width="40" tipvaluecolor="#FF333333" borderround="7,7" bkcolor="#ffdce1e7" font="0" textpadding="6,2,0,0" maxchar="2" tipvalue="5" multiline="false" textcolor="#ff333333" rich="false" transparent="false">
						    	</RichEdit>
						</HorizontalLayout>

						<HorizontalLayout height="2"></HorizontalLayout>

						<HorizontalLayout height="28">
						    <Label name="api_proxy_type" padding="26,0,0,0" textpadding="0,0,0,0" text="代理协议" texttooltip="true" endellipsis="true" autocalcwidth="true" maxwidth="500" textcolor="#FF333333" hottextcolor="ffFF0000" font="8"></Label>

						    <CheckBox name="check_api_speed_first" group="pro" width="18" height="18"  padding="26,7,0,1" normalimage="file='radio.png' source='0,0,18,18'" selectedimage="file='radio.png' source='18,0,36,18'" disabledimage="file='file='radio.png' dest='36,0,54,36'" selected="true" />
						    <Label name="api_speed_first" padding="6,0,0,0" textpadding="0,0,0,0" text="速度优先" texttooltip="true" endellipsis="true" autocalcwidth="true" maxwidth="500" textcolor="#FF333333" hottextcolor="ffFF0000" font="8"></Label>

						    <CheckBox name="check_api_ipv4_first" group="pro" width="18" height="18"  padding="26,7,0,1" normalimage="file='radio.png' source='0,0,18,18'" selectedimage="file='radio.png' source='18,0,36,18'" disabledimage="file='file='radio.png' dest='36,0,54,36'"/>
						    <Label name="api_ipv4_first" padding="6,0,0,0" textpadding="0,0,0,0" text="IPv4" texttooltip="true" endellipsis="true" autocalcwidth="true" maxwidth="500" textcolor="#FF333333" hottextcolor="ffFF0000" font="8"></Label>


						    <CheckBox name="check_api_ipv6_first" group="pro" width="18" height="18"  padding="26,7,0,1" normalimage="file='radio.png' source='0,0,18,18'" selectedimage="file='radio.png' source='18,0,36,18'" disabledimage="file='file='radio.png' dest='36,0,54,36'"/>
						    <Label name="api_ipv6_first" padding="6,0,0,0" textpadding="0,0,0,0" text="IPv6" texttooltip="true" endellipsis="true" autocalcwidth="true" maxwidth="500" textcolor="#FF333333" hottextcolor="ffFF0000" font="8"></Label>

						</HorizontalLayout>



						<HorizontalLayout height="52">
		          <Button name="test_api_link" padding="26,8,0,0" height="29" width="150" text="测试提取" textpadding="6,6,6,6" texttooltip="true" endellipsis="true" align="center" font="5" borderround="7,7" textcolor="#FFe6ebf2" hottextcolor="#FFFFFFFF" pushedtextcolor="FFe6ebf2" disabledtextcolor="#ffb1b1b1" bkcolor="#FF006fdf" hotbkcolor="#ff0055ab" disabledbkcolor="#ffdce1e7"/>
              <Control width="10"/>
              <Button name="view_ip" padding="0,8,0,0" height="29" width="150" text="查看历史提取IP" textpadding="6,6,6,6" texttooltip="true" endellipsis="true" align="center" font="5" borderround="7,7" textcolor="#FFe6ebf2" hottextcolor="#FFFFFFFF" pushedtextcolor="FFe6ebf2" disabledtextcolor="#ffb1b1b1" bkcolor="#FF006fdf" hotbkcolor="#ff0055ab"	disabledbkcolor="#ffdce1e7"/>
              <Control width="10"/>
              <Button name="clear_ip" padding="0,8,0,0" height="29" width="150" text="清空历史IP记录" textpadding="6,6,6,6" texttooltip="true" endellipsis="true" align="center" font="5" borderround="7,7" textcolor="#FFe6ebf2" hottextcolor="#FFFFFFFF" pushedtextcolor="FFe6ebf2" disabledtextcolor="#ffb1b1b1" bkcolor="#FF006fdf" hotbkcolor="#ff0055ab"	disabledbkcolor="#ffdce1e7"/>
						</HorizontalLayout>


						<HorizontalLayout inset="26,0,26,0" height="1">
			        <Control height="2" bkcolor="#ffc6c9cd"/>
			      </HorizontalLayout>

			      <HorizontalLayout inset="26,0,26,0" height="26">
			          <Label name="api_test_status" padding="0,2,0,0" textpadding="2,0,0,0" text="" width="600" align="left" texttooltip="true" endellipsis="true" textcolor="#FF0055ab" font="5"></Label>
			      </HorizontalLayout>


						<HorizontalLayout height="38">
			        <Control width="198" />
			         <Button name="batch_apply_api_btn" padding="12,2,0,0" height="29" width="200" text="批量应用到..." textpadding="10,0,10,0" texttooltip="true" endellipsis="true" font="5" borderround="7,7" textcolor="#FFe6ebf2" hottextcolor="#FFFFFFFF" bkcolor="#FF006fdf" hotbkcolor="#ff0055ab" disabledtextcolor="#ffb1b1b1"	disabledbkcolor="#ffdce1e7"/>
      	       <Button name="confirm_api_btn" padding="12,2,0,0" height="29" width="200" text="确认" textpadding="10,0,10,0" texttooltip="true" endellipsis="true" font="5" borderround="7,7" textcolor="#FFe6ebf2" hottextcolor="#FFFFFFFF" bkcolor="#FF006fdf" hotbkcolor="#ff0055ab"	disabledtextcolor="#ffb1b1b1" disabledbkcolor="#ffdce1e7"/>
         			<Control width="26"/>
						</HorizontalLayout>

          </VerticalLayout>

				</TabLayout>


      </VerticalLayout>

  </TabLayout>

</HorizontalLayout>
</VerticalLayout>
</Window>
