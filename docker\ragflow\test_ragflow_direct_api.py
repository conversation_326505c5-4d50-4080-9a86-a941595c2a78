#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RAGFlow 直接 API 测试
绕过 MCP 协议，直接测试 RAGFlow 的知识库查询功能
"""

import json
import requests
import time
from typing import Dict, Any, Optional, List

class RAGFlowDirectAPITest:
    """RAGFlow 直接 API 测试"""
    
    def __init__(self, 
                 ragflow_url: str = "http://************:9380",
                 api_key: str = "ragflow-UzYTlhMjIwNjI0YzExZjA4NzUyMDI0Mm"):
        self.ragflow_url = ragflow_url.rstrip('/')
        self.api_key = api_key
        
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}",
            "User-Agent": "RAGFlow-Direct-API-Test/1.0",
            "Accept": "application/json"
        }
        
        print(f"🔗 RAGFlow 直接 API 测试")
        print(f"   RAGFlow 服务器: {self.ragflow_url}")
        print(f"   API密钥: {self.api_key[:20]}...")
    
    def test_ragflow_connectivity(self) -> bool:
        """测试 RAGFlow 主服务连通性"""
        print(f"\n🌐 测试 RAGFlow 主服务连通性...")
        
        try:
            # 测试主页
            response = requests.get(self.ragflow_url, timeout=10)
            print(f"   主页状态: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ RAGFlow 主服务正常")
                return True
            else:
                print(f"⚠️  RAGFlow 主服务响应异常: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ RAGFlow 主服务连接失败: {e}")
            return False
    
    def test_api_endpoints(self) -> Dict[str, Any]:
        """测试各种可能的 API 端点"""
        print(f"\n🔍 测试 RAGFlow API 端点...")
        
        # 常见的 RAGFlow API 端点
        endpoints = [
            "/api/v1/status",
            "/api/v1/health",
            "/api/v1/datasets",
            "/api/v1/chat",
            "/api/v1/retrieval",
            "/api/v1/conversation",
            "/v1/chat/completions",
            "/health",
            "/status"
        ]
        
        results = {}
        
        for endpoint in endpoints:
            try:
                url = f"{self.ragflow_url}{endpoint}"
                print(f"   测试: {endpoint}")
                
                # 尝试 GET 请求
                response = requests.get(url, headers=self.headers, timeout=5)
                results[endpoint] = {
                    "method": "GET",
                    "status_code": response.status_code,
                    "response": response.text[:200] if response.text else "Empty"
                }
                
                print(f"     GET {response.status_code}: {response.text[:50]}...")
                
                # 如果 GET 失败，尝试 POST
                if response.status_code >= 400:
                    post_response = requests.post(url, headers=self.headers, json={}, timeout=5)
                    if post_response.status_code != response.status_code:
                        results[f"{endpoint}_POST"] = {
                            "method": "POST",
                            "status_code": post_response.status_code,
                            "response": post_response.text[:200] if post_response.text else "Empty"
                        }
                        print(f"     POST {post_response.status_code}: {post_response.text[:50]}...")
                
            except Exception as e:
                results[endpoint] = {"error": str(e)}
                print(f"     错误: {e}")
        
        return results
    
    def test_chat_api(self, question: str) -> Optional[Dict[str, Any]]:
        """测试聊天 API"""
        print(f"\n💬 测试聊天 API: {question}")
        
        # 尝试不同的聊天 API 格式
        chat_endpoints = [
            "/api/v1/chat",
            "/v1/chat/completions",
            "/api/v1/conversation"
        ]
        
        # 不同的请求格式
        request_formats = [
            {
                "message": question,
                "conversation_id": "test-conversation"
            },
            {
                "messages": [{"role": "user", "content": question}],
                "model": "ragflow"
            },
            {
                "query": question,
                "stream": False
            },
            {
                "question": question
            }
        ]
        
        for endpoint in chat_endpoints:
            for i, request_data in enumerate(request_formats):
                try:
                    url = f"{self.ragflow_url}{endpoint}"
                    print(f"   尝试: {endpoint} (格式 {i+1})")
                    
                    response = requests.post(
                        url,
                        headers=self.headers,
                        json=request_data,
                        timeout=30
                    )
                    
                    print(f"     状态: {response.status_code}")
                    
                    if response.status_code == 200:
                        try:
                            result = response.json()
                            print(f"✅ 聊天 API 成功!")
                            print(f"     响应: {json.dumps(result, ensure_ascii=False, indent=2)[:300]}...")
                            return result
                        except:
                            print(f"     响应文本: {response.text[:200]}...")
                    else:
                        print(f"     错误: {response.text[:100]}...")
                        
                except Exception as e:
                    print(f"     异常: {e}")
        
        print("❌ 所有聊天 API 尝试都失败了")
        return None
    
    def test_retrieval_api(self, question: str) -> Optional[Dict[str, Any]]:
        """测试检索 API"""
        print(f"\n🔍 测试检索 API: {question}")
        
        retrieval_endpoints = [
            "/api/v1/retrieval",
            "/api/v1/search",
            "/api/v1/query"
        ]
        
        request_formats = [
            {
                "question": question,
                "top_k": 5
            },
            {
                "query": question,
                "limit": 5
            },
            {
                "text": question
            }
        ]
        
        for endpoint in retrieval_endpoints:
            for i, request_data in enumerate(request_formats):
                try:
                    url = f"{self.ragflow_url}{endpoint}"
                    print(f"   尝试: {endpoint} (格式 {i+1})")
                    
                    response = requests.post(
                        url,
                        headers=self.headers,
                        json=request_data,
                        timeout=30
                    )
                    
                    print(f"     状态: {response.status_code}")
                    
                    if response.status_code == 200:
                        try:
                            result = response.json()
                            print(f"✅ 检索 API 成功!")
                            print(f"     响应: {json.dumps(result, ensure_ascii=False, indent=2)[:300]}...")
                            return result
                        except:
                            print(f"     响应文本: {response.text[:200]}...")
                    else:
                        print(f"     错误: {response.text[:100]}...")
                        
                except Exception as e:
                    print(f"     异常: {e}")
        
        print("❌ 所有检索 API 尝试都失败了")
        return None
    
    def run_comprehensive_test(self) -> Dict[str, Any]:
        """运行综合测试"""
        print("🚀 开始 RAGFlow 直接 API 综合测试")
        print("=" * 60)
        
        results = {
            "connectivity": False,
            "endpoints": {},
            "chat_test": None,
            "retrieval_test": None
        }
        
        try:
            # 1. 连通性测试
            results["connectivity"] = self.test_ragflow_connectivity()
            
            if not results["connectivity"]:
                print("❌ RAGFlow 主服务不可达，跳过后续测试")
                return results
            
            # 2. 端点测试
            results["endpoints"] = self.test_api_endpoints()
            
            # 3. 聊天 API 测试
            test_question = "候鸟浏览器如何配置代理？"
            results["chat_test"] = self.test_chat_api(test_question)
            
            # 4. 检索 API 测试
            results["retrieval_test"] = self.test_retrieval_api(test_question)
            
            # 5. 生成报告
            self.generate_report(results)
            
            return results
            
        except KeyboardInterrupt:
            print(f"\n\n⏹️ 测试被用户中断")
            return results
        except Exception as e:
            print(f"\n❌ 测试过程中发生错误: {e}")
            return results
    
    def generate_report(self, results: Dict[str, Any]):
        """生成测试报告"""
        print(f"\n" + "=" * 60)
        print(f"📋 RAGFlow 直接 API 测试报告")
        print(f"=" * 60)
        
        print(f"🌐 连通性: {'✅ 正常' if results['connectivity'] else '❌ 失败'}")
        
        if results["endpoints"]:
            print(f"\n🔍 可用端点:")
            for endpoint, info in results["endpoints"].items():
                if "error" not in info and info.get("status_code", 0) < 400:
                    print(f"   ✅ {endpoint}: {info.get('status_code', 'N/A')}")
        
        if results["chat_test"]:
            print(f"\n💬 聊天 API: ✅ 可用")
        else:
            print(f"\n💬 聊天 API: ❌ 不可用")
        
        if results["retrieval_test"]:
            print(f"\n🔍 检索 API: ✅ 可用")
        else:
            print(f"\n🔍 检索 API: ❌ 不可用")
        
        print(f"\n💡 建议:")
        if not results["connectivity"]:
            print("   - 检查 RAGFlow 服务器状态")
            print("   - 确认服务器地址和端口")
        elif not results["chat_test"] and not results["retrieval_test"]:
            print("   - 检查 API 密钥是否正确")
            print("   - 确认 RAGFlow 版本和 API 格式")
            print("   - 查看 RAGFlow 文档了解正确的 API 端点")
        else:
            print("   - 可以使用直接 API 调用代替 MCP 协议")
            print("   - 考虑在 Wing 客户端中集成直接 API 调用")


def main():
    """主函数"""
    tester = RAGFlowDirectAPITest()
    results = tester.run_comprehensive_test()
    
    # 保存结果
    with open("ragflow_direct_api_test_results.json", "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 测试结果已保存到: ragflow_direct_api_test_results.json")


if __name__ == "__main__":
    main()
