#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DOCX文件重命名脚本
根据文件内容将英文文件名改为中文文件名
"""

import os
import re
from pathlib import Path
from docx import Document

def extract_title_from_docx(docx_path):
    """从DOCX文件中提取标题"""
    try:
        doc = Document(docx_path)
        
        # 尝试从前几个段落中找到标题
        for paragraph in doc.paragraphs[:5]:
            text = paragraph.text.strip()
            if text and len(text) > 3:
                # 清理标题文本
                title = text.replace('\n', ' ').replace('\r', ' ')
                title = re.sub(r'\s+', ' ', title)
                return title
        
        return None
        
    except Exception as e:
        print(f"❌ 读取文件失败: {docx_path} - {str(e)}")
        return None

def get_chinese_filename_mapping():
    """定义英文文件名到中文文件名的映射"""
    mapping = {
        'api-appendix.docx': 'API_附录（国家码、时区、语言、系统和分辨率）.docx',
        'api-browser.docx': 'API_环境开启关闭.docx',
        'api-code.docx': 'API_错误码对照表.docx',
        'api-example.docx': 'API_多种语言脚本示例.docx',
        'api-group.docx': 'API_分组管理.docx',
        'api-help.docx': 'API_使用须知.docx',
        'api-http.docx': 'API_HTTP模式说明.docx',
        'api-json.docx': 'API_JSON在线格式化工具.docx',
        'api-login.docx': 'API_帐号登录.docx',
        'api-members.docx': 'API_获取成员列表.docx',
        'api-plugin.docx': 'API_插件管理.docx',
        'api-postman-debug.docx': 'API_POSTMAN调试候鸟API接口.docx',
        'api-postman-example.docx': 'API_调试接口JSON数据官方更新下载.docx',
        'api-postman.docx': 'API_POSTMAN下载及安装.docx',
        'api-question.docx': 'API_常见问题.docx',
        'api-script.docx': 'API_脚本管理.docx',
        'api-session.docx': 'API_环境管理.docx',
        'api.docx': 'API_简介.docx'
    }
    return mapping

def analyze_file_content(docx_path):
    """分析文件内容，智能推断中文名称"""
    title = extract_title_from_docx(docx_path)
    filename = Path(docx_path).name
    
    if not title:
        return None
    
    # 根据标题内容智能推断中文名称
    title_lower = title.lower()
    
    if '附录' in title or 'appendix' in title_lower:
        return 'API_附录（国家码、时区、语言、系统和分辨率）.docx'
    elif '环境开启' in title or '环境关闭' in title or 'browser' in title_lower:
        return 'API_环境开启关闭.docx'
    elif '错误码' in title or 'code' in title_lower or '对照表' in title:
        return 'API_错误码对照表.docx'
    elif '脚本示例' in title or 'example' in title_lower:
        return 'API_多种语言脚本示例.docx'
    elif '分组管理' in title or 'group' in title_lower:
        return 'API_分组管理.docx'
    elif '使用须知' in title or 'help' in title_lower:
        return 'API_使用须知.docx'
    elif 'HTTP模式' in title or 'http' in title_lower:
        return 'API_HTTP模式说明.docx'
    elif 'JSON' in title or 'json' in title_lower:
        return 'API_JSON在线格式化工具.docx'
    elif '帐号登录' in title or '账号登录' in title or 'login' in title_lower:
        return 'API_帐号登录.docx'
    elif '成员列表' in title or 'members' in title_lower:
        return 'API_获取成员列表.docx'
    elif '插件管理' in title or 'plugin' in title_lower:
        return 'API_插件管理.docx'
    elif 'POSTMAN调试' in title or 'postman-debug' in filename:
        return 'API_POSTMAN调试候鸟API接口.docx'
    elif 'JSON数据' in title or 'postman-example' in filename:
        return 'API_调试接口JSON数据官方更新下载.docx'
    elif 'POSTMAN下载' in title or 'postman.docx' in filename:
        return 'API_POSTMAN下载及安装.docx'
    elif '常见问题' in title or 'question' in title_lower:
        return 'API_常见问题.docx'
    elif '脚本管理' in title or 'script' in title_lower:
        return 'API_脚本管理.docx'
    elif '环境管理' in title or 'session' in title_lower:
        return 'API_环境管理.docx'
    elif 'api.docx' in filename:
        return 'API_简介.docx'
    else:
        # 如果无法智能推断，使用原标题
        clean_title = re.sub(r'[<>:"/\\|?*]', '_', title)
        return f'API_{clean_title}.docx'

def rename_docx_files(docx_dir):
    """重命名目录中的所有DOCX文件"""
    docx_path = Path(docx_dir)
    docx_files = list(docx_path.glob("*.docx"))
    
    if not docx_files:
        print(f"❌ 在目录 {docx_dir} 中没有找到DOCX文件")
        return
    
    print(f"📁 找到 {len(docx_files)} 个DOCX文件")
    print("🔄 开始重命名...")
    print("=" * 80)
    
    # 获取预定义的映射
    predefined_mapping = get_chinese_filename_mapping()
    
    success_count = 0
    
    for docx_file in sorted(docx_files):
        old_filename = docx_file.name
        
        # 首先尝试使用预定义映射
        if old_filename in predefined_mapping:
            new_filename = predefined_mapping[old_filename]
        else:
            # 如果没有预定义映射，尝试智能分析
            new_filename = analyze_file_content(str(docx_file))
            if not new_filename:
                print(f"⚠️  跳过: {old_filename} (无法确定中文名称)")
                continue
        
        new_filepath = docx_path / new_filename
        
        # 检查新文件名是否已存在
        if new_filepath.exists() and new_filepath != docx_file:
            print(f"⚠️  跳过: {old_filename} -> {new_filename} (目标文件已存在)")
            continue
        
        try:
            # 重命名文件
            docx_file.rename(new_filepath)
            print(f"✅ {old_filename}")
            print(f"   -> {new_filename}")
            success_count += 1
            
        except Exception as e:
            print(f"❌ 重命名失败: {old_filename} - {str(e)}")
    
    print("=" * 80)
    print(f"🎉 重命名完成! 成功: {success_count}/{len(docx_files)}")

def preview_rename_plan(docx_dir):
    """预览重命名计划"""
    docx_path = Path(docx_dir)
    docx_files = list(docx_path.glob("*.docx"))
    
    if not docx_files:
        print(f"❌ 在目录 {docx_dir} 中没有找到DOCX文件")
        return
    
    print(f"📁 找到 {len(docx_files)} 个DOCX文件")
    print("👀 重命名预览:")
    print("=" * 80)
    
    predefined_mapping = get_chinese_filename_mapping()
    
    for docx_file in sorted(docx_files):
        old_filename = docx_file.name
        
        if old_filename in predefined_mapping:
            new_filename = predefined_mapping[old_filename]
        else:
            new_filename = analyze_file_content(str(docx_file))
            if not new_filename:
                new_filename = "❓ 无法确定"
        
        print(f"📄 {old_filename}")
        print(f"   -> {new_filename}")
        print()

if __name__ == "__main__":
    docx_directory = r"F:\augment\output\docx_files"
    
    print("📝 DOCX文件重命名工具")
    print(f"📂 目录: {docx_directory}")
    print()
    
    # 先预览重命名计划
    print("=" * 60)
    print("第一步：预览重命名计划")
    print("=" * 60)
    preview_rename_plan(docx_directory)
    
    # 执行重命名
    print("=" * 60)
    print("第二步：执行重命名")
    print("=" * 60)
    rename_docx_files(docx_directory)
