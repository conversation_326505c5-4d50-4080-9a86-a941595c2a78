#!/bin/bash

# RAGFlow完整备份脚本
# 用途: 备份RAGFlow的所有数据，包括配置、Docker镜像、数据卷
# 作者: RAGFlow迁移助手
# 日期: $(date +%Y-%m-%d)

set -e  # 遇到错误立即退出

# 配置变量
BACKUP_BASE_DIR="/mnt/ragflow/temp3"
BACKUP_DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="$BACKUP_BASE_DIR/ragflow-backup-$BACKUP_DATE"
RAGFLOW_DIR="/mnt/ragflow"
DOCKER_COMPOSE_DIR="$RAGFLOW_DIR/ragflow/docker"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查函数
check_prerequisites() {
    log_info "检查备份前置条件..."
    
    # 检查是否为root用户
    if [ "$EUID" -ne 0 ]; then
        log_error "请使用root用户运行此脚本"
        exit 1
    fi
    
    # 检查Docker是否运行
    if ! systemctl is-active --quiet docker; then
        log_error "Docker服务未运行"
        exit 1
    fi
    
    # 检查RAGFlow目录是否存在
    if [ ! -d "$RAGFLOW_DIR" ]; then
        log_error "RAGFlow目录不存在: $RAGFLOW_DIR"
        exit 1
    fi
    
    # 检查docker-compose.yml是否存在
    if [ ! -f "$DOCKER_COMPOSE_DIR/docker-compose.yml" ]; then
        log_error "docker-compose.yml不存在: $DOCKER_COMPOSE_DIR/docker-compose.yml"
        exit 1
    fi
    
    # 检查磁盘空间
    AVAILABLE_SPACE=$(df $BACKUP_BASE_DIR | tail -1 | awk '{print $4}')
    if [ "$AVAILABLE_SPACE" -lt 10485760 ]; then  # 10GB in KB
        log_warning "可用磁盘空间不足10GB，备份可能失败"
        read -p "是否继续? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    log_success "前置条件检查通过"
}

# 创建备份目录
create_backup_dir() {
    log_info "创建备份目录: $BACKUP_DIR"
    mkdir -p "$BACKUP_DIR"
    log_success "备份目录创建完成"
}

# 停止RAGFlow服务
stop_ragflow() {
    log_info "停止RAGFlow服务..."
    cd "$DOCKER_COMPOSE_DIR"
    
    if docker-compose ps | grep -q "Up"; then
        docker-compose down
        log_success "RAGFlow服务已停止"
    else
        log_info "RAGFlow服务未运行"
    fi
}

# 备份配置文件
backup_config() {
    log_info "备份RAGFlow配置文件..."

    # 显示目录大小
    RAGFLOW_SIZE=$(du -sh "$RAGFLOW_DIR" | cut -f1)
    log_info "RAGFlow目录大小: $RAGFLOW_SIZE"

    # 备份整个ragflow目录，排除备份目录自身和其他不必要的文件
    log_info "开始打包配置文件..."
    tar czf "$BACKUP_DIR/ragflow-config.tar.gz" \
      --exclude='*/temp3/*' \
      --exclude='*/logs/*' \
      --exclude='*/data/*' \
      --exclude='*/.git/*' \
      --exclude='*/node_modules/*' \
      --exclude='*/cache/*' \
      --exclude='*/tmp/*' \
      --exclude='*/__pycache__/*' \
      --exclude='*.log' \
      --exclude='*.tar.gz' \
      --exclude='*.tar' \
      -C "$(dirname $RAGFLOW_DIR)" "$(basename $RAGFLOW_DIR)"

    # 单独备份重要配置文件
    cp "$DOCKER_COMPOSE_DIR/docker-compose.yml" "$BACKUP_DIR/"
    [ -f "$DOCKER_COMPOSE_DIR/.env" ] && cp "$DOCKER_COMPOSE_DIR/.env" "$BACKUP_DIR/"

    log_success "配置文件备份完成: $(du -sh $BACKUP_DIR/ragflow-config.tar.gz | cut -f1)"
}

# 备份Docker镜像
backup_docker_images() {
    log_info "备份Docker镜像..."

    # 设置Docker临时目录到我们的备份空间
    export DOCKER_TMPDIR="$BACKUP_DIR/docker-tmp"
    mkdir -p "$DOCKER_TMPDIR"

    # 获取RAGFlow相关镜像
    IMAGES=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep -E "(ragflow|mysql|minio|elasticsearch|valkey)" | tr '\n' ' ')

    if [ -n "$IMAGES" ]; then
        log_info "备份镜像: $IMAGES"
        log_info "使用临时目录: $DOCKER_TMPDIR"

        # 设置Docker使用我们的临时目录
        DOCKER_TMPDIR="$DOCKER_TMPDIR" docker save $IMAGES -o "$BACKUP_DIR/ragflow-images.tar"

        # 清理临时目录
        rm -rf "$DOCKER_TMPDIR"

        log_success "Docker镜像备份完成: $(du -sh $BACKUP_DIR/ragflow-images.tar | cut -f1)"
    else
        log_warning "未找到RAGFlow相关镜像"
    fi
}

# 备份Docker数据卷
backup_docker_volumes() {
    log_info "备份Docker数据卷..."

    # 获取RAGFlow相关数据卷
    VOLUMES=$(docker volume ls -q | grep ragflow)

    if [ -n "$VOLUMES" ]; then
        for volume in $VOLUMES; do
            log_info "备份数据卷: $volume"

            # 检查数据卷是否有数据
            VOLUME_SIZE=$(docker run --rm -v $volume:/data alpine du -s /data | cut -f1)

            if [ "$VOLUME_SIZE" -gt 0 ]; then
                # 特别检查MinIO数据卷中的文档
                if [[ "$volume" == *"minio"* ]]; then
                    log_info "检查MinIO数据卷中的文档..."
                    DOC_COUNT=$(docker run --rm -v $volume:/data alpine find /data -type f \( -name "*.pdf" -o -name "*.doc*" -o -name "*.txt" -o -name "*.md" -o -name "*.ppt*" -o -name "*.xls*" \) | wc -l)
                    TOTAL_FILES=$(docker run --rm -v $volume:/data alpine find /data -type f | wc -l)
                    log_info "MinIO数据卷统计: 总文件数=$TOTAL_FILES, 文档文件数=$DOC_COUNT"
                fi

                # 使用我们的临时目录作为Docker容器的临时空间
                docker run --rm \
                    -v $volume:/source \
                    -v "$BACKUP_DIR":/backup \
                    --tmpfs /tmp:size=1G \
                    alpine tar czf /backup/${volume}.tar.gz -C /source .

                BACKUP_SIZE=$(du -sh "$BACKUP_DIR/${volume}.tar.gz" | cut -f1)
                log_success "数据卷 $volume 备份完成: $BACKUP_SIZE"

                # 验证备份文件
                if [ -f "$BACKUP_DIR/${volume}.tar.gz" ]; then
                    BACKUP_FILE_SIZE=$(stat -c%s "$BACKUP_DIR/${volume}.tar.gz" 2>/dev/null || stat -f%z "$BACKUP_DIR/${volume}.tar.gz" 2>/dev/null)
                    if [ "$BACKUP_FILE_SIZE" -lt 1000 ]; then
                        log_warning "备份文件 $volume.tar.gz 可能有问题 (大小: ${BACKUP_FILE_SIZE}字节)"
                    fi
                fi
            else
                log_warning "数据卷 $volume 为空，创建空备份文件"
                touch "$BACKUP_DIR/${volume}.tar.gz"
            fi
        done
    else
        log_warning "未找到RAGFlow相关数据卷"
    fi
}

# 备份数据库
backup_database() {
    log_info "备份MySQL数据库..."

    # 检查MySQL容器是否存在
    if docker ps -a | grep -q ragflow-mysql; then
        # 临时启动MySQL容器进行备份
        log_info "启动MySQL容器进行数据库备份..."

        # 创建临时目录给MySQL容器使用
        MYSQL_TMPDIR="$BACKUP_DIR/mysql-tmp"
        mkdir -p "$MYSQL_TMPDIR"

        docker run -d --name temp-ragflow-mysql \
            -v docker_ragflow-mysql-data:/var/lib/mysql \
            -v "$MYSQL_TMPDIR":/tmp \
            -e MYSQL_ROOT_PASSWORD=infiniflow \
            mysql:8.0.39 >/dev/null

        # 等待MySQL启动
        sleep 30

        # 备份数据库
        if docker exec temp-ragflow-mysql mysqldump -u root -pinfiniflow rag_flow > "$BACKUP_DIR/ragflow-database.sql" 2>/dev/null; then
            log_success "数据库备份完成: $(du -sh $BACKUP_DIR/ragflow-database.sql | cut -f1)"
        else
            log_warning "数据库备份失败，可能是密码错误或数据库不存在"
        fi

        # 清理临时容器和目录
        docker stop temp-ragflow-mysql >/dev/null 2>&1
        docker rm temp-ragflow-mysql >/dev/null 2>&1
        rm -rf "$MYSQL_TMPDIR"
    else
        log_warning "未找到MySQL容器，跳过数据库备份"
    fi
}

# 创建备份信息文件
create_backup_info() {
    log_info "创建备份信息文件..."
    
    cat > "$BACKUP_DIR/backup-info.txt" << EOF
RAGFlow备份信息
===============
备份时间: $(date)
源服务器: $(hostname -I | awk '{print $1}')
备份脚本版本: 1.0
RAGFlow安装路径: $RAGFLOW_DIR

系统信息:
- 操作系统: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)
- 内核版本: $(uname -r)
- Docker版本: $(docker --version)
- Docker Compose版本: $(docker-compose --version 2>/dev/null || echo "未安装")

备份内容:
- 配置文件: ragflow-config.tar.gz
- Docker镜像: ragflow-images.tar
- 数据卷备份: $(ls $BACKUP_DIR/*-data.tar.gz 2>/dev/null | wc -l) 个文件
- 数据库备份: $([ -f "$BACKUP_DIR/ragflow-database.sql" ] && echo "是" || echo "否")

Docker镜像列表:
$(docker images --format "{{.Repository}}:{{.Tag}} {{.Size}}" | grep -E "(ragflow|mysql|minio|elasticsearch|valkey)")

Docker数据卷列表:
$(docker volume ls | grep ragflow)

MinIO文档统计:
$(docker run --rm -v docker_ragflow-minio-data:/data alpine sh -c "
echo '总文件数:' \$(find /data -type f | wc -l)
echo '文档文件数:' \$(find /data -type f \( -name '*.pdf' -o -name '*.doc*' -o -name '*.txt' -o -name '*.md' -o -name '*.ppt*' -o -name '*.xls*' \) | wc -l)
echo '数据卷大小:' \$(du -sh /data | cut -f1)
echo '文档类型分布:'
find /data -type f | sed 's/.*\.//' | sort | uniq -c | sort -nr | head -5
" 2>/dev/null || echo "无法获取MinIO统计信息")

备份文件大小:
$(du -sh $BACKUP_DIR/* 2>/dev/null || echo "无备份文件")

API配置:
- API密钥: ragflow-UzYTlhMjIwNjI0YzExZjA4NzUyMDI0Mm
- MCP Server: 已启用
- 端口配置: 80, 443, 9380, 9382

注意事项:
1. 恢复时请确保目标服务器有足够的磁盘空间
2. 恢复前请安装Docker和Docker Compose
3. 如有端口冲突，请修改docker-compose.yml中的端口映射
4. 恢复后需要修改IP地址配置
EOF
    
    log_success "备份信息文件创建完成"
}

# 创建最终备份包
create_final_backup() {
    log_info "创建最终备份包..."

    cd "$BACKUP_BASE_DIR"
    FINAL_BACKUP="ragflow-complete-backup-$BACKUP_DATE.tar.gz"

    tar czf "$FINAL_BACKUP" "$(basename $BACKUP_DIR)"

    # 计算备份包大小和MD5
    BACKUP_SIZE=$(du -sh "$FINAL_BACKUP" | cut -f1)
    BACKUP_MD5=$(md5sum "$FINAL_BACKUP" | cut -d' ' -f1)

    log_success "最终备份包创建完成:"
    echo "  文件名: $FINAL_BACKUP"
    echo "  大小: $BACKUP_SIZE"
    echo "  MD5: $BACKUP_MD5"
    echo "  路径: $BACKUP_BASE_DIR/$FINAL_BACKUP"

    # 创建一个符号链接到根目录方便访问
    ln -sf "$BACKUP_BASE_DIR/$FINAL_BACKUP" "/tmp/ragflow-latest-backup.tar.gz"
    log_info "创建快捷链接: /tmp/ragflow-latest-backup.tar.gz"
}

# 重启RAGFlow服务
restart_ragflow() {
    log_info "重启RAGFlow服务..."
    cd "$DOCKER_COMPOSE_DIR"
    docker-compose up -d
    log_success "RAGFlow服务已重启"
}

# 清理临时文件
cleanup() {
    log_info "清理临时备份目录..."
    rm -rf "$BACKUP_DIR"
    log_success "清理完成"
}

# 主函数
main() {
    echo "========================================"
    echo "       RAGFlow完整备份脚本"
    echo "========================================"
    echo "备份时间: $(date)"
    echo "备份目录: $BACKUP_DIR"
    echo "========================================"
    
    # 执行备份步骤
    check_prerequisites
    create_backup_dir
    stop_ragflow
    backup_config
    backup_docker_images
    backup_docker_volumes
    backup_database
    create_backup_info
    create_final_backup
    restart_ragflow
    cleanup
    
    echo "========================================"
    log_success "RAGFlow备份完成！"
    echo "========================================"
    echo "备份文件: $BACKUP_BASE_DIR/ragflow-complete-backup-$BACKUP_DATE.tar.gz"
    echo "快捷链接: /tmp/ragflow-latest-backup.tar.gz"
    echo ""
    echo "传输命令示例:"
    echo "scp $BACKUP_BASE_DIR/ragflow-complete-backup-$BACKUP_DATE.tar.gz root@58.49.146.17:/mnt/ragflow-backup/"
    echo "或使用快捷链接:"
    echo "scp /tmp/ragflow-latest-backup.tar.gz root@58.49.146.17:/mnt/ragflow-backup/"
    echo ""
    echo "备份目录: $BACKUP_BASE_DIR"
    echo "========================================"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
