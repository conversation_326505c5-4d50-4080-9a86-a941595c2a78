﻿<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Window size="1500,590" sizebox="4,4,4,4" caption="0,0,0,50" mininfo="953,590" roundcorner="5,5,5,5" showshadow="true" shadowsize="3" shadowposition="0,0" shadowcolor="#ff999999" fademode="true">
  <Include source="Default.xml" />

  <VerticalLayout width="1200" height="590" bkcolor="#FFf5f7f8">
    <HorizontalLayout height="37">

    	 <Button name="login" bkimage="icon.png" padding="8,6,0,0" width="28" height="28"/>
      <Label name="session_list_title" padding="6,4,0,0" text="本地帐户批量导入环境管理器" width="400" textcolor="#FF616161" hottextcolor="#ff000000" align="left" font="8"></Label>

      <Control />
      <Button text="返回上一层/重新导入" visible="false" padding="0,6,0,0" name="back" tooltip="返回上一层" width="320" height="30" textcolor="#ff000000" disabledtextcolor="#FFB3B3B3" align="center" font="14"	bkimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
      <Button name="minbtn" width="28" height="26" tooltip="最小化" normalimage="minbtn.png" hotimage="minbtn_hover.png" pushedimage="minbtnpush.png" />
      <Button name="maxbtn" width="28" height="26" tooltip="最大化" normalimage="maxbtn.png" hotimage="maxbtn_hover.png" pushedimage="maxbtnpush.png" />
      <Button name="restorebtn" visible="false" width="28" height="26" tooltip="还原" normalimage="restorebtn.png" hotimage="restorebtn_hover.png" pushedimage="restorebtnpush.png" />
      <Button name="closebtn" width="28" height="26"  tooltip="关闭" normalimage="closebtn.png" hotimage="closebtn_hover.png" pushedimage="closebtnpush.png" />

    </HorizontalLayout>
  <HorizontalLayout name="bkground">


 <HorizontalLayout name="loading_data" height="590" bkcolor="#ffffffffff" visible="true">

	    <VerticalLayout height="590" >

					     <HorizontalLayout name="loading_data" height="40" padding="60,10,0,0" textsetpos="true">
                 <GifAnim name="tipgif" visible="true" bkimage="tips.gif" height="28" width="28" padding="0,4,0,0" auto="true" align="center"/>
                 <Label name="msg1" text="请点击打开" autocalcwidth="true" textcolor="#FF616161" hottextcolor="#ff000000" align="center"></Label>
                 <Button text="环境导入模板(.xlsx)" padding="10,4,0,0" textpadding="20,1,0,0" name="downxls" width="138" height="30" textcolor="#FF000000" disabledtextcolor="#FFB3B3B3" align="center" font="21" bkimage="file=&apos;excel_btn_normal.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;excel_btn_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;excel_btn_Click.png&apos; corner=&apos;5,10,5,10&apos;"/>
                 <Label name="msg2" text="并按照模板里的要求填写后再导入" padding="10,0,0,0" width="480" textcolor="#FF616161" hottextcolor="#ff000000" align="left"></Label>
					     </HorizontalLayout>


					     <HorizontalLayout height="300">
					    	 <Control />
                 <VerticalLayout width="400">
                   <Control />
                   <VerticalLayout height="180">
                   	 <HorizontalLayout width="400">
                   	 	 <Control />
                       <Label bkimage="upload.png" width="100" height="100" align="center" ></Label>
                       <Control />
                     </HorizontalLayout>
                     <Label name="msg7" text="选择本地账户数据文件(.xlsx)" padding="0,10,0,0" width="400" textcolor="#FF696969" hottextcolor="#ff000000" align="center"></Label>
                   <Button text="点击开始导入" name="open" padding="0,10,0,0" textpadding="0,-4,0,0" height="40" bkcolor="#ff4b8cdf" hotbkcolor="#ffa2c1eb" hottextcolor="#ff00368f" textcolor="#FFFFFFFF" disabledtextcolor="#FFB3B3B3" align="center" font="18" borderround="10,10"/>
                   </VerticalLayout>
                   <Control />
                 </VerticalLayout>
					    	 <Control />
					     </HorizontalLayout>
<HorizontalLayout height="60">
	</HorizontalLayout>
              <VerticalLayout height="70" padding="60,0,0,0">

					    	<Label name="msg3" text="导入数据温馨提示：" width="953" textcolor="#FF616161" hottextcolor="#ff000000" align="left" font="9"></Label>
                <HorizontalLayout height="24" textsetpos="true">
                  <Label name="msg4" textpadding="20,0,0,0" text="" width="910" textcolor="#FF616161" hottextcolor="#ff000000" align="left" font="0" endellipsis="true"></Label>
                  <Button text="查看帮助" padding="0,0,0,0" name="help" width="158" height="24" textcolor="#FF608af3" disabledtextcolor="#FFB3B3B3" align="center" font="21" endellipsis="true" />
                </HorizontalLayout>
                <HorizontalLayout height="24">
                <Label name="msg5" textpadding="20,0,0,0" text="" width="706" textcolor="#FF616161" hottextcolor="#ff000000" align="left" font="0" endellipsis="true"></Label>
                <Label name="msg6" textpadding="0,0,0,0" text="" width="60" textcolor="#FF608af3" hottextcolor="#ff000000" align="left" font="0"></Label>
                </HorizontalLayout>
					     </VerticalLayout>

      </VerticalLayout>

 </HorizontalLayout>

		<VerticalLayout name="data" visible="false">

			<HorizontalLayout height="56" >
			    	 <VerticalLayout width="440">
               <Combo name="searchlist" reselect="true" dropboxsize="0,450" bordersize="0" padding="21,10,0,10" width="420" height="36" borderround="5,5" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="4,2,0,0" bkcolor="#ffdce1e7"
														normalimage="file='Profile\Setting_Combox_Normal_big_large.png' corner='5,5,30,5'" hotimage="file='Profile\Setting_Combox_Hover_large.png' corner='5,5,30,5'" pushedimage="file='Profile\Setting_Combox_Click_large.png' corner='5,5,25,10'"
												combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,30,5'"
												itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0">
               </Combo>
				         <RichEdit name="session_search" pos="20,10,0,10" height="36" width="378" tipvaluecolor="#FF333333" borderround="3,3" bkcolor="#ffdce1e7" font="8" textpadding="10,8,20,0" maxchar="300" tipvalue="请输入关键字查找.." multiline="false" textcolor="#ff333333" rich="false" transparent="false" float="true">
				      </RichEdit>
				     </VerticalLayout>
				     <VerticalLayout width="30">
				        <CheckBox name="opt_searchl" selected="false"  visible="true" padding="10,26,0,0" height="18" width="16" normalimage="file='list_check_normal.png'" hotimage="list_check_hover.png" selectedimage="file='list_checked_actvice_normal.png'" ></CheckBox>
				     </VerticalLayout>
				     <VerticalLayout width="320">
				        <Label name="searchl" padding="4,16,0,0" text="搜索包含在列表中各环境项关键词" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" align="left" font="16"></Label>
				     </VerticalLayout>

        <HorizontalLayout name="importedarea" width="220" visible="false">
            <VerticalLayout width="30">
              <CheckBox name="opt_showf" selected="false" padding="10,26,0,0" height="18" width="16" normalimage="file='list_check_normal.png'" hotimage="list_check_hover.png" selectedimage="file='list_checked_actvice_normal.png'" ></CheckBox>
            </VerticalLayout>
            <VerticalLayout >
              <Label name="showfl" padding="4,25,0,0" text="仅显示导入失败数据" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" align="left" font="16"></Label>
            </VerticalLayout>
          </HorizontalLayout>

        <Control />
        <VerticalLayout width="260">
          <Combo name="importtype" reselect="true" bordersize="0" padding="0,12,0,10" width="250" height="36" borderround="3,3" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" bkcolor="#ffdce1e7"
              normalimage="file='Profile\Proxy_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\proxy_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\proxy_Combox_Click.png' corner='5,5,25,10'"
              combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
              itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0" textpadding="0,0,20,0" endellipsis="true">
          </Combo>
        </VerticalLayout>
            <!--<VerticalLayout width="230">
              <Combo name="agent" bordersize="0" padding="21,0,0,10" width="200" height="36" borderround="3,3" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="4,2,0,0" bkcolor="#ffdce1e7"
                   normalimage="file='Profile\Setting_Combox_Normal_big_large.png' corner='5,5,25,10'" hotimage="file='Profile\Setting_Combox_Hover_large.png' corner='5,5,25,10'" pushedimage="file='Profile\Setting_Combox_Click_large.png' corner='5,5,25,10'"
               combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
               itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0">
              </Combo>
            </VerticalLayout>-->
      </HorizontalLayout>


      <HorizontalLayout bkcolor="#FFffffff">
        	<List name="list_session_manager" scrollwheel="true" reselect="true" bordersize="1,1,1,1" itembkcolor="#FFffffff" itemselectedbkcolor="#FFe0e0e0" itemhotbkcolor="#FFf2f2f2" bordercolor="#FFD9DADB" vscrollbar="true">
						<ListHeader height="36" bordersize="1" bordercolor="#FFD7D7D7" bkcolor="#FFF9F9FA">
							<ListHeaderItem text="操作" name="header_device_choice" width="60" align="left" textpadding="20,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="环境名称" name="header_name" width="180" align="left" textpadding="20,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="备注" name="header_desc" width="120" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <!--<ListHeaderItem text="分组" name="header_group" width="100" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>-->
              <ListHeaderItem text="代理类型" name="header_proxytype" width="120" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <!--<ListHeaderItem text="使用方式" name="header_usetype" width="120" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>-->
              <ListHeaderItem text="代理主机" name="header_proxy" width="120" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="代理端口" name="header_proxyport" width="70" align="center" textpadding="0,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="代理账号" name="header_proxyuser" width="120" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="代理密码" name="header_proxypass" width="120" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="国家/地区" name="header_area" width="110" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="账户名称" name="header_username" width="100" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="账户" name="header_user" width="90" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="密码" name="header_pass" width="90" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="报告" name="header_checkstatus" width="100" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="导入状态" name="header_status" width="200" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
						</ListHeader>

          </List>
				</HorizontalLayout>
		</VerticalLayout>

	</HorizontalLayout>
    <HorizontalLayout name="btnarea1" height="52" bkcolor="#ffe9e9e9" inset="20,0,0,0">
      <HorizontalLayout name="btnarea" visible="false">

      <CheckBox name="opt_checkAll" text="" textpadding="77,1,0,0" selected="false"  visible="true" padding="3,16,0,0" height="18" width="16" normalimage="file='list_check_normal.png'" hotimage="list_check_hover.png" selectedimage="file='list_checked_actvice_normal.png'" ></CheckBox>
        <Label name="totalmsg" padding="50,0,0,0" text="" width="180" textcolor="#FF000000" hottextcolor="#ff000000" align="left" font="16"></Label>
         <Control width="100" />

        <!--<VerticalLayout width="180">
          <Control />
          <Button text="批量自定义环境名称" name="renames" enabled="false" tooltip="批量自定义环境名称" padding="20,2,0,0" width="160" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	bkimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
          <Control />
        </VerticalLayout>-->
        <HorizontalLayout width="290" padding="0,16,0,0" height="20" >
          <Button name="checkproxys" padding="16,1,0,0" align="left" height="20" width="124" text="批量检测代理" font="23" hottextcolor="#FF005ed3" />
          <Label name="checkproxysl" text="超时设定：" padding="4,0,0,0" width="66" align="left" font="5"></Label>
          <Edit name="timeout" padding="0,0,0,0" text="5" align="center" height="20" width="52" tipvaluecolor="#FF000000" nativebkcolor="#FFffffff" borderround="3,3" bkcolor="#ffffffff" font="8" textpadding="0,0,0,0" tipvalue="5" maxchar="10" multiline="false" textcolor="#ff000000" rich="false" transparent="false" />
          <Label name="checkproxysl2" text="秒" padding="10,1,0,0" width="12" align="left" font="5"></Label>
        </HorizontalLayout>

        <VerticalLayout width="240">
          <Control />
          <Button text="环境分组管理器" name="groupmgr" tooltip="环境分组管理器" padding="0,2,0,0" width="240" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	bkimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
          <Control />
        </VerticalLayout>

        <HorizontalLayout width="270">
        <Control />
        <VerticalLayout width="250">
          <Combo name="group" bordersize="0" padding="0,10,0,10" width="250" height="36" borderround="3,3" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" bkcolor="#ffdce1e7"
              normalimage="file='Profile\Proxy_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\proxy_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\proxy_Combox_Click.png' corner='5,5,25,10'"
              combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
              itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0" textpadding="0,0,24,0" endellipsis="true">
          </Combo>
        </VerticalLayout>
        <Control />
      </HorizontalLayout>

        <VerticalLayout width="300">
          <Control />
          <Button text="导入到指定环境分组" name="import" tooltip="导入到指定环境分组" enabled="false" padding="0,2,0,0" width="300" height="30" textcolor="#FFE55D16" disabledtextcolor="#FFB3B3B3" align="center" font="14"	bkimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
          <Control />
        </VerticalLayout>

      <Control />
      </HorizontalLayout>
    </HorizontalLayout>
    <Control name="dragicon" float="true" width="14" height="16" bkimage="dragicon.png"/>
  </VerticalLayout>
</Window>
