﻿<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Window size="500,200" caption="0,0,0,80" roundcorner="5,5,5,5" showshadow="true" shadowsize="3" shadowposition="0,0" shadowcolor="#ff6272A4" fademode="true">
	<Include source="Default.xml" />

  <VerticalLayout width="953" height="590" bkcolor="#FF282A36">

    <HorizontalLayout height="37">

    	 <Button name="login" bkimage="icon.png" padding="8,6,0,0" width="28" height="28"/>
      <Label name="title" padding="6,4,0,0" text="批量自定义环境名称" width="180" textcolor="#FF616161" hottextcolor="#ff000000" align="left" font="8"></Label>

      <Control />
      <Button name="minbtn" width="28" height="26" tooltip="最小化" normalimage="minbtn.png" hotimage="minbtn_hover.png" pushedimage="minbtnpush.png" />
      <Button name="closebtn" width="28" height="26"  tooltip="关闭" normalimage="closebtn.png" hotimage="closebtn_hover.png" pushedimage="closebtnpush.png" />

    </HorizontalLayout>

  <HorizontalLayout name="bkground" visible="true">

		<VerticalLayout height="603" padding="20,6,20,0">

      <!--<HorizontalLayout height="26">
        <VerticalLayout maxwidth="220">
          <Label name="lname" padding="22,6,0,0" text="请输入环境名称" maxwidth="180"   textcolor="#FF333333" hottextcolor="ffFF0000" font="8"></Label>
        </VerticalLayout>
      </HorizontalLayout>-->

      <HorizontalLayout height="34" >

        <VerticalLayout width="215" height="34">
          <Label name="system_pad" padding="1,6,0,0" text="系统" width="200"   textcolor="#FF333333" hottextcolor="ffFF0000" font="8"></Label>
        </VerticalLayout>
        <VerticalLayout width="290" height="34">
          <Label name="reso_pad"  textpadding="35,6,10,0" text="分辨率" texttooltip="true" endellipsis="true" width="260"   textcolor="#FF333333" hottextcolor="ffFF0000" font="8"></Label>
        </VerticalLayout>

      </HorizontalLayout>

      <HorizontalLayout height="36" >
        <VerticalLayout width="240">
          <Combo name="system" dropboxsize="0,600" bordersize="0" padding="0,0,0,10" width="220" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="0,0,0,0" bkcolor="#ffdce1e7"
              normalimage="file='Profile\Proxy_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\proxy_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\proxy_Combox_Click.png' corner='5,5,25,10'"
              combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
              itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0" >

          </Combo>
        </VerticalLayout>
        <VerticalLayout width="10"/>
        <VerticalLayout width="240">
          <Combo name="reso" dropboxsize="0,600" bordersize="0" padding="0,0,0,10" width="210" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="0,0,0,0" bkcolor="#ffdce1e7"
          normalimage="file='Profile\Proxy_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\proxy_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\proxy_Combox_Click.png' corner='5,5,25,10'"
          combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
          itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0">
          </Combo>
          <Edit name="resotext" pos="0,0,0,10" height="36" width="178" tipvaluecolor="#FF5f5f5f" nativebkcolor="#FFDCE1E7" borderround="7,7" bkcolor="#ffdce1e7" font="0" textpadding="10,0,0,0" maxchar="6000" multiline="false" textcolor="#ff333333" rich="false" transparent="false" float="true"/>
        </VerticalLayout>
      </HorizontalLayout>


      <HorizontalLayout inset="0,0,0,0" height="373">

				</HorizontalLayout>






		</VerticalLayout>


	</HorizontalLayout>

	  <HorizontalLayout inset="0,0,0,0" height="33" visible="false">
        	  <Label name="confignamearea_tiptitle1" padding="12,6,0,0" text="温馨提示：新环境名称不允许设定与已有的环境存在相同的名称。" width="480" textcolor="#FFcdcdcd" hottextcolor="#ff000000" font="8"></Label>
				</HorizontalLayout>

    <HorizontalLayout height="52" inset="20,0,0,0" bkcolor="#ffe9e9e9">
         <Control />
      <!--<VerticalLayout width="420">

       </VerticalLayout>-->
        <VerticalLayout width="170">
      		<Control />
          <Button text="确定" name="btnok"   padding="16,2,0,0"  width="120" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	bkimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
          <Control />
       </VerticalLayout>

<Control />
    </HorizontalLayout>
  </VerticalLayout>
</Window>
