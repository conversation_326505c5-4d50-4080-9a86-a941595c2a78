第三十八章
候鸟单机运行体系（新增体系）

客户端单机离线安全运行模式

第三十八章
候鸟单机运行体系（新增体系）

客户端单机离线安全运行模式

依据传统，具体工作步骤、顺序安排，人员分配在工作群里具体阐述。

综述：

由于市场上近期出现同行产品“比特指纹浏览器”服务器/客户端被攻破导致其公司客户群体数百万美金与相关资金被盗的严重先例，导致全国各行业客户对自已的数据安全高度敏感，进而不再敢于使用纯云端的产品。

为迎合客户普遍的自有商业数据支持高安全防护的隐私诉求，经公司会议讨论，现候鸟产品提供脱离官方服务器运行的支持，客户自有重要运营数据仅且只能保存在客户本地硬盘模式，即客户端纯本地化隐私数据高度安全运行模式。

此模式对外提供后，将进一步拉开与同行的产品距离，在数据安全防护上，彻底将同行远远抛在身后，在几乎所有同行采用的WEBUI模式下的产品性质(同行数据只能存在于服务器端模式)上，同行产品将很难在短时间超越候鸟第三十八章节功能，即很难克服大多数中高端客户的商用数据安全及隐私的顾虑。

国内所有同行企业在相当长的一段时间内，完全无法跟随，学习，或超越候鸟的功能。这是由候鸟整套体系架构决定的先天优势。

会极大的减少正式服和备份服的硬件压力。

约定 仅付费用户有权利进行联机、脱机使用，有利于促进新用户进行交费。

离线，上线可切换机制，与团队协作约定无冲突。用户需要进行团队协作时，可灵活切换到上线即可，运营再离线使用，权利下放给商业客户。环境包的导入导出模式天然支持完全离线模式下的团队协作。

前述：

具体实施办法：

通过在SETUP面板提供候鸟MBDATA数据本地化运行总开关，通过API提供同类总开关支持，来提供用户可灵活控制候鸟产品的：实时版本验证开启与关闭、实时同步开启与关闭、定时同步开启与关闭、脚本开启与关闭、插件开启与关闭。登录时，服务器端不再同步到本地及版本验证。

约定：除上述与服务器交互的部份支持客户开启与关闭外，唯一不允许用户控制的为客户端与服务器端的身体验证、登录验证环节。

此约定作为基础约定在后续版本中长期存在。

B、首次安装使用，默认为联网状态，在总开关关闭时【断网状态】：客户端以用户本地数据为主要数据源的中心思想。

C、新装安装包默认为上线模式运行。

未付费帐户不支持切换上线、离线功能。即当用户未交费时进行上线，离线总开关切换时，应弹出窗口提示用户付费购买后才可使用。

当用户设定为离线模式后，需要在服务器记载，以保证此用户未开启上线前，无论如何安装，使用，都为离线状态。通过登录串返回的JSON来控制。

即用户在异地首次安装和使用旧帐号，以服务器中记录的最新状态来判断上线/离线运行。

在客户端里设置为离线或上线需要填入登录密码。（此项暂时不做）

切换到离线模式，同时WEB控制台的相应功能要显示 离线状态 字样。

服务器端传回给客户端的途径只有一个，即在登录JSON里返回。

此模式可参照成熟产品：Steam的上线与离线。

工作步骤：

1、登录界面增加支持显示离线模式验证

A： 右上角开关 默认显示用户已设定的状态。如果在本地没有状态标志位，则默认均为联机模式（即箭头A处开关默认为关）。

字样显示：离线模式。

B、当离线模式开启时，左上角的字样显示为B箭头图示。

C、当离线模式开启时，箭头C的字样显示为C箭头图示。

2、服务器端的登录JSON返回增加离线、联机状态节点返回。参数名定义后在云笔记里进行标注，并写明添加此节点时的日期，便于以后查询。

3、总开关按钮，变为离线时，上面这个图片变成变线的图标即可。

4、实际开关逻辑的添加

1、联网总开关 关闭后，下面所有项开关全部自动切换为已禁用模式。

2、联网总开关 开启后，下面所有项开关全部自动切换为已启用模式。

3、联网总开关 关闭后， 允许用户开启个别下列项。

A、如 子项2 启用时，则 子项1 要同时启用。

B、如子项3启用时，则子项1要同时启用。（这里要一起确认下）

C、以下类推。

D、增加一个用户网站登录帐户的同步的项。

5、联网总开关的 开启 与 关闭。 需要用户三种方式进行验证，验证通过方可生效。

1、输入登录密码。 开启与关闭。

2、微信扫二维码进行验证。

3、邮箱发送验证码进行验证。

6、在首次使用联网总开关时，需要弹出用户告知书窗体（内容随后附上），详细描述离线使用后的各种可能导致的意外场景。用户确定知晓后（左下角勾选项：下次不再提示我），才可进入第6项的弹窗。

7、服务器端接口增加 上线离线 总开关的指令，并通过此指令在服务器端更新状态。指令格式：1|1|1|1|1|1

8、

服务器端通过心跳，控制客户端切换本地环境、网络环境的 约定要有，在云笔记里加上。 客户端衔接上。（这一个功能要有，但不对外，对内保留有效即可）防着以后要用上。

8、官网页面增加部份。

一、具体流程与界面说明：

主面板：在主面板界面提供上线/离线运行模式

将意见反馈项放到上图指示位置。

A项内容（切换显示）：上线……， 进入离线模式…

在离线模式下，左上角主ICON采用离线标志显示。

SETUP面板：在SETUP面板界面提供上线/离线运行模式

二、通过SETUP或菜单按钮点击，主ICON要切换显示为离线状态ICON.

三、官网API文档区域说明：

API首页重点展示此章节的开关说明，即支持APISERVER启动时以离线模式运行。

API在登录请求的参数中，要增加参数：online 来对应上述功能。

在分享环节，通过WEB控制台控制子帐户进入网络模式，以达到自动接受分享过程。然后再自动让其回到本地模式。


================================================== 表格内容 ==================================================

{
"message "Session Script List Success",
"code": 0,
"data": {
"listcontainer": [
        {
        "Session_Name": “商用业务环境一”
        "Session_ID": "373808cb37bd63f5f7d92415e736e85f",
"Group_Name": “default”,
“Actived_script_id”:” O73808cb37bd63f5f7d92415e736e999”,
“Actiived_script_name”:”这是一个脚本例子”,
“Actiived_script_encode”:”true”,
"Script_Count": "4",
"UnActived_script_list": 
                    [{
                     "UnActived_script_encode" : "false",
                     "UnActived_script_Name" : "AAA",
                     "UnActived_script_ID" : "17c70e014d61b1fa43d3638ca5a1bc21"
                         },{
                     "UnActived_script_encode" : "false",
                     "UnActived_script_Name" : "BBB",
                     "UnActived_script_ID" : "17c70e014d61b1fa43d3638ca5a1bc22"
                         }],
"status": 0
}

}
}

参数名称 | 类型 | 必传 | 样例串/默认值 | 说明
Session_ID | array | 是 | 373808cb37bd63f5f7d92415e736e85f, 705cc4c139e69b729a2fd277f30e1863 | 指定环境ID查询环境插件集合