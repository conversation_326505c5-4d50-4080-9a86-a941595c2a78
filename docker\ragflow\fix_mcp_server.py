#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RAGFlow MCP Server 修复脚本
用于修复和优化 RAGFlow MCP Server 配置，确保可以从外部访问
"""

import os
import json
import subprocess
import time
import requests
from pathlib import Path

class RAGFlowMCPFixer:
    """RAGFlow MCP Server 修复工具"""
    
    def __init__(self):
        self.docker_dir = Path(__file__).parent
        self.compose_file = self.docker_dir / "docker-compose.yml"
        self.env_file = self.docker_dir / ".env"
        
        print("🔧 RAGFlow MCP Server 修复工具")
        print(f"   工作目录: {self.docker_dir}")
        print(f"   Compose文件: {self.compose_file}")
    
    def check_docker_status(self) -> bool:
        """检查 Docker 服务状态"""
        print("\n1️⃣ 检查 Docker 服务状态...")
        
        try:
            result = subprocess.run(["docker", "ps"], capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ Docker 服务正常运行")
                return True
            else:
                print("❌ Docker 服务未运行")
                return False
        except Exception as e:
            print(f"❌ Docker 检查失败: {e}")
            return False
    
    def check_ragflow_container(self) -> dict:
        """检查 RAGFlow 容器状态"""
        print("\n2️⃣ 检查 RAGFlow 容器状态...")
        
        try:
            result = subprocess.run(
                ["docker", "ps", "-a", "--filter", "name=ragflow-server", "--format", "json"],
                capture_output=True, text=True
            )
            
            if result.returncode == 0 and result.stdout.strip():
                container_info = json.loads(result.stdout.strip())
                print(f"✅ 找到 RAGFlow 容器")
                print(f"   状态: {container_info.get('State', 'Unknown')}")
                print(f"   端口: {container_info.get('Ports', 'Unknown')}")
                return container_info
            else:
                print("❌ 未找到 RAGFlow 容器")
                return {}
        except Exception as e:
            print(f"❌ 容器检查失败: {e}")
            return {}
    
    def update_docker_compose(self) -> bool:
        """更新 Docker Compose 配置"""
        print("\n3️⃣ 更新 Docker Compose 配置...")
        
        try:
            # 读取当前配置
            with open(self.compose_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 确保 MCP 端口映射正确
            if "9382:9382" not in content:
                print("⚠️  添加 MCP 端口映射...")
                # 在 ports 部分添加 9382 端口
                if "ports:" in content:
                    content = content.replace(
                        "ports:",
                        "ports:\n      - 9382:9382  # MCP Server端口"
                    )
            
            # 确保 MCP 主机配置为 0.0.0.0
            if "--mcp-host=127.0.0.1" in content:
                print("⚠️  修改 MCP 主机配置为允许外部访问...")
                content = content.replace(
                    "--mcp-host=127.0.0.1",
                    "--mcp-host=0.0.0.0"
                )
            
            # 写回文件
            with open(self.compose_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ Docker Compose 配置已更新")
            return True
            
        except Exception as e:
            print(f"❌ 更新 Docker Compose 失败: {e}")
            return False
    
    def restart_ragflow(self) -> bool:
        """重启 RAGFlow 服务"""
        print("\n4️⃣ 重启 RAGFlow 服务...")
        
        try:
            # 停止服务
            print("   停止现有服务...")
            subprocess.run(
                ["docker-compose", "down"],
                cwd=self.docker_dir,
                check=True
            )
            
            time.sleep(5)
            
            # 启动服务
            print("   启动服务...")
            subprocess.run(
                ["docker-compose", "up", "-d"],
                cwd=self.docker_dir,
                check=True
            )
            
            print("✅ RAGFlow 服务重启完成")
            return True
            
        except Exception as e:
            print(f"❌ 重启服务失败: {e}")
            return False
    
    def wait_for_service(self, timeout: int = 120) -> bool:
        """等待服务启动"""
        print(f"\n5️⃣ 等待服务启动 (最多 {timeout} 秒)...")
        
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                # 检查 RAGFlow 主服务
                response = requests.get("http://localhost:9380/", timeout=5)
                if response.status_code == 200:
                    print("✅ RAGFlow 主服务已启动")
                    
                    # 检查 MCP 服务
                    try:
                        mcp_response = requests.get("http://localhost:9382/sse", timeout=5)
                        print("✅ MCP 服务已启动")
                        return True
                    except:
                        print("   MCP 服务还未就绪，继续等待...")
                        
            except:
                pass
            
            print("   等待服务启动...")
            time.sleep(10)
        
        print("❌ 服务启动超时")
        return False
    
    def test_mcp_connection(self) -> bool:
        """测试 MCP 连接"""
        print("\n6️⃣ 测试 MCP 连接...")
        
        try:
            # 测试 SSE 端点
            response = requests.get("http://localhost:9382/sse", timeout=10)
            print(f"   SSE 端点状态: {response.status_code}")
            
            # 测试健康检查
            try:
                health_response = requests.get("http://localhost:9382/health", timeout=5)
                print(f"   健康检查状态: {health_response.status_code}")
            except:
                print("   健康检查端点不可用")
            
            if response.status_code in [200, 404]:  # 404 也可能是正常的
                print("✅ MCP 服务连接正常")
                return True
            else:
                print("❌ MCP 服务连接异常")
                return False
                
        except Exception as e:
            print(f"❌ MCP 连接测试失败: {e}")
            return False
    
    def check_firewall(self) -> None:
        """检查防火墙配置提示"""
        print("\n7️⃣ 防火墙配置检查...")
        print("⚠️  请确保以下端口在服务器防火墙中开放:")
        print("   - 9380 (RAGFlow 主服务)")
        print("   - 9382 (MCP Server)")
        print()
        print("CentOS 防火墙配置命令:")
        print("   sudo firewall-cmd --permanent --add-port=9380/tcp")
        print("   sudo firewall-cmd --permanent --add-port=9382/tcp")
        print("   sudo firewall-cmd --reload")
    
    def generate_client_config(self) -> None:
        """生成客户端配置示例"""
        print("\n8️⃣ 生成客户端配置...")
        
        config = {
            "ragflow_mcp": {
                "server_url": "http://58.49.146.17:9382",
                "api_key": "ragflow-UzYTlhMjIwNjI0YzExZjA4NzUyMDI0Mm",
                "timeout": 30,
                "retry_count": 3,
                "endpoints": {
                    "sse": "/sse",
                    "messages": "/messages/",
                    "health": "/health"
                }
            }
        }
        
        config_file = self.docker_dir / "wing_client_config.json"
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 客户端配置已生成: {config_file}")
    
    def run_fix(self) -> bool:
        """运行完整修复流程"""
        print("🚀 开始 RAGFlow MCP Server 修复流程")
        print("=" * 60)
        
        # 1. 检查 Docker
        if not self.check_docker_status():
            return False
        
        # 2. 检查容器
        self.check_ragflow_container()
        
        # 3. 更新配置
        if not self.update_docker_compose():
            return False
        
        # 4. 重启服务
        if not self.restart_ragflow():
            return False
        
        # 5. 等待服务启动
        if not self.wait_for_service():
            return False
        
        # 6. 测试连接
        if not self.test_mcp_connection():
            return False
        
        # 7. 防火墙提示
        self.check_firewall()
        
        # 8. 生成客户端配置
        self.generate_client_config()
        
        print("\n" + "=" * 60)
        print("🎉 RAGFlow MCP Server 修复完成！")
        print()
        print("📋 下一步操作:")
        print("1. 确保服务器防火墙开放 9380 和 9382 端口")
        print("2. 使用生成的客户端配置文件")
        print("3. 运行诊断脚本验证连接")
        
        return True


def main():
    """主函数"""
    print("RAGFlow MCP Server 修复工具")
    print("用于修复和优化 MCP Server 配置")
    print()
    
    fixer = RAGFlowMCPFixer()
    
    try:
        success = fixer.run_fix()
        if success:
            print("\n✅ 修复完成！可以开始配置 Wing 客户端")
        else:
            print("\n❌ 修复过程中遇到问题，请检查日志")
            
    except KeyboardInterrupt:
        print("\n\n⏹️  修复被用户中断")
    except Exception as e:
        print(f"\n❌ 修复过程中发生错误: {str(e)}")


if __name__ == "__main__":
    main()
