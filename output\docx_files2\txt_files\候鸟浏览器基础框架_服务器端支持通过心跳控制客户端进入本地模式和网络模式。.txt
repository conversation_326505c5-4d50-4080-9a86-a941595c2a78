服务器端支持通过心跳控制客户端进入本地模式和网络模式。

服务器端支持通过心跳控制客户端进入本地模式和网络模式。

WEB控制台对子帐户管理时，子帐户列表要显示当前客户端的状态：运行状态，网络状态（本地模式/网络模式）

当用户分享环境给子帐户时，如果子帐户在本地模式，需要服务器通过心跳给客户端下发上线指令，然后再下发分享环境。下发完成后，收到客户端接收成功的状态报告后，再将其客户端置为本地模式。

========================================================

关于ITEM包的历史条目恢复管理窗口。

关于使用CODESIGN KEY模式，将USB KEY交由客户，采用每个客户端一个USB KEY的物理模式。同时支持所有数据仅在本地保存（或将USB KEY做为数据存储空间）方式，提供更强大的安全属性。（不会影响收益，只会助涨收益）但对于咱们研发要求会高几个层级。

[20231023]

第三十九章
候鸟指纹轨迹体系（新增体系）

客户端/服务器端环境指纹轨迹监控

依据传统，具体工作步骤、顺序安排，人员分配在工作群里具体阐述。

综述：

用户的商用环境对指纹的数值稳定性要求高，历史版本中，用户要进行指纹变化的监控，只能人工记录环境运行时，访问YALALA后的指纹值，这个行为基本所有客户都不会进行，过于繁琐，因此当前章节提供指纹运行轨迹的报表功能，在用户运行环境前，可检测和查看指纹的变化，此功能作为强化用户使用候鸟开展业务前的信心值进行提供。

面板各项控件颜色数值。

WEBGL： BB045BAA9A10071D|1     或 BB045BAA9A10071D|0

1表示绿， 0表示红

每个指纹后面的|0或者|1，代表指纹和上一次指纹的对比结果，1表示一致，0表示不一致​

默认场景下，用户点击右上角的按钮来触发这个事件

而且此功能的可用性会高很多，因为用户会习惯于对于重要的业务环境在正式开展业务前，先进行一次点击上图按钮的操作来判断是否指纹稳定。

这块功能也是其它同行不具备的功能，属于拉开同行距离的一块相对重要的工作。

因为几乎所有的用户的环境中，实际正式使用的业务环境数量（这个数量中需要指纹稳定的环境数量），通常只占其所有环境总量的极少一部份。

这极少的一部份，我们引导用户通过上面的按钮来进行指纹（yalala）请求，并反回到上图面板的列表中，通过前置化，展示给用户指纹是否稳定，让用户强化其信心对环境开展业务运行（用户因害怕IP关联导致封号的担忧，可通过这部份来彻底解决掉）。

yalala的指纹值的返回接口，先做到云笔记中，此接口提供给客户端使用

步骤如下：

候鸟浏览器访问yalala网站，在访问URL后缀加上环境的session_id，例如：https://www.yalala.com/?wd=mb&id=xxxxxxxxxxx

PHP成功获得js响应数据即可。

这是不是说，在浏览器打开这个网址之后，网页端获取到相应指纹，传递给服务端保存起来，后面客户端要用我再发给你？

10:43

这是不是说，在浏览器打开这个网址之后，网页端获取到相应指纹，传递给服务端保存起来，后面客户端要用我再发给你？

an, 2023/9/19

对的，就是这个意思

这一块的功能要提供前，逻辑走通的关键环节就是PHP获取JS的数据，这个实现成功后，才有后面的逻辑。

我这有一点问题

1. yalala提交数据给服务端，直接就是session_id和指纹。也没有token可以验证合法性，理论上来说，用户可以用postman模拟来覆盖掉其他用户的环境指纹数据

2.目前存在一个session_id多个环境的情况（主账户分享给子账号就会出现），这样的话，仅靠session_id记录的指纹，取出的时候，准确性会降低

可以加上token提交

上图这部份数据要和1项的图的数据一并保存

这部份入表主要是供用户参考，关键值仍旧是指纹的那个串码。

HSTSa的指纹可以不用判断，其它的六个指纹值无论如何刷新，重启环境，都未见有变化。你就可以用来做判断了。

记得放备份服，这个数据量会比较大

举个例子，这个地方网页端是调用了3个字段，我存储时是存储这个字符串，还是存储所有的字段，到时在重新组合？

存储所有的字段，这样灵活点

字段比较多，这个表时间长了肯定会很庞大

定义成一个热表和一个冷表模式，你看如何，默认搜索热表，热表找不到再找冷表。 热表中超过一定时间后的冷门数据移到冷表中，如果冷表中有搜索结果的，再移回到热表。

热表和冷表的结构完全相同。

每个环境最多保存10条记录就差不多了。保存过多意义不大，还会影响查询效率

然后客户端后面请求过来，你返回此环境的10条记录就可以，要求每条记录不相同才保存

主要是记录指纹的变化趋势

只保存单条最新的没有意义，意义在于出现了变化的情况。

嗯，指纹出现变化则表示用户业务存在风险的可能

这个环境对应的业务是相同的，则这个环境的指纹变化图，同时显示在团队协作的帐户下是对的。

另外你写表的时侯，每个记录的写入时间要有个单独的字段，记录时间值。这个时间值不可缺少。

你判断记录的不同，根据指纹的不同来判断应该就可以。不用判断所有字段。

‘


================================================== 表格内容 ==================================================

{
"message "Session Plugin List Success",
"code": 0,
"data": {
"listcontainer": [
        {
        "Session_Name": “商用业务环境一”
        "Session_ID": "373808cb37bd63f5f7d92415e736e85f",
"Group_Name": “default”,
"Plugins_Count": "4",
"Plugin_list": 
                    [{
                     "Plugin_Name" : "AAA",
                     "Plugin_Id" : " jjbnhpnlakcdgfnnldamfeinfmahhdlm"，
                     "Plugin_Ver" : "1.0.0.0"

                         },
{
                     "Plugin_Name" : "BBB",
                     "Plugin_Id" : " jjbnhpnlakcdgfnnldamfeinfmahhdlm ",
                     "Plugin_Ver" : "1.0.0.0"

                         }],
"status": 0
}

}
}

参数名称 | 类型 | 必传 | 样例串/默认值 | 说明
Session_ID | string | 是 | 373808cb37bd63f5f7d92415e736e85f | 指定环境ID
Plugin_ID | array | 是 | ncennffkjdiamlpmcbajkmaiiiddgioo | 指定删除插件ID（支持多个）