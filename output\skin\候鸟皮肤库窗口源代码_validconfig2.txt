﻿<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Window size="363,692" caption="0,0,0,50" showshadow="true" roundcorner="5,5,5,5" showshadow="true" shadowsize="3" shadowposition="0,0" shadowcolor="#ff6272A4" fademode="true">
	<Include source="Default.xml" />

  <VerticalLayout width="363" height="692" bkcolor="#FF282A36">
    <HorizontalLayout height="26">
      <Control />

      <Button name="minbtn" width="28" height="26" tooltip="最小化" normalimage="minbtn.png" hotimage="minbtn_hover.png" pushedimage="minbtnpush.png" />
      <Button name="closebtn" width="28" height="26"  tooltip="关闭" normalimage="closebtn.png" hotimage="closebtn_hover.png" pushedimage="closebtnpush.png" />
    </HorizontalLayout>
  <HorizontalLayout name="bkground">


		<VerticalLayout width="362" height="603">
			<HorizontalLayout height="32">
				<VerticalLayout width="80">
			      <Label name="username" padding="22,0,0,0" text="环境名称" width="80" textcolor="#FF333333" hottextcolor="ffFF0000" font="8"></Label>
			  </VerticalLayout>
			  <VerticalLayout width="30">
			      <Button name="config_tip" padding="6,5,0,0" height="15" width="15"  normalimage="file='helpbtn.png'" hotimage="file='helpbtn_hover.png'" pushedimage="file='helpbtn_push.png'"/>
			  </VerticalLayout>
			</HorizontalLayout>

			<HorizontalLayout height="36" >
			    	 <VerticalLayout>
				     <RichEdit name="search_edit" padding="21,0,0,10" height="36" width="320" borderround="3,3" bkcolor="#ffdce1e7" font="8" textpadding="10,8,20,0" maxchar="300" tipvalue="请输入环境名称" multiline="false" textcolor="#ff333333" rich="false" transparent="false">
				      </RichEdit>
				     </VerticalLayout>
			</HorizontalLayout>



			<HorizontalLayout height="22">
			</HorizontalLayout>
			<HorizontalLayout height="32">
				<VerticalLayout width="80">
			      <Label name="username" padding="22,0,0,0" text="操作系统" width="80" textcolor="#FF333333" hottextcolor="ffFF0000" font="8"></Label>
			  </VerticalLayout>
			</HorizontalLayout>

			<HorizontalLayout height="36" >
			    	 <VerticalLayout>
				      <Combo name="sendCombo" bordersize="0" padding="21,0,0,10" width="320" height="36" borderround="3,3" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="10,0,0,0" bkcolor="#ffdce1e7"
										normalimage="file='Profile\Setting_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\Setting_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\Setting_Combox_Click.png' corner='5,5,25,10'"
										combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
										itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" >
											<ListLabelElement height="36" pos="66,0,0,0" textpadding="10,0,0,0" text="  Windows 10" font="0"selected="true">
												<Label name="textLab" pos="66,0,0,0" textpadding="10,0,0,0" text="Windows 10"  height="36" width="320" textcolor="#FF000000"/>
											</ListLabelElement>
											<ListLabelElement height="26" pos="66,0,0,0" text="  Windows 7" font="0">
												<!--<Label name="imgLab" float="true" pos="2,1,0,0" height="24" width="24" bkimage="Setting\Setting_Combox_List_icon.png" visible="false"/>-->
												<Label name="textLab" float="true" pos="66,0,0,0" text="Windows 7" pos="0,0,0,0" height="36" width="320" textcolor="#FF000000"/>
											</ListLabelElement>
										</Combo>
				     </VerticalLayout>
			</HorizontalLayout>



			<HorizontalLayout height="22">
			</HorizontalLayout>
			<HorizontalLayout height="32">
				<VerticalLayout width="120">
			      <Label name="username" padding="22,0,0,0" text="UserAgent" width="120" textcolor="#FF333333" hottextcolor="ffFF0000" font="8"></Label>
			  </VerticalLayout>
			</HorizontalLayout>

			<HorizontalLayout height="36" >
			    	 <VerticalLayout>

				       <Combo name="sendCombo" bordersize="0" padding="21,0,0,10" width="320" height="36" borderround="3,3" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="10,0,0,0" bkcolor="#ffdce1e7"
										normalimage="file='Profile\Setting_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\Setting_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\Setting_Combox_Click.png' corner='5,5,25,10'"
										combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
										itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" >
											<ListLabelElement height="36" pos="9,0,0,0" textpadding="10,0,0,0" text="  Mozilla/5.0(Macintosh; Intel Mac OS X..)" font="0"selected="true">
												<Label name="textLab" pos="9,0,0,0" textpadding="10,0,0,0" text="东一区 UTC+1"  height="36" width="320" textcolor="#FF000000"/>
											</ListLabelElement>
											<ListLabelElement height="26" text="  IEXPLORER/6.0(Microsoft; Internet Explorer)" font="0">
												<!--<Label name="imgLab" float="true" pos="2,1,0,0" height="24" width="24" bkimage="Setting\Setting_Combox_List_icon.png" visible="false"/>-->
												<Label name="textLab" float="true" text="东二区 UTC+2" pos="0,0,0,0" height="36" width="320" textcolor="#FF000000"/>
											</ListLabelElement>
										</Combo>



				     </VerticalLayout>
			</HorizontalLayout>


	    <HorizontalLayout height="22">
			</HorizontalLayout>
			<HorizontalLayout height="32">
				<VerticalLayout width="120">
			      <Label name="username" padding="22,0,0,0" text="时区" width="120" textcolor="#FF333333" hottextcolor="ffFF0000" font="8"></Label>
			  </VerticalLayout>
			</HorizontalLayout>

			<HorizontalLayout height="36" >
			    	 <VerticalLayout>
						       <Combo name="sendCombo" bordersize="0" padding="21,0,0,10" width="320" height="36" borderround="3,3" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="10,0,0,0" bkcolor="#ffdce1e7"
										normalimage="file='Profile\Setting_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\Setting_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\Setting_Combox_Click.png' corner='5,5,25,10'"
										combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
										itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" >
											<ListLabelElement height="36" pos="9,0,0,0" textpadding="10,0,0,0" text="  东一区 UTC+1" font="0"selected="true">
												<Label name="textLab" pos="9,0,0,0" textpadding="10,0,0,0" text="东一区 UTC+1"  height="36" width="320" textcolor="#FF000000"/>
											</ListLabelElement>
											<ListLabelElement height="26" text="  东二区 UTC+2" font="0">
												<!--<Label name="imgLab" float="true" pos="2,1,0,0" height="24" width="24" bkimage="Setting\Setting_Combox_List_icon.png" visible="false"/>-->
												<Label name="textLab" float="true" text="东二区 UTC+2" pos="0,0,0,0" height="36" width="320" textcolor="#FF000000"/>
											</ListLabelElement>
										</Combo>
				     </VerticalLayout>
			</HorizontalLayout>



	    <HorizontalLayout height="22">
			</HorizontalLayout>
			<HorizontalLayout height="32">
				<VerticalLayout width="120">
			      <Label name="username" padding="22,0,0,0" text="代理" width="120" textcolor="#FF333333" hottextcolor="ffFF0000" font="8"></Label>
			  </VerticalLayout>
			</HorizontalLayout>

			<HorizontalLayout height="36" >
			    	 <VerticalLayout>
				      <Combo name="sendCombo" bordersize="0" padding="21,0,0,10" width="320" height="36" borderround="3,3" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="10,0,0,0" bkcolor="#ffdce1e7"
										normalimage="file='Profile\Setting_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\Setting_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\Setting_Combox_Click.png' corner='5,5,25,10'"
										combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
										itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" >
											<ListLabelElement height="36" pos="66,0,0,0" textpadding="10,0,0,0" text="  美国 202.178.287.22" font="0"selected="true">
												<Label name="textLab" pos="66,0,0,0" textpadding="10,0,0,0" text="美国 202.178.287.22"  height="36" width="320" textcolor="#FF000000"/>
											</ListLabelElement>
											<ListLabelElement height="26" pos="66,0,0,0" text="  日本 132.56.88.100" font="0">
												<!--<Label name="imgLab" float="true" pos="2,1,0,0" height="24" width="24" bkimage="Setting\Setting_Combox_List_icon.png" visible="false"/>-->
												<Label name="textLab" float="true" pos="66,0,0,0" text="日本 132.56.88.100" pos="0,0,0,0" height="36" width="320" textcolor="#FF000000"/>
											</ListLabelElement>
										</Combo>

				     </VerticalLayout>
			</HorizontalLayout>



			<HorizontalLayout height="24">
        <VerticalLayout width="100">
           <Label name="buy_proxy" padding="22,6,0,0" text="购买代理" width="100" textcolor="#FF519cff" align="left" font="5" cursor="hand"></Label>
          </VerticalLayout>
			</HorizontalLayout>


      <HorizontalLayout height="22">
			</HorizontalLayout>
			<HorizontalLayout height="32">
				<VerticalLayout width="50">
			      <CheckBox name="chk_radio_code" width="18" height="18"  padding="22,4,0,1" normalimage="file='check.png' source='18,0,36,18'" selectedimage="file='check.png' source='0,0,18,0'"/>
			  </VerticalLayout>
			  <VerticalLayout width="90">
			      <Label name="username" padding="0,0,0,0" text="音频指纹" width="90" textcolor="#FF333333" hottextcolor="ffFF0000" font="8"></Label>
			  </VerticalLayout>













			  <VerticalLayout width="20">
			  	<Control width="20"/>
			  </VerticalLayout>

			  <VerticalLayout width="50">
			      <CheckBox name="chk_radio_code" width="18" height="18"  padding="22,4,0,1" normalimage="file='check.png' source='18,0,36,18'"/>
			  </VerticalLayout>
			  <VerticalLayout width="120">
			      <Label name="username" padding="0,0,0,0" text="字体指纹" width="120" textcolor="#FF333333" hottextcolor="ffFF0000" font="8"></Label>
			  </VerticalLayout>
			</HorizontalLayout>


      <HorizontalLayout height="6">
			 </HorizontalLayout>
			<HorizontalLayout height="32">
				<VerticalLayout width="50">
			      <CheckBox name="chk_radio_code" width="18" height="18"  padding="22,4,0,1" normalimage="file='check.png' source='18,0,36,18'"/>
			  </VerticalLayout>
			  <VerticalLayout width="90">
			      <Label name="username" padding="0,0,0,0" text="CANVAS指纹" width="90" textcolor="#FF333333" hottextcolor="ffFF0000" font="8"></Label>
			  </VerticalLayout>

			  <VerticalLayout width="20">
			  	<Control width="20"/>
			  </VerticalLayout>

			  <VerticalLayout width="50">
			      <CheckBox name="chk_radio_code" width="18" height="18"  padding="22,4,0,1" normalimage="file='check.png' source='18,0,36,18'"/>
			  </VerticalLayout>
			  <VerticalLayout width="120">
			      <Label name="username" padding="0,0,0,0" text="WEBRTC指纹" width="120" textcolor="#FF333333" hottextcolor="ffFF0000" font="8"></Label>
			  </VerticalLayout>
			</HorizontalLayout>

      <HorizontalLayout height="6">
			 </HorizontalLayout>
			<HorizontalLayout height="32">
				<VerticalLayout width="50">
			      <CheckBox name="chk_radio_code" width="18" height="18"  padding="22,4,0,1" normalimage="file='check.png' source='18,0,36,18'"/>
			  </VerticalLayout>
			  <VerticalLayout width="90">
			      <Label name="username" padding="0,0,0,0" text="DNS指纹" width="90" textcolor="#FF333333" hottextcolor="ffFF0000" font="8"></Label>
			  </VerticalLayout>

			  <VerticalLayout width="20">
			  	<Control width="20"/>
			  </VerticalLayout>
			</HorizontalLayout>

		</VerticalLayout>


	</HorizontalLayout>
    <HorizontalLayout height="52" bkcolor="#ff519cff">
         <Control />
      <VerticalLayout>
      	<Control />
          <Button name="configbtn" padding="12,0,0,0" height="16" width="91" normalimage="file='newbtn.png' source='0,0,91,16'" hotimage="file='newbtn.png' source='91,0,182,16'"/>
          <Control />
       </VerticalLayout>
<Control />
    </HorizontalLayout>
  </VerticalLayout>
</Window>
