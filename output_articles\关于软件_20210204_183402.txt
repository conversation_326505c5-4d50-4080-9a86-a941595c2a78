标题: 关于软件
英文标题: About Software
ID: 38
分类ID: 26
添加时间: 1612434842
更新时间: 1712459235
访问次数: 0
SEO标题: 候鸟浏览器关于软件的常见问题
SEO关键词: 候鸟浏览器关于软件的常见问题
SEO描述: 候鸟浏览器关于软件的常见问题

================================================== 内容 ==================================================
### Q：浏览器窗口大小边框太大，无法缩小如何解决？
A：是的，浏览器窗口大小是在创建环境的时候固定下来的，改变大小后浏览器指纹会跟着变化，会影响到环境的安全性。如果您想把浏览器的窗口变小可以在创建或修改环境重新选择更小的分辨率。

------------
### Q：为什么下载安装软件后点击创建环境，提示无法创建更多的会话？无法创建环境？
A：有可能是您还没有付费购买套餐，您在该页面[https://www.mbbrowser.com/price](https://www.mbbrowser.com/price "https://www.mbbrowser.com/price") 购买套餐后使用。

------------
### Q：候鸟浏览器支持手机或IPAD上操作吗？
A：候鸟暂时不支持移动端安装操作的，但是可以在PC端创建环境的时候选择手机的UA模拟手机端页面。

------------
### Q：候鸟浏览器是在本机运行还是在服务器（VPS）上运行?
A：只要是windows 7以上的操作系统都可以的。

------------
### Q：候鸟浏览器支持什么代理IP类型？
A：目前市场上几乎所有的ipv4类型和代理IP提供商都支持的。ipV6的IP除外。

------------
### Q：候鸟浏览器支持创建多少个环境？
A：个人无限版及以上套餐没有创建环境数量的限制。

------------
### Q：候鸟浏览器如何使用，有详细的教程吗？
A：[https://www.mbbrowser.com/help](https://www.mbbrowser.com/help "https://www.mbbrowser.com/help") 您可以看一下官网的帮助页面有详细的配置教程。

------------
### Q：在候鸟客户端可以将环境分组吗？
A：从********版本起已经支持分组功能。

------------
### Q：候鸟浏览器可以一个环境对应一个代理IP和端口吗？
A：是的，可以的。候鸟防关联指纹浏览器可以一个环境对应一个代理IP和端口。

------------
### Q：创建环境的时候代理proxy第一个选项我应该选择什么类型？
A：根据您购买的代理IP选择相对应的类型，比如常用的socks5或http。

------------
### Q：创建环境面板上设置代理IP部分的账号密码需要填吗？
A：代理IP的账号密码是选填的，要看一下您的购买代理IP是否有账号密码，如果有的话就需要填，没有的话就可以不填。

------------
### Q：候鸟浏览器自定义首页吗？
A：候鸟浏览器支持自定义首页；登录客户端后再右下角“齿轮”标志-通用设置-默认起始页处更改。

------------
### Q：一个代理IP 只能登录一个账号吗？还可以同时登录多个账号？
A：候鸟浏览器本身对代理IP对应多账号没有限制，但是，建议一个IP登录一个账号，否则无法实现彻底防关联。

------------
### Q：用候鸟浏览器登录多账号，电脑可以关机吗？
A：正常的用户通过浏览器浏览网站是不需要挂机的，根据您的需求正常使用候鸟就可以！

------------
### Q：用候鸟浏览器注册网站账号，出现验证码和人工验证，正常吗？
A：一些网站登录和注册的时候提示验证码和人工验证是正常的，这个情况跟代理IP的纯净度有关系，如果一个代理IP在短时间重复访问一个网站就会被网站要求进行人工验证，该情况跟浏览器没有关系。

------------
### Q：候鸟浏览器可以修改MAC网卡地址吗？
A：候鸟是一款防关联浏览器，网站通过浏览器读取客户端的MAC地址只能通过FLASH插件，目前FLASH插件已经停止更新，chrome浏览器内核也已经不在支持FLASH插件，所以网站无法读取MAC网卡地址，也不需要进行修改。

------------
### Q：候鸟浏览器的UA 数量够吗？如果UA设置重复了怎么办？
A：首先候鸟的UA 是随机的，我们有300万以上的UA库，理论上够用的。而且UA记录的是操作系统和浏览器信息。这2项本身可选择产品就很少。所以各种排列组合下 依然会有大量重复可能，也是正常符合逻辑的。

------------
### Q：子账号环境可以分享给主账号吗？子账户可以创建环境吗？
A：子账号是可以把创建的环境分享给主账号的，同时在客户端子账号和主账号的权限是一致的，可以独立创建环境。子账号环境可以分享给主账号吗？子账户可以创建环境吗？

------------
### Q：候鸟浏览器登录网站的账号密码是自动备份的吗，可以实现免登录吗？
A：候鸟浏览器登录网站的账号密码是自动备份的，前提是您在登录网站的时候要勾选“保持登录状态”等选项；实现免登录取决于网站端的规则，候鸟浏览器的规则跟chrome是一样的。

------------
### Q：我用的是动态IP第一次创建环境检测安全度是100%，为什么在次打开就是70%？
A：如果您使用的是动态IP，建议在配置环境窗口将“WebRTC指纹”选项去掉勾选，因为您的动态IP每次打开环境的IP不一样，但是WbeRTC的公网IP却是您上一次，会导致IP地址和WebRTC公网地址不匹配。

![](6d7b2882624511f09a0d0242ac130006/images/image_58287c3573d2.png)

------------
### Q：我用luminati或911S5的代理IP，在使用候鸟的过程中，LMP和911S5的客户端需要开启吗？
A：是的，如果使用luminati或者911s5都需要打开，代理IP才能正常访问。

------------
### Q：候鸟防关联浏览器对电脑配置有什么要求吗？
A：建议4核处理器，8g以上内存，windows7以上操作系统。

------------
### Q：使用候鸟指纹浏览器可以加速吗？为什么我用代理IP访问网站速度很慢？
A：候鸟浏览器对访问网站进行了优化处理，浏览速度主要是代理IP决定的，本身使用代理IP访问网站的速度不如本机直接访问快。

------------
### Q：为什么候鸟浏览器内置的UA数量这么少，刷新几次都是同一个？
A：目前候鸟内置300万以上的UA库，为了保证环境的安全性UA和分辨率参数是绑定的，有些非主流分辨率尺寸下面的UA数量少是正常的，建议您选择不同的浏览器分辨率进行刷新UA，就会发现UA重复的情况是很少的，如果还不满足您对UA数量的要求，候鸟软件也支持自定义UA，可以将您自己的UA复制到候鸟里使用。

------------
### Q：候鸟超级浏览器可以伪装定位GPS地图位置吗？
A：：PC端浏览器的定位功能是根据IP的地理位置判断的。跟手机端不同，手机端是通过GPS，所以只能通过更换IP来解决定位问题。

------------
### Q：候鸟浏览器同时打开多个环境占用CPU和内存资源很大出现卡顿，如何解决？
A：候鸟浏览器是基于chrome内核开发的，每个环境相当于一个独立的chrome浏览器，每个浏览器里面又包含多个浏览器窗口标签，所以对电脑配置的要求相对高一些，建议您不要同时开启太多浏览器环境及窗口标签，提高电脑硬件配置也可以解决。

------------
### Q：yalala网站监测提示DNS获取失败，正常吗？
A：这种情况是因为使用代理IP，获取DNS延时太高导致的，在刷新一次就有了，对浏览器环境的安全性没有影响。

------------
### Q：候鸟浏览器的客户端缓存文件是否可以删除，如果硬盘满了该如何清理？
A：不建议删除，您可以通过软件的设置面板，将存储位置切换到更大的硬盘空间里。

![](6d7b2882624511f09a0d0242ac130006/images/image_92714f4414f3.png)

------------
### Q：为什么我用luminati的动态ip打开yalala网站每次刷新的时区不一样？
A：首先使用候鸟防关联浏览器配置环境的时候时区参数是根据您的代理IP的地理位置自动匹配的，如果您使用的是短效动态代理IP，就会导致检测IP的时候使用的是一个IP，创建环境后打开浏览器又换成另一个IP，这样就会导致每次打开都有可能是不同的IP，时区还是您第一次自动匹配的时区，所以如果您对浏览器时区的变化介意的话，建议不要使用短效的动态IP。使用固定IP不会出现这类问题。

------------
### Q：为什么用luminati代理ip无法打开paypal等网站？
A：有可能是因为luminati代理IP将paypal等网站屏蔽了，请联系luminati官方客服进行确认，该问题跟候鸟浏览器没有关系。为什么用luminati代理ip无法打开paypal等网站？

------------
### Q：Cookie号导入后没反应,导入不全,导不进去,登录不了需要重新输入密码的原因？
A：cookie导入失败有以下四个原因：

**一、Cookie格式不兼容**

Cookie格式分两种一种是JSON格式，一种是CURL库格式也叫纯文本格式，很多软件可能只支持其中的一种格式，这样就会导致cookie的导入失败。当然也有两个方法可以解决：1、通过JSON在线编辑器进行格式的转换，转换之后在导入；2、通过候鸟浏览器进行导入（候鸟同时支持这两种格式的导入）。

**二、Cookie有效期过期了**

Cookie是储存在用户本地终端上的数据，很多网站在开发的时候会给客户端的cookie数据定一个有效期，只有有过开发经验的小伙伴才知道每个网站对cookie的有效期是不一样的，有长有短，大概就是1个月左右。如果cookie数据过了有效期，那么导入之后即使是提示成功的，但是我们在打开网站的时候也看不到登录状态。这个问题即使是程序员通过修改代码也不好解决，给大家一个解决方案，可以用候鸟浏览器导入，候鸟浏览器会根据当前的cookie有效期自动延长一年。当然大家如果想延长cookie的有效期也可以通过在候鸟浏览器导入导出就可以实现，这样就可以解决不修改代码就可以延长有效期了。

**三、Cookie代码不全有残缺**

第三种情况也是比较常见的，而且我们还不容易发现，因为不懂JS代码。有的时候号商和我们自己复制的cookie代码没有复制完全，或者本身就缺一些元素，有头没尾，有尾没头。这样就会导致浏览器识别不出来cookie的完整信息，导致导入不成功。
关于cookie代码不全的残缺问题，在我们拿到cookie代码之后先自查一下，看看代码是否是有效的，用json在线格式转换工具（http://www.bejson.com/jsoneditoronline/ ）检查一下，如果在转换格式的时候出现报错，比如下图，那就说明我们的cookie代码有问题或代码全有残缺。候鸟仿关联指纹浏览器有代码补全的功能，只要不是关键字段缺失基本都能导入成功。

![](6d7b2882624511f09a0d0242ac130006/images/image_c719f37f609f.png)

**四、Cookie代码加密处理过**

还有一个原因会导致cookie号导入失败，无法实现免密登录，那就是cookie数据被加密过。如何区分是否被加密呢，打开cookie看到的都是你看不懂的代码那就100%被加密了。这个就没有办法了，因为解密是一个非常繁琐的过程，一般人是做不到的。当然候鸟也可以导出加密的cookie文件，但是只支持在候鸟浏览器进行导入才可以成功。

------------
### Q：候鸟环境包可以导入到其他指纹浏览器吗？
A：候鸟环境包仅支持候鸟浏览器用户的导入导出，其他浏览器暂不支持！

------------
### Q：候鸟环境包都包含那些信息？
A：候鸟环境包包含浏览器指纹信息、User Agent、屏幕分辨率、代理IP、时区、语言等几十个参数的设置信息，因该功能涉及到多个独家技术，更多参数不便透露！

------------
### Q：候鸟环境包中的原IP失效了，换IP打开环境安全吗？
A：可以的，环境包中包含原设备信息，环境是不变的，IP变化一般不在风控范围。比如我们自己用的手机设备，每次搜索信号、换地点链接WIFI都会换一次IP，所以合理的更换IP是没问题的。

------------
### Q：候鸟一个环境包中可以最多包含几个网站的账号信息？
A：一个环境包可以包含很多网站的账号信息，就像一个独立浏览器一样。比如您用同一个环境登录了邮箱、购物、支付等网站账号，这些账号都会记录在这个环境包之内。前提是这些网站账号要勾选“保持登录状态”并保存账号密码。

------------
### Q：为什么导入的候鸟环境包没有账号的登录状态?
A:有三种可能：1:、导入的的环境包内的Cookie信息失效了（cookie是有时效期的）；2、您在原来的浏览器环境中登录账号，没有在网站的登录页面勾选“保持登录状态”；3、登录后没有在浏览器提示中确认保存账号和密码；4、有可能是账号被更改了密码；

------------
### Q：使用Selenium时，被网站检测到了，要怎么隐藏Selenium的特征，常规操作已经使用了，如 --disable-blink-features=AutomationControlled?
A:在使用Selenium时，有些网站会检测到自动化工具的特征，这可能会导致您的操作被拒绝或者被封禁。为了避免这种情况发生，可以采取以下措施来隐藏Selenium的特征：

修改User-Agent：将默认的User-Agent修改成浏览器的User-Agent，这样能够防止部分简单的反爬机制或用户识别，可通过修改 WebDriver 的 Options 来实现。

使用代理IP：更换 IP 地址也是常见的反反爬机制，所以使用代理IP 可以降低被屏蔽的几率。可自行寻找高质量的代理IP供应商购买IP地址。

模拟真人操作：尽量让程序模拟“人”在操作。比如让程序不要连续爬同一个页面，设置随机时间间隔、随机移动鼠标等等，建议在程序中加入一些人类操作，如点击、滚动、输入，这些可以让网站难以区分是否为人工操作。

使用 Headless 模式：Headless 模式可以让 WebDriver 不打开浏览器窗口而运行，这样可以使其看起来更像是正常的网络请求。

其他辅助方式：可以启用 cookies 和 sessionStorage，并配置相应的信息；也可以使用 WebDriver 的一些隐式等待来缓慢加载页面。

以上是常见的一些方法，但并不能保证百分之百不被检测到，因为防火墙也在更新升级之中。所以，在应用程序中模拟真实浏览器行为、修改 User-Agent 等策略同时借助多台服务器轮流IP，能有效避免反爬虫策略带来的限制。

------------

### Q: 防关联浏览器设置完环境以后，网址可以自己设定吗（候鸟浏览器默认yalala.com）
A: 可以支持自己设定浏览器首页。（如亚马逊，脸书，eBay，知乎，京东等，设置规范：
> https://www.amazon.com

------------
### Q: 关联超级浏览器可以注册完直接下单吗？
A: 不建议直接下单，容易封号，通常需要养号3天左右甚至更长时间。具体需要随时与圈内人士沟通咨询。


------------
### Q: 我们这个支持多人使用同一账号吗？ 团队版 支持添加子账号可以实现。
A: 是可以的。目前个人版不支持多人同时登录，建议选择团队协作版（3+N人模式），这样您的团队应用起来，更加灵活，效率也更高！换设备登录也可以同步环境指纹，不分享就相当于各自用各自的。

------------
### Q: 一个IP 地址下，只能登录一个账号么？可以浏览器多环境登录多个账号不？
A: 软件本身没有限制，但是，建议一个IP创建一个环境，登录一个账号，否则无法实现彻底防关联。如果业务网站风控要求不高，可以多个环境使用一个ip，毕竟每个环境是独立的设备指纹。

------------
### Q: 我们用浏览器注册亚马逊账号，出现验证码了，怎么回事？
A: 这个跟我们软件无关。验证码是亚马逊官网随机的一个规则。跟IP纯净度有关，建议多换两个ip试试。当然也可以多创建两个不同环境来尝试过验证。

------------
### Q: 如果我有1000个亚马逊或谷歌账号，是不是就需要创建1000个浏览器环境？
A: 是的，一个账号一个环境，（就相当于一个账号一个独立的设备了）。候鸟浏览器可以满足您一台设备，批量操作这些账号了，节省时间成本。

------------
### Q: 之前用Chrome浏览器配代理ip ，每次都要清除缓存。这个就不用了吧？
A: 候鸟防关联浏览器不需要的，我们的cookie都是单独隔离的。即使要清除cookie，右键直接选择清除即可。

------------
### Q: 候鸟防关联浏览器创建号不会占用电脑内存吧？会卡吗？
A: 开几个环境，几乎很少占用的（电脑配置最好是固态硬盘存储安装候鸟，内存16G以上）。但是跟同时打开多个环境会有关系（每个环境500M内存）。
如果觉得电脑运行环境卡，可以尝试开启候鸟的设置选项中的本地模式，可以有效提高运行速度。

------------
### Q: 如果误操作删除了环境，想要恢复如何处理？
A: 两个办法解决

（1）通过软件左上角数据备份，进入后选择当天且最大的文件，点击后  选择下方的从备份后恢复按钮。

（2）网页后台登录后，找到历史环境列表，可以选择相应的环境，恢复历史环境发送到客户端   [https://www.mbbrowser.com/console/console/session/history](https://www.mbbrowser.com/console/console/session/history "https://www.mbbrowser.com/console/console/session/history")

------------
### Q: 使用过程中，多开环境觉得卡，怎么办？
A: （杀毒和管家那些不要开，如360卫士，金山，火绒，瑞星，鲁大师，腾讯或联想管家等，都退出）：

（1）候鸟安装在C盘（C盘空间越大越好）以外的的NVME/SSD分区，比如D,E盘，优先使用NVME 固态硬盘，其次SSD固态硬盘；不建议使用外接移动硬盘或U盘!且优先尽量使用官网最新版！

（2）存储路径放到NVME,M2，SSD固态硬盘最好，并做好4K对齐（京东一般固态几百元可以搞定，可以有效提升运行顺畅的根本办法）。

（3）如果本机除了候鸟窗口最大化，其他程序开启也最大化，建议不要所有窗口摞到一起（如本机浏览器，和一些其他软件程序），将其他窗口最小化，只保留候鸟在桌面最大化即可。

（4）如果出现短暂黑屏，可以尽快在客户端面板先停止那个黑屏的环境运行，再开启运行。如果实在卡住了，就要任务管理器先关闭任务进程。

（5）环境窗口可以尽量小一些（拉伸窗口的前提一定是静态长效ip搭建环境！比如用手机模拟环境或任意win分辨率然后拉伸），一个是可以多个一起看方便一个确实比全屏不卡；不过右上角按钮要是没有显示，确实要全屏看好些。

（6）附加小技巧：每个环境运行之间间隔20秒以上，这样可以有效让已经运行的环境导致电脑的CPU上去90%就会卡住，从而瞬间降下来。

（7）尽可能提高配置，如增加电脑系统内存。

（8）更换高电脑性能CPU。


------------
### Q: 如果长期使用本地模式操作候鸟软件，之后更换设备登录同一个账号，出现环境数量不一样（缺少环境显示），如何解决？
A: 两个办法使用一个即可，建议使用方法2 更为简单！

（1）复制电脑1，就是使用本地模式的那台候鸟data文件复制给电脑2的候鸟data目录中，且对应。

（2）导出电脑1的环境包给电脑2本地保存后，然后电脑2候鸟导入本地环境包即可恢复同步环境。

------------
### Q: 如果有个别环境，配置代理检测没问题，运行后打不开任何网页，怎么办？
A: （1）新建一个环境，配置同样代理ip，打开网页看看是否正常；

（2）新环境打开没问题的话，可以尝试右键问题环境名称后选择克隆环境（单个克隆即可，名称修改一下）再次打开是否正常，没问题的话就用克隆环境（因为指纹和之前环境一模一样），原环境可以删除。

------------
### Q: 支持selenium 自动化么？
A: 可以。在新建自动化脚本窗口配置对应信息 【脚本类型】有Puppeteer和Selenium种类型选择 ！

【脚本语言】脚本类型为Puppeteer，脚本语言为JS，脚本类型为Puppeteer，脚本语言为Python 【脚本名称】根据您的需求自定义脚本名称 【脚本描述】脚本功能详细描述。具体详见[https://help.mbbrowser.com/tutorial/autoscripts.html](https://help.mbbrowser.com/tutorial/autoscripts.html "https://help.mbbrowser.com/tutorial/autoscripts.html")

------------
### Q: 我的候鸟软件，换设备登录不了，提示在线状态，怎么办？
A: 可以通过个人账号后台点击强制下线，或者联系客服下线。[https://www.mbbrowser.com/console/console](https://www.mbbrowser.com/console/console "https://www.mbbrowser.com/console/console")

------------
### Q: 我使用候鸟很长时间了，偶尔会出现网络同步报错提示，或插件下载失败提示。如何处理？
A: 建议您卸载现有版本，安装官网的最新版本使用。

------------
### Q: 分享环境失败，怎么解决？
A: 建议您更新安装候鸟官网下载页面的最新版本！[https://www.mbbrowser.com/download](https://www.mbbrowser.com/download "https://www.mbbrowser.com/download")

------------
### Q: Tiktok或Youtube上传视频文件，很慢如何解决？
A: 建议确保本机网络顺畅的前提下，关闭防火墙，另外上传前开启gpu按钮（具体位置在客户端右下角齿轮点击后找到浏览器设置按钮第二个选项 点击GPU渲染开启）备注：上传完毕后，将这个按钮迅速关闭。

------------
### Q: 自动化脚本有没有渠道提供，帮我们用户指导一下呢？
A: 自动化脚本插件除了chatgpt可以智能写好需求；也可以通过一些自动化插件来实现辅助指导，比如候鸟官方的插件库的这些自动化插件（具体步骤点击左上角设置按钮，找到插件管理器选项，下拉后有 自动化脚本插件库）。

------------
### Q: 候鸟的ua到底如何使用配置？
A: 关于ua的使用

（1）点击右上角的分辨率 ，不同的数字  ，来生成更多的ua， 数据到自己的候鸟账号，每次创建新环境可以不同的ua供自己选择，从而不会和他人共享一个ua设备。

（2）根据自己的业务平台 ，选择用什么系统来做， 如Windows电脑端，  iPhone或  Android等手机端的ua设备环境等（系统选择详见候鸟客户端 环境设置面板的第一行  系统）

（3）以后每次打开环境可以切换随机的ua，如果做注册类，刷单类业务 ，可以经常切换ua ；如果要求稳定的环境 ，养号或者固定的搭配不变，就可以不动ua 使用默认的。

（4）当然您如果有自己的ua资源，可以在ua管理按钮中，添加自己的ua数据，您的候鸟账号可以独享专属。

------------
### Q: 我电脑安装候鸟，新建环境后，右下角没有创建环境的按钮，为什么？
A: 针对新建环境面板，下面不显示创建环境的按钮一行，可以将本机的桌面显示电脑尺寸（具体步骤为在电脑桌面鼠标右键 选择显示设置），然后找到缩放与布局选项，那里改成100%


------------
### Q: 导入候鸟环境的cookie格式有什么讲究吗？
A: 要能实现导入候鸟，必须有关键的七个字段，只要字段对应正确，导入没问题。 具体不明白，可以联系客服。
` {"domain","expirationDate","httpOnly","name","path","secure","value"}`

================================================== 英文内容 ==================================================
### Q：Browser window size border is too large to shrink how to solve?
A：Yes, the browser window size is fixed when the environment is created, and changing the size will change the browser fingerprint, affecting the security of the environment. If you want to make the browser window smaller, you can re-select a smaller resolution when creating or modifying the environment.

------------
### Q：Why a message indicating that more sessions cannot be created is displayed when I click Create Environment after downloading and installing the software? Can't create an environment?
A：It is possible that you have not yet paid for the package you are on that page [https://www.mbbrowser.com/price](https://www.mbbrowser.com/price "https://www.mbbrowser.com/price") Use after purchase package.

------------
### Q：Does Mbbrowser support mobile or IPAD operation?
A：Mbbrowser does not support mobile terminal installation operation, but you can select the UA simulation mobile terminal page when creating the environment on the PC side.

------------
### Q：Does Mbbrowser run locally or on a server (VPS)?
A：As long as windows 7 or higher operating system is acceptable.

------------
### Q：What proxy IP type does Migratory Bird Browser support?
A：It is supported by almost all ipv4 types and proxy IP providers on the market. ipV6 IP addresses are excluded.

------------
### Q：How many environments does  Mbbrowser support?
A：There is no limit to the number of environments created for Personal Unlimited and above.

------------
### Q：How to use Mbbrowser, there is a detailed tutorial?
A：[https://www.mbbrowser.com/help](https://www.mbbrowser.com/help "https://www.mbbrowser.com/help") You can see the help page of the official website for a detailed configuration tutorial.

------------
### Q：Can environments be grouped in the Mbbrowser client?
A：Grouping has been supported since version ********.

------------
### Q：Does Mbbrowser have a proxy IP and port for an environment?
A：Yes, you can. Mbbrowser anti - association fingerprint browser can correspond to a proxy IP and port environment.

------------
### Q：What type should I select as the first proxy option when creating the environment?
A：Select the appropriate type based on the proxy IP you purchased, such as socks5 or http.

------------
### Q：Do I need to fill in the password for setting proxy IP on the Create Environment panel?
A：The account password of the agent IP is optional. It is necessary to check whether your purchase agent IP has the account password. If there is one, you need to fill it in.

------------
### Q：Does Mbbrowser customize home page?
A：Mbbrowser supports custom home page; Change the Gear flag - General Settings - Default start page in the lower right corner after logging in to the client.

------------
### Q：Can only one proxy IP Address be used to log in to one account? Can you log in to multiple accounts at the same time?
A：The Mbbrowser has no restriction on multiple accounts corresponding to the proxy IP address. However, you are advised to log in to one account using one IP address. Otherwise, the connection cannot be completely prevented.

------------
### Q：With Mbbrowser login multi account, computer can shut down?
A：Normal users browse the website through the browser is not need to hang up, according to your needs normal use of mbbrowser can!

------------
### Q：With Mbbrowser to register website account, verification code and manual verification, normal?
A：Some websites login and registration prompts verification code and manual verification is normal, this situation is related to the purity of the proxy IP, if a proxy IP in a short time to repeatedly visit a site will be the site required for manual verification, the situation has nothing to do with the browser.

------------
### Q：Mbbrowser can change MAC network card address?
A：Mbbrowser is an anti-association browser. Only the FLASH plug-in can be used to read the MAC address of the client through the browser. Currently, the FLASH plug-in has stopped updating, and the chrome browser kernel no longer supports the FLASH plug-in.

------------
### Q：Does Mbbrowser have enough UA? What if the UA Settings are repeated?
A：First of all, the UA of Mbbrowser is random, we have hundreds of thousands of UA bank, theoretically enough. And the UA records operating system and browser information. These are the few options on their own. Therefore, it is normal and logical that there will still be a large number of repetition possibilities under various permutations and combinations.

------------
### Q：Can the subaccount environment be shared with the main account? Can sub-accounts create environments?
A：The sub-account can share the created environment with the master account. Meanwhile, the permissions of the client terminal account and the master account are the same. Therefore, the sub-account can create an environment independently. Can the subaccount environment be shared with the main account? Can sub-accounts create environments?

------------
### Q：Mbbrowser login website account password is automatically backed up, can realize free login?
A：Mbbrowser website account password is automatically backed up, the premise is that when you log in to the website to check the "keep login status" and other options; The implementation of no-login depends on the rules on the site side, and the rules for Migratory Bird are the same as for chrome.

------------
### Q：I used dynamic IP to create the environment for the first time and check the security is 100%, why is it 70% on the second opening?
A：If you use a dynamic IP address, you are advised to deselect WebRTC Fingerprint in the Configuration environment window. This is because the IP address of the environment is different every time you open the dynamic IP address, but the WbeRTC public IP address is the last one. As a result, the IP address does not match the WebRTC public address.

![](6d7b2882624511f09a0d0242ac130006/images/image_420ca2df7871.png)

------------
### Q：I use the proxy IP of luminati or 911S5. Do I need to open the client of LMP and 911S5 during the process of using Mbbrowser?
A：Yes, if you are using luminati or 911s5, you need to turn on the proxy IP for normal access.

------------
### Q：Do you have any requirements for computer configuration?
A：It is recommended to have a 4-core processor, at least 8g memory, and a windows7 or higher operating system.

------------
### Q：Can use Mbbrowser speed up? Why is it slow for me to access websites using proxy IP？
A：Mbbrowser to visit the website for optimization processing, browsing speed is mainly determined by the proxy IP, itself using the proxy IP to visit the website speed is not as fast as the local direct access.

------------
### Q：Why is there so little UA built into the Mbbrowser, and it refreshes the same several times?
A：In order to ensure the safety of the environment, UA and resolution parameters are bound. It is normal that the number of UA under some non-mainstream resolution size is small. It is recommended that you choose different browser resolution to refresh UA, and you will find that the situation of UA repetition is very few. Migratory Bird software also supports custom UA, you can copy your own UA to use in migratory bird.

------------
### Q：Can Mbbrowser disguise GPS map location?
A：The location function of the browser on the PC is determined based on the geographical location of the IP address. Unlike mobile phones, which use GPS, the location problem can only be solved by changing IP addresses.

------------
### Q：How to solve the problem that the mbbrowser opens multiple environments at the same time, which consumes a lot of CPU and memory resources.
A：Mbbrowser is based on the chrome kernel development, each environment is equivalent to an independent chrome browser, each browser contains a number of browser window label, so the requirements for computer configuration is relatively high, it is recommended that you do not open too many browser environment and window label, improve the computer hardware configuration can also be solved.

------------
### Q：yalala website monitoring prompt DNS acquisition failure, normal?
A：This is because the proxy IP address is used and the DNS acquisition delay is too high. The DNS server is updated once and has no impact on the security of the browser environment.

------------
### Q：Can the client cache file of Mbbrowser be deleted, and how to clean it if the hard disk is full?
A：You are not advised to delete it. You can use the Settings panel of the software to switch the storage location to a larger disk space.

![](6d7b2882624511f09a0d0242ac130006/images/image_27e865a1b628.png)

------------
### Q：Why did I open the yalala website with luminati's dynamic ip and refresh the time zone differently each time?
A：First of all, the time zone parameter is automatically matched according to the geographical location of your proxy IP when configuring the environment using the migratory bird anti-association browser. If you use the short-acting dynamic proxy IP, it will lead to the use of one IP when detecting the IP, and then change the browser to another IP after creating the environment, which will lead to a different IP each time it is opened. The time zone is also the one you automatically match for the first time, so if you care about the change in browser time zone, it is recommended not to use short-acting dynamic IP. This problem does not occur with fixed IP.

------------
### Q：Why is it impossible to open websites such as paypal using luminati proxy ip?
A：It may be because the luminati proxy IP has blocked paypal and other websites. Please contact luminati official customer service for confirmation. This problem has nothing to do with Migratory Bird browser. Why is it impossible to open websites such as paypal using luminati proxy ip?

------------
### Q：Cookie number does not react after import, import is not complete, guide not in, login can not need to re-enter the password reason?
A：The cookie import fails for the following four reasons:

**1、Cookie format is not compatible**

There are two types of Cookie formats: JSON format and CURL library format, also known as plain text format. Some software may support only one of these formats, which may cause cookie import failure. Of course, there are two ways to solve this problem: 1. Convert the format through the JSON online editor, and import the format after conversion; 2. Import through Migratory Bird browser (Migratory Bird supports both import formats).

**2、The Cookie has expired**

Cookie is the data stored on the user's local terminal. Many websites will set a validity period for cookie data on the client when developing. Only those with development experience know that each website has a different validity period for cookie, which is about 1 month. If the cookie data expired, then import even if the prompt success, but we can not see the login status when we open the website. This problem is not easy to solve even programmers by modifying the code, to give you a solution, you can use the migratory bird browser import, migratory bird browser will automatically extend the validity of the current cookie for one year. Of course, if you want to extend the validity of the cookie can also be achieved by importing and exporting in the migratory bird browser, so that you can solve the problem without modifying the code can be extended.

**3、Cookie code is not all incomplete**

The third case is also relatively common, and we are not easy to spot, because we do not understand the JS code. Sometimes the number quotient and our own copy of the cookie code is not completely copied, or itself is missing some elements, have a head without a tail, a tail without a head. As a result, the browser cannot identify the complete information of the cookie, resulting in a failure to import.
Disability issues about cookie code is not complete, after we get the cookie code inspection first, and see if the code is valid, use json format conversion tool (http://www.bejson.com/jsoneditoronline/) to check online, If an error occurs while converting the format, as shown in the figure below, then our cookie code is faulty or completely broken. Migratory bird imitation associated fingerprint browser has the function of code completion, as long as the key field is not missing basically can import successfully.

![](6d7b2882624511f09a0d0242ac130006/images/image_c719f37f609f.png)

**4、The Cookie code is encrypted. **

There is another reason that the cookie number import fails and encryption-free login cannot be achieved, that is, the cookie data is encrypted. How do you tell if it's encrypted? If you open a cookie and you see code that you don't understand, it's 100% encrypted. There is no way out of this, because decryption is a very tedious process, ordinary people cannot do it. Of course, Migratory bird can also export encrypted cookie files, but only support import in Migratory bird browser can be successful.

------------
### Q：Can the Mbbrowser Environment Pack be imported into other fingerprint browsers?
A：Mbbrowser environment package only supports the import and export of Mbbrowser users, other browsers do not support!

------------
### Q：What information does the Mbbrowser Environment Pack contain?
A：Mbbrowser environment package contains dozens of parameter Settings such as browser fingerprint information, User Agent, screen resolution, agent IP, time zone, language, etc., because this function involves several exclusive technologies, more parameters are not disclosed!

------------
### Q：The original IP in the Mbbrowser environment package is invalid. Is it safe to change the IP address to open the environment?
A：Yes, the environment package contains the original device information. The environment is unchanged, and IP changes are not subject to risk control. For example, for our own mobile devices, IP will be changed every time we search signal, change location and connect to WIFI, so it is no problem to change IP reasonably.

------------
### Q：Mbbrowser an environmental package can contain at most how many website account information?
A：An environment package can contain account information for many websites, just like a standalone browser. For example, if you use the same environment to log in email, shopping, payment and other website accounts, these accounts will be recorded in the environment package. The premise is that these website accounts to check "login status" and save the account password.

------------
### Q：Why does the imported Mbbrowser environment package have no login status of the account?
A：There are three possibilities: 1. The Cookie information in the imported environment package is invalid (cookie has an expiration period); 2. 2. You logged in to the account in the original browser environment, but did not check "Maintain login status" on the login page of the website; 3. You do not confirm to save the account and password in the browser prompt after login. 4. It is possible that the account password has been changed;

------------
### Q：When using Selenium, it has been detected by a website. How can I hide the features of Selenium when routine operations have already been used, such as --disable-blink-features=AutomationControlled?
A：When using Selenium, some websites detect characteristics of automated tools, which may result in your actions being denied or blocked. To prevent this from happening, you can take the following steps to hide Selenium's characteristics:

Modify user-agent: Change the default user-agent to the user-agent of the browser to prevent some simple anti-crawling mechanisms or User identification. You can modify WebDriver Options to achieve this.

Using a proxy IP address: Changing IP addresses is also a common anti-creep mechanism, so using a proxy IP address can reduce the chance of being blocked. You can find a high quality proxy IP provider to purchase IP addresses.

Simulation of human operation: try to let the program simulate "human" in operation. For example, let the program not continuously climb the same page, set a random time interval, random mouse and so on, suggest adding some human operations in the program, such as click, scroll, input, these can make the site difficult to distinguish whether it is manual operation.

Use Headless mode: Headless mode lets WebDriver run without opening a browser window, which makes it look more like a normal network request.

Other auxiliary methods: You can enable cookies and sessionStorage and configure the corresponding information. You can also use some of WebDriver's implicit waits to load pages slowly.

These are common methods, but there is no guarantee that they will not be detected, because the firewall is also being updated. Therefore, the restrictions brought by anti-crawler policies can be effectively avoided by simulating real browser behaviors and modifying User-Agent policies in applications while taking advantage of multiple servers' IP turns.

------------

### Q: After setting up the environment for the anti association browser, can the website be set by yourself (the default for the Mbbrowser is yalala.com)
A: You can support setting your own browser homepage. (For example, on Amazon, Facebook, eBay, Zhihu, JD.com, etc.), setting specifications:
> https://www.amazon.com

------------

### Q: Can I directly place an order after registering with an associated super browser?
A: It is not recommended to place an order directly, as it is prone to account suspension and usually requires account maintenance for about 3 days or even longer. Specific communication and consultation with industry insiders are required at any time.

------------

### Q: Does this support multiple people using the same account? The team version supports adding sub accounts.
A: It is possible. At present, the personal version does not support multiple people logging in at the same time. It is recommended to choose the team collaboration version (3+N person mode), so that your team can apply it more flexibly and efficiently! Switching devices and logging in can also synchronize environmental fingerprints, without sharing, it is equivalent to using their own.

------------

### Q: Can I only log in to one account under one IP address? Can I log in to multiple accounts in multiple browser environments?
A: The software itself has no restrictions, but it is recommended to create an environment for each IP and log in to an account, otherwise complete anti association cannot be achieved. If the risk control requirements for business websites are not high, multiple environments can use the same IP, as each environment has independent device fingerprints.

------------

### Q: We registered an Amazon account using a browser and encountered a verification code. What happened?
A: This is not related to our software. The verification code is a random rule on Amazon's official website. It is related to the purity of the IP, and it is recommended to try changing to two more IPs. Of course, you can also create two different environments to try verification.

------------

### Q: If I have 1000 Amazon or Google accounts, do I need to create 1000 browser environments?
A: Yes, one account, one environment (equivalent to one account, one independent device). The Mbbrowser can meet your needs for batch operation of these accounts on one device, saving time and cost.

------------

### Q: Previously, when using Chrome browser with proxy IP, I had to clear the cache every time. Isn't this enough?
A: The bird defense associated browser does not require it, as our cookies are isolated separately. Even if you want to clear the cookie, right-click and select Clear directly.

------------

### Q: Will the creation of a bird defense associated browser account not occupy computer memory? Will it get stuck?
A: Open several environments, almost occupying very little (the computer configuration is best to use solid-state drive storage for installation, with over 16GB of memory). But it is related to opening multiple environments at the same time (500 MB of memory per environment).
If you feel that the computer is running in a slow environment, you can try turning on the local mode in the Mbbrowser settings option, which can effectively improve the running speed.

------------

### Q: If the environment is accidentally deleted, how do you want to recover it?
A: Two ways to solve it

(1) By backing up data in the upper left corner of the software, select the file with the largest size of the day and click on the "Restore from Backup" button below.

(2) After logging in to the webpage backend, find the list of historical environments, select the corresponding environment, and restore the historical environment by sending it to the client [https://www.mbbrowser.com/console/console/session/history](https://www.mbbrowser.com/console/console/session/history "https://www.mbbrowser.com/console/console/session/history")

------------

### Q: What should I do if I feel stuck in the environment during use?
A: (Do not open antivirus and butler apps, such as 360 Guardian, Kingsoft, Huorong, Rising, Master Lu, Tencent, or Lenovo Butler, etc.):

(1) Mbbrowser are installed on NVME/SSD partitions outside of the C drive (larger C drive space is better), such as D and E drives. NVME solid-state drives are preferred, followed by SSD solid-state drives; It is not recommended to use external portable hard drives or USB drives! And prioritize using the latest version of the official website as much as possible!

(2) It is best to place the storage path on NVME, M2, or SSD solid-state drives, and ensure 4K alignment (JD usually pays a few hundred yuan for solid-state drives, which can effectively improve smooth operation).

(3) If other programs are also maximized except for the Mbbrowser window, it is recommended not to stack all windows together (such as the local browser and some other software programs), minimize other windows, and only keep the Mbbrowser on the desktop maximized.

(4) If there is a brief black screen, you can stop running in the black screen environment in the client panel as soon as possible and then start running. If it really gets stuck, the task manager should first shut down the task process.

(5) The environment window can be as small as possible (the prerequisite for stretching the window is to build a static and long-lasting IP environment! For example, using a mobile phone to simulate the environment or any win resolution and then stretching it), one is that it can be viewed together for convenience, and the other is indeed not stuck compared to full screen; However, if the button in the upper right corner is not displayed, it is indeed better to see it in full screen.

(6) Additional tip: The interval between running each environment should be more than 20 seconds, which can effectively prevent the already running environment from causing the computer's CPU to freeze by 90%, resulting in an instant drop.

(7) Try to improve the configuration as much as possible, such as increasing computer system memory.

(8) Replace the high-performance CPU of the computer.

------------

### Q: How to solve the problem of using local mode to operate Mbbrowser software for a long time, and then changing the device to log in to the same account, resulting in different numbers of environments (lack of environment display)?
A: Use one of the two methods, it is recommended to use method 2 for simplicity!

(1) Copying computer 1 means copying the Mbbrowser data file from the local mode to the Mbbrowser data directory of computer 2, and corresponding to it.

(2) Export the environment package from computer 1 to computer 2 and save it locally. Then, computer 2 can import the local environment package to restore the synchronized environment.

------------

### Q: If there are individual environments and there is no problem with configuring proxy detection, but no web pages can be opened after running, what should I do?
A: (1) Create a new environment, configure the same proxy IP, and open a webpage to see if it works properly;

(2) If it is OK to open the new environment, you can try to right-click the name of the problem environment and select Clone Environment (just a single clone, change the name). If it is OK, you can use Clone Environment (because the fingerprint is the same as the previous environment). The original environment can be deleted.

------------

### Q: Does it support selenium automation?
A: Sure. Configure the corresponding information in the new automation script window. There are Puppeter and Selenium types to choose from!
[Script Language] The script type is Puppeter, the script language is JS, the script type is Puppeter, and the script language is Python. [Script Name] Customize the script name according to your needs. [Script Description] Detailed description of the script function. For specific details, please refer to [https://help.mbbrowser.com/tutorial/autoscripts.html](https://help.mbbrowser.com/tutorial/autoscripts.html "https://help.mbbrowser.com/tutorial/autoscripts.html")

------------

### Q: My Mbbrowser software is unable to log in when changing devices, and it shows an online status prompt. What should I do?
A: You can click on force offline through the personal account backend, or contact customer service to offline. [https://www.mbbrowser.com/console/console](https://www.mbbrowser.com/console/console "https://www.mbbrowser.com/console/console")

------------

### Q: I have been using Mbbrowser for a long time, and occasionally there are network synchronization error messages or plugin download failure messages. How to handle it?
A: We suggest that you uninstall the existing version and install the latest version on the official website to use.

------------

### Q: Sharing environment failed, how to solve it?
A: We suggest that you update and install the latest version of the official website download page for Mbbrowser! [https://www.mbbrowser.com/download](https://www.mbbrowser.com/download "https://www.mbbrowser.com/download")

------------

### Q: How to solve the problem of slow video file upload on Tiktok or YouTube?
A: It is recommended to turn off the firewall and enable the GPU button before uploading, while ensuring a smooth local network. The specific location can be found in the lower right corner of the client's gear. Click on the browser settings button and select the second option to enable GPU rendering. Note: After uploading, quickly turn off this button.

------------

### Q: Is there a channel to provide automation scripts? Can you help us users guide them?
A: Automation script plugins can intelligently write requirements in addition to chatgpt; Some automation plugins can also be used to assist guidance, such as the automation plugins in the official plugin library of Mbbrowser (specific steps: click the settings button in the upper left corner, find the plugin manager option, and the automation script plugin library will be available after dropdown).

------------

### Q: How to use and configure the UA of Mbbrowser?
A: Regarding the use of UA

(1) Click on the resolution and different numbers in the upper right corner to generate more UA, and transfer the data to your Mbbrowser account. Each time you create a new environment, you can choose different UA for yourself, so you won't share a single UA device with others.

(2) Based on your business platform, choose which system to use, such as the UA device environment for Windows computers, iPhones, Android, and other mobile devices (system selection can be found in the first line of the Mbbrowser client environment settings panel)

(3) In the future, you can switch between random UAs every time you open the environment. If you are doing registration or brushing business, you can frequently switch between UAs; If a stable environment is required, and the number or fixed combination remains unchanged, the default can be used without any changes.

(4) Of course, if you have your own UA resources, you can add your own UA data in the UA management button, and your Mbbrowser account can enjoy exclusive access.

------------

### Q: After installing Mbbrowser on my computer and creating a new environment, there is no create environment button in the bottom right corner. Why?
A: For the New Environment panel, the button for creating the environment is not displayed in the row below. You can display the computer size on the desktop of this computer (the specific steps are to right-click on the computer desktop and select Display Settings), then find the Zoom and Layout options, and change them to 100%

------------

### Q: Is there any particular format for importing cookies into Mbbrowser environments?
A: To be able to import Mbbrowser, there must be seven key fields. As long as the fields correspond correctly, importing is not a problem. If you don't understand the specifics, you can contact customer service.
`{"domain", "expirationDate", "https Only", "name", "path", "secure", "value"}`