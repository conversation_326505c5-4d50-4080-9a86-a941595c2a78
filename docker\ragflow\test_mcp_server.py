#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RAGFlow MCP Server 测试脚本
"""

import requests
import json
import time
import sys

class RAGFlowMCPTester:
    def __init__(self, base_url="http://localhost:9382", api_key="ragflow-mcp-2025"):
        self.base_url = base_url
        self.api_key = api_key
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
            "User-Agent": "RAGFlow-MCP-Tester/1.0"
        }
    
    def test_connection(self):
        """测试MCP Server连接"""
        print("🔗 测试MCP Server连接...")
        
        try:
            # 尝试连接健康检查端点
            response = requests.get(f"{self.base_url}/health", timeout=10)
            if response.status_code == 200:
                print(f"✅ MCP Server连接成功 (状态码: {response.status_code})")
                return True
            else:
                print(f"⚠️  MCP Server响应异常 (状态码: {response.status_code})")
                return False
        except requests.exceptions.ConnectionError:
            print("❌ 无法连接到MCP Server，请检查服务是否启动")
            return False
        except requests.exceptions.Timeout:
            print("❌ 连接超时，MCP Server可能还在启动中")
            return False
        except Exception as e:
            print(f"❌ 连接测试失败: {str(e)}")
            return False
    
    def test_api_endpoints(self):
        """测试API端点"""
        print("\n🧪 测试API端点...")
        
        # 常见的API端点
        endpoints = [
            "/health",
            "/api/v1/status",
            "/api/v1/info",
            "/mcp/status",
            "/version"
        ]
        
        for endpoint in endpoints:
            try:
                url = f"{self.base_url}{endpoint}"
                response = requests.get(url, headers=self.headers, timeout=5)
                
                if response.status_code == 200:
                    print(f"✅ {endpoint}: 可用 (状态码: {response.status_code})")
                    try:
                        data = response.json()
                        print(f"   响应: {json.dumps(data, indent=2, ensure_ascii=False)[:200]}...")
                    except:
                        print(f"   响应: {response.text[:100]}...")
                elif response.status_code == 404:
                    print(f"⚠️  {endpoint}: 端点不存在 (状态码: {response.status_code})")
                else:
                    print(f"❌ {endpoint}: 错误 (状态码: {response.status_code})")
                    
            except requests.exceptions.Timeout:
                print(f"⏰ {endpoint}: 请求超时")
            except Exception as e:
                print(f"❌ {endpoint}: 请求失败 - {str(e)}")
    
    def test_mcp_query(self):
        """测试MCP查询功能"""
        print("\n📝 测试MCP查询功能...")
        
        # 测试查询数据
        test_queries = [
            {
                "query": "Hello, MCP Server!",
                "context": "test"
            },
            {
                "query": "候鸟浏览器如何使用？",
                "context": "ragflow_test"
            }
        ]
        
        query_endpoints = [
            "/api/v1/query",
            "/mcp/query",
            "/query"
        ]
        
        for endpoint in query_endpoints:
            print(f"\n🔍 测试端点: {endpoint}")
            
            for i, query_data in enumerate(test_queries, 1):
                try:
                    url = f"{self.base_url}{endpoint}"
                    response = requests.post(
                        url, 
                        headers=self.headers, 
                        json=query_data, 
                        timeout=10
                    )
                    
                    if response.status_code == 200:
                        print(f"✅ 查询 {i}: 成功 (状态码: {response.status_code})")
                        try:
                            result = response.json()
                            print(f"   结果: {json.dumps(result, indent=2, ensure_ascii=False)[:300]}...")
                        except:
                            print(f"   结果: {response.text[:200]}...")
                    else:
                        print(f"❌ 查询 {i}: 失败 (状态码: {response.status_code})")
                        print(f"   错误: {response.text[:200]}")
                        
                except requests.exceptions.Timeout:
                    print(f"⏰ 查询 {i}: 请求超时")
                except Exception as e:
                    print(f"❌ 查询 {i}: 请求失败 - {str(e)}")
    
    def check_docker_status(self):
        """检查Docker容器状态"""
        print("\n🐳 检查Docker容器状态...")
        
        try:
            import subprocess
            
            # 检查RAGFlow容器状态
            result = subprocess.run(
                ["docker", "ps", "--filter", "name=ragflow", "--format", "table {{.Names}}\t{{.Status}}\t{{.Ports}}"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                print("📊 RAGFlow容器状态:")
                print(result.stdout)
            else:
                print("❌ 无法获取Docker容器状态")
                
        except subprocess.TimeoutExpired:
            print("⏰ Docker命令执行超时")
        except FileNotFoundError:
            print("❌ Docker命令未找到，请确保Docker已安装")
        except Exception as e:
            print(f"❌ 检查Docker状态失败: {str(e)}")
    
    def run_full_test(self):
        """运行完整测试"""
        print("🚀 RAGFlow MCP Server 完整测试")
        print("=" * 50)
        
        # 检查Docker状态
        self.check_docker_status()
        
        # 等待服务启动
        print("\n⏳ 等待MCP Server启动...")
        time.sleep(5)
        
        # 测试连接
        if not self.test_connection():
            print("\n❌ MCP Server连接失败，测试终止")
            return False
        
        # 测试API端点
        self.test_api_endpoints()
        
        # 测试查询功能
        self.test_mcp_query()
        
        print("\n" + "=" * 50)
        print("🎉 MCP Server测试完成！")
        
        return True

def main():
    """主函数"""
    print("RAGFlow MCP Server 测试工具")
    print("配置信息:")
    print(f"  MCP Server地址: http://localhost:9382")
    print(f"  API密钥: ragflow-mcp-2025")
    print()
    
    tester = RAGFlowMCPTester()
    
    try:
        tester.run_full_test()
    except KeyboardInterrupt:
        print("\n\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {str(e)}")

if __name__ == "__main__":
    main()
