												        			 										<Button name="btnuamana"  bkimage="bk_quit_normal.png" hotimage="bk_quit_hot.png" pushedimage="bk_quit_hot.png" padding="21,5,0,0"  width="145" height="26" text="UserAgent管理器"/>
																											</VerticalLayout>
																												  <Control />
																							</HorizontalLayout>


																						<HorizontalLayout height="12">
																							</HorizontalLayout>
																					   <HorizontalLayout height="28">
																								<VerticalLayout width="160">
																							      <Label  name="ploginpass" padding="22,0,0,0" text="国家代号：" width="160" textcolor="#FF333333"  font="8"></Label>
																							  </VerticalLayout>
																							</HorizontalLayout>
																							<HorizontalLayout height="36" >
																							    	 <VerticalLayout>
																								     <Combo name="system" itemtextpadding="10,0,0,0" bordersize="0" padding="21,0,0,10" width="280" height="36" borderround="3,3" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="4,2,0,0" bkcolor="#ffdce1e7"	normalimage="file='Profile\Setting_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\Setting_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\Setting_Combox_Click.png' corner='5,5,25,10'" combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'" itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" >
																										<ListLabelElement height="36" pos="66,0,0,0" textpadding="10,0,0,0" text="US" font="0" >
																											<Label name="telnet" pos="66,0,0,0" textpadding="10,0,0,0" text="US"  height="36" width="320" textcolor="#FF000000"/>
																										</ListLabelElement>

																										<ListLabelElement height="36" pos="66,0,0,0" textpadding="10,0,0,0" text="JP" font="0" selected="true">
																											<Label name="socks4" pos="66,0,0,0" textpadding="10,0,0,0" text="JP"  height="36" width="320" textcolor="#FF000000"/>
																										</ListLabelElement>

															                      <ListLabelElement height="36" pos="66,0,0,0" textpadding="10,0,0,0" text="AE" font="0" >
																											<Label name="socks5noauth" pos="66,0,0,0" textpadding="10,0,0,0" text="AE"  height="36" width="320" textcolor="#FF000000"/>
																										</ListLabelElement>

																										<ListLabelElement height="36" pos="66,0,0,0" textpadding="10,0,0,0" text="ED" font="0" >
																											<Label name="socks5auth" pos="66,0,0,0" textpadding="10,0,0,0" text="ED"  height="36" width="320" textcolor="#FF000000"/>
																										</ListLabelElement>

																										<ListLabelElement height="36" pos="66,0,0,0" textpadding="10,0,0,0" text="EA" font="0" >
																											<Label name="HTTPnoauth" pos="66,0,0,0" textpadding="10,0,0,0" text="EA"  height="36" width="320" textcolor="#FF000000"/>
																										</ListLabelElement>

																										<ListLabelElement height="36" pos="66,0,0,0" textpadding="10,0,0,0" text="BC" font="0" >
																											<Label name="HTTPauth" pos="66,0,0,0" textpadding="10,0,0,0" text="BC"  height="36" width="320" textcolor="#FF000000"/>
																										</ListLabelElement>

																									</Combo>
																								     </VerticalLayout>
																							</HorizontalLayout>
																							 <Control height="12"/>

																								<HorizontalLayout height="36" >
																							    	 <VerticalLayout width="320">
																								       <RichEdit name="lnginput" padding="21,0,0,0" height="36" width="320" tipvaluecolor="#FF5f5f5f" borderround="3,3" bkcolor="#ffdce1e7" font="8" textpadding="10,8,0,0" maxchar="300" tipvalue="en-US,en;q=0.5" multiline="false" textcolor="#FF5f5f5f" rich="false" transparent="false">
																								      </RichEdit>
																								     </VerticalLayout>
																								     <Control />
																							</HorizontalLayout>

																							 	<HorizontalLayout height="50" >
																							    	  <VerticalLayout width="50">
																												      <CheckBox name="NA" width="18" height="18"  padding="22,12,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="false" />
																												  </VerticalLayout>
																												  <VerticalLayout width="60">
																												      <Label name="NA_dec" padding="0,9,0,0" text="禁止弹窗" width="60" textcolor="#FF333333" font="8"></Label>
																												  </VerticalLayout>
																												  <Control width="12"/>
																												   <VerticalLayout width="50">
																												      <CheckBox name="NA" width="18" height="18"  padding="22,12,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="false" />
																												  </VerticalLayout>
																												  <VerticalLayout width="102">
																												      <Label name="NA_dec" padding="0,9,0,0" text="允许服务模式" width="100" textcolor="#FF333333"  font="8"></Label>
																												  </VerticalLayout>
																												   <VerticalLayout width="50" visible="false">
																												      <CheckBox name="NA" width="18" height="18"  padding="22,12,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="false" />
																												  </VerticalLayout>
																												  <VerticalLayout width="120" visible="false">
																												      <Label name="NA_dec" padding="0,9,0,0" text="禁止外文输入" width="120" textcolor="#FF333333"  font="8"></Label>
																												  </VerticalLayout>
																												  <Control />
																							</HorizontalLayout>


				</VerticalLayout>

				<VerticalLayout>


																						 <Control height="55"/>
																						  <HorizontalLayout height="28">
																			<VerticalLayout width="160">
																		      <Label  name="proxyip" padding="22,0,0,0" text="屏幕设定：" width="160" textcolor="#FF333333"  font="8"></Label>
																		  </VerticalLayout>
																		</HorizontalLayout>
																						  	<HorizontalLayout height="36" >
																									    	 <VerticalLayout width="170">
																										       <RichEdit name="widthinput" padding="21,0,0,0" height="36" width="170" tipvaluecolor="#FF5f5f5f" borderround="3,3" bkcolor="#ffdce1e7" font="8" textpadding="10,8,0,0" maxchar="300" tipvalue="1024" multiline="false" textcolor="#FF5f5f5f" rich="false" transparent="false">
																										      </RichEdit>
																										     </VerticalLayout>
																										     <Control width="10" />
																									    	 <VerticalLayout width="162">
																										      <RichEdit name="heightinput" height="36" width="162" tipvaluecolor="#FF5f5f5f" borderround="3,3" bkcolor="#ffdce1e7" font="8" textpadding="10,8,0,0" maxchar="300" tipvalue="768" multiline="false" textcolor="#FF5f5f5f" rich="false" transparent="false">
																										      </RichEdit>
																										     </VerticalLayout>

																										     <Control />
																									</HorizontalLayout>



																							<HorizontalLayout height="32">
																							    	    <VerticalLayout width="50">
																												      <CheckBox name="NA" width="18" height="18"  padding="22,12,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="false" />
																												  </VerticalLayout>
																												  <VerticalLayout width="128">
																												      <Label name="NA_dec" padding="0,9,0,0" text="模拟虚拟屏幕方案" width="125" textcolor="#FF333333"  font="8"></Label>
																												  </VerticalLayout>

																												   <VerticalLayout width="50">
																												      <CheckBox name="NA" width="18" height="18"  padding="22,12,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="false" />
																												  </VerticalLayout>
																												  <VerticalLayout width="122">
																												      <Label name="NA_dec" padding="0,9,0,0" text="模拟触摸屏幕方案" width="120" textcolor="#FF333333"  font="8"></Label>
																												  </VerticalLayout>

																												  <Control />
																							</HorizontalLayout>


																							<HorizontalLayout height="12">
																							</HorizontalLayout>


																					   <HorizontalLayout height="32">
																								      <VerticalLayout width="180">
												        			 										<Button name="save"  bkimage="bk_quit_normal.png" hotimage="bk_quit_hot.png" pushedimage="bk_quit_hot.png" padding="21,5,0,0"  width="145" height="26" text="屏幕配置生成器"/>
																											</VerticalLayout>
																											<VerticalLayout width="160" visible="false">
												        			 										<Button name="save"  bkimage="bk_quit_normal.png" hotimage="bk_quit_hot.png" pushedimage="bk_quit_hot.png" padding="0,5,0,0"  width="135" height="26" text="WebGL管理器"/>
																											</VerticalLayout>
																											<VerticalLayout width="160" visible="false">
												        			 										<Button name="save"  bkimage="bk_quit_normal.png" hotimage="bk_quit_hot.png" pushedimage="bk_quit_hot.png" padding="0,5,0,0"  width="135" height="26" text="扩展会话管理器"/>
																											</VerticalLayout>
																											<VerticalLayout width="160" visible="false">
												        			 										<Button name="save"  bkimage="bk_quit_normal.png" hotimage="bk_quit_hot.png" pushedimage="bk_quit_hot.png" padding="0,5,0,0"  width="135" height="26" text="字体管理器"/>
																											</VerticalLayout>

																												  <Control />
																							</HorizontalLayout>

				</VerticalLayout>

				<VerticalLayout>
				    <Control height="55"/>
																	  <HorizontalLayout height="28">
																			<VerticalLayout width="160">
																		      <Label  name="proxyip" padding="22,0,0,0" text="GPS坐标位置设定" width="160" textcolor="#FF333333"  font="8"></Label>
																		  </VerticalLayout>
																		</HorizontalLayout>
																		<HorizontalLayout height="36" >
																		    	 <VerticalLayout>
																			     <RichEdit name="configname" padding="21,0,0,10" height="36" width="320" tipvaluecolor="#FF5f5f5f" borderround="3,3" bkcolor="#ffdce1e7" font="8" textpadding="10,8,20,0" maxchar="300" tipvalue="41.8574" multiline="false" textcolor="#ff333333" rich="false" transparent="false">
																			      </RichEdit>
																			     </VerticalLayout>
																		</HorizontalLayout>
																		<HorizontalLayout height="12">
																							</HorizontalLayout>
																		<HorizontalLayout height="36" >
																		    	 <VerticalLayout>
																			     <RichEdit name="configname" padding="21,0,0,10" height="36" width="320" tipvaluecolor="#FF5f5f5f" borderround="3,3" bkcolor="#ffdce1e7" font="8" textpadding="10,8,20,0" maxchar="300" tipvalue="-87.62" multiline="false" textcolor="#ff333333" rich="false" transparent="false">
																			      </RichEdit>
																			     </VerticalLayout>
																		</HorizontalLayout>
																		<HorizontalLayout height="12">
																							</HorizontalLayout>
																			<HorizontalLayout height="36" width="540">
																		    	 <VerticalLayout width="300">
																			      <Combo name="system" bordersize="0" padding="21,0,0,10" width="280" height="36" borderround="3,3" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="4,2,0,0" bkcolor="#ffdce1e7"
																									normalimage="file='Profile\Setting_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\Setting_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\Setting_Combox_Click.png' corner='5,5,25,10'"
																									combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
																									itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0">
																										<ListLabelElement height="36" pos="66,0,0,0" textpadding="10,0,0,0" text="USA stats" font="0" >
																											<Label name="telnet" pos="66,0,0,0" textpadding="10,0,0,0" text="USA stats"  height="36" width="320" textcolor="#FF000000"/>
																										</ListLabelElement>

																										<ListLabelElement height="36" pos="66,0,0,0" textpadding="10,0,0,0" text="Alabama" font="0" selected="true">
																											<Label name="socks4" pos="66,0,0,0" textpadding="10,0,0,0" text="Alabama"  height="36" width="320" textcolor="#FF000000"/>
																										</ListLabelElement>

															                      <ListLabelElement height="36" pos="66,0,0,0" textpadding="10,0,0,0" text="Alaska" font="0" >
																											<Label name="socks5noauth" pos="66,0,0,0" textpadding="10,0,0,0" text="Alaska"  height="36" width="320" textcolor="#FF000000"/>
																										</ListLabelElement>

																										<ListLabelElement height="36" pos="66,0,0,0" textpadding="10,0,0,0" text="Arizona" font="0" >
																											<Label name="socks5auth" pos="66,0,0,0" textpadding="10,0,0,0" text="Arizona"  height="36" width="320" textcolor="#FF000000"/>
																										</ListLabelElement>

																										<ListLabelElement height="36" pos="66,0,0,0" textpadding="10,0,0,0" text="California" font="0" >
																											<Label name="HTTPnoauth" pos="66,0,0,0" textpadding="10,0,0,0" text="California"  height="36" width="320" textcolor="#FF000000"/>
																										</ListLabelElement>

																										<ListLabelElement height="36" pos="66,0,0,0" textpadding="10,0,0,0" text="Connecticut" font="0" >
																											<Label name="HTTPauth" pos="66,0,0,0" textpadding="10,0,0,0" text="Connecticut"  height="36" width="320" textcolor="#FF000000"/>
																										</ListLabelElement>

																									</Combo>
																			     </VerticalLayout>




																		</HorizontalLayout>


																		<HorizontalLayout height="12">
																		</HorizontalLayout>

																					<HorizontalLayout height="36">
																	          <VerticalLayout width="300">
																			      	   <RichEdit name="" padding="21,0,0,10" height="36" width="298" tipvaluecolor="#FF5f5f5f" borderround="3,3" bkcolor="#ffdce1e7" font="8" textpadding="10,8,20,0" maxchar="300" tipvalue="America/Chicago" multiline="false" textcolor="#ff333333" rich="false" transparent="false"></RichEdit>
																			      </VerticalLayout>
																		      </HorizontalLayout>
																			    <HorizontalLayout height="12">
																		      </HorizontalLayout>
																		<HorizontalLayout height="36" width="540">
																		    	 <VerticalLayout width="300">
																			       <Combo name="system" bordersize="0" padding="21,0,0,10" width="280" height="36" borderround="3,3" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="4,2,0,0" bkcolor="#ffdce1e7"
																									normalimage="file='Profile\Setting_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\Setting_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\Setting_Combox_Click.png' corner='5,5,25,10'"
																									combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
																									itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0">
																										<ListLabelElement height="36" pos="66,0,0,0" textpadding="10,0,0,0" text="+1345" font="0" >
																											<Label name="telnet" pos="66,0,0,0" textpadding="10,0,0,0" text="1345"  height="36" width="280" textcolor="#FF000000"/>
																										</ListLabelElement>

																										<ListLabelElement height="36" pos="66,0,0,0" textpadding="10,0,0,0" text="+1200" font="0" selected="true">
																											<Label name="socks4" pos="66,0,0,0" textpadding="10,0,0,0" text="1200"  height="36" width="280" textcolor="#FF000000"/>
																										</ListLabelElement>

															                      <ListLabelElement height="36" pos="66,0,0,0" textpadding="10,0,0,0" text="-1100" font="0" >
																											<Label name="socks5noauth" pos="66,0,0,0" textpadding="10,0,0,0" text="1100"  height="36" width="280" textcolor="#FF000000"/>
																										</ListLabelElement>
																									</Combo>
																			     </VerticalLayout>

																		</HorizontalLayout>
																		<HorizontalLayout height="12">
																			</HorizontalLayout>
																		 <HorizontalLayout height="28">
																			<VerticalLayout width="500">
																		      <Label  name="proxyip" padding="22,0,0,0" text="提示：当坐标字段不为空时，gps位置仅适用于自定义数据" width="500" textcolor="#FF333333"  font="8"></Label>
																		  </VerticalLayout>
																		</HorizontalLayout>
				</VerticalLayout>



			  <VerticalLayout>
				    <Control height="55"/>
																	  <HorizontalLayout height="28">
																			<VerticalLayout width="360">
																		      <Label  name="proxyip" padding="22,0,0,0" text="指纹、插件和其他设置" width="360" textcolor="#FF333333"  font="8"></Label>
																		  </VerticalLayout>
																		</HorizontalLayout>

																		 <HorizontalLayout height="32" >
																		        <VerticalLayout width="50">
																					      <CheckBox name="Canvas" width="18" height="18"  padding="22,12,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="true" />
																					  </VerticalLayout>
																					  <VerticalLayout width="180">
																					      <Label name="lcanvas" padding="0,9,0,0" text="启用唯一Canvas指纹" width="180" textcolor="#FF333333"  font="8"></Label>
																					  </VerticalLayout>
																					  <VerticalLayout width="50">
																					  </VerticalLayout>
																					   <VerticalLayout width="50">
																					      <CheckBox name="WAV1" width="18" height="18"  padding="22,12,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="true" />
																					  </VerticalLayout>
																					  <VerticalLayout width="180">
																					      <Label name="lwav" padding="0,9,0,0" text="启用唯一音频指纹" width="180" textcolor="#FF333333"  font="8"></Label>
																					  </VerticalLayout>
																		 </HorizontalLayout>

																		 <HorizontalLayout height="32" >
																		        <VerticalLayout width="50">
																					      <CheckBox name="FONTCODE" width="18" height="18"  padding="22,12,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="true" />
																					  </VerticalLayout>
																					  <VerticalLayout width="180">
																					      <Label name="lfontcode" padding="0,9,0,0" text="启用唯一字体指纹" width="180" textcolor="#FF333333"  font="8"></Label>
																					  </VerticalLayout>
																					  <VerticalLayout width="50">
																					  </VerticalLayout>
																					   <VerticalLayout width="50">
																					      <CheckBox name="WEBRTC" width="18" height="18"  padding="22,12,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="true" />
																					  </VerticalLayout>
																					  <VerticalLayout width="180">
																					      <Label name="lwebrtc" padding="0,9,0,0" text="启用唯一矩形指纹" width="180" textcolor="#FF333333"  font="8"></Label>
																					  </VerticalLayout>
																		 </HorizontalLayout>
																		 <HorizontalLayout height="32" >
																		        <VerticalLayout width="50">
																					      <CheckBox name="WAV" width="18" height="18"  padding="22,12,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="true" />
																					  </VerticalLayout>
																					  <VerticalLayout width="180">
																					      <Label name="lwav" padding="0,9,0,0" text="使用自定义插件和MimeType" width="180" textcolor="#FF333333"  font="8"></Label>
																					  </VerticalLayout>
																					   <VerticalLayout width="50">
																					  </VerticalLayout>
																					   <VerticalLayout width="50">
																					      <CheckBox name="WAV" width="18" height="18"  padding="22,12,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="true" />
																					  </VerticalLayout>
																					  <VerticalLayout width="180">
																					      <Label name="lwav" padding="0,9,0,0" text="退出前保存并加密cookies" width="180" textcolor="#FF333333"  font="8"></Label>
																					  </VerticalLayout>
																		 </HorizontalLayout>
																		 <HorizontalLayout height="32" >
																		      <VerticalLayout width="50">
																					      <CheckBox name="WAV" width="18" height="18"  padding="22,12,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="true" />
																					  </VerticalLayout>
																					  <VerticalLayout width="220">
																					      <Label name="lwav" padding="0,9,0,0" text="启用Flash动画（警告：不安全!）" width="220" textcolor="#FF333333"  font="8"></Label>
																					  </VerticalLayout>
																					   <VerticalLayout width="10">
																					  </VerticalLayout>
																					  <VerticalLayout width="50">
																					      <CheckBox name="WAV" width="18" height="18"  padding="22,12,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="true" />
																					  </VerticalLayout>
																					  <VerticalLayout width="180">
																					      <Label name="lwav" padding="0,9,0,0" text="阻止Canvas输出" width="180" textcolor="#FF333333"  font="8"></Label>
																					  </VerticalLayout>
																		 </HorizontalLayout>
																		 <HorizontalLayout height="32" visible="false">
																		        <VerticalLayout width="50">
																					      <CheckBox name="DNS" width="18" height="18"  padding="22,12,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="true" />
																					  </VerticalLayout>
																					  <VerticalLayout width="180">
																					      <Label name="ldns" padding="0,9,0,0" text="使用动态指纹" width="180" textcolor="#FF333333"  font="8"></Label>
																					  </VerticalLayout>
																					   <VerticalLayout width="50">
																					  </VerticalLayout>
																		 </HorizontalLayout>
																		   <Control height="10"/>
																		 <HorizontalLayout height="32" visible="false">
																		         <VerticalLayout width="180" bkcolor="#ffffffff">
							        			                   <Button name="dt"  bkimage="bk_quit_normal.png" hotimage="bk_quit_hot.png" pushedimage="bk_quit_hot.png" padding="21,5,0,0"  width="145" height="26" text="配置动态指纹"/>
											                      </VerticalLayout>
																		 </HorizontalLayout>
				</VerticalLayout>
			</TabLayout>
		</HorizontalLayout>

					<HorizontalLayout height="42">
								<VerticalLayout width="120">
									<Control width="120" />
								</VerticalLayout>


				      		<Control width="220" bkcolor="#ffffffff" />
				      		<VerticalLayout width="140" bkcolor="#ffffffff">
							        			 <Button name="importcookie"  bkimage="bk_quit_normal.png" hotimage="bk_quit_hot.png" pushedimage="bk_quit_hot.png" padding="4,5,0,0"  width="135" height="26" text="导入Cookie"/>
											</VerticalLayout>
											<VerticalLayout width="140" bkcolor="#ffffffff">
							        			 <Button name="save"  bkimage="bk_quit_normal.png" hotimage="bk_quit_hot.png" pushedimage="bk_quit_hot.png" padding="4,5,0,0"  width="135" height="26" text="保存到文件"/>
											</VerticalLayout>
											<VerticalLayout width="76" bkcolor="#ffffffff">
							        			 <Button name="save"  bkimage="bk_quit_normal.png" hotimage="bk_quit_hot.png" pushedimage="bk_quit_hot.png" padding="4,5,0,0"  width="66" height="26" text="删除"/>
											</VerticalLayout>
						      		<VerticalLayout width="76" bkcolor="#ffffffff">
							        			 <Button name="save"  bkimage="bk_quit_normal.png" hotimage="bk_quit_hot.png" pushedimage="bk_quit_hot.png" padding="4,5,0,0"  width="66" height="26" text="保存"/>
											</VerticalLayout>
								   <Control bkcolor="#ffffffff" />
					</HorizontalLayout>
		</VerticalLayout>
	</VerticalLayout>
</Window>