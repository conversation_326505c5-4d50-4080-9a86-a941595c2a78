<?xml version="1.0" encoding="UTF-8"?>
<Window>
  <ListContainerElement name="pluginsitem" height="40" bordersize="1,0,0,0" bordercolor="#FF6272A4" bkcolor="#FF282A36" selectedbkcolor="#FF44475A">
    <HorizontalLayout name="operate">
      <CheckBox name="opt_item" selected="false"  visible="true" padding="13,11,0,0" height="18" width="16" normalimage="file='list_check_normal.png'" hotimage="list_check_hover.png" selectedimage="file='list_checked_actvice_normal.png'" ></CheckBox>
    </HorizontalLayout>
    <Label name="name" text="" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" endellipsis="true" width="150" align="left" textpadding="15,0,0,0" />
    <HorizontalLayout width="140">
      <Combo reselect="true" enabled="false" endellipsis="true" name="version" bordersize="0" padding="6,4,6,0" height="32" borderround="3,3" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="0,0,10,0" bkcolor="#ffdce1e7"
                      normalimage="file='Profile\Proxylist_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\proxylist_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\proxylist_Combox_Click.png' corner='5,5,25,10'"
                      combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
                      itemendellipsis="true" itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0" >
      </Combo>
    </HorizontalLayout>
    <Label name="dec" text="" width="290" endellipsis="true" align="left" textpadding="15,0,0,0" />
    <Label name="ctime" text="2020-04-02 14:36:57" width="150" endellipsis="true" align="left" textpadding="15,0,0,0" />
    <Label name="id" text="" width="260" endellipsis="true" align="left" textpadding="15,0,0,0" />
  </ListContainerElement>
</Window>