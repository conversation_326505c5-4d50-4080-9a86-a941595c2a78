标题: 浏览器指纹：WebRTC
英文标题: Browser Fingerprint：WebRTC
ID: 59
分类ID: 25
添加时间: 1612517258
更新时间: 1685607107
访问次数: 0
SEO标题: 浏览器指纹：WebRTC
SEO关键词: 浏览器指纹：WebRTC
SEO描述: 浏览器指纹：WebRTC

================================================== 内容 ==================================================
WebRTC是一种浏览器插件，通常被需要快速直接连接的网络应用程序所应用。WebRTC通过UDP协议来建立连接，因此它并不会通过你在浏览器配置文件中使用的代理服务器进行路由。即便您使用了代理，网站也能借此获取您真实的公共和本地IP地址。该插件可被用于泄漏你的本地IP地址或追踪媒体设备。

WebRTC插件会泄漏什么信息：
外网IP地址、
内网IP地址

### WebRTC的不同模式

**外网IP地址**

“基于IP配置WebRTC外网IP（Fill WebRTC Public IP based on the IP）”选项意味着，当您启动浏览器配置文件时，它首先会连到我们的服务器。我们将检测该浏览器配置文件的真实外部IP，将其返回并设置WebRTC的外网IP。如果你的IP在会话中途发生变更，WebRTC的外网IP地址也会立即相应地进行调整。

![候鸟浏览器webrtc配置](https://admin.mbbrowser.com/static/upload/images/article/2021/02/05/1612516451416636.png "候鸟浏览器webrtc配置")

禁用此选项意味着网站无法通过您的浏览器Webrtc插件获取到您的IP地址。

![候鸟浏览器禁用webrtc](https://admin.mbbrowser.com/static/upload/images/article/2021/02/05/1612516748100061.png "候鸟浏览器禁用webrtc")

**内网IP地址**

您在内网IP地址处点击“随机”，可以随机一个内网的IP地址。

![候鸟浏览器随机内网IP地址](https://admin.mbbrowser.com/static/upload/images/article/2021/02/05/1612517056751939.png "候鸟浏览器随机内网IP地址")

有效的本地IP范围

*********** to ***************

一些本地IP地址的例子：

************
************

注意：如果您使用的是动态代理IP，建议您禁用Webrtc功能。

================================================== 英文内容 ==================================================
WebRTC is a browser plug-in commonly used by web applications that require a fast direct connection. WebRTC establishes connections over UDP, so it doesn't route through the proxy server you use in your browser profile. Even if you use a proxy, websites can get your real public and local IP address. The plugin can be used to leak your local IP address or track media devices.

What information does the WebRTC plugin leak?
External IP address,
Intranet IP address

### Different Modes O WebRTC

**External IP address**

the "Fill WebRTC Public IP based on the IP" option means that when you launch your browser profile, it will first connect to our server. We will detect the true external IP for the browser profile, return it and set the external IP for WebRTC. If your IP changes in the middle of a session, WebRTC's external IP address is immediately adjusted accordingly.

![](6d7b2882624511f09a0d0242ac130006/images/image_871487d913e7.png)

Disabling this option means that websites cannot obtain your IP address through your browser's Webrtc plug-in.

![](6d7b2882624511f09a0d0242ac130006/images/image_6450cbe6e81f.png)

**Intranet IP address**

You can click Random at the Intranet IP address to select an Intranet IP address.

![](6d7b2882624511f09a0d0242ac130006/images/image_80e1bb6d17c0.png)

Valid local IP address range

*********** to ***************

Some examples of local IP addresses:

************
************

Note: If you are using a dynamic proxy IP, it is recommended that you disable the Webrtc feature.