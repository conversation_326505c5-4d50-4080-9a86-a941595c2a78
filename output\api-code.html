<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>/api/code</title>
  <style>
/* 基础重置与排版 */
body {
  margin: 0;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.7;
  color: #333;
  background-color: #fff;
  max-width: 960px;
  margin: 0 auto;
  box-sizing: border-box;
}

/* 段落 */
p {
  margin: 1em 0;
}

/* 标题 */
h1, h2, h3, h4, h5, h6 {
  margin: 1.5em 0 0.8em;
  color: #2c3e50;
  font-weight: 600;
  line-height: 1.3;
}

h1 { font-size: 2em; border-bottom: 1px solid #eee; padding-bottom: 0.5em; }
h2 { font-size: 1.6em; }
h3 { font-size: 1.4em; }
h4 { font-size: 1.2em; }

/* 列表 */
ul, ol {
  margin: 1em 0;
  padding-left: 2em;
}

li {
  margin: 0.4em 0;
}

/* 引用块 */
blockquote {
  margin: 1.5em 0;
  padding: 0.8em 1.5em;
  background-color: #f9f9f9;
  border-left: 4px solid #ddd;
  color: #666;
  font-style: italic;
  border-radius: 0 4px 4px 0;
}

/* 代码行内 */
code {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  background-color: #f3f4f6;
  color: #e9602d;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-size: 0.95em;
  white-space: nowrap;
}

/* 代码块 */
pre {
  margin: 1.5em 0;
  padding: 1.2em;
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  overflow-x: auto;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
}

pre code {
  background: none;
  color: inherit;
  padding: 0;
  font-size: inherit;
  white-space: pre;
  display: block;
}

/* 表格 */
table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5em 0;
  font-size: 14px;
  overflow: hidden;
  box-shadow: 0 0 0 1px #e0e0e0;
  border-radius: 6px;
}

th, td {
  padding: 10px 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

th {
  background-color: #f8f9fa;
  color: #2c3e50;
  font-weight: 600;
  white-space: nowrap;
}

tr:nth-child(even) {
  background-color: #f9f9fb;
}

tr:hover {
  background-color: #f0f5ff;
}

/* 链接 */
a {
  color: #1a73e8;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

/* 分隔线 */
hr {
  border: 0;
  height: 1px;
  background: #ddd;
  margin: 2em 0;
}

/* 图片 */
img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 1em 0;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
  </style>
</head>
<body>
  <h2>错误码对照表</h2> <h3>CODE 错误码表</h3> <div class="table"><table><thead><tr><td>错误码</td><td>Message值 / 描述说明</td></tr></thead> <tbody><tr><td>0</td><td>全局</td><td>成功</td></tr> <tr><td>-1</td><td>全局</td><td>登录失败 / App_ID 或 App_KEY 非法</td></tr> <tr><td>-2</td><td>全局</td><td>登录失败 / 连接候鸟服务器超时[curl 状态码]</td></tr> <tr><td>-3</td><td>全局</td><td>当前登录帐户套餐已过期</td></tr> <tr><td>-4</td><td>全局</td><td>Login_Expire</td></tr> <tr><td>-5</td><td>全局</td><td>Login_ServerError / 服务器访问错误</td></tr> <tr><td>-6</td><td>全局</td><td>Login_MbSvrError / 启动 mbservice 失败</td></tr> <tr><td>-7</td><td>全局</td><td>Login_Uping / 还有上传在进行，需要等其完成才能登录</td></tr> <tr><td>-8</td><td>全局</td><td>Login_Occupy / 有其它实例已占用登录</td></tr> <tr><td>-9</td><td>全局</td><td>数据同步失败:超时</td></tr> <tr><td>-10</td><td>全局</td><td>数据处理失败：数据损坏导致无法加载</td></tr> <tr><td>-11</td><td>全局</td><td>数据处理失败：当前目录没有写权限，目录位置</td></tr> <tr><td>-12</td><td>全局</td><td>登录失败 / App_ID 处于在线状态</td></tr> <tr><td>-13</td><td>全局</td><td>删除环境失败</td></tr> <tr><td>-14</td><td>全局</td><td>导入 COOKIE 失败: COOKIE 格式不合法</td></tr> <tr><td>-15</td><td>全局</td><td>导出 COOKIE 失败: COOKIE 内容为空</td></tr> <tr><td>-101</td><td>创建/更新环境</td><td>创建/更新环境失败: 环境名称超长</td></tr> <tr><td>-102</td><td>创建/更新环境</td><td>创建/更新环境失败: 环境描述超长</td></tr> <tr><td>-103</td><td>创建/更新环境</td><td>创建/更新环境失败: 分组不存在</td></tr> <tr><td>-104</td><td>创建/更新环境</td><td>创建/更新环境失败: 代理服务器填入值不合法</td></tr> <tr><td>-105</td><td>创建/更新环境</td><td>创建/更新环境失败: 代理检测失败,无效代理</td></tr> <tr><td>-106</td><td>创建/更新环境</td><td>创建/更新环境失败: 初始化时区失败</td></tr> <tr><td>-107</td><td>创建/更新环境</td><td>创建/更新环境失败: 操作系统名称填写错误</td></tr> <tr><td>-108</td><td>创建/更新环境</td><td>创建/更新环境失败: 环境分辨率填写错误</td></tr> <tr><td>-109</td><td>创建/更新环境</td><td>创建/更新环境失败: USERAGENT 未填入有效值</td></tr> <tr><td>-110</td><td>创建/更新环境</td><td>创建/更新环境失败: 环境默认语言未填入有效值</td></tr> <tr><td>-111</td><td>创建/更新环境</td><td>创建/更新环境失败: FingerPrint_Setting 填入值非法</td></tr> <tr><td>-112</td><td>创建/更新环境</td><td>创建/更新环境失败: 导入 COOKIE 文件内容不合法</td></tr> <tr><td>-10000</td><td>全局</td><td>未知异常</td></tr></tbody></table></div> <h3>Status 错误码表</h3> <div class="table"><table><thead><tr><td>错误码</td><td>描述说明</td></tr></thead> <tbody><tr><td>0</td><td>成功</td></tr> <tr><td>-1</td><td>初始化数据失败</td></tr> <tr><td>-2</td><td>启动浏览器内核失败</td></tr> <tr><td>-3</td><td>当前浏览器环境：插件下载失败</td></tr> <tr><td>-4</td><td>当前浏览器环境：插件加载失败</td></tr> <tr><td>-5</td><td>当前浏览器环境：自动化脚本下载失败</td></tr> <tr><td>-6</td><td>当前浏览器环境：自动化脚本加载失败</td></tr> <tr><td>-7</td><td>当前浏览器环境：已经运行</td></tr> <tr><td>-8</td><td>当前浏览器环境：已经加入运行队列</td></tr> <tr><td>-9</td><td>当前浏览器环境：初始化 CDP 失败</td></tr> <tr><td>-10</td><td>当前浏览器环境：初始化 Service 失败</td></tr> <tr><td>-11</td><td>当前浏览器环境：CD 监听失败</td></tr> <tr><td>-12</td><td>当前浏览器环境：DP 退出</td></tr> <tr><td>-13</td><td>当前浏览器环境：连接失败</td></tr> <tr><td>-14</td><td>当前浏览器环境：初始化环境失败</td></tr> <tr><td>-15</td><td>当前浏览器环境：GetShortPathName 失败</td></tr> <tr><td>-16</td><td>当前浏览器环境：申请内存失败</td></tr> <tr><td>-17</td><td>当前浏览器环境：登录退出</td></tr> <tr><td>-18</td><td>当前浏览器环境：未收到响应信息</td></tr> <tr><td>-19</td><td>当前浏览器环境：关闭失败</td></tr> <tr><td>-20</td><td>Headless 打开项无法用 stop 关闭，建议使用 kill 关闭</td></tr> <tr><td>-21</td><td>当前浏览器环境：强制关闭失败</td></tr> <tr><td>-22</td><td>未找到指定的环境SessionId</td></tr> <tr><td>-5010</td><td>POST串请求到API SERVER URL地址非法，或请求的目标API URL中有非法字符存在</td></tr> <tr><td>-10000</td><td>未知错误</td></tr></tbody></table></div> <div class="anchor-placeholder"></div> <h3>CURL代理检测错误码表</h3> <div class="table"><table><thead><tr><td>错误码</td><td>描述说明</td></tr></thead> <tbody><tr><td>0</td><td>CURLE_OK</td><td>成功</td></tr> <tr><td>1</td><td>CURLE_UNSUPPORTED_PROTOCOL</td><td>未支持的协议。此版cURL 不支持这一协议。</td></tr> <tr><td>2</td><td>CURLE_FAILED_INIT</td><td>初始化失败。</td></tr> <tr><td>3</td><td>CURLE_URL_MALFORMAT</td><td>URL格式错误。</td></tr> <tr><td>4</td><td>CURLE_NOT_BUILT_IN</td><td>功能未编译进此版本cURL。</td></tr> <tr><td>5</td><td>CURLE_COULDNT_RESOLVE_PROXY</td><td>无法解析代理服务器。</td></tr> <tr><td>6</td><td>CURLE_COULDNT_RESOLVE_HOST</td><td>无法解析主机名。</td></tr> <tr><td>7</td><td>CURLE_COULDNT_CONNECT</td><td>无法连接到远程服务器。</td></tr> <tr><td>8</td><td>CURLE_WEIRD_SERVER_REPLY</td><td>服务器回复异常。</td></tr> <tr><td>9</td><td>CURLE_REMOTE_ACCESS_DENIED</td><td>远程访问被拒绝。</td></tr> <tr><td>10</td><td>CURLE_FTP_ACCEPT_FAILED</td><td>FTP接受失败。</td></tr> <tr><td>11</td><td>CURLE_FTP_WEIRD_PASS_REPLY</td><td>FTP密码回复异常。</td></tr> <tr><td>12</td><td>CURLE_FTP_ACCEPT_TIMEOUT</td><td>FTP接受超时。</td></tr> <tr><td>13</td><td>CURLE_FTP_WEIRD_PASV_REPLY</td><td>FTP PASV回复异常。</td></tr> <tr><td>14</td><td>CURLE_FTP_WEIRD_227_FORMAT</td><td>FTP 227格式异常。</td></tr> <tr><td>15</td><td>CURLE_FTP_CANT_GET_HOST</td><td>FTP无法获取主机。</td></tr> <tr><td>16</td><td>CURLE_HTTP2</td><td>HTTP/2错误。</td></tr> <tr><td>17</td><td>CURLE_FTP_COULDNT_SET_TYPE</td><td>FTP无法设置类型。</td></tr> <tr><td>18</td><td>CURLE_PARTIAL_FILE</td><td>部分文件传输失败。</td></tr> <tr><td>19</td><td>CURLE_FTP_COULDNT_RETR_FILE</td><td>FTP无法检索文件。</td></tr> <tr><td>20</td><td>CURLE_OBSOLETE20</td><td>已废弃。</td></tr> <tr><td>21</td><td>CURLE_QUOTE_ERROR</td><td>命令执行错误。</td></tr> <tr><td>22</td><td>CURLE_HTTP_RETURNED_ERROR</td><td>HTTP请求返回错误。</td></tr> <tr><td>23</td><td>CURLE_WRITE_ERROR</td><td>写入错误。</td></tr> <tr><td>24</td><td>CURLE_OBSOLETE24</td><td>已废弃。</td></tr> <tr><td>25</td><td>CURLE_UPLOAD_FAILED</td><td>上传失败。</td></tr> <tr><td>26</td><td>CURLE_READ_ERROR</td><td>读取错误。</td></tr> <tr><td>27</td><td>CURLE_OUT_OF_MEMORY</td><td>内存不足。</td></tr> <tr><td>28</td><td>CURLE_OPERATION_TIMEDOUT</td><td>操作超时。</td></tr> <tr><td>29</td><td>CURLE_OBSOLETE29</td><td>已废弃。</td></tr> <tr><td>30</td><td>CURLE_FTP_PORT_FAILED</td><td>FTP PORT命令失败。</td></tr> <tr><td>31</td><td>CURLE_FTP_COULDNT_USE_REST</td><td>FTP无法使用REST命令。</td></tr> <tr><td>32</td><td>CURLE_OBSOLETE32</td><td>已废弃。</td></tr> <tr><td>33</td><td>CURLE_RANGE_ERROR</td><td>范围错误。</td></tr> <tr><td>34</td><td>CURLE_HTTP_POST_ERROR</td><td>HTTP POST请求错误。</td></tr> <tr><td>35</td><td>CURLE_SSL_CONNECT_ERROR</td><td>SSL连接错误。</td></tr> <tr><td>36</td><td>CURLE_BAD_DOWNLOAD_RESUME</td><td>下载恢复错误。</td></tr> <tr><td>37</td><td>CURLE_FILE_COULDNT_READ_FILE</td><td>无法读取文件。</td></tr> <tr><td>38</td><td>CURLE_LDAP_CANNOT_BIND</td><td>LDAP无法绑定。</td></tr> <tr><td>39</td><td>CURLE_LDAP_SEARCH_FAILED</td><td>LDAP搜索失败。</td></tr> <tr><td>40</td><td>CURLE_OBSOLETE40</td><td>已废弃。</td></tr> <tr><td>41</td><td>CURLE_FUNCTION_NOT_FOUND</td><td>函数未找到。</td></tr> <tr><td>42</td><td>CURLE_ABORTED_BY_CALLBACK</td><td>回调函数中断。</td></tr> <tr><td>43</td><td>CURLE_BAD_FUNCTION_ARGUMENT</td><td>函数参数错误。</td></tr> <tr><td>44</td><td>CURLE_OBSOLETE44</td><td>已废弃。</td></tr> <tr><td>45</td><td>CURLE_INTERFACE_FAILED</td><td>接口失败。</td></tr> <tr><td>46</td><td>CURLE_OBSOLETE46</td><td>已废弃。</td></tr> <tr><td>47</td><td>CURLE_TOO_MANY_REDIRECTS</td><td>重定向过多。</td></tr> <tr><td>48</td><td>CURLE_UNKNOWN_OPTION</td><td>未知选项。</td></tr> <tr><td>49</td><td>CURLE_SETOPT_OPTION_SYNTAX</td><td>选项语法错误。</td></tr> <tr><td>50</td><td>CURLE_OBSOLETE50</td><td>已废弃。</td></tr> <tr><td>51</td><td>CURLE_OBSOLETE51</td><td>已废弃。</td></tr> <tr><td>52</td><td>CURLE_GOT_NOTHING</td><td>未收到任何数据。</td></tr> <tr><td>53</td><td>CURLE_SSL_ENGINE_NOTFOUND</td><td>SSL引擎未找到。</td></tr> <tr><td>54</td><td>CURLE_SSL_ENGINE_SETFAILED</td><td>SSL引擎设置失败。</td></tr> <tr><td>55</td><td>CURLE_SEND_ERROR</td><td>发送错误。</td></tr> <tr><td>56</td><td>CURLE_RECV_ERROR</td><td>接收错误。</td></tr> <tr><td>57</td><td>CURLE_OBSOLETE57</td><td>已废弃。</td></tr> <tr><td>58</td><td>CURLE_SSL_CERTPROBLEM</td><td>SSL证书问题。</td></tr> <tr><td>59</td><td>CURLE_SSL_CIPHER</td><td>SSL密钥问题。</td></tr> <tr><td>60</td><td>CURLE_PEER_FAILED_VERIFICATION</td><td>对等方验证失败。</td></tr> <tr><td>61</td><td>CURLE_BAD_CONTENT_ENCODING</td><td>内容编码错误。</td></tr> <tr><td>62</td><td>CURLE_OBSOLETE62</td><td>已废弃。</td></tr> <tr><td>63</td><td>CURLE_FILESIZE_EXCEEDED</td><td>文件大小超过限制。</td></tr> <tr><td>64</td><td>CURLE_USE_SSL_FAILED</td><td>使用SSL失败。</td></tr> <tr><td>65</td><td>CURLE_SEND_FAIL_REWIND</td><td>发送重绕失败。</td></tr> <tr><td>66</td><td>CURLE_SSL_ENGINE_INITFAILED</td><td>SSL引擎初始化失败。</td></tr> <tr><td>67</td><td>CURLE_LOGIN_DENIED</td><td>登录被拒绝。</td></tr> <tr><td>68</td><td>CURLE_TFTP_NOTFOUND</td><td>TFTP文件未找到。</td></tr> <tr><td>69</td><td>CURLE_TFTP_PERM</td><td>TFTP权限错误。</td></tr> <tr><td>70</td><td>CURLE_REMOTE_DISK_FULL</td><td>远程磁盘空间不足。</td></tr> <tr><td>71</td><td>CURLE_TFTP_ILLEGAL</td><td>TFTP非法操作。</td></tr> <tr><td>72</td><td>CURLE_TFTP_UNKNOWNID</td><td>TFTP未知ID。</td></tr> <tr><td>73</td><td>CURLE_REMOTE_FILE_EXISTS</td><td>远程文件已存在。</td></tr> <tr><td>74</td><td>CURLE_TFTP_NOSUCHUSER</td><td>TFTP用户不存在。</td></tr> <tr><td>75</td><td>CURLE_OBSOLETE75</td><td>已废弃。</td></tr> <tr><td>76</td><td>CURLE_OBSOLETE76</td><td>已废弃。</td></tr> <tr><td>77</td><td>CURLE_SSL_CACERT_BADFILE</td><td>SSL CA证书文件错误。</td></tr> <tr><td>78</td><td>CURLE_REMOTE_FILE_NOT_FOUND</td><td>远程文件未找到。</td></tr> <tr><td>79</td><td>CURLE_SSH</td><td>SSH错误。</td></tr> <tr><td>80</td><td>CURLE_SSL_SHUTDOWN_FAILED</td><td>SSL关闭失败。</td></tr> <tr><td>81</td><td>CURLE_AGAIN</td><td>资源暂时不可用。</td></tr> <tr><td>82</td><td>CURLE_SSL_CRL_BADFILE</td><td>SSL CRL文件错误。</td></tr> <tr><td>83</td><td>CURLE_SSL_ISSUER_ERROR</td><td>SSL颁发者错误。</td></tr> <tr><td>84</td><td>CURLE_FTP_PRET_FAILED</td><td>FTP PRET命令失败。</td></tr> <tr><td>85</td><td>CURLE_RTSP_CSEQ_ERROR</td><td>RTSP CSEQ错误。</td></tr> <tr><td>86</td><td>CURLE_RTSP_SESSION_ERROR</td><td>RTSP会话错误。</td></tr> <tr><td>87</td><td>CURLE_FTP_BAD_FILE_LIST</td><td>FTP文件列表错误。</td></tr> <tr><td>88</td><td>CURLE_CHUNK_FAILED</td><td>分块失败。</td></tr> <tr><td>89</td><td>CURLE_NO_CONNECTION_AVAILABLE</td><td>无可用连接。</td></tr> <tr><td>90</td><td>CURLE_SSL_PINNEDPUBKEYNOTMATCH</td><td>SSL固定公钥不匹配。</td></tr> <tr><td>91</td><td>CURLE_SSL_INVALIDCERTSTATUS</td><td>SSL证书状态无效。</td></tr> <tr><td>92</td><td>CURLE_HTTP2_STREAM</td><td>HTTP/2流错误。</td></tr> <tr><td>93</td><td>CURLE_RECURSIVE_API_CALL</td><td>递归API调用。</td></tr> <tr><td>94</td><td>CURLE_AUTH_ERROR</td><td>认证错误。</td></tr> <tr><td>95</td><td>CURLE_HTTP3</td><td>HTTP/3错误。</td></tr> <tr><td>96</td><td>CURLE_QUIC_CONNECT_ERROR</td><td>QUIC连接错误。</td></tr> <tr><td>97</td><td>CURLE_PROXY</td><td>代理错误。</td></tr> <tr><td>98</td><td>CURLE_SSL_CLIENTCERT</td><td>客户端SSL证书错误。</td></tr> <tr><td>99</td><td>CURLE_UNRECOVERABLE_POLL</td><td>不可恢复的轮询错误。</td></tr> <tr><td>100</td><td>CURLE_TOO_LARGE</td><td>请求过大。</td></tr> <tr><td>101</td><td>CURLE_ECH_REQUIRED</td><td>需要ECH。</td></tr></tbody></table></div>
</body>
</html>