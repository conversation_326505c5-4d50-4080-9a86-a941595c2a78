# API_帐号登录_05_实时切换账号并重新登录

## 功能描述
实时切换帐号并登录

## 所属模块
帐号登录

## API信息

- **路径**: `/login`
- **方法**: POST
- **内容类型**: application/json
- **服务器地址**: http://127.0.0.1:8186

## 请求参数

| 参数名称   | 类型    | 是否必传 | 示例/默认值                             | 说明       |
|--------|-------|------|-------------------------------------|----------|
| Account| 字符串 | 是    | "<EMAIL>"                     | 用户凭证：帐号 |
| APP_ID | 字符串 | 是    | "7e147176e1d756eb03c0e18e7b640c23"  | 用户凭证：应用ID |
| APP_KEY| 字符串 | 是    | "kwMTYxNG_MzlkZjhlZmM2YWNiY2M5MTkw" | 用户凭证：应用密钥 |
| return | 字符串 | 否    | "on"                                | 控制台输出："on"（默认），"off" |
| logs   | 字符串 | 否    | "on"                                | 日志记录："on"（默认），"off" |
| hide   | 字符串 | 否    | "on"                                | UI模式："on"（默认，全自动化），"off"（半自动化） |

## 请求示例

```json
{
    "Account": "<EMAIL>",
    "APP_ID": "7e147176e1d756eb03c0e18e7b640c23",
    "APP_KEY": "kwMTYxNG_MzlkZjhlZmM2YWNiY2M5MTkw",
    "return": "on",
    "logs": "on",
    "hide": "on"
}
```

## 成功响应

```json
{
    "msg": "登录成功",
    "status": 0,
    "data": "登录帐号: <EMAIL>"
}
```

## 使用说明

1. 用于实时切换账号并重新登录
2. 支持控制台输出和日志记录配置
3. 可选择全自动化或半自动化UI模式
4. 建议使用[POSTMAN调试工具](/api/postman-example)进行接口测试

## 相关链接

- [POSTMAN调试工具](/api/postman-example)
- [错误码对照表](/api/code)
- [使用须知](/api/help)
