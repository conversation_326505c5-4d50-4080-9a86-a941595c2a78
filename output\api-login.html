<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>/api/login</title>
  <style>
/* 基础重置与排版 */
body {
  margin: 0;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.7;
  color: #333;
  background-color: #fff;
  max-width: 960px;
  margin: 0 auto;
  box-sizing: border-box;
}

/* 段落 */
p {
  margin: 1em 0;
}

/* 标题 */
h1, h2, h3, h4, h5, h6 {
  margin: 1.5em 0 0.8em;
  color: #2c3e50;
  font-weight: 600;
  line-height: 1.3;
}

h1 { font-size: 2em; border-bottom: 1px solid #eee; padding-bottom: 0.5em; }
h2 { font-size: 1.6em; }
h3 { font-size: 1.4em; }
h4 { font-size: 1.2em; }

/* 列表 */
ul, ol {
  margin: 1em 0;
  padding-left: 2em;
}

li {
  margin: 0.4em 0;
}

/* 引用块 */
blockquote {
  margin: 1.5em 0;
  padding: 0.8em 1.5em;
  background-color: #f9f9f9;
  border-left: 4px solid #ddd;
  color: #666;
  font-style: italic;
  border-radius: 0 4px 4px 0;
}

/* 代码行内 */
code {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  background-color: #f3f4f6;
  color: #e9602d;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-size: 0.95em;
  white-space: nowrap;
}

/* 代码块 */
pre {
  margin: 1.5em 0;
  padding: 1.2em;
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  overflow-x: auto;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
}

pre code {
  background: none;
  color: inherit;
  padding: 0;
  font-size: inherit;
  white-space: pre;
  display: block;
}

/* 表格 */
table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5em 0;
  font-size: 14px;
  overflow: hidden;
  box-shadow: 0 0 0 1px #e0e0e0;
  border-radius: 6px;
}

th, td {
  padding: 10px 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

th {
  background-color: #f8f9fa;
  color: #2c3e50;
  font-weight: 600;
  white-space: nowrap;
}

tr:nth-child(even) {
  background-color: #f9f9fb;
}

tr:hover {
  background-color: #f0f5ff;
}

/* 链接 */
a {
  color: #1a73e8;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

/* 分隔线 */
hr {
  border: 0;
  height: 1px;
  background: #ddd;
  margin: 2em 0;
}

/* 图片 */
img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 1em 0;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
  </style>
</head>
<body>
  <h2>帐号登录</h2> <ul><li><p>1、候鸟浏览器支持本地API的功能，帮助用户通过程序化的方式来启动和关闭浏览器等基础API功能，还可以配合Selenium和Puppeteer等自动化框架来实现浏览器操作的自动化。</p></li> <li><p>2、仅支持客户端V3.9.2.114以上版本，请下载客户端最新版本<a>【下载候鸟浏览器最新版】</a></p></li></ul> <div class="ant-divider ant-divider-horizontal"></div> <iframe></iframe> <h3>【HTTP】帐号登录</h3> <ul><li><p><strong>http模式需配合使用CLI命令行启动客户端，见<a class="">【HTTP模式说明】</a>，也可以用下面的bat文件示例修改对应的account、app_id和app_key并放到apiserver.exe的同级目录中，就可以轻松快捷地把apiserver运行起来。</strong></p></li></ul> <blockquote><p>apiserver.bat</p></blockquote> <div class="code-view"><pre><code>ApiServer.exe --port=8186 --account=<EMAIL> --app_id=7e147176e1d756eb03c0e18e7b640c23 --app_key=ExOTNjMWNWYzZTU5ZjYzMGEzZDU4ZDI3 --return=on --logs=on</code></pre> </div> <h3>【HTTP】与APISERVER交互、请求地址详述</h3> <ul><li><p><strong>Apiserver成功运行后，您可以使用以下方式与Apiserver进行通讯</strong></p></li> <li><p><strong class="pcolor">http://127.0.0.1:8186</strong> 或 <strong class="pcolor">http://localhost:8186</strong></p></li> <li><p>例如实时切换帐号并重新登录，您可以：</p></li> <li><p>Post方式 请求地址：http://127.0.0.1:8186/login</p></li> <li><p>POST格式：Content-Type：application/json</p></li> <li><p>然后POST您的json数据到此地址，Apiserver实时收到并实时执行，中间没有任何延迟。</p></li></ul> <h3>【HTTP】实时切换账号并重新登录</h3> <ul><li><p><strong>在登录成功apiserver之后，您可以使用以下接口实时切换账号</strong></p></li> <li><p><strong class="pcolor">【最大请求频率：10次/分钟】</strong></p></li> <li><p>Path：/login</p></li> <li><p>Method：POST</p></li> <li><p>Content-Type：application/json</p></li></ul> <blockquote><p>请求参数</p> <p>以下参数获取可前往<a class="">【使用须知】</a>查看</p></blockquote> <div class="table"><table><thead><tr><td>参数名称</td> <td>类型</td> <td>必传</td> <td>样例串/默认值</td> <td>说明</td></tr></thead> <tbody><tr><td>Account</td> <td>string</td> <td>是</td> <td><EMAIL></td> <td>用户凭证 Account</td></tr> <tr><td>APP_ID</td> <td>string</td> <td>是</td> <td>7e147176e1d756eb03c0e18e7b640c23</td> <td>用户凭证 app_Id</td></tr> <tr><td>APP_KEY</td> <td>string</td> <td>是</td> <td>kwMTYxNG_MzlkZjhlZmM2YWNiY2M5MTkw</td> <td>用户凭证 app_key</td></tr> <tr><td>return</td> <td>string</td> <td>否</td> <td>on (default)</td> <td><p>on: APISERVER.EXE 所有需返回给用户的数据[JSON/其它]，所有状态信息，返回到 CONSOLE 界面。（当用户未填写此参数时，默认为 ON）</p><p>off: 所有返回给用户的数据[JSON/其它]、所有状态信息，不在 CONSOLE 界面显示。</p></td></tr> <tr><td>logs</td> <td>string</td> <td>否</td> <td>on (default)</td> <td><p>on: APISERVER.EXE 所有需返回给用户的 JSON 数据，所有状态信息，明文写入 API_LOG 目录【非 LOG 目录】，LOG 文件前缀例：test@qq.com_API_log。（当用户未填写此参数时，默认为 ON）</p><p>off: 所有返回给脚本的 JSON 数据、所有状态信息，不写入LOG。</p></td></tr> <tr><td>hide</td> <td>string</td> <td>否</td> <td>on (default)</td> <td><p>on: APISERVER.EXE 以(全自动化)模式启动，启动后会同时自动屏蔽已登录的 mbbrowser.exe 主面板，保障客户在自动化运行中，避免人工通过主面板同时操控环境所带来的业务风险。（当用户未填写此参数时，默认为 ON）</p><p>off: APISERVER.EXE 以支持(半自动化/全自动化)模式启动，启动后会同时显示 mbbrowser.exe 主面板，供客户在自动化运行中，可人工通过主面板操控环境，强化业务控制能力。</p><p>[此参数仅在产品版本号: 4.8.20.134 及以后有效]</p></td></tr></tbody></table></div> <blockquote><p>请求示例</p></blockquote> <div class="code-view"><pre><code>{ "APP_ID": "7e147176e1d756eb03c0e18e7b640c23", "APP_KEY": "kwMTYxNG_MzlkZjhlZmM2YWNiY2M5MTkw", "Account": "<EMAIL>" }</code></pre> </div> <blockquote><p>返回参数</p></blockquote> <div class="code-view"><pre><code>{ "msg": "Login Success", "status": 0, "data": "Login Aaccount: <EMAIL>" }</code></pre> </div> <p><a class="ant-btn ant-btn-primary">使用POSTMAN调试此接口</a></p> <h3>【HTTP】退出 APISERVER 并关闭客户端(退出登录)</h3> <ul><li><p>Path：/api/v1/quit</p></li> <li><p>Method：POST</p></li> <li><p>Content-Type：application/json</p></li> <li><p>接口描述：停止并关闭APISERVER，关闭成功Code返回0。</p></li></ul> <blockquote><p>返回参数</p></blockquote> <div class="code-view"><pre><code>{ "message": "APISERVER shut down.", "code": 0, "data": true }</code></pre> </div> <p><a class="ant-btn ant-btn-primary">使用POSTMAN调试此接口</a></p>
</body>
</html>