			  </VerticalLayout>

			</HorizontalLayout>

      <HorizontalLayout height="32">

				<VerticalLayout width="80">
			      <Label padding="22,2,0,0" name="PublicIP" text="公网IP" texttooltip="true" endellipsis="true" width="80" textcolor="#FF333333" hottextcolor="ffFF0000" font="8"></Label>
			  </VerticalLayout>




			  <VerticalLayout width="36">
			       <CheckBox name="public_ip_check" width="18" height="18"  padding="12,4,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="false" />
			  </VerticalLayout>
			   <VerticalLayout width="122">
			      <Label name="WanIpInfo" padding="0,0,0,0" text="自动识别代理IP" width="122" align="left" textcolor="#FF333333" hottextcolor="#FF005ed3" font="5"></Label>
			  </VerticalLayout>

			  <VerticalLayout width="8">
			  </VerticalLayout>
         <VerticalLayout width="67" height="40">
           <CheckBox name="public_ip_switch" visible="false" padding="16,8,0,10" width="52" height="24"  normalimage="switch_off.png" selectedimage="switch_on.png" disabledimage="switch_off.png" />
			  </VerticalLayout>
				<VerticalLayout width="100">
			      <Label padding="22,0,0,0" name="PrivateIP" text="内网IP" width="100" align="left" textcolor="#FF333333" hottextcolor="ffFF0000" font="8"></Label>
			  </VerticalLayout>
			   <VerticalLayout width="120">
					 <Button name="RandLocalIP" padding="10,8,0,0"  text="随机" width="120" align="left" textcolor="#FF519cff" hottextcolor="#FF005ed3" font="5"></Button>
			  </VerticalLayout>
			  <VerticalLayout width="26">
			  </VerticalLayout>
			  <VerticalLayout width="67" height="40">
          <CheckBox name="PrivateIP_switch" padding="14,4,0,10" width="52" height="24"  normalimage="switch_off.png" selectedimage="switch_on.png" disabledimage="switch_off.png" />
			  </VerticalLayout>
			</HorizontalLayout>



			<HorizontalLayout height="36" >

	           <RichEdit name="public_ip" wanttab="false" padding="21,0,0,10" height="36" width="290" tipvaluecolor="#FF6272A4" borderround="7,7" bkcolor="#FF21222C" font="8" textpadding="10,8,20,0" maxchar="300" tipvalue="Enter the public IP address.." multiline="false" textcolor="#FFF8F8F2" rich="false" transparent="false">

				      		</RichEdit>

				      			<RichEdit name="privite_ip" wanttab="false" padding="21,0,0,10" height="36" width="290" tipvaluecolor="#FF6272A4" borderround="7,7" bkcolor="#FF21222C" font="8" textpadding="10,8,20,0" maxchar="300" tipvalue="Enter or random Private IP address.." multiline="false" textcolor="#FFF8F8F2" rich="false" transparent="false">

				      		</RichEdit>

			</HorizontalLayout>

      <HorizontalLayout inset="20,0,26,0" height="18">
      </HorizontalLayout>
      <HorizontalLayout inset="20,0,26,0" height="2">
        <Control height="2" bkcolor="#ffc6c9cd"/>
      </HorizontalLayout>


			   <HorizontalLayout height="10">
			</HorizontalLayout>

			<HorizontalLayout height="32">
				<VerticalLayout width="120">
			      <Label name="txt_description" padding="22,0,0,0" textpadding="0,0,10,0" text="指纹识别设置" texttooltip="true" endellipsis="true" width="120" textcolor="#FF333333" hottextcolor="ffFF0000" font="8"></Label>
			  </VerticalLayout>
			  <VerticalLayout width="144">
			      <Button name="GetIPInfo" padding="10,-4,0,0" height="32" textpadding="0,0,20,0" text="自动匹配IP时区信息" texttooltip="true" endellipsis="true" width="144" textcolor="#FF519cff" hottextcolor="#ff014e9d" font="0"></Button>
			  </VerticalLayout>
			   <VerticalLayout width="400">
			      <Label name="txt_descriptionmsg" padding="30,-4,0,0" height="32" textpadding="0,0,20,0" texttooltip="true" endellipsis="true" text="候鸟会根据您的代理自动设置识别设置，如非必要不需要修改。" width="340" textcolor="#FF9ea2a8" hottextcolor="#ff9ea2a8" font="0"></Label>
			  </VerticalLayout>
			</HorizontalLayout>



			<HorizontalLayout height="32">
				<VerticalLayout width="62">
			      <Label name="ltimezone" padding="22,0,0,0" height="32" textpadding="0,6,0,0" text="时区" width="60" textcolor="#FF333333" hottextcolor="FF333333" font="0"></Label>
			  </VerticalLayout>
			   <VerticalLayout width="14">
			  </VerticalLayout>
			  <VerticalLayout width="68">
			  </VerticalLayout>
			   <VerticalLayout width="67" height="40">
           <CheckBox name="timezone_switch" visible="false" padding="16,8,0,10" width="52" height="24"  normalimage="switch_off.png" selectedimage="switch_on.png" disabledimage="switch_off.png" />
			  </VerticalLayout>
			  <VerticalLayout width="62">
			      <Label name="lcountry" padding="4,0,0,0" height="32" textpadding="0,6,0,0" text="国家" width="60" textcolor="#FF333333" hottextcolor="FF333333" font="0"></Label>
			  </VerticalLayout>
			   <VerticalLayout width="80">
			  </VerticalLayout>
			   <VerticalLayout width="67" height="40">
           <CheckBox name="country_switch" visible="false" padding="16,8,0,10" width="52" height="24"  normalimage="switch_off.png" selectedimage="switch_on.png" disabledimage="switch_off.png" />
			  </VerticalLayout>
			<VerticalLayout width="62">
			      <Label name="llangvage" padding="4,0,0,0" height="32" textpadding="0,6,0,0" text="语言" width="60" textcolor="#FF333333" hottextcolor="FF333333" font="0"></Label>
			  </VerticalLayout>
			  <VerticalLayout width="76">
			  </VerticalLayout>
       <VerticalLayout width="67" height="40">
         <CheckBox name="langvage_switch" visible="false" padding="16,8,0,10" width="52" height="24"  normalimage="switch_off.png" selectedimage="switch_on.png" disabledimage="switch_off.png" />
			  </VerticalLayout>
			</HorizontalLayout>

			<HorizontalLayout height="36" >
			    	 <VerticalLayout width="210">
						       <Combo name="timezone" bordersize="0" padding="21,0,0,10" width="186" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="0,0,45,0" bkcolor="#ffdce1e7"
										normalimage="file='Profile\timezone_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\timezone_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\timezone_Combox_Click.png' corner='5,5,25,10'"
										combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
										itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0" endellipsis="true">
										</Combo>
                    <Edit name="timezoneedit" pos="21,0,0,10" width="150" height="36" nativebkcolor="#FFDCE1E7" bkcolor="#FFDCE1E7" float="true" />
				     </VerticalLayout>

 			    	 <VerticalLayout width="210">
						       <Combo name="country" bordersize="0" padding="4,0,0,10" width="202" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="0,0,45,0" bkcolor="#ffdce1e7"
										normalimage="file='Profile\short_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\short_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\short_Combox_Click.png' corner='5,5,25,10'"
										combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
										itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0" endellipsis="true">
										</Combo>
                    <Edit name="countryedit" pos="4,0,0,10" width="168" height="36" nativebkcolor="#FFDCE1E7" bkcolor="#FFDCE1E7" float="true" />
				     </VerticalLayout>



				      <VerticalLayout width="210">
						       <Combo name="lnginput" bordersize="0" padding="4,0,0,10" width="196" height="36" borderround="7,7" textcolor="#FF000000" disabledtextcolor="#FFA7A6AA" itemalign="left" itemfont="0" textpadding="0,0,45,0" bkcolor="#ffdce1e7"
										normalimage="file='Profile\timezone_Combox_Normal.png' corner='5,5,25,10'" hotimage="file='Profile\timezone_Combox_Hover.png' corner='5,5,25,10'" pushedimage="file='Profile\timezone_Combox_Click.png' corner='5,5,25,10'"
										combowndbkimage="file='Profile\Setting_Combox_List_BG.png' corner='5,5,25,10'"
										itemhotbkcolor="#FFeff3f7" itemselectedbkcolor="#FFc3cedc" itemtextpadding="10,0,0,0" endellipsis="true">
										</Combo>
                    <Edit name="lnginputedit" pos="4,0,0,10" width="162" height="36" nativebkcolor="#FFDCE1E7" bkcolor="#FFDCE1E7" float="true" />
				     </VerticalLayout>



			</HorizontalLayout>





      <HorizontalLayout height="12">
			</HorizontalLayout>
			<HorizontalLayout height="32">
				<VerticalLayout width="50">
			      <CheckBox name="WAV" width="18" height="18"  padding="22,4,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="true" />
			  </VerticalLayout>
			  <VerticalLayout width="90">
			      <Label name="lwav" padding="0,0,0,0" text="音频指纹" width="90" textcolor="#FF333333" hottextcolor="ffFF0000" font="8"></Label>
			  </VerticalLayout>

			  <VerticalLayout width="20">
			  	<Control width="20"/>
			  </VerticalLayout>

			  <VerticalLayout width="50">
			      <CheckBox name="FONTCODE" width="18" height="18"  padding="22,4,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="true" />
			  </VerticalLayout>
			  <VerticalLayout width="120">
			      <Label name="lfontcode" padding="0,0,0,0" text="字体指纹" width="120" textcolor="#FF333333" hottextcolor="ffFF0000" font="8"></Label>
			  </VerticalLayout>

			  <VerticalLayout width="50">
			      <CheckBox name="DNS" width="18" height="18"  padding="22,4,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="true" />
			  </VerticalLayout>
			  <VerticalLayout width="90">
			      <Label name="ldns" padding="0,0,0,0" text="DNS指纹" width="90" textcolor="#FF333333" hottextcolor="ffFF0000" font="8"></Label>
			  </VerticalLayout>

			</HorizontalLayout>


      <HorizontalLayout height="6">
			 </HorizontalLayout>
			<HorizontalLayout height="32">
				<VerticalLayout width="50">
			      <CheckBox name="CANVAS" width="18" height="18"  padding="22,4,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="true" />
			  </VerticalLayout>
			  <VerticalLayout width="90">
			      <Label name="lcanvas" padding="0,0,0,0" text="CANVAS指纹" width="90" textcolor="#FF333333" hottextcolor="ffFF0000" font="8"></Label>
			  </VerticalLayout>

			  <VerticalLayout width="20">
			  	<Control width="20"/>
			  </VerticalLayout>

        <HorizontalLayout name="UNCHANGEDAREA" width="170" height="32" visible="false">
          <VerticalLayout width="50">
            <CheckBox name="UNCHANGED" width="18" height="18"  padding="22,4,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="true" />
          </VerticalLayout>
          <VerticalLayout width="120" >
            <Label name="lunchange" padding="0,0,0,0" text="保持原指纹无变化" textpadding="0,0,5,0" texttooltip="true" endellipsis="true" width="120" textcolor="#FF333333" hottextcolor="ffFF0000" font="8"></Label>
          </VerticalLayout>
        </HorizontalLayout>

        <VerticalLayout width="50">
          <CheckBox name="NOTALLOWRESIZE" width="18" height="18"  padding="22,4,0,1" normalimage="file='check.png' source='0,0,18,18'" selectedimage="file='check.png' source='18,0,36,18'" disabledimage="file='file='check.png' dest='36,0,54,36'" selected="true" />
        </VerticalLayout>
        <VerticalLayout width="172">
          <Label name="lnotallowresize" padding="0,0,0,0" text="保持浏览器窗口大小锁定" textpadding="0,0,10,0" texttooltip="true" endellipsis="true" width="172" textcolor="#FF333333" hottextcolor="ffFF0000" font="8"></Label>
        </VerticalLayout>

			</HorizontalLayout>





			  <HorizontalLayout inset="20,0,26,0" height="2">
        <Control height="2" bkcolor="#ffc6c9cd"/>
      </HorizontalLayout>




			<HorizontalLayout height="74">


			  <VerticalLayout width="150">
			  	<Button name="rdnconfigbtn" padding="23,26,0,0" height="52" width="140" text="获取随机配置" textpadding="0,0,20,0" texttooltip="true" endellipsis="true" align="left" font="8" textcolor="#FF006fdf" hottextcolor="#FF004a95" />
			  </VerticalLayout>
			  <VerticalLayout width="174">
			  	<Label name="session__num_dec" padding="22,10,0,0" height="52" width="164" text="设置批量生成环境数量" textpadding="0,0,20,0" texttooltip="true" endellipsis="true" align="left" font="8" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD"></Label>
			  </VerticalLayout>

			  <VerticalLayout width="90">
				     		<RichEdit name="session__num" text="20" wanttab="false" padding="0,8,0,0" height="50" width="90" bordersize="2" bordercolor="#FF6272A4" tipvaluecolor="#FF6272A4" borderround="7,7" bkcolor="#FF21222C" font="12" textpadding="10,12,10,0" maxchar="3" tipvalue="20" multiline="false" textcolor="#FFF8F8F2" rich="false" transparent="false">
				      		</RichEdit>
				     </VerticalLayout>
				      <VerticalLayout width="30">
         </VerticalLayout>
			    <VerticalLayout width="220">
             <Button name="configbtn" padding="10,8,0,0" height="50" width="161" text="批量创建环境" textpadding="10,0,10,0" texttooltip="true" endellipsis="true" font="11" borderround="5,5" textcolor="#FFF8F8F2" hottextcolor="#FFFFFFFF" bkcolor="#FF6272A4" hotbkcolor="#FFBD93F9"	/>
         </VerticalLayout>
			    <VerticalLayout width="8">

         </VerticalLayout>


			</HorizontalLayout>



		</VerticalLayout>


</Window>
