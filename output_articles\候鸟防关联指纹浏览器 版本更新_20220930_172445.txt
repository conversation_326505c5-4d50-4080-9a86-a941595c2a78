标题: 候鸟防关联指纹浏览器 版本更新
英文标题: Mbbrowser Version Updated
ID: 114
分类ID: 27
添加时间: 1664529885
更新时间: 1753264279
访问次数: 0
SEO标题: 候鸟防关联指纹浏览器版本更新
SEO关键词: 候鸟防关联指纹浏览器版本更新
SEO描述: 候鸟防关联指纹浏览器版本更新

================================================== 内容 ==================================================
### 【2025年07月22日】版本: **********

**版本功能重要更新**  支持向下兼容所有历史候鸟客户端版本

#### 新版本功能更新列表

• 修复APISERVER 模式在本地模式中，处理环境仍有校验环境版本的问题。

• 新增Apiserver列出用户脚本库中所有脚本的接口和功能逻辑。

• 完善了团队协作中，对于各分组环境下的海量帐户、UA、PROXY代理、插件、AUTOSCRIPT的分享、管理、删除机制。
参见：[浏览器插件管理器使用教程](https://help.mbbrowser.com/tutorial/plugin.html "浏览器插件管理器使用教程")

• 此版本产品已进行各个业务细节的大量效率优化，窗口响应效率，底层数据处理效率，中英文描述及规范化显示。

• 此版本针对客户端PROXY代理模块和用户商业行为高效响应进行了大量优化。

• 此版本深入完善UI人机交互界面，修复消息对话框的显示问题。

• 添加apiserver的shell/getpath、shell/getbasedata和session/script_import的接口和相关的功能逻辑。

• Apiserver提供一个隐藏模式启动,--mainhide=on，启动后不添加托盘图标，只能api模式退出

• 添加在批量环境启动时显示停止的菜单项和相关的功能逻辑。

• 针对chrome 87指纹版本更新为后台分析chrome是否异常中断，避免chrome弹出控制台窗口。

• 添加环境编辑窗口代理检测的海外网络代理检测的功能逻辑。

#### 候鸟指纹浏览器多环境窗口同步群控功能完善说明：

• 通过修改遍历tab的算法，增加重试机制，增加保存及修改BaseChromeObj标签上下文的方式，解决了关闭第一个tab后，群控失效的问题。

• 修复多于一个子窗口时，群控命令对后面的子窗口控制异常的情况。

• 修复关闭群控后，再次打开群控异常的情况。

• 修复主窗口点击+新建tab，子窗口未同步新建,以及url栏回车时子窗口会新建tab的情况。

• 修改对多个子窗的群控打开urls命令为并行执行，以提高运行效率，并行逻辑已完成，不过并行后，出现后继其它命令失灵的情况。

• 关闭首个tab后出现群控失灵的情况，测试已完全解决。

• 执行切换tab命令时，不再使用超时，以避免切换后出现tab上下文操作不正常的情况。

• 解决群控打开多个url出现异常的情况，自前开多个url功能已测试正常。

• 解决窗口排列风格值未正确传入cdp，导致子窗口会显示到其它窗口后层的情况。

• 修复重排窗口时，控制面板设定的窗口大小和间距未生效的情况。


#### 候鸟指纹浏览器内核完善：

• 调试修改后，清空内容命令多次后存在失效的情况。

• 文本输入命令执行多次后，存在命令卡死的情况，发现是浏览页面输入焦点自动跳走的原因，需要用户重新点击输入框；

• 统一标签页和关闭当前标签页命令存在失效的情况有所改善，但和打开url命令多次操作后，还是会有异常情况已解决。

官网下载地址：[129内核版(Win10/11可用)](https://download.mbbrowser.com/release/20250721_MBbrowserSetup_**********_Core_129.exe "129内核版")，[105内核版(Win7/10/11可用)](https://download.mbbrowser.com/release/20250721_MBbrowserSetup_**********_Core_105.exe "105内核版")


------------

### 【2025年03月18日】版本: **********

**版本功能重要更新**  支持向下兼容所有历史候鸟客户端版本


#### 主要更新内容

• 此版本首次提供单环境/海量环境在运行时的实时值守服务

> 当内存出现异常, CPU温度过高，第三方软件恶意杀进程，系统休眠或系统运行速度明显减慢等各种软硬件问题，导致您的浏览器环境自动异常退出时，新增内置的实时值守服务新功能，将第一时间把闪退或崩溃的环境重新运行起来。通过这一创新功能，用户可放心已启动的环境24小时不间断运行，值守服务确保环境稳定运行。

• 完善切换产品客户端界面放大缩小DPI时，可能导致软件闪退的情况

> 通过优化界面适配算法和增强稳定性测试，已完善了产品客户端在调整DPI时可能出现的闪退问题。

• 新增支持API_URL自动提取代理功能，每次运行浏览器环境可自动提取IP平台商新代理IP或提取失败自动通过API_URL重新提取代理IP

> 已支持代理IP平台商：BrightData, IPIDEA, 521 Proxy, Okey Proxy, Tank IP, Go Proxy, 922 Proxy, 360 Proxy, ABC Proxy, Smart Proxy, IPFoxy, Cloudam, Pia S5 Proxy, Dove Proxy, LunaProxy, IP Fly, 922 S5 Proxy, IP2WORLD, Sky IP, Rola IP

• 更新候鸟内核到129.0.6668.112。新版内核优化了页面渲染速度，经测试在一定程度上减少了内存占用，安全性能和未有明显提升，浏览速度比上个版本快5%左右

• 完善候鸟内核在无痕模式下，webrtc公网IP地址偶见存在未修改的情况，已解决处理。

• 完善即定功能：

> 禁止网页自动弹出谷歌翻译浮窗的功能偶见未生效的问题，已解决处理。

• 新增一键粘帖代理IP串到客户端的代理IP面板中，不用多次粘帖，节省体力。

• 新加入一批最新的USER-AGENT商业数据库，同时添加了商用最新版移动平台User-Agent。

• 新增新版候鸟chrome内核对于老浏览器插件，过期插件的支持，对于谷歌明确声明的新版内核不再支持老版插件的情况，候鸟内核进行了大量完善，候鸟内核不仅可支持最新版本插件，对于很旧的插件依旧可以实时支持安装入环境并正常运行。

• 此版本首次支持客户通过数据备份管理器，将所有业务数据完整一键备份到本地，和从本地一键导入到客户端之功能。

> 原来只有一种备份方式，即对MBDATA目录进行备份的方式，此方式为新增辅助方式，在外出需要将数据备到U盘时，可采用此方式。特点是占用空间小，备份速度快，缺点是没有MBDATA目录数据完整。

• 本地日志管理器新增记录项：随机指纹运行环境将指纹信息加入到日志中。

• 本地日志管理器新增记录项：对高级配置列表项的操作，自动写入候鸟日志管理器。

• 新增：已安装插件列表：右键菜单添加“添加此插件到我的个人插件库中”菜单项和功能逻辑。

• 首次安装，候鸟客户端主面板窗口位置默认调整到：显示在屏幕右侧。

• 添加环境管理窗口批量修改环境系统和分辨率的功能逻辑。

• 新增数据目录检测功能，在启动时，如果检测到MBDATA数据文件夹或您已设定的数据文件夹不能正常访问，右下角自动弹出系统提示窗口进行提示。


官网下载地址：[129内核版(Win10/11可用)](https://download.mbbrowser.com/release/20250312_MBbrowserSetup_6.9.28.196_Core_129.exe "129内核版")，[105内核版(Win7/10/11可用)](https://download.mbbrowser.com/release/20250312_MBbrowserSetup_6.9.28.196_Core_105.exe "105内核版")


------------


### 【2025年01月02日】版本: *********

候鸟指纹浏览器 ********* 版本，支持向下兼容所有历史候鸟客户端版本。

#### 新版本更新说明

版本更新日期: 2025-01-02

版本号: v*********

更新概述:

> 本次更新主要围绕环境编辑窗口的功能优化、高级属性控制逻辑完善、内核迭代升级、多国语言支持以及用户体验提升展开。修复了多个已知问题，新增了多项实用功能，并对界面和性能进行了优化。

#### 主要更新内容

#### 一、环境编辑窗口功能优化

1. **默认起始页逻辑新增**
   - 添加环境中可添加多个默认起始页功能，仅在勾选时自动打开多个默认起始页。
   - 一键批量将指定环境的默认起始页设定应用到其它分组下多个环境中。

2. **打开页面逻辑优化**
   - 添加窗口自适应屏幕大小逻辑，默认提供当前屏幕最合适的分辨率来展开环境页面。
   - 修复打开页面顺序变动的问题。
   - 添加打开页面数量限制提示窗口及相关逻辑。

3. **一键批量应用功能新增**
   - 新增从环境配置窗口一键批量应用功能，避免逐个环境反复设置的问题。
   - 新增批量应用环境设定的功能逻辑流程。

4. **新增语种支持**
   - 添加支持多国语言（LANGUAGE）。
   - 新增支持：越南语、印地语、土耳其语、泰语、西班牙语、葡萄牙语、乌克兰语。

5. **高级指纹属性控制**
   - 新增高级指纹属性控制：提供环境面板上配置高级指纹模板，模板参数每位对应一个属性值，当前支持24个值。
   - 完成高级属性全局值和局部值的读取自动本地保存备档。

#### 二、高级属性功能新增与优化

1. **新增高级属性控制逻辑**
   - 不显示图片、禁用自动播放、视频播放静音、不弹出Google翻译窗口、不弹出保存密码窗口、不弹出要求显示通知窗口、禁止网页自动读写系统剪贴板、代理或网络不通畅时自动停止打开浏览器、开启浏览器开发者模式设备模拟、退出时自动清理浏览器缓存、自动打开上次浏览的网页、退出自动备份环境Cookies、iPhone/Android模拟页面的圆点以光标箭头显示、启用本机GPU显卡硬件加速、启用环境运行时随机指纹。

2. **修复高级属性问题**
   - 修复环境摄像头属性打开后无效的问题。
   - 修复高级设置子项disable时，Check勾选窗不能显示的问题。

#### 三、内核升级与性能优化

1. **内核更新**
   - 迭代更新Chrome 105和129内核，修复个别情况下WebRTC显示Public IP不正确的问题。
   - 完成Chrome 105和129节省内存模式的内核逻辑。

2. **性能优化**
   - 优化程序启动后各列表的下载流程，由串行下载改为并行下载。
   - 修复环境高级设置中偶见的内存泄漏问题。

#### 四、用户体验提升

1. **界面优化**
   - 调整主窗口、登录窗口和环境编辑窗口的窗口属性。
   - 修改过期窗口的显示方式。
   - 添加候鸟托盘菜单动态切换DPI的功能逻辑。

2. **功能新增**
   - 添加环境编辑窗口的两个按钮的网页跳转功能逻辑。
   - 添加登录信息管理窗口右键菜单批量删除的功能逻辑。
   - 添加插件管理窗口、自动脚本管理窗口、分配IP管理窗口的导出环境到本地的功能逻辑。

3. **提示与限制**
   - 添加登录信息管理窗口的配置打开页面提示信息。
   - 修复登录信息窗口勾选后的按钮显示问题。
   - 添加环境编辑窗口打开页面数量限制提示窗口的XML和相关功能逻辑。

#### 五、问题修复

1. **环境编辑窗口问题**
   - 修复再次打开环境编辑窗口未弹出到最前的问题。
   - 修复环境编辑窗口删除按钮在某些情况下删除失败的问题。
   - 修复环境编辑窗口保存后再次进入打开页面顺序变动的问题。

2. **登录信息管理问题**
   - 修复登录信息管理窗口右键菜单设置开始页面的问题。
   - 修复登录信息窗口添加打开页面的passitem名称为空的问题。

3. **其他问题**
   - 修复存储登录信息XML出现的异常崩溃问题。
   - 修复修改代理失败直接退出的问题。
   - 修复自动脚本导出窗口和Cookies导出窗口的导出路径问题。

#### 总结

本次更新通过功能优化、问题修复和性能提升，进一步增强了软件的稳定性和用户体验。新增的高级属性控制逻辑和内核升级为用户提供了更强大的功能和更流畅的操作体验。建议用户及时更新以享受最新功能。

如有任何问题或建议，请联系技术支持团队。

官网下载地址：[129内核版(Win10/11可用)](https://download.mbbrowser.com/release/20241227_MBbrowserSetup_*********_Core_129.exe "129内核版")，[105内核版(Win7/10/11可用)](https://download.mbbrowser.com/release/20241227_MBbrowserSetup_*********_Core_105.exe "105内核版")，[87内核版(Win7/10/11可用)](https://download.mbbrowser.com/release/20241227_MBbrowserSetup_*********_Core_87.exe "87内核版")

------------


### 【2024年10月27日】版本: *********

候鸟指纹浏览器 ********* 版本，支持向下兼容所有历史候鸟客户端版本。

#### 新版本内核更新说明

候鸟防关联指纹浏览器 ********* 内置最新版本 防关联指纹 CHROME 内核：129.0.6650.0 版本，此 Chrome 内核版本美国谷歌公司于 2024 年 8 月 11日发布。该版本重点在于提升安全性、兼容性以及性能优化，进一步完善浏览体验。<span style="font-weight:bold;color:#1a73e8;">应谷歌技术规范要求,此内核仅支持 Windows 10/11+ 操作系统。</span>

防关联内核主要变化：

新功能：

• 引入了请求头完整性支持功能（Request Header Integrity），增加了对 HTTP 请求头的安全防护功能，进一步保障用户数据的安全。Mbbrowser Chrome 129.0.6650.0 在内存使用方面引入了多项优化，旨在减少资源占用，提升整体防关联浏览器性能。首先，它通过内存分配管理的优化，减少了后台标签页的内存消耗，例如仅在标签页重新激活时恢复占用资源。这种方法降低了长时间运行浏览器时的内存使用量，特别是在多标签浏览时能显著节省内存。

• 此外，新版引入了「本地窗口遮挡」(Native Window Occlusion)技术，专门为 Windows 系统优化。当窗口在后台或被遮挡时，浏览器会自动减少资源分配，确保仅对前台窗口分配主要资源，进一步减轻内存压力。这种方式在提升性能的同时，也在后台窗口或标签页保持高效的内存管理

安全修复：

• 本次防关联内核更新修复了多项关键漏洞，进一步强化了浏览器的安全防护。更新还包含了针对 WINDOWS 平台的 Perfetto TraceProcessor 的优化，使得业务开展、API 调用和调试的稳定性更高。

• 在防关联 129.0.6650.0 版本中，开发者们进行了大量安全修复，改进了浏览器的整体防护能力。例如，修复了在 WebRTC、JavaScript 引擎、和 v8 引擎中发现的一些漏洞，这些修复可以防止恶意代码执行并提升浏览器在处理网络请求和渲染页面时的安全性。此外，还更新了与 PDF 和图形渲染相关的代码库，以提高文件处理的稳定性。

其他改进：

• 针对 PC 浏览器虚拟 Android ARM32、ARM64 以及 IPHONE IOS设备进行了兼容性优化，大幅提升了模拟移动端上的浏览器运行效果。还在 Windows 平台上增强了 PGO（Profile Guided Optimization）构建的稳定性，优化了资源使用率，提高了应用的响应速度。

• 内存管理得到了改进，减少了崩溃的情况。

• 此版本针对 Windows 11 进行了优化，提高了设备上的稳定性。

此次更新进一步提升了本次新版防关联内核在跨平台的稳定性和性能表现，使得用户在 PC 上批量虚拟各物理设备上的使用体验更为流畅。

#### 候鸟 129 防关联内核首次支持：

虚拟手机、平板环境时在指纹浏览器页面中，**小圆点与鼠标箭头光标切换**。

以下两类模式支持实时切换：

小圆点模式：

• 延续并保持谷歌原生的防关联内核默认模式，首次进入候鸟客户端运行环境默认以小圆点模式运行。原汁原味的防关联 CHROME 内核运行场景。

鼠标箭头光标模式：

• 通过在设置面板中进入浏览器设置，勾选：模拟手机页面的小圆点切换为光标箭头。使候鸟防关联内核在虚拟各种手机型号、平板等设备时，不再以小圆点方式运行，以鼠标光标箭头模式运行保证客户业务的稳定，健康有序开展，同时保证原汁原味的防关联 CHROME 内核运行场景。

广大电商客户可在此新版中，彻底解决因进入国际互联网手机页面后，因小圆点光标而开展业务受阻的历史遗留问题。

#### 新版本功能更新列表

• 已解决在环境运行时，再次进行指纹检测遇到的报错和乱码问题。

• 候鸟 UA 管理器：已解决少数系统中，UA 商业库管理窗口中，左键复制 UA值存在未正确对应的情况。

• 候鸟 UA 管理器：人工设置 UserAgent 时，新增自动检测与校验 UserAgent数值，当数值为空，默认 UserAgent 值根据设定系统，自动指定当前较新的商用数值。

• 候鸟客户端 UI 界面：主面板环境项右下角时间信息默认颜色不随事件的变化而变化，已修复。

• 候鸟客户端 UI 界面：修复代理服务器管理窗口滚动条滑块偶尔无法选中的问题。

• 候鸟客户端 UI 界面：环境列表项增加关闭网络连接错误信息按钮及相关逻辑，避免网络恢复后红字提示仍旧存在的情况。

• 候鸟客户端 UI 界面：完善 richedit 控件代码，添加控件的字符串的差异化的显示逻辑。

• 候鸟客户端 UI 界面： 完善候鸟所有列表的显示效果，所有数据列表新增支持平滑滚动。

• 候鸟客户端 环境管理引擎：修复少数情况下，候鸟程序获取的环境主窗口句柄不正确，获取的环境窗口句柄加入是否有最大最小化属性判断。

• 候鸟客户端 环境管理引擎：修复少数情况下，启动首个环境的主窗口被关闭后，窗口控制失控的问题。

• 候鸟客户端 环境管理引擎： 修复极少数情况下，异地初次登录时，运行环境时存在打开上一次浏览 tab 页面失败的情况。

• 候鸟自动化 API 服务器：新增接口 -> 指定环境中的指定脚本设定为非激活状态

• 候鸟自动化 API 服务器：新增接口 ->将未激活脚本从指定环境中移除

• 候鸟客户端 插件库管理引擎：完善插件管理窗口的搜索功能，新增支持输入关键字对海量插件库中，插件描述文本的实时搜索。

• 首次提供支持代理服务器检测报告，在修改环境的面板中，检查代理失败后，通过右侧磁帖窗体显示详细报错细节，并内置 Traceroute 数据路由报告，方便客户分析代理服务器失败时的详细原因及故障位置。

官网下载地址：[129内核版](https://download.mbbrowser.com/release/20241027_MBbrowserSetup_*********_Core_129.exe "129内核版")，[105内核版](https://download.mbbrowser.com/release/20241025_MBbrowserSetup_*********_Core_105.exe "105内核版")，[87内核版](https://download.mbbrowser.com/release/20241025_MBbrowserSetup_*********_Core_87.exe "87内核版")


------------

### 【2024年07月15日】版本: *********

候鸟指纹浏览器 ********* 版本，支持向下兼容所有历史候鸟客户端版本。

#### 新版本功能更新列表

• 首次增加全球多线路设定，支持客户在运营业务体系下：通过自行选择不同的线路来避免本地网络进入海外国际互联网后，由于国家firewall问题无法与候鸟服务器通讯。
> 支持一键勾选写入HOSTS模式，可在DNS失效的极端恶劣的本地全局海外网络环境下，仍能保证高速登录、同步、配置环境并开展自有业务；

##### 【多线路网络的特征】

以下两类模式支持实时切换：

云同步/网络模式：
> 一键查看最快的连入网络线路速率，并首次可自行切换不同的线路，来保证环境在运行时，不会出现数据无法备份问题，当进行变更环境数据、积累新增环境数据（数据包含但不限于：环境配置、环境指纹、环境历史浏览记录、COOKIES、登录帐户表等所有关键数据），其环境变更，将实时通过多线路平台进行版本备份到云端。

纯单机/本地强安全模式：
> 沿续并保持与官方云端服务器、与外界无任何数据交互行为，所有客户数据仅在客户自有机器中，本地运转，无任何外界通讯行为，保持高安全性，高隐私性纯本地模式运行，多线路网络此时仅保证100%客户端的心跳正常无误(不出现帐户验证通讯失败)。

广大电商客户可在此新版中，彻底解决因进入国际互联网后客户端与国内服务器无法通讯导致的登录失败、同步失败等历史问题。

• 首次提供 候鸟指纹浏览器Puppeteer / Selenium公共脚本库，公共脚本集合服务，在候鸟指纹浏览器客户端中可一键查看各个脚本功能的相关代码。

• Puppeteer / Selenium公共脚本库增加提供脚本分类：数据采集、页面测试与验证、SEO优化、截图生成、表单自动填充与提交、用户行为模拟、页面监控和报警、网站内容更新检测、广告和跟踪脚本拦截、社交媒体自动化、爬虫管理和分布式爬取、数据可视化与报表生成、自动化登录与会话管理、页面性能分析、自动化部署与监控等各部份脚本代码示例。

• Selenium 脚本运行框架，首次新增对JAVA语言的支持。在候鸟客户端内进行JAVA脚本的编写可一键安装JAVA语言支持包，需要繁琐的配置，即装即用模式，一步完成所有JAVA环境配置。

• UI完善:  首次新增对于所有各类数据列表，需要鼠标滑轮滚动数据过程中，首次提供惯性滑轮高速数据阅览模式。类ANDROID/IPHONE的界面惯性效果更加人性化。

• 此版本首次新增支持候鸟指纹浏览器客户端主面板置顶后，点击系统的最小化或Fn+M最小化，客户端面板不再跟随消失的特性。

• 此版本对于海量环境的客户端进行了大幅度的完善。对于单帐户(单客户端下)：数千个环境的批量修改，批量配置，一键指派速度与响应性能有较大提高，经联合测试均未见有偶发卡顿现像。

• 优化候鸟客户端启动后，由于客户机的屏幕DPI切换、屏幕高分辨率等设置，导致候鸟客户端偶尔有看不见主面板的情况进行了针对性完善。

• 候鸟浏览器环境名称、脚本名称等自定义名称流程中，加入了自动对非法字符的检测，避免非法字符导致名称类信息显示异常。

• API部份：修复API SERVER在无头模式下运行时，偶发不显主窗口登录界面，随机异常的情况。

• 修复海量环境在批量复制/克隆过程中，存在多次重复上传，直接影响到复制/克隆效率的情况，已解决。

官网下载地址：[105内核版](https://download.mbbrowser.com/release/20240715_MBbrowserSetup_*********_Core_105.exe "105内核版")，[87内核版](https://download.mbbrowser.com/release/20240715_MBbrowserSetup_*********_Core_87.exe "87内核版")，[86内核版](https://download.mbbrowser.com/release/20240715_MBbrowserSetup_*********_Core_86.exe "86内核版")

------------


### 【2024年05月24日】版本: *********

候鸟指纹浏览器 ********* 版本，支持向下兼容所有历史候鸟客户端版本。

#### 新版本功能更新列表

• 此版本首次全面对客户内部提供官方海量商用自动化脚本库服务，自动化脚本管理器中，客户可以自行查找最符合自已业务需要的自动化脚本，并将官方库中的脚本转入自已的私人脚本库中，然后小幅度调整脚本代码来符合自已的业务需求，最终一键指派到自已的海量环境中，配合业务开展需要来运行。

• 此版本首次全面支持三套IP归属地、时区、国家、环境语言，全自动检测引擎，在SETUP面板中，客户可以自行指定最符合业务需求的IP检测引擎，首次支持IP2Location、IP-API、Maxmind三套检测引擎三合一模式。

> 客户在设定好时区检测引擎后，创建环境、更换代理IP时，进行时区检测时将以客户指定的时区检测引擎进行IP归属地、时区、国家、环境语言检测。

• 二次更新候鸟独有的user-agent 商业库，去除了少量部份已过期的商用记录。

• 新增一键批量导出每个会话环境到独立压缩包。原海量环境数据，只能导出到一个压缩包的历史遗留问题彻底解决，用户可一次性导出多个独立环境包，方便后期有选择的导入回候鸟。参见：[批量本地环境导出使用教程](https://help.mbbrowser.com/tutorial/item.html "批量本地环境导出使用教程")

• 脚本、插件、日志、环境查找等各个搜索框、新增搜索关键字历史下拉列表，此版本支持所有历史查找关键字多次使用时，不再重复输入，在下拉列表中鼠标点击选中即可二次搜索。

• 解决环境运行期间，无法查看环境配置的历史遗留问题，新增在环境运行中，可以查看已运行的各个环境详细配置参数。

• 新增环境分辨率设置可手工自定义填入值，在环境配置面板中，分辨率下拉列表框二次优化，新增支持可自行输入的环境分辨率下拉列表控件。

• 修复打开一键批量创建窗口时，偶发性候鸟异常退出的问题。

• 会话环境管理器新增一键批量修改、替换海量环境的默认起始页面地址。

• 候鸟帐号管理器里，此版本已新增6个商用站点，原商用站点仍旧有效。

• 候鸟自动化脚本APISERVER，本次版本已完善 Create API 中, Disable_img及Disable_video传参的对应逻辑，已测试通过。

#### 【候鸟下个版本功能发布更新预告：】

计划下个版本首次提供候鸟群控体系

群控历史研发进度报告：

• 完成窗口同步中cdp程序中鼠标、键盘操作的基本同步，鼠标移动的响应速度，已从原来js程序的8秒以上，提高到1-3秒左右,目前tab切换后同步异常还未解决，另外其它响应事件的同步已加入。

• 窗口同步处理两个以上子窗口同步操作时，共用同一中间变量导致同步失败的问题，改为每个子窗口使用独立中间变量后正常。

• 切换tab窗口时，加入新逻辑：遍历所有tab窗口，找到指定url的tab激活，并保存当前tab对象为实际同步操作的对象，已完成，目前测试切换tab后，同步操作已能实现效果，不过子窗口的url存在为空或值有个别差异的情况，后面还需要加入按tab顺序号来激活保存tab对象。

• 修复群控底层逻辑返回windows id存在错误的情况；

• 如果切换tab时，传入的url和子同步窗口的实际url不一致，增加按tab序号切换的逻辑

• mousemove消息的同步操作加入超时1秒控制，避免因mousemove操作堵塞响应流程，加入此逻辑后，同步响应效率有较为明显改善。

• 关闭tab时同步操作尚有问题的情况已解决。

• 修复群控底层切换tab监听失败的问题，已修复；

• 同步子窗口在cdp中单独用一数组保存，启动chrome时传入SyncType=0x39标志为子窗口，保存到该数组，主窗口关闭时，所有子窗口都关闭，已完成；

• 处理测试目标站点新开tab页面后，不能同步切换到正确的tab项问题，已解决；

• 修改键盘输入为每个字符实时同步显示，不再等全部输完才显示，已完成；

• 解决多子窗口情况下，存在内存访问冲突的情况，已解决，目前测试同开4个窗口时，同步操作响应速度已较为理想，不过多次操作后，存在个别窗口同步停止的情况，切换tab后能恢复，已完成。

• 测试目标站点，登录时，鼠标按下拖动验证码的操作不能同步的情况，已解决。

• 鼠标移动到目标站点登录按钮时弹出登录窗口后，在登录窗口的点击操作同步存在问题，估计需要有切换frame的操作，已解决。

• 启动候鸟内核后，如果是同步窗口项，启动后默认不打开历史url,已完成；

• 修复执行个别命令异常后，会直接退线程，导致同步不能继续的问题，已解决已完成；

官网下载地址：[105内核版](https://download.mbbrowser.com/release/20240523_MBbrowserSetup_*********_Core_105.exe "105内核版")，[87内核版](https://download.mbbrowser.com/release/20240523_MBbrowserSetup_*********_Core_87.exe "87内核版")，[86内核版](https://download.mbbrowser.com/release/20240523_MBbrowserSetup_*********_Core_86.exe "86内核版")

------------


### 【2024年03月25日】版本: *********

#### 版本功能更新

##### * 增加共享模式，支持在候鸟团队帐户体系下环境共享/批量共享多帐户间协同模式共同使用。

1、首次增加共享模式，支持在候鸟团队帐户体系下：环境共享/批量共享多帐户间协同模式共同使用，支持海量环境一键批量共享给远程多个子帐户。
支持在子帐户间同时协作配合进行业务开展，当指定环境由创建帐户共享给其它多个帐户后，创建帐户可随时收回共享出去环境，也可以长期多帐户共同维护共享的环境。

> 【环境共享的特征】
共享的环境被某一个（或多个）帐户同时变更环境数据、积累新增环境数据（数据包含但不限于：环境配置、环境指纹、环境历史浏览记录、COOKIES、登录帐户表等所有关键数据），其环境变更，将实时体现在所有共有此环境的多个候鸟帐户下。

2、候鸟指纹浏览器WEB控制台新增共享完整功能，在WEB控制台使用环境共享功能，您的客户端版本最低版本应为：********* 版本，最低不能低于此版本。

3、候鸟日志管理器增加共享与分享的日志记录功能。环境的来源，时间等信息实时记录在日志管理器中。

4、候鸟最新版内核更新：修复代理帐号密码中如果包含一些特殊字符后，可能出现的代理失败的情况。

5、完善全新帐户首次使用时，由于没有自定义UA数据，存在一定概率的环境浏览器分辨率变量没有初始化的情况，已修改全屏分辨率变量初始化位置，保证能够初始化。

6、此版本首次新增支持候鸟指纹浏览器客户端主面板置顶后，点击系统的最小化或Fn+M最小化，客户端面板不再跟随消失的特性。

7、完善环境编辑窗口、批量环境创建窗口和代理编辑窗口的代理类型下拉列表的显示逻辑，首次支持基于xml的代理设置显示, 为代理IP平台商的API接入提供后续业务支撑。

8、候鸟 APISERVER部份：修复启动apisvr时--hide=on参数有一定概率失效的问题， 修复api中分组删除逻辑中，如果组名含非英文符号时，删除失败的情况。

9、候鸟浏览器105版内核更新与优化，增加右键菜单翻译功能。

10、新增支持帐户下接收到的共享环境，在候鸟导入导出环境包时，本地导入和导出到本地的环境包自动去除共享属性，在本地以普通环境包存储。

11、候鸟日志管理器：增加人工在环境的历史版本中进行切换时，日志管理器自动记录版本切换详单功能。

12、优化候鸟指纹浏览器客户端主界面列表中，环境的创建时间的显示顺序与显示格式，方便快速在海量环境中梳理出常用的、重要的环境集合。

官网下载地址：[105内核版](https://download.mbbrowser.com/release/20240323_MBbrowserSetup_*********_Core_105.exe "105内核版")，[87内核版](https://download.mbbrowser.com/release/20240323_MBbrowserSetup_*********_Core_87.exe "87内核版")，[86内核版](https://download.mbbrowser.com/release/20240323_MBbrowserSetup_*********_Core_86.exe "86内核版")


------------


### 【2024年02月01日】版本: 5.3.2.166

#### 版本功能更新

##### * 在USER-AGENT管理器中正式提供完整的300万条 USER-AGENT商业库数据服务。

1、此版本首次在USER-AGENT管理器中，增加USER-AGENT 300万条商业库供客户环境使用，新增支持客户将商业库中的USER-AGENT导入到自有库中使用，新增支持直接将商业库中的数据直接插入的环境中使用，并提供细致的UA多种类别选择，实时列出客户需要的USER-AGENT供商业应用。

2、新增登录管理窗口列表右键菜单“复制到我的环境帐户库”和"查看登录密码/隐藏登录密码"菜单项功能逻辑。

3、新增“复制到我的环境帐户库”和"查看登录密码/隐藏登录密码"的操作记录到环境操作日志功能逻辑。

4、新增日志窗口、环境登录管理窗口、插件管理器、脚本管理器窗口的数据列表排序功能。

5、优化候鸟登录窗口的显示、运行效率，二次完善候鸟产品的窗口UI界面、多国语言规范显示。

6、SETUP设置面板中，浏览器选项栏，新增自动备份环境书签和自动备份环境历史记录到本地的选项，开启后环境中的书签和历史记录将永久保存不会出现遗失，团队协作中，环境分享将连同书签和历史记录一并分享。

7、二次完善在环境管理面板和环境数据包管理面板中，运行非当前分组环境时，运行的环境状态存在显示错误情况。

8、UI界面完善，已解决当点击各面板中的下拉框时，下拉框中的文本在高dpi下会向左偏移的历史遗留问题。

官网下载地址：[105内核版](https://download.mbbrowser.com/release/20240130_MBbrowserSetup_5.3.2.166_Core_105.exe "105内核版")


------------


### 【2023年12月29日】版本: 5.2.8.164

#### 版本功能更新

##### * 新增 指纹轨迹，环境版本切换

1、此版本首次支持客户端全环境下指纹轨迹检测与报表反馈功能。候鸟指纹浏览器支持实时监控您的环境所有指纹稳定性，在您开始运行候鸟浏览器内核前，您可以通过指纹轨迹来方便的判断环境运行是否处于安全稳定的指纹数据下。候鸟的指纹轨迹图表，可以快速的一键检测到各个重要指纹数据是否与历史数据一致。不需要人工进行指纹数值对比。对于有变化的指纹，在轨迹图表里能够第一时间展示出来，并为您的业务开展保驾护航。

2、此版本的候鸟浏览器首次支持环境版本切换功能。如果您希望将指定的环境切换到您在历史中某一天设置过环境参数的版本上，您可以使用此功能方便的自由切换指定环境的各个历史配置版本。值得注意的是，由于本地模式下，您的所有环境数据仅在您的电脑里存储，因此，此功能不支持本地模式，仅支持网络模式；

3、此版本的候鸟浏览器首次支持chromium内核为105的版本，目前105版本为国内外用户广泛使用的版本，候鸟浏览器秉承稳定高效的原则，以国内外用户广泛使用的较新内核版本作为主要使用版本。在特殊网站或场景中，您也可以通过官网安装其它内核版本，如86、87版本来匹配不同的网站。

4、修复脚本编辑器传入文件路径中有中文时，导致文件访问失败的问题；

5、候鸟内核105版本可使用候鸟官方公共插件库，无需通过GOOGLE商店进行安装。

6、API部份：候鸟APISERFVER 调整addproxy接口的逻辑，"Is_CheckProxy"参数为1时代理检测如不通过不再添加此新代理，并返回失败信息。仅检测成功后添加此代理。

7、API部份：添加新接口/proxy/add的相关的检测代理的功能逻辑，详见API官方栏目及在线文档；

8、客户端部份：解决候鸟主程序由本地模式改回联网模式后，程序启动时有极少数崩溃的现象；

9、客户端UI：修复界面中阴影窗口和主窗口中间会插入其它窗口显示的问题，已做修复。；

10、客户端UI：修复启动环境后，鼠标滚动客户端UI的环境列表时，列表前部出现空白行的情况；

11、客户端UI: 用户添加环境编辑窗口UserAgent、代理ip、代理用户名密码等编辑框超长时，自动显示tip来完整显示内容的功能逻辑；

12、API部份：新增完善启动环境 api中，"args": [ "--disable-extensions", "--blink-settings=imagesEnabled=false" ]中的执行逻辑。

13、API部份：api启动环境接口二次完善启动各个环境的间隔时间的逻辑。

14、API部份：新增ApiServer新接口/browser/status的功能逻辑。详见官网API栏目。

15、API部份：二次完善API删除item指令，对于有可能运行环境异常而导致删除失败的情况进行了处理。

16、安装部份：候鸟安装包启动逻辑，加入定时更新进度条的逻辑，解决进度条开始安装时有10秒的停滞情况。

官网下载地址：[105内核版](https://download.mbbrowser.com/release/20231229_MBbrowserSetup_5.2.8.164_Core_105.exe "105内核版")，[87内核版](https://download.mbbrowser.com/release/20231229_MBbrowserSetup_5.2.8.164_Core_87.exe "87内核版")，[86内核版](https://download.mbbrowser.com/release/20231229_MBbrowserSetup_5.2.8.164_Core_86.exe "86内核版")


------------


### 【2023年10月16日】版本: *********

#### 版本功能更新

##### * 新增 切换 本地模式 单机版安全架构

1、此版本**首次**支持客户端切换到完全**本地模式**下运行。候鸟指纹浏览器本地模式，支持您的所有商业数据仅且只存储在本地设备里，并提供候鸟产品所有功能特性，商业使用过程中，不再进行任何敏感数据的同步/通讯，候鸟客户端自动禁止数据与云端存储同步，因此带来更快的响应速度，更高的数据安全保障，提供更流畅的用户体验，保证客户业务及客户系统的高可靠性和稳定性。并支持您在单机模式与网络模式中进行自由实时切换；

2、候鸟浏览器内核更新，兼容与强化海量设备上网络站点，海量页面的访问、指纹高安全性；

3、新增支持 APISERVER 自动化脚本服务器 基于候鸟本地模式下的运行；

4、优化切换组后，一些没有置顶的环境项显示置顶的现像；

5、优化候鸟客户端主界面环境列表的显示与响应效率；

6、优化在海量环境下(2000+)，手工删除单个环境时存在随机出现的卡顿现像；

7、新增在登录窗口，切换候鸟客户端运行在本地模式或网络模式下的开关；

8、新增会话环境管理窗口、插件管理窗口、脚本管理窗口、登录信息管理窗口的环境 ID 搜索功能，供使用 APISERVER 自动化脚本时的环境 ID 查询用途；

9、大幅优化产品响应性能，经测试，普通计算机加载完成 5000 个环境仅需要 6 秒（旧版本 17 秒），5000 项环境在分组的切换用时已提升到 2 秒以下；

10、优化语言列表为 MAP 类型，加快界面查找获取对应语言文本的速度；

11、添加日志管理窗口：编辑环境操作记录、日志导出功能、操作记录复制功能的相关的功能逻辑；

12、完善 APISERVER 自动化服务器下，API 方式安装本地自定义插件小概率失败的情况。

官网下载地址：[中文版](https://download.mbbrowser.com/release/20231012_MBbrowserSetup_*********.exe "中文版")  ， [多语言英文版](https://download.mbbrowser.com/release/20231016_MBbrowserSetup_MutiLanguage_*********.exe "多语言英文版")  ， [105内核订制版](https://download.mbbrowser.com/release/20231016_MBbrowserSetup_CoreCustomized_105_*********.exe "105内核订制版")

------------


### 【2023年09月14日】版本: **********

#### 版本功能更新

1、此版本首次支持客户一键批量指派海量代理IP到海量的环境中，无需逐个添加或修改代理；

2、批量指派代理IP服务支持随机指派模式和顺序指派模式，新增支持海量环境指定替换多个代理IP；

3、首次支持基于本地环境的日志管理器，通过日志管理器可查询历史登录退出记录和使用时长；

4、新增本地环境日志管理器可查询各个环境操作的历史，支持针对某一个特定环境查询其历史使用轨迹；

5、添加候鸟创建、批量创建、克隆、批量克隆环境相关的日志记录功能逻辑；

6、首次支持主面板列表的滚动栏风格提升，鼠标移入到主面板，滚动条半透明显示；

7、UA商业指纹匹配库从300万条已增加到360万条，在此版本中已提供商业使用；

8、已修复脚本管理器、帐号管理器、插件管理器窗口中，B区和C区的列表点击名称排序后，会去排序A区列表的情况。

9、二次完善随机USER-AGENT的API接口，随机的USER-AGENT API支持匹配上更全面的手机端同时无任何延迟；

10、新增APISERVER POST传入json语法的检测逻辑，如果json格式不合法，则将直接停止流程，返回json语法错误提示；

11、新增候鸟APISERVER中删除环境接口参数Is_Delete_All，支持一条指令可物理清空本地所有环境。

12、二次完善自动化脚本导入，已优化一次性选择多个脚本文件包进行导入的效率和稳定性。

13、二次完善自动化脚本导出过程中，脚本包小概率导出失败和加密脚本没有成功导出的情况。

官网下载地址：[中文版](https://download.mbbrowser.com/release/20230912_MBbrowserSetup_**********.exe "中文版")  ， [多语言英文版](https://download.mbbrowser.com/release/20230914_MBbrowserSetup_MutiLanguage_**********.exe "多语言英文版")  ， [105内核订制版](https://download.mbbrowser.com/release/20230914_MBbrowserSetup_CoreCustomized_105_**********.exe "105内核订制版")

------------



### 【2023年08月01日】版本: **********

#### 版本功能更新

1、此版本首次支持客户自行开发、自创建，并导入自定义插件到候鸟的功能，无需通过GOOGLE商店审核，即可将自定义插件一键批量分配到需要的多个环境中；

2、此版本首次支持主面板右键菜单指定环境下，COOKIES详情与有效时长核对功能，在指定的环境上点击右键可查看环境当前使用的COOKIE和COOKIE的有效时长；

3、优化在多种DPI显示模式下，列表头的显示效果与默认DPI时显示效果不一致的情况；

4、新增并强化环境版本一键批量校验功能，方便在网络阻塞的条件下，一键进行环境版本批量同步/校验，在异地使用时，确保环境包为最新版本；

5、环境创建API接口增加代理检测状态报告，避免在批量创建环境时，由于代理检测耗时较长而导致的误以为创建卡顿；

6、添加候鸟主程序导入环境和导出环境的路径记忆功能；

7、新增候鸟商用User-Agent库17465条，用于支撑客户各类业务中的User-Agent匹配需求；

8、二次强化ApiServer多语言国际支持，默认以英文界面显示所有返回信息；

9、优化当客户在User-Agent管理器中，自行添加自定义User-Agent到UA库时，User-Agent管理器偶然有显示的最新UA信息和添加信息未对应的情况；

10、添加插件列表xml存储和加载FILE_PATH字段的相关的逻辑。

官网下载地址：[中文版](https://download.mbbrowser.com/release/20230731_MBbrowserSetup_**********.exe "中文版")  ， [多语言英文版](https://download.mbbrowser.com/release/20230801_MBbrowserSetup_MutiLanguage_**********.exe "多语言英文版")  ， [105内核订制版](https://download.mbbrowser.com/release/20230801_MBbrowserSetup_CoreCustomized_105_4.9.66.152.exe "105内核订制版")

------------



### 【2023年07月06日】版本: 4.9.40.142

#### 版本功能更新

1、增加Api模式下环境id的复制功能，新增主面板上任意环境上点右键菜单支持复制环境ID；

2、调整环境管理窗口的界面布局细节；

3、调整listid接口的查询功能逻辑，调整输出结果；

4、修优Apiserver启动时窗口自动居中；

5、优化候鸟的combo下拉框滚动条的宽度，方便拖动滚动条；

6、新增账户管理窗口的列表框Ctrl+A的全选功能逻辑；

7、强化Api create item时TimeZone值可能存在设置不正确的情况，已优化；

8、优化当mbbrowser.exe已经在运行时，用户通过apiserver.bat【带hide=off】执行后，mbbrowse重新登录后不能正常显示的情况，已优化；

9、增加API判断脚本文件是否存在的逻辑，不存在增加在APISERVER窗口内提示报错信息；

10、新添加listid接口session_user_agent参数的功能逻辑；

11、新添加idcontainer接口的system,resolution,useragent信息的输出功能逻辑；

12、新添加session的creawte接口的system,resolution,useragent参数的输入功能逻辑；

13、优化session的creawte的cookie参数输入后cookies信息没有正常输入的问题。

下载地址： [官网下载](https://download.mbbrowser.com/release/20230705_MBbrowserSetup_4.9.40.142.exe "官网下载")

------------



### 【2023年06月30日】版本: **********

#### 版本功能更新

1、优化增强duilib的combo控件在鼠标拖动滚动条，解决鼠标移动到下拉窗口后释放鼠标，下拉窗口自动关闭的问题；

2、优化添加点击列表checkbox自动选中当前选项的功能逻辑；

3、优化登录对话框用户名下拉列表的滚动条外观问题；

4、帐号读写过程中，增加字段中有xml转义符全部进行转换逻辑；

5、修复帐号的login字段存储时未兼容xml非法字符的问题，已解决；

6、优化延迟打开chrome的线程加入异常中断判断，避免此线程异常中断后，无法再启动所有配置Item ，已完成；

7、强化api修改高级配置接口中的ua获取逻辑，避免多线程可能导致的获取错误数据；

8、api启动命令及login命令加入--hide参数，首次支持APISERVER控制支持mbbrowse客户端界面与API指令同时控制环境；

9、修复多个跳转地址的多语言版本区别；

10、添加本地导入窗口和分组管理窗口的窗口大小记忆；

11、添加会话环境管理窗口CTRL+A全选的功能逻辑；

12、增加各个管理窗口的界面布局和窗口大小记忆的功能逻辑；

13、优化并调整会话环境编辑窗口的滚动条宽度；

14、设置编译配置中打开启用内存大地址，首次支持达到5000个以上环境高效运行；

15、修复uilib库的shadow显示逻辑中内存分配失败后程序异常的情况；

16、修复显示修改配置信息时，如果配置的分辨率不在ua库中，就无法显示出自定义ua值，已解决；

17、导入xml记录的流程中，新增加入根据输入的UA值自动分析对应系统和分辨率的逻辑；

18、修复浏览器平铺窗口高dpi模式下控件失真的问题；

19、优化启动环境浏览器窗口显示逻辑，当前首次启动配置项时，执行居中窗口的操作，否则使用环境的上一次默认位置；

20、优化多DPI支持下环境居中窗口函数因DPI设置产生计算错误，导致居中位置不正确的问题；

21、首次支持所有列表管理窗口ctrl+a全选功能逻辑。

下载地址： [官网下载](https://download.mbbrowser.com/release/20230630_MBbrowserSetup_**********.exe "官网下载")

------------



### 【2023年06月15日】版本: **********

#### 版本功能更新

##### * 此版本 首次支持 简中，繁中，英，俄，日 四国语言。

1、优化高dpi情况下，窗口大小保存及调取错误的情况。

2、新增添加拖动子窗口改变大小后存储子窗口大小的功能逻辑。

3、完善调整主界面和部分界面的xml细节。

4、修复获取ip信息时，经纬度信息获取后未能正确保存的情况。

5、完善代理检测状态判断方式，由按钮文本判断改为指定bool变量来判断。

6、完善chrome帐号数据检测时，因localstata文件被占用，导至读取失败而重新建立localstata文件的问题。

7、升级cdp.exe到版本*******，更新chromedp库到最新的0.9.1，更新cdproto库到********版本。

8、完善登录对话框切换语言部分字符串未切换语言的问题。

9、优化和修复缩放最小化窗口可能存在的异常情况。

10、优化SetMinInfo时计算高dpi适应值到GetMinInfo中进行计算。

11、支持多语言下tooltip的鼠标停留快速显示全文。

12、优化多语言版本下，启动cdp极少数情况异常的情况。

13、优化并修复代理下拉框滚动稍微卡顿的问题。

14、首次支持拖动窗口大小后保持记忆效应，大幅强化操作效率。

15、优化插件管理窗口布局细节，修复在高dpi下的显示问题。

16、优化登录窗口用户名下拉窗在用户名清空后选择可能无法显示在用户名输入框的情况。

17、优化useragent管理窗口未选中的情况下检测勾选useragent项的功能逻辑。

18、优化全选后，支持一次性批量直接复制多个session_id或plugin_id的功能，方便API调用时取值用途。

19、完善登录窗口的下拉条，首次支持多用户登录后，可通过下拉条切换多个帐户，无需重新输入。

20、优化候鸟启动启动速度，同时显示多语种时某些字段不影响操作效率。

21、修复候鸟设置对话框的语言栏下拉框和主界面菜单不一致的问题。

22、添加候鸟的登录过程，控制台跳转，购买跳转等功能逻辑的多语言强化支持。

23、首次添加新增支持API接口：【从我的脚本库中指派脚本到目标环境中】。

下载地址： [官网下载](https://download.mbbrowser.com/release/20230615_MBbrowserSetup_**********.exe "官网下载")

------------



### 【2023年05月11日】版本: **********

#### 版本功能更新

首次全面支持高分辨率、高DPI显示器。此版本向下覆盖并兼容所有历史版本功能。

一、提供基于DPI 100%，125%，150%，175%，200%高分辨率2K、4K、5K、8K市面上所有显示器，并同时支持苹果高分显示器。

二、新增提供所有环境下各COOKIE历史版本存储与管理平台服务，支持通过控制台管理自有环境下所有历史COOKIE，并支持进行业务COOKIE在线分析。

三、新增COOKIE安全服务，提供COOKIE全局开关，支持用户自行对COOKIE多版本存储的开通与禁用。

四、修复API plugin_install 接口安装下载插件时极少量出现失败的情况。

五、提供获取成员列表服务，支持团队协作用户在脚本内自动查询团队成员的各类状态。

六、添加插件管理窗口和脚本管理窗口列表右键菜单"复制环境id到剪贴板"的功能逻辑。

七、新增插件安装API接口：plugin_install接口的插件版本号参数的功能逻辑。

八、提供查询指定环境ID的配置数据API功能，并提供以下各类接口模式。

九、首次彻底解决在高分辨率显示器下，高DPI下，界面字体模糊的历史遗留问题。

十、提供更新环境高级指纹参数API功能，并提供以下各类接口模式更新环境。

十一、首次提供Apiserver的id_script_list、id_script_active、id_plugin_list、plugin_delete、list、plugin_install6个接口的流程逻辑。

十二、优化客户端添加服务器模板的功能逻辑，首次提供业务模板用户自定义管理平台。

十三、修改环境面板的模板下拉框的显示逻辑，强化日常使用体验，新创建的环境将默认显示在最上方（最近一周新增加的环境项，按时间默认显示在TOP项的后面）。

十四、修复代理管理器批量检测代理，未完成检测时关闭代理管理器极少数情况下会出现崩溃现象。

参见：[API使用文档](https://www.mbbrowser.com/api "API使用文档")

下载地址： [官网下载](https://download.mbbrowser.com/release/20230511_MBbrowserSetup_**********.exe "官网下载")

**【旧版内核】** 下载地址：[官网下载](https://download.mbbrowser.com/release/20230512_MBbrowserSetup_CoreCustomized_**********.exe "官网下载")

------------



### 【2023年03月23日】版本: *********

#### 版本功能更新

1. **首次提供 脚本自动化API/API服务器/控制台全面对外支持。**

	一、提供基于HTTP协议的APISERVER实时服务器，支持用户控制环境在前、后台运行的模式下开展自动化业务。

	二、新增帐户API凭据认证，与用户登录名、密码物理分隔，支持团队协作场景业务开展过程中通过APP_ID/APP_KEY鉴权下开展常规业务。

	三、新增API服务器日志服务，支持用户调试脚本过程中，通过API控制台的实时回传和日志进行业务逻辑代码分析与调试。

	四、提供API服务器数据回传控制功能，避免用户运行自动业务过程中大量历史垃圾数据堆积。

	五、提供获取成员列表服务，支持团队协作用户在脚本内自动查询团队成员的各类状态。

	六、提供打开环境、关闭环境、强制终止环境的API接口支持，用于启动指定的环境，启动成功后可以获取浏览器接口执行 selenium 和 puppeteer 自动化脚本。

	七、提供获取环境列表完整API功能。

	八、提供查询指定环境ID的配置数据API功能，并提供以下各类接口模式。

	九、提供创建环境完整功能API功能，并提供以下各类接口模式创建环境。

	十、提供更新环境高级指纹参数API功能，并提供以下各类接口模式更新环境。

	十一、提供更新环境API功能，并提供以下各类接口模式。

	十二、提供更新环境代理API功能，并提供以下各类接口模式。

	十三、提供删除环境API功能。

	十四、提供导入Cookie、导出Cookie等API功能，并提供以下各类接口模式。

	十五、提供获取随机UA11、清除环境本地缓存等API功能，并提供以下各类接口模式。

	十六、提供API服务器成功错误ERROR/STATUS API代码表，支持用户调试过程、运营过程快速查询异常情况。

	参见：[API使用文档](https://www.mbbrowser.com/api "API使用文档")

2. **二次完善提供 自动化脚本的加解密功能（脚本源代码保护功能）**

	一、支持用户自行对自动化脚本进行强加密模式，加密后的脚本可直接在候鸟的环境中加载高效运行。

	二、自动化脚本加解密同时支持候鸟已推出的双WebDriver自动化引擎。

	三、 加密后的脚本支持导出到本地，您可以将加密后的脚本安全的传递给您的同事进行导入而无须担心脚本内容泄露。

	四、 加密后的脚本，支持团队协作，支持分享给您的团队帐户在加密状态下使用。

	五、 加密脚本在候鸟客户端中采用银行级128位算法加密，加密脚本的密码由您自行设定并妥善保管 ，加密后的脚本除了密码设定/拥有者，其它任何人仅可以运行此脚本外，均无法查看脚本内容，无法修改脚本内容，无法解密脚本内容。

	六、 如您将密码告知对方，对方将可以对此脚本进行解密并获得此脚本的明文内容。

	七、 您设定的密码为脚本加解密的唯一钥匙：请您务必自行妥善保管 ，如遗失/遗忘您自行设定的密码，候鸟官方客服无法帮助您解密脚本，您将彻底无法解密脚本。

3. **首次支持 候鸟客户端环境管理面板支持API功能。**

	一、在环境管理器中增加会话环境的唯一ID的列表功能，保障用户在进行脚本代码维护中，可通过客户端批量取出指定环境ID集合。

	二、在分组管理器中，以分组为单位提供会话环境的唯一ID列表功能，保障用户在脚本代码维护中，通过客户端批量取出指定环境ID集合。

4. **WEB控制台首次提供API服务的后台管理功能。**

	一、通过控制台支持用户进行APISERVER的强制下线服务。

	二、支持用户在线申领API凭据，凭据实时生效并提供APISERVER服务。

	三、支持凭据的使用日志查询，凭据的在线时长查询。提供APISERVER凭据的当前状态查询，提供团队协作用户有效的利用凭据资源。

	四、支持API控制台使用日志查询，提供API请求URL和IP查询，同时提供请求时间查询。减少客服工作量。

5. **优化APISERVER控制台环境的收发效率，增加API行业中常规的限流逻辑，规避商用机器受到来自API服务的不合理的海量攻击。**

6. **增加API服务的收费策略，修正API服务的免费政策，采用先全面免费，后期官方根据差异化服务来逐步收费的政策思想来进行。**

7. **此版本产品迭代完成各个业务细节的大量效率优化，窗口响应效率，底层数据处理效率，中英文描述及规范化显示。**

 下载地址： [官网下载](https://download.mbbrowser.com/release/20230323_MBbrowserSetup_*********.exe "官网下载")

------------



### 【2022年11月22日】版本: *********

#### 版本功能更新

##### 首次提供 环境高级分类面板 (二级面板)

<p><img src="6d7b2882624511f09a0d0242ac130006/images/image_081f66ea06fc.png" width="360" /></p>

一、按环境类别进行环境操作，根据环境的类别不同，您可以在商业活动中，从您海量的环境库中，快速定位并使用到符合业务需要的目标环境群。

二、环境高级分类面板支持记忆功能，每次进入此面板默认记忆并展示上次使用的规则。

三、环境高级分类面板支持所有主面板右键菜单（运行环境、编辑环境等）所有功能。

四、支持通过时间维度和环境类别维度进行分类展示，可快速定位到需要的环境。

五、提供图1蓝框中按钮进行主面板与二级面板进行切换。

##### 首次全面提供 自动化脚本的加解密功能（脚本源代码保护功能）。

![](6d7b2882624511f09a0d0242ac130006/images/image_aa8b64c8884b.png)

一、支持用户自行对自动化脚本进行强加密模式，加密后的脚本可直接在候鸟的环境中加载高效运行。

二、自动化脚本加解密同时支持候鸟已推出的双WebDriver自动化引擎。 

三、 加密后的脚本支持导出到本地，您可以将加密后的脚本安全的传递给您的同事进行导入而无须担心脚本内容泄露。

四、 加密后的脚本，支持团队协作，支持分享给您的团队帐户在加密状态下使用。

五、 加密脚本在候鸟客户端中采用银行级128位算法加密，加密脚本的密码由您自行设定并妥善保管 ，加密后的脚本除了密码设定/拥有者，其它任何人仅可以运行此脚本外，均无法查看脚本内容，无法修改脚本内容，无法解密脚本内容。

六、 如您将密码告知对方，对方将可以对此脚本进行解密并获得此脚本的明文内容。

七、 您设定的密码为脚本加解密的唯一钥匙：请您务必自行妥善保管 ，如遗失/遗忘您自行设定的密码，候鸟官方客服无法帮助您解密脚本，您将彻底无法解密脚本。

##### 首次支持 主面板/二级面板 多选批量操作功能。

<p><img src="6d7b2882624511f09a0d0242ac130006/images/image_5192e1418269.png" width="360" /></p>

一、按住 **Ctrl键+鼠标左键** 点击各个环境项，可快速批量对环境进行操作，支持的操作有：批量置顶、批量运行、批量停止、批量转移到分组、批量导出、批量删除。

二、按住 **Shift键+鼠标左键** 可一次性在主面板选择海量环境，可快速批量对环境进行操作，支持的操作有：批量置顶、批量运行、批量停止、批量转移到分组、批量导出、批量删除。

![](6d7b2882624511f09a0d0242ac130006/images/image_64b017e8d856.png)

二次优化网络代理服务器管理，此版本 **首次提供对共用同一个代理的多个环境进行批量更换代理** 的功能。

对于使用相同代理的多个环境，无须再次手工一个个逐个环境中更换代理，通过网络代理服务器管理器，可一次性对指定的多个环境进行批量更换代理。


•	大幅优化团队协作分享效率及控制台环境下发效率，环境分享与下发将实时到达您或子帐户的客户端，不再有任何等候时间。

•	修复脚本管理窗口未选中环境激活脚本失败的问题。

•	完善了安装流程，优化了安装过程与安装界面，提供全新的安装界面。

•	通过控制台强制客户端下线功能二次完善已完成。

•	此版本产品迭代完成各个业务细节的大量效率优化，窗口响应效率，底层数据处理效率，中英文描述及规范化显示。

•	此版本迭代完成针对客户端PROXY代理模块和用户商业行为高效响应进行了大量优化。

 下载地址： [官网下载](https://download.mbbrowser.com/release/20221127_MBbrowserSetup_*********.exe "官网下载")

------------

### 【2022年10月20日】版本: *********
支持向下兼容所有历史候鸟客户端版本。


#### 版本功能更新

1. 此版本为首个支持 海量环境与批量代理高速检测功能之版本。

2. 全新支持一分钟检测300个代理，高于国内外产品批量代理检测速度达 6 倍。

3. 全新支持即时暂停检测，恢复检测功能，超出同行产品无法暂停检测等功能。

4. 全新支持检测代理并同时自动优化商户购买海量帐户群批量导入时的自动IP匹配，UA，国家，时区等关键值全自动匹配。

5. 同时支持仅代理批量检测，以毫秒级显示检测并区分各代理服务器的连接速率。新增三大搜索引擎代理同步连通效率，以保障代理的有效性检测。

 下载地址： [官网下载](https://download.mbbrowser.com/release/20221018_MBbrowserSetup_*********.exe "官网下载")

------------



### 【2022年09月30日】版本: *********
支持向下兼容所有历史候鸟客户端版本。


#### 版本功能更新

1. 首次支持全环境批量分配模式，支持2000-10000个环境一键批量分配帐户、UA、PROXY、插件、AUTOSCRIPT自动化到您指定的多个环境。

2. 首次支持商用客户自行购买的商业帐户数据集合，进行EXCEL格式批量导入，首次支持全局本地帐户EXCEL批量修改、支持帐户前期导入批量修改/导入后期批量环境修改行为。
参见：[批量本地帐户导入使用教程](https://help.mbbrowser.com/tutorial/import_data.html "批量本地帐户导入使用教程")

3. 首次支持全环境帐户批量/单项指派到环境后，在浏览器内核中全自动响应，并在目标网站登录页全自动进行帐户密码登录完成。

4. 完善了团队协作中，对于各分组环境下的海量帐户、UA、PROXY代理、插件、AUTOSCRIPT的分享、管理、删除机制。
参见：[浏览器插件管理器使用教程](https://help.mbbrowser.com/tutorial/plugin.html "浏览器插件管理器使用教程")

5. 此版本产品已进行各个业务细节的大量效率优化，窗口响应效率，底层数据处理效率，中英文描述及规范化显示。

6. 此版本针对客户端PROXY代理模块和用户商业行为高效响应进行了大量优化。

7. 此版本二次完善已对外开放自动化的底层WEBDRIVER，同时支持 Selenium底层自动化驱动与Puppeteer底层自动化驱动。

8. 此版本首次全面支持 Oxylabsauto(Oxylabs住宅代理)、Lumauto(动态住宅)、Luminati_http、Luminati_https、SmartProxy第三方代理IP。

 下载地址： [官网下载](https://download.mbbrowser.com/release/20220930_MBbrowserSetup_*********.exe "官网下载")

------------



### 新版本常规 安装/升级 官方说明

1. 卸载旧版本，候鸟MBDATA数据目录不用触动，不要删除。
2. 安装新版本。
3. 登录新版本，在SETUP设置面板查看MBDATA目录路径并确认正确指向到原MBDATA目录。
4. 升级完成。

注1：为保证产品服务质量，保证客户业务稳定，任何时侯，候鸟官方不会强制升级客户当前已使用版本。客户进行新版本升级，不会影响到您的配置环境和已运行的各历史业务数据，日常使用中，客户自有业务数据，请您定时通过产品客户端自带的环境导入导出功能，备份到本地妥善保管。

注2：MBDATA目录为您的业务关键数据，请放置在NVME/SSD高速硬盘里，以大幅提高您的业务运营响应速度。

#### 候鸟官方技术部

================================================== 英文内容 ==================================================
### [July 22, 2025] Version: **********

**Important version feature update**  Supports backward compatibility with all historical versions of the Migratory Bird client

#### Main updates

• Fixed the issue where the API SERVER mode still had a problem verifying the environment version in local mode.

• The newly added API server lists the interfaces and functional logic of all scripts in the user script library.

• Improved the sharing, management, and deletion mechanisms for massive accounts, UAs, PROXY proxies, plugins, and AUTOSCRIPT in various group environments during team collaboration.
See: [Tutorial on using the browser plugin manager](https://help.mbbrowser.com/tutorial/plugin.html "Tutorial on using the browser plugin manager")

• This version of the product has undergone extensive efficiency optimization in various business details, including window response efficiency, underlying data processing efficiency, as well as Chinese and English descriptions and standardized display.

• This version has undergone significant optimizations for the client PROXY module and efficient response to user business behaviors.

• This version has undergone thorough refinement of the UI (user interface) human-computer interaction interface, and has fixed the display issue of the message dialog box.

• Add interfaces and related functional logic for shell/getpath, shell/getbasedata, and session/script_import of the apiserver.

• The API server provides a hidden mode startup option, --mainhide=on, which prevents the addition of a tray icon after startup and can only be exited in API mode

• Add a menu item that displays "Stop" when the batch environment is started, along with the related functional logic.

• For the fingerprint version update of Chrome 87, it is set to analyze whether Chrome is abnormally interrupted in the background, to avoid Chrome popping up a console window.

• Add the functional logic for overseas network proxy detection in the environment editing window proxy detection.

#### Description of the improved multi-environment window synchronization and group control function of Migratory Bird Fingerprint Browser:

• By modifying the algorithm for traversing tabs, adding a retry mechanism, and enhancing the way of saving and modifying the context of BaseChromeObj tags, the issue of group control failure after closing the first tab has been resolved.

• Fixed the issue where the group control command had abnormal control over subsequent child windows when there were multiple child windows.

• Fixed the issue where the group control function was abnormal after being closed and reopened.

• Fixed the issue where clicking + New Tab in the main window did not synchronize with the sub-window, and the sub-window would create a new tab when pressing Enter in the URL bar.

• Modify the command to open URLs for multiple sub-windows in a group control to execute in parallel to improve operational efficiency. The parallel logic has been implemented, but after parallelization, subsequent commands have failed.

• The issue of group control malfunctioning after closing the first tab has been fully resolved through testing.

• When executing the tab switching command, timeout is no longer used to avoid abnormal operation of tab context after switching.

• Resolved the issue where multiple URLs opened by the group control resulted in exceptions. The function of opening multiple URLs from the front end has been tested and is functioning normally.

• Resolved the issue where the window arrangement style value was not correctly passed to CDP, resulting in child windows being displayed behind other windows.

• Fixed the issue where the window size and spacing set in the control panel did not take effect when rearranging windows.


#### Improvement of the kernel of the migratory bird fingerprint browser:

• After debugging and modification, the command to clear content may fail after being executed multiple times.

• After executing the text input command multiple times, there is a situation where the command gets stuck. It is found to be caused by the automatic focus jumping away from the input box on the browsing page, requiring the user to re-click the input box;

• The issue of the "Unify Tabs" and "Close Current Tab" commands failing has been improved, but there are still exceptions after multiple operations with the "Open URL" command. This issue has been resolved.

Official download link: [129 Kernel Version (compatible with Win10/11)](https://download.mbbrowser.com/release/20250721_MBbrowserSetup_**********_Core_129.exe "129 Kernel Version"), [105 Kernel Version (compatible with Win7/10/11)](https://download.mbbrowser.com/release/20250721_MBbrowserSetup_**********_Core_105.exe "105 Kernel Version")


------------



### [March 18, 2025] Version: **********

**Important Version Update** Supports backward compatibility with all historical MBbrowser client versions


#### Main Updates

• This version introduces real-time guardian service for single/mass environments during runtime

> When browser environments automatically exit abnormally due to various hardware and software issues such as memory anomalies, high CPU temperature, malicious process killing by third-party software, system hibernation, or noticeably slower system operation, the newly added built-in real-time guardian service will immediately restart crashed or failed environments. Through this innovative feature, users can rest assured that their launched environments will run 24 hours uninterrupted, with the guardian service ensuring stable operation.

• Improved potential crashes when switching product client interface DPI scaling

> Through optimized interface adaptation algorithms and enhanced stability testing, the potential crashes when adjusting DPI have been resolved.

• Added support for automatic proxy extraction via API_URL, which can automatically extract new proxy IPs from IP platform providers each time browser environment runs or automatically re-extract proxy IP via API_URL if extraction fails

> Now supporting IP platform providers: BrightData, IPIDEA, 521 Proxy, Okey Proxy, Tank IP, Go Proxy, 922 Proxy, 360 Proxy, ABC Proxy, Smart Proxy, IPFoxy, Cloudam, Pia S5 Proxy, Dove Proxy, LunaProxy, IP Fly, 922 S5 Proxy, IP2WORLD, Sky IP, Rola IP

• Updated MBbrowser kernel to 129.0.6668.112. The new kernel optimizes page rendering speed and reduces memory usage to some extent based on testing. While security performance shows no significant improvement, browsing speed is about 5% faster than the previous version

• Fixed occasional unmodified WebRTC public IP addresses in incognito mode.

• Fixed the intermittent ineffectiveness of the feature preventing automatic Google Translate popup windows.

• Added one-click proxy IP string pasting to client proxy IP panel, eliminating the need for multiple pastes and saving effort.

• Added new batch of latest USER-AGENT commercial database and latest mobile platform User-Agents.

• Added support in new MBbrowser Chrome kernel for older browser plugins and expired plugins. Despite Google's declaration that new kernel versions no longer support old plugins, MBbrowser kernel has been extensively improved to support not only the latest plugins but also allow real-time installation and normal operation of very old plugins.

• This version introduces support for complete data backup to local storage and one-click import back to client through the data backup manager.

> Previously there was only one backup method - backing up the MBDATA directory. This new auxiliary method is ideal for backing up data to USB drives when traveling. Its advantages include small space occupation and fast backup speed, though it doesn't include complete MBDATA directory data.

• Local log manager now includes new record: random fingerprint information is added to logs when running environments with random fingerprints.

• Local log manager now includes new record: operations on advanced configuration items are automatically written to MBbrowser log manager.

• New addition: Added "Add this plugin to my personal plugin library" menu item and functionality to installed plugins list right-click menu.

• On first installation, MBbrowser client main panel window position now defaults to the right side of screen.

• Added functionality for batch modification of environment system and resolution in environment management window.

• Added data directory detection feature that automatically displays a system notification in the bottom right corner when MBDATA data folder or your configured data folder cannot be accessed normally at startup.


Download URL: [129 kernel version (Win10/11)](https://download.mbbrowser.com/release/20250312_MBbrowserSetup_6.9.28.196_Core_129.exe "129 kernel version"), [105 kernel version (Win7/10/11)](https://download.mbbrowser.com/release/20250312_MBbrowserSetup_6.9.28.196_Core_105.exe "105 kernel version")

------------

### [January 2, 2025] Version: *********

The migratory bird fingerprint browser version ********* supports backward compatibility with all historical migratory bird client versions.

#### New version update instructions

Version update date: 2025-01-02

Version number: v*********

Update Overview:

>This update mainly focuses on optimizing the functionality of the environment editing window, improving advanced attribute control logic, iterating and upgrading the kernel, supporting multiple languages, and enhancing user experience. Fixed multiple known issues, added multiple practical features, and optimized the interface and performance.

#### Main update content

#### 1、 Optimization of Environment Editing Window Function

1. ** Default Start Page Logic Added **
-Multiple default start pages can be added to the environment, and they will only automatically open when checked.
-One click batch application of the default start page settings for a specified environment to multiple environments in other groups.

2. ** Open page logic optimization **
-Add window adaptive screen size logic, which defaults to providing the most suitable resolution for the current screen to expand the environment page.
-Fix the issue of changing the order of opening pages.
-Add a prompt window for limiting the number of open pages and related logic.

3. ** One click batch application function added **
-Add a one click batch application function from the environment configuration window to avoid the problem of repeated settings for each environment.
-Add a functional logic flow for setting up batch application environments.

4. ** Added language support **
-Add support for multiple languages (Language).
-New support: Vietnamese, Hindi, Turkish, Thai, Spanish, Portuguese, Ukrainian.

5. ** Advanced fingerprint attribute control **
-New advanced fingerprint attribute control: Provides configuration of advanced fingerprint templates on the environment panel, with each template parameter corresponding to a property value, currently supporting 24 values.
-Automatically save backup files locally by reading global and local values of advanced attributes.

#### 2、 Advanced attribute function addition and optimization

1. ** Add advanced attribute control logic **
-Do not display images, disable autoplay, mute video playback, do not pop up Google Translate window, do not pop up save password window, do not pop up request display notification window, disable automatic web page reading and writing system clipboard, automatically stop opening browser when proxy or network is not working, enable browser developer mode device emulation, automatically clear browser cache when exiting, automatically open last viewed web page, exit automatic backup environment cookies, display dots on iPhone/Android simulation page as cursor arrows, enable local GPU graphics hardware acceleration, enable random fingerprint when environment runs.

2. ** Fix advanced attribute issues **
-Fix the issue where the environmental camera properties become invalid when turned on.
-Fix the issue where the Check checkbox cannot be displayed when the advanced settings sub item is disabled.

#### 3、 Kernel upgrade and performance optimization

1. ** Kernel update **
-Iteratively update Chrome 105 and 129 kernels to fix the issue of WebRTC displaying incorrect Public IP in some cases.
-Complete the kernel logic for Chrome 105 and 129's memory saving mode.

2. ** Performance optimization **
-Optimize the download process of each list after program startup, changing from serial download to parallel download.
-Fix the occasional memory leak issue in the advanced settings of the environment.

#### 4、 User experience improvement

1. ** Interface optimization **
-Adjust the window properties of the main window, login window, and environment editing window.
-Modify the display mode of expired windows.
-Add the function logic of dynamically switching DPI in the bird tray menu.

2. ** New features added **
-Add the webpage jump function logic for the two buttons in the environment editing window.
-Add the logic for batch deletion function in the right-click menu of the login information management window.
-Add plugin management window, automatic script management window, and allocate IP management window to export the environment to the local functional logic.

3. ** Tips and Restrictions **
-Add a configuration prompt for the login information management window to open the page.
-Fix the button display issue after selecting the login information window.
-Add XML and related functional logic for the environment editing window to limit the number of open pages prompt window.

#### 5、 Problem fixing

1. ** Environment editing window issue **
-Fixed the issue where the environment editing window did not pop up to the front when reopened.
-Fixed the issue where the delete button in the environment editing window failed to delete in certain situations.
-Fixed the issue of changing the order of opening the page again after saving the environment editing window.

2. ** Login information management issues **
-Fixed the issue of setting the start page in the right-click menu of the login information management window.
-Fixed the issue where the passitem name on the login information window when adding an open page is empty.

3. ** Other issues **
-Fix the abnormal crash issue of storing login information XML.
-Fixed the issue of direct exit when modifying proxy failed.
-Fixed the export path issue for the automatic script export window and cookie export window.

#### Summary

This update further enhances the stability and user experience of the software through feature optimization, problem fixing, and performance improvement. The newly added advanced attribute control logic and kernel upgrade provide users with more powerful features and a smoother operating experience. It is recommended that users update in a timely manner to enjoy the latest features.

If you have any questions or suggestions, please contact the technical support team.

Official website download link: [129 kernel version (available on Windows 10/11)](https://download.mbbrowser.com/release/20241227_MBbrowserSetup_*********_Core_129.exe "129 kernel version"), [105 kernel version (available on Win7/10/11)](https://download.mbbrowser.com/release/20241227_MBbrowserSetup_*********_Core_105.exe "105 kernel version"), [87 kernel version (available on Win7/10/11)](https://download.mbbrowser.com/release/20241227_MBbrowserSetup_*********_Core_87.exe "87 kernel version")

------------

### [October 27, 2024] Version: *********

The MBbrowser Fingerprint Browser ********* version supports backward compatibility with all previous versions of the MBbrowser client.

#### New version kernel update description

MBbrowser Anti-Correlation Fingerprint Browser ********* Built-in latest version Anti-Correlation Fingerprint CHROME kernel: 129.0.6650.0 version, this Chrome kernel version was released by Google Inc. on August 11, 2024. This version focuses on improving security, compatibility, and performance optimization to further improve the browsing experience. <span style="font-weight:bold;color:#1a73e8;">According to Google specifications, this kernel only supports Windows 10/11+operating systems.</span>

Main changes of the anti-correlation kernel:

New features:

• The Request Header Integrity support function has been introduced, adding security protection for HTTP request headers and further ensuring the security of user data. Mbbrowser Chrome 129.0.6650.0 introduces multiple optimizations in memory usage, aiming to reduce resource consumption and improve overall anti-correlated browser performance. Firstly, it reduces the memory consumption of background tabs through optimization of memory allocation management, such as only restoring resources when tabs are reactivated. This method reduces the memory usage when running the browser for a long time, especially in multi-tab browsing, which can significantly save memory.

• In addition, the new version introduces the "Native Window Occlusion" technology, which is optimized for Windows systems. When the window is in the background or is blocked, the browser will automatically reduce resource allocation to ensure that only the foreground window is allocated with the main resources, further reducing memory pressure. This approach not only improves performance, but also maintains efficient memory management in the background window or tab page

Security fixes:

• This anti-correlation kernel update fixes multiple critical vulnerabilities and further strengthens the security protection of the browser. The update also includes optimizations for the Perfetto TraceProcessor on the WINDOWS platform, which makes business development, API calls, and debugging more stable.

• In the anti-correlation 129.0.6650.0 version, developers have made a lot of security fixes and improved the overall protection capabilities of the browser. For example, fixes for some vulnerabilities found in WebRTC, JavaScript engine, and v8 engine can prevent malicious code execution and improve the security of the browser when processing network requests and rendering pages. In addition, the code libraries related to PDF and graphics rendering have been updated to improve the stability of file processing.

Other improvements:

• Compatibility optimization has been made for PC browser virtual Android ARM32, ARM64, and IPHONE IOS devices, greatly improving the browser running effect on simulated mobile devices. We have also enhanced the stability of PGO (Profile Guided Optimization) builds on the Windows platform, optimized resource usage, and improved the response speed of applications.

• Memory management has been improved, reducing crashes.

This version is optimized for Windows 11 and improves stability on the device.

This update further improves the stability and performance of the new anti-correlation kernel across platforms, making the user experience of batch virtualization of physical devices on PCs more smooth.

#### MBbrowser 129 Anti-association kernel first support:

In the virtual mobile phone and tablet environment, the **small dot and mouse arrow cursor switch** in the fingerprint browser page.

The following two modes support real-time switching:

Small dot mode:

• Continue and maintain the default mode of Google's native anti-association kernel, and run in the small dot mode by default when first entering the MBbrowser client operating environment. Original anti-correlated CHROME kernel running scenario.

Mouse arrow cursor mode:

• Go to the browser settings in the settings panel and check the box to simulate the small circle switch on the phone page as a cursor arrow. When the anti-correlation kernel for MBbrowser is used to simulate various mobile phone models, tablets, and other devices, it no longer operates in a small dot mode, but instead operates in a mouse cursor arrow mode to ensure the stability, healthy and orderly development of customer business, while also ensuring the original anti-correlation CHROME kernel operation scenario.

The majority of e-commerce customers can completely solve the historical problem of being blocked from conducting business due to the small circular cursor after entering the international Internet mobile page in this new version.

#### List of new version features

• Resolved the error and garbled text issues encountered when performing fingerprint detection again during environment operation.

• MBbrowser UA Manager: We have resolved the issue of incorrect correspondence between the left-click copy UA value and the UA commercial library management window in a small number of systems.

• MBbrowser UA Manager: When manually setting the UserAgent, a new automatic detection and verification of the UserAgent value is added. When the value is empty, the default UserAgent value is automatically assigned based on the set system, using the most recent commercial value.

• MBbrowser Client UI Interface: The default color of the time information in the lower right corner of the main panel environment item does not change with the change of events. This has been fixed.

• MBbrowser Client UI Interface: Fixed an issue where the scrollbar slider in the proxy server management window occasionally failed to be selected.

• MBbrowser Client UI Interface: The environment list item has been enhanced with a button to close the network connection error message and associated logic, to prevent the red text prompt from persisting even after the network is restored.

• MBbrowser Client UI Interface: Enhance the richedit control code and incorporate a logic for displaying control string differences.

• MBbrowser Client UI Interface: Enhance the display of all lists for MBbrowser, and add support for smooth scrolling to all data lists.

• MBbrowser Client Environment Management Engine: Fixed a few cases where the MBbrowser program obtained an incorrect environment main window handle, and added a maximum and minimum minimize attribute judgment to the obtained environment window handle.

• MBbrowser Client Environment Management Engine: Fixed the issue where the window control was lost in a few cases after the main window of the first environment was closed.

• MBbrowser Client Environment Management Engine: Fixed the rare case where the previous tab page failed to open when running the environment during initial login from a different location.

• MBbrowser Automation API Server: Add Interface -> Set the Specified Script in the Specified Environment to the Inactive State

• MBbrowser Automation API Server: Add interface -> Remove inactive scripts from specified environment

• MBbrowser Client Plug-in Library Management Engine: Enhance the search functionality of the plug-in management window, and introduce real-time search capabilities for plug-in descriptions in the massive plug-in library by entering keywords.

• For the first time, we provide a proxy server detection report. In the panel for modifying the environment, after checking for proxy failures, detailed error details are displayed through the right-hand side post window, and a built-in Traceroute data routing report is provided to facilitate customer analysis of the detailed reasons and location of the proxy server failure.


Download Url: [129 kernel version](https://download.mbbrowser.com/release/20241027_MBbrowseretup_*********_Core_129.exe "129 kernel version"), [105 kernel version](https://download.mbbrowser.com/release/20241025_MBbrowseretup_*********_Core_105.exe "105 kernel version"), [87 kernel version](https://download.mbbrowser.com/release/20241025_MBbrowseretup_*********_Core_87.exe "87 kernel version")

------------


### [July 15, 2024] Version: *********

The MBBrowser fingerprint browser version ********* supports backward compatibility with all historical MBBrowser client versions.

#### List of new version feature updates

• It is the first time to add global multi line settings to support customers in the operating business system: by choosing different lines to avoid local networks from accessing the overseas Internet, they cannot communicate with migrant servers due to the national firewall problem.
> Support one click selection of write to HOSTS mode, which can ensure high-speed login, synchronization, environment configuration, and conduct self owned business even in extremely harsh local, global, and overseas network environments with DNS failure;

##### Characteristics of Multi Line Networks

The following two modes support real-time switching:

Cloud synchronization/network mode:
> One click view of the fastest network connection speed and the ability to switch between different lines for the first time to ensure that the environment is running without data backup issues. When changing environment data or accumulating new environment data (including but not limited to: environment configuration, environment fingerprint, environment browsing history, COOKIES, login account table, and all key data), the environment changes will be backed up to the cloud in real-time through a multi line platform.

Pure standalone/local strong security mode:
> Continuing and maintaining no data interaction with official cloud servers or the outside world, all customer data is only stored on the customer's own machine, running locally without any external communication behavior, maintaining high security, high privacy, and operating in pure local mode. The multi line network only ensures that 100% of the client's heartbeat is normal and error free (without account verification communication failure).

In this new version, e-commerce customers can completely solve the historical problems such as login failure and synchronization failure caused by the inability of the client to communicate with domestic servers after accessing the Internet.

For the first time, we provide the Puppeteer/Selenium public script library and script collection service for the MBBrowser Fingerprint Browser. In the MBBrowser Fingerprint Browser client, you can easily view the relevant code for each script function with just one click.

Puppeteer/Selenium public script library adds script categories: data collection, page testing and validation, SEO optimization, screenshot generation, form auto filling and submission, user behavior simulation, page monitoring and alerting, website content update detection, ad and tracking script interception, social media automation, crawler management and distributed crawling, data visualization and report generation, automated login and session management, page performance analysis, automated deployment and monitoring, and other script code examples.

Selenium script running framework, adding support for JAVA language for the first time. Writing JAVA scripts in the MBBrowser client allows for one click installation of JAVA language support packages, requiring tedious configuration in the install and use mode, completing all JAVA environment configurations in one step.

• UI improvement: For the first time, for all types of data lists, an inertia wheel high-speed data viewing mode is provided during the process of scrolling data with the mouse wheel. The interface inertia effect of ANDROID/IPHONE is more user-friendly.

• This version adds for the first time support for the MBBrowser Fingerprint Browser client. After placing the main panel at the top, clicking on the system's minimize or Fn+M minimize will no longer cause the client panel to disappear.

• This version has undergone significant improvements for clients in massive environments. For a single account (under a single client): batch modification, batch configuration, and one click assignment of thousands of environments have significantly improved speed and response performance, and no occasional lag has been observed through joint testing.

• After optimizing the launch of the MBBrowser client, targeted improvements were made to address occasional situations where the main panel was not visible due to the client's screen DPI switching, screen high resolution, and other settings.

• In the custom name process of the MBBrowser browser environment name, script name, etc., automatic detection of illegal characters has been added to avoid abnormal display of name information caused by illegal characters.

• API section: Fixed the situation where the API SERVER occasionally does not display the main window login interface and experiences random exceptions when running in headless mode.

• Fixed the issue of multiple repeated uploads during batch replication/cloning in a massive environment, which directly affected the efficiency of replication/cloning. The problem has been resolved.

Download Url：[105 kernel version](https://download.mbbrowser.com/release/20240715_MBbrowserSetup_*********_Core_105.exe "105 kernel version")，[87 kernel version](https://download.mbbrowser.com/release/20240715_MBbrowserSetup_*********_Core_87.exe "87 kernel version")，[86 kernel version](https://download.mbbrowser.com/release/20240715_MBbrowserSetup_*********_Core_86.exe "86 kernel version")


------------


### [May 24, 2024] Version: *********

MBBrowser version ********* supports backward compatibility with all historical MBBrowser client versions.

#### New version feature update list

• For the first time, this version provides comprehensive official commercial automation script library services for customers internally. In the automation script manager, customers can search for the most suitable automation scripts for their own business needs, transfer the scripts from the official library to their own private script library, and then adjust the script code slightly to meet their business needs. Finally, they can be assigned to their own massive environment with just one click to run according to the needs of business development.

• This version fully supports three sets of IP address, time zone, country, and environmental language for the first time, with a fully automatic detection engine. In the SETUP panel, customers can specify the IP detection engine that best meets their business needs. For the first time, it supports IP2Location IP-API, Maxmind three sets of detection engine three in one mode.

> After setting up the time zone detection engine, when creating an environment or changing the proxy IP, the customer will use the time zone detection engine specified by the customer for IP location, time zone, country, and environment language detection.

• Second update of the unique user agent commercial library for MBBrowser, removing a small number of expired commercial records.

• Add one click batch export of each session environment to an independent compressed file. The historical legacy problem of exporting massive environmental data to only one compressed package has been completely solved. Users can export multiple independent environmental packages at once, making it convenient to selectively import them back to MBBrowser in the later stage. Refer to: [Batch Local Environment Export User Guide](https://help.mbbrowser.com/tutorial/item.html "Batch Local Environment Export Usage Tutorial")

• Script, plugin, log, environment search, and other search boxes, as well as a new search keyword history dropdown list. This version supports multiple use of all historical search keywords without repeated input. Click and select in the dropdown list to perform a second search.

• Resolve the historical legacy issue of not being able to view the environment configuration during environment operation. Add a new feature in environment operation where detailed configuration parameters of each running environment can be viewed.

• New environment resolution settings can be manually customized and filled in with values. In the environment configuration panel, the resolution drop-down list box is optimized twice, and a new environment resolution drop-down list control that supports self input is added.

• Fix the issue of occasional abnormal exit of MBBrowser when opening a one click batch creation window.

• The Session Environment Manager adds one click batch modification and replacement of default start page addresses for massive environments.

• In the MBBrowser Account Manager, this version has added 6 new commercial sites, and the original commercial sites are still valid.

• The MBBrowser automation script APISERVER has been improved in the Create API in this version, The corresponding logic for disabling img and disabling video parameters has been tested and passed.

#### 【Next version feature release update trailer:】

Plan to provide the MBBrowser group control system for the first time in the next version

Progress report on group control research and development:

• Complete the basic synchronization of mouse and keyboard operations in the CDP program during window synchronization. The response speed of mouse movement has increased from more than 8 seconds in the original JS program to about 1-3 seconds. Currently, the synchronization exception after tab switching has not been resolved, and synchronization of other response events has been added.

• When synchronizing two or more sub windows, the problem of synchronization failure caused by sharing the same intermediate variable is resolved by using independent intermediate variables for each sub window.

• When switching tab windows, add new logic: traverse all tab windows, find the tab activation with the specified URL, and save the current tab object as the actual synchronization operation object. It has been completed. Currently, after testing the tab switching, the synchronization operation can achieve the effect. However, there are cases where the URLs of sub windows are empty or there are individual differences in values. Later, it is necessary to add activation and saving tab objects according to the tab order number.

• Fix the situation where the underlying logic of group control returns an error in the Windows ID;

• If the incoming URL and the actual URL of the sub synchronization window do not match when switching tabs, add logic to switch by tab number

• The synchronization operation of mousemove messages is controlled by a timeout of 1 second to avoid blocking the response process due to mousemove operations. After adding this logic, the synchronization response efficiency has been significantly improved.

• The issue with synchronizing when closing the tab has been resolved.

• Fixed the issue of failed switching tab monitoring at the bottom level of group control, which has been fixed;

• The same step sub window is saved in a separate array in CDP. When starting Chrome, the SyncType=0x39 flag is passed as a sub window, which is saved to this array. When the main window is closed, all sub windows are closed, and it is completed;

• The issue of not being able to synchronize and switch to the correct tab items after opening a new tab page on the testing target site has been resolved;

• Modify keyboard input to display each character synchronously in real-time, no longer waiting for all input to be completed before displaying, completed;

• The issue of memory access conflicts in the case of multiple sub windows has been resolved. Currently, when testing four windows at the same time, the response speed of the synchronization operation is relatively ideal. However, after multiple operations, there are some cases where the synchronization of individual windows stops. After switching the tab, it can be restored, and it has been completed.

• Test the target site. The issue of the mouse pressing and dragging the verification code during login not synchronizing has been resolved.

• When the mouse moves to the target site login button, a login window pops up. There is a synchronization issue with the click operation in the login window, and it is estimated that there is a need to switch frames. The issue has been resolved.

• After starting the MBBrowser kernel, if it is a synchronization window item, the historical URL will not be opened by default after startup, and it has been completed;

• Fixed the issue of thread dropout after executing individual commands, which resulted in synchronization not continuing. The issue has been resolved and completed;

Download Url：[105 kernel version](https://download.mbbrowser.com/release/20240523_MBbrowserSetup_*********_Core_105.exe "105 kernel version")，[87 kernel version](https://download.mbbrowser.com/release/20240523_MBbrowserSetup_*********_Core_87.exe "87 kernel version")，[86 kernel version](https://download.mbbrowser.com/release/20240523_MBbrowserSetup_*********_Core_86.exe "86 kernel version")


------------


### [March 25, 2024] Version: *********

#### Version function update

##### * Add a sharing mode to support the collaborative use of multiple accounts in the environment sharing/batch sharing under the MBBrowser team account system.

1. For the first time, the sharing mode has been added, supporting the collaborative use of multiple accounts under the MBBrowser team account system: environment sharing/batch sharing, and supporting one click batch sharing of massive environments to remote multiple sub accounts.
Support simultaneous collaboration and cooperation between sub accounts for business development. When the designated environment is shared by the account creation to multiple other accounts, the account creation can retrieve the shared environment at any time, or multiple accounts can maintain the shared environment together for a long time.

>[Characteristics of Environmental Sharing]
The shared environment is affected by one (or more) account simultaneously changing environmental data, accumulating new environmental data (including but not limited to: environmental configuration, environmental fingerprints, environmental history browsing records, COOKIES, login account tables, and all other key data), and its environmental changes will be reflected in real-time in all multiple MBBrowser accounts that share this environment.

2. The MBBrowser Fingerprint Browser web console has added a complete sharing function. When using the environment sharing function in the web console, the minimum version of your client should be version *********, which cannot be lower than this version.

3. The MBBrowser log manager has added the function of sharing and sharing log records. The source, time, and other information of the environment are recorded in real-time in the log manager.

4. MBBrowser Latest Version Kernel Update: Fixed the possibility of proxy failure if the proxy account password contains some special characters.

5. When using a brand new account for the first time, there is a certain probability that the environment browser resolution variable will not be initialized due to the lack of custom UA data. The initialization position of the full screen resolution variable has been modified to ensure that it can be initialized.

6. For the first time, this version has added the feature of supporting the main panel of the MBBrowser Fingerprint Browser client to be at the top. Clicking on the system's minimize or Fn+M minimize will no longer cause the client panel to disappear.

7. Improve the display logic of the dropdown list of proxy types in the environment editing window, batch environment creation window, and proxy editing window, and support the display of XML based proxy settings for the first time, providing subsequent business support for API access of proxy IP platform providers.

8. Part of APISERVER for MBBrowsers: Fix the issue where the hide=on parameter has a certain probability of failure when starting APISVR. Fix the situation in the group deletion logic in the API where deletion fails if the group name contains non English symbols.

9. MBBrowser Browser version 105 kernel update and optimization, adding right-click menu translation function.

10. Add support for shared environments received under the account. When importing and exporting environment packages to a MBBrowser, the local imported and exported environment packages will automatically remove their shared attributes and be stored locally as regular environment packages.

11. MBBrowser Log Manager: Added the function of automatically recording version switching details when manually switching between historical versions of the environment.

12. Optimize the display order and format of the environment creation time in the main interface list of the MBBrowser Fingerprint Browser client, making it convenient to quickly sort out commonly used and important environment collections in massive environments.

Download Url: [105 kernel version](https://download.mbbrowser.com/release/20240323_MBbrowserSetup_*********_Core_105.exe "105 kernel version"), [87 kernel version](https://download.mbbrowser.com/release/20240323_MBbrowserSetup_*********_Core_87.exe "87 kernel version"), [86 kernel version](https://download.mbbrowser.com/release/20240323_MBbrowserSetup_*********_Core_86.exe "86 kernel version")


------------


### [February 1, 2024] Version: 5.3.2.166

#### Version function update

##### * Officially providing complete 3 million USER-AGENT commercial library data services in the USER-AGENT Manager.

1、This version is the first to add 3 million commercial libraries of USER-AGENT for customer environments in the USER-AGENT Manager. It also includes support for customers to import USER-AGENT from the commercial library into their own libraries for use. It also includes support for directly inserting data from the commercial library into the environment for use, and provides detailed selection of multiple UA categories, listing the USER-AGENT that customers need in real time for commercial applications.

2、Add logical functions for the right-click menus "Copy to My Environment Account Library" and "View Login Passwords/Hide Login Passwords" in the login management window list.

3、Add the logic of the "Copy to My Environment Account Library" and "View/Hide Login Passwords" operations to the environment operation log.

4、Add data list sorting function for log window, environment login management window, plugin manager, and script manager window.

5、Optimize the display and operational efficiency of the migratory bird login window, and further improve the window UI interface and multi language standardized display of migratory bird products.

6、In the SETUP settings panel, in the browser options bar, add options for automatic backup of environment bookmarks and automatic backup of environment history to the local area. When enabled, bookmarks and history in the environment will be permanently saved without loss. In team collaboration, environment sharing will be shared along with bookmarks and history.

7、In the environmental management panel and environmental packet management panel, when running a non current grouped environment, there may be display errors in the running environment status.

8、The UI interface is well-developed, and the historical legacy issue of text in the dropdown boxes shifting to the left under high dpi has been resolved when clicking on the dropdown boxes in each panel.

Download Url：[105 kernel version](https://download.mbbrowser.com/release/20240130_MBbrowserSetup_5.3.2.166_Core_105.exe "105 kernel version")


------------



### [December 29, 2023] Version: 5.2.8.164

#### Version function update

##### * New fingerprint trajectory added, environment version switching

1. This version supports fingerprint trajectory detection and report feedback for the first time in the client's entire environment. The Migratory Bird Fingerprint Browser supports real-time monitoring of all fingerprint stability in your environment. Before you start running the Migratory Bird Browser kernel, you can easily determine whether the environment is running under secure and stable fingerprint data by using fingerprint trajectories. The fingerprint trajectory chart of migratory birds can quickly detect whether important fingerprint data is consistent with historical data with just one click. No manual comparison of fingerprint values is required. For fingerprints with changes, they can be displayed in the trajectory chart at the first time and provide support for your business development.

2. This version of the Migratory Bird browser supports environment version switching for the first time. If you want to switch the specified environment to a version where you have set environmental parameters on a certain day in history, you can use this feature to easily and freely switch between various historical configuration versions of the specified environment. It is worth noting that in local mode, all your environmental data is only stored on your computer, so this feature does not support local mode, only network mode;

3. This version of the Migratory Bird browser supports the Chromium 105 kernel for the first time. Currently, version 105 is widely used by domestic and foreign users. The Migratory Bird browser adheres to the principle of stability and efficiency, and uses the newer kernel version widely used by domestic and foreign users as the main version. In special websites or scenarios, you can also install other kernel versions, such as 86 and 87, through the official website to match different websites.

4. Fix the issue of file access failure caused by Chinese characters in the file path passed in by the script editor;

5. The migratory bird kernel version 105 can use the official migratory bird public plugin library, without the need to install it through the Google store.

6. API section: Adjust the logic of the addproxy interface for the Migratory Bird APISERFVER. When the "Is-CheckProxy" parameter is set to 1, if the proxy detection fails, no new proxy will be added and a failure message will be returned. Add this agent only after successful detection.

7. API section: Add new interface/proxy/add related detection agent function logic, please refer to the official API column and online documentation for details;

8. Client side: Resolve the rare occurrence of crashes during program startup when the migratory bird main program changes from local mode to network mode;

9. Client UI: Fixed the issue of inserting other windows for display between the shadow window and the main window in the interface, which has been fixed.;

10. Client UI: Fixed the situation where a blank line appeared at the front of the list when scrolling the environment list of the client UI with the mouse after starting the environment;

11. Client UI: The functional logic of automatically displaying tips to fully display content when the user adds an environment editing window such as UserAgent, proxy IP, proxy username and password, etc., and the editing boxes are too long;

12. API section: Add and improve the execution logic in the startup environment API for "args": ["-- disable extensions", "-- blink settings=imagesEnable=false"].

13. API section: The logic of the interval time for API startup environment interface secondary improvement to start each environment.

14. API section: Add functional logic for the new API/browser/status of ApiService. Please refer to the API section on the official website for details.

15. API section: The API was further improved to remove the item instruction, and handling situations where the deletion may fail due to abnormal operating environment was addressed.

16. Installation part: Starting logic for the migratory bird installation package, adding logic for regularly updating the progress bar, to solve the 10 second lag situation when the progress bar starts to install.

Download Url: [105 kernel version](https://download.mbbrowser.com/release/20231229_MBbrowserSetup_5.2.8.164_Core_105.exe "105 kernel version"), [87 kernel version](https://download.mbbrowser.com/release/20231229_MBbrowserSetup_5.2.8.164_Core_87.exe "87 kernel version"), [86 kernel version](https://download.mbbrowser.com/release/20231229_MBbrowserSetup_5.2.8.164_Core_86.exe "86 kernel version")

------------


### 【October 16, 2023】Version: *********

#### Version Function Update

##### * Added the security architecture for switching the local mode to the standalone version

1. This version ** for the first time ** supports the client to switch to the full ** local mode **. Mbbrowser local mode, support all your business data only and only stored in the local device, and provide all the functions and features of migratory bird products, commercial use process, no longer carry out any sensitive data synchronization/communication, mbbrowser automatically prohibits data synchronization with cloud storage, thus bringing faster response speed, higher data security. Provide a smoother user experience to ensure the high reliability and stability of customer services and customer systems. And support you in the stand-alone mode and network mode for free real-time switching;

2. Mbbrowser kernel update, compatible with and enhanced network sites on massive devices, access to massive pages, high fingerprint security;

3. Added support for APISERVER automated script server to run in mbbrowser local mode;

4. After optimizing the switching group, some environment items without the top display the top image;

5. Optimize the display and response efficiency of the main interface environment list of the mbbrowser;

6. Optimized the random appearance of a catch-out when manually deleting a single environment in a massive environment (2000+);

7. Added the switch of running Mbbrowser in local mode or network mode in the login window;

8. Added the environment ID search function of session environment management window, plug-in management window, script management window, and login information management window for environment ID query when using APISERVER automated scripts;

9. Greatly optimized product response performance. After testing, it only takes 6 seconds for the ordinary computer to load 5000 environments (17 seconds for the old version), and the switching time for 5000 environments in groups has been increased to less than 2 seconds;

10. Optimize the language list to MAP type, speed up the interface search and obtain the corresponding language text speed;

11. Add log management window: Edit environment operation record, log export function, operation record replication function related function logic;

12. improve the APISERVER automation server, API mode to install local custom plug-in small probability of failure.

Download Url： [Chinese Version](https://download.mbbrowser.com/release/20231012_MBbrowserSetup_*********.exe "Chinese Version")  ， [Multilingual English Version](https://download.mbbrowser.com/release/20231016_MBbrowserSetup_MutiLanguage_*********.exe "[Multilingual English Version")  ， [105 Customized Kernel Edition](https://download.mbbrowser.com/release/20231016_MBbrowserSetup_CoreCustomized_105_*********.exe "105 Customized Kernel Edition")

------------



### 【September 14, 2023】Version: **********

#### Version Function Update

1. This version for the first time supports customers to batch assign massive proxy IP addresses to massive environments with one click, without adding or modifying proxies one by one;

2. batch assignment proxy IP service supports random assignment mode and sequential assignment mode, new support for massive environment to specify multiple proxy IP;

3. The log manager based on the local environment is supported for the first time, through which the log manager can query the history login and exit records and the duration of use;

4. The new local environment log manager can query the history of each environment operation, and support to query its historical usage track for a specific environment;

5. Add log logic related to migratory bird creation, batch creation, cloning, batch cloning environment;

6. For the first time, the scroll bar style of the main panel list is improved, the mouse is moved to the main panel, and the scroll bar is translucent.

7. UA commercial fingerprint matching library has been increased from 3 million to 3.6 million, in this version has been provided for commercial use;

8. fixed the script manager, account manager, plug-in manager window, B and C area list after clicking the name sort, will go to the A area list.

9. Improve the API interface of random USER-AGENT twice, and the random USER-AGENT API supports matching more comprehensive mobile terminals without any delay;

10. Added the APISERVER POST to detect the syntax of incoming json. If the json format is invalid, the process will be stopped directly and a json syntax error message will be returned.

11. Added the parameter Is_Delete_All for deleting the environment interface in migratory bird APISERVER. An instruction is supported to physically empty all local environments.

12. improve the automatic script import twice, has optimized the efficiency and stability of selecting multiple script file packages for import at one time.

13. In the process of automatic script export, the script package fails to export with small probability and the encryption script fails to export successfully.

Download Url： [Chinese Version](https://download.mbbrowser.com/release/20230912_MBbrowserSetup_**********.exe "Chinese Version")  ， [Multilingual English Version](https://download.mbbrowser.com/release/20230914_MBbrowserSetup_MutiLanguage_**********.exe "[Multilingual English Version")  ， [105 Customized Kernel Edition](https://download.mbbrowser.com/release/20230914_MBbrowserSetup_CoreCustomized_105_**********.exe "105 Customized Kernel Edition")

------------



### 【August 01, 2023】Version: **********

#### Version Function Update

1. this version for the first time to support customers to develop, create, and import custom plugins to migratory bird function, without the GOOGLE Store review, you can one-click batch distribution of custom plugins to multiple environments;

2. For the first time, this version supports the function of checking COOKIE details and valid duration under the specified environment by right-clicking on the specified environment. You can view the COOKIES currently used in the environment and the valid duration of cookies;

3. optimize in a variety of DPI display mode, the display effect of the list header and the default DPI display effect is inconsistent;

4. Add and strengthen the one-click batch verification function of the environment version, which is convenient for batch synchronization/verification of the environment version with one click under the condition of network congestion, and ensure that the environment package is the latest version when used in remote locations;

5. environment creation API interface to add agent detection status report, to avoid the batch creation of environments, due to the long time of agent detection caused by the mistake of creating a delay;

6. add migratory bird main program import environment and export environment path memory function;

7. 17,465 commercial User-Agent libraries for migratory birds were added to support the User-Agent matching needs of customers in various businesses;

8. the second strengthening of ApiServer multi-language international support, the default English interface to display all the returned information;

9. Optimize the situation that the latest UA information displayed in the User-Agent manager and the added information do not correspond when the User adds user-defined User-Agent to the UA library by itself;

10. Add plug-in list xml storage and load the FILE_PATH field related logic.

Download Url： [Chinese Version](https://download.mbbrowser.com/release/20230731_MBbrowserSetup_**********.exe "Chinese Version")  ， [Multilingual English Version](https://download.mbbrowser.com/release/20230801_MBbrowserSetup_MutiLanguage_**********.exe "[Multilingual English Version")  ， [105 Customized Kernel Edition](https://download.mbbrowser.com/release/20230801_MBbrowserSetup_CoreCustomized_105_4.9.66.152.exe "105 Customized Kernel Edition")

------------



### 【July 06, 2023】Version: 4.9.40.142

#### Version Function Update

1. Added replication function of environment id in Api mode, added support for replication environment ID in the right-click menu of any environment on the main panel;

2. adjust the environmental management window interface layout details;

3. adjust the listid interface query function logic, adjust the output result;

4. improve the Apiserver startup window automatic center;

5. Optimize the width of the combo drop-down scroll bar for migratory birds to facilitate dragging the scroll bar;

6. Add the all-select function logic of the list box Ctrl+A in the account management window;

7. When strengthening Api create item, the TimeZone value may be set incorrectly, which has been optimized;

8. Optimized when mbbrowser.exe is already running, mbbrowse cannot be displayed normally after the user logs in to MBBrowse again after executing it through apiserver.bat [with hide=off].

9. Added the API to determine whether the script file exists logic, no added error message in the APISERVER window;

10. Add the function logic of the session_user_agent parameter of the listid interface.

11. the newly added idcontainer interface system, resolution, useragent information output function logic;

12. the newly added session creawte interface system, resolution, useragent parameters input function logic;

13. The cookie information is not entered normally after the cookie parameters of the session creawte are entered.

Download Url： [Download From Official Website](https://download.mbbrowser.com/release/20230705_MBbrowserSetup_4.9.40.142.exe "Download From Official Website")

------------



### 【June 30, 2023】Version: **********

#### Version Function Update

1. Optimize and enhance duilib combo control by dragging the scroll bar with the mouse to solve the problem that the drop-down window automatically closes when the mouse is released after the mouse is moved to the drop-down window;

2. Add the function logic of clicking checkbox to automatically select the current option;

3. Optimize the appearance of the scroll bar of the user name drop-down list in the login dialog box;

4. In the process of account reading and writing, add all the xml escape characters in the fields for conversion logic;

5. Fix the problem that the login field of the account is not compatible with illegal xml characters when stored.

6. Optimize the delay in opening the chrome thread to add abnormal interrupt judgment, so as to avoid that after this thread is interrupted abnormally, all configuration items cannot be started again, completed;

7. strengthen the api to modify the ua acquisition logic in the advanced configuration interface, to avoid the acquisition of wrong data that may be caused by multithreading;

8. api startup command and login command add --hide parameter, support APISERVER control for the first time support mbbrowse client interface and API command simultaneously control environment;

9. Fix the difference between multi-language versions of multiple jump addresses;

10. add local import window and group management window window window size memory;

11. add the session environment management window CTRL+A all selected functional logic;

12. increase the interface layout of each management window and the functional logic of window size memory;

13. Optimize and adjust the scrollbar width of the session environment editing window;

14. Open and enable large memory addresses in compilation configuration to support efficient operation of more than 5000 environments for the first time;

15. Fix uilib library shadow display logic after memory allocation failure program abnormal situation;

16. When modifying the configuration information, if the configured resolution is not in the ua library, the user-defined ua value cannot be displayed.

17. In the process of importing xml records, added the logic of automatically analyzing the corresponding system and resolution according to the input UA value;

18. fix the browser tile window high dpi mode control distortion problem;

19. Optimize the browser window display logic for the startup environment. When the configuration item is started for the first time, perform the operation in the center window.

20. optimize the multi-DPI support environment center window function due to DPI Settings caused by calculation errors, resulting in incorrect center position;

21. for the first time to support all the list management window ctrl+a select all logic.

Download Url： [Download From Official Website](https://download.mbbrowser.com/release/20230630_MBbrowserSetup_**********.exe "Download From Official Website")

------------



### 【June 15, 2023】Version: **********

#### Version Function Update

##### * This version supports simplified chinese, traditional chinese, english, russian, japanese four languages.

1. optimize the high dpi case, window size saving and access errors.

2. Add the function logic of storing the size of a sub-window after dragging it to change its size.

3. Improve and adjust the xml details of the main interface and some interfaces.

4. Fix the situation that latitude and longitude information is not saved correctly when ip information is obtained.

5. improve the proxy detection status judgment, from the button text judgment to the specified bool variable to judge.

6. improve chrome account data detection, due to the localstata file is occupied, leading to read failure and re-establish the localstata file problem.

7. upgrade cdp.exe to version *******, update chromedp library to the latest 0.9.1, update cdproto library to version ********.

8. Improve the problem that some strings in the login dialog box do not switch languages.

9. Optimize and fix possible exceptions in the zoom minimization window.

10. When optimizing SetMinInfo, calculate the high dpi adaptation value into GetMinInfo for calculation.

11. support multi-language tooltip mouse stop quickly display full text.

12. Optimize the rare exception when cdp is started in the multi-language version.

13. Optimized and fixed the agent drop-down box scrolling slightly stuck.

14. the first time to support the size of the drag window to maintain the memory effect, greatly enhance the efficiency of operation.

15. Optimize the plug-in management window layout details, fix the display problem under high dpi.

16. optimize the login window user name window after clearing the user name selection may not be displayed in the user name input box.

17. Optimize the function logic of the selected useragent item when the useragent management window is not selected.

18. After optimizing all selection, it supports the function of directly copying multiple session_id or plugin_id in a batch at one time to facilitate the value use when the API is called.

19. Improve the drop-down bar of the login window. After multi-user login is supported for the first time, multiple accounts can be switched through the drop-down bar without re-entering.

20. optimize the start-up speed of migratory birds, while displaying multiple languages, some fields do not affect the operation efficiency.

21. Fixed the inconsistency between the language drop-down box of the Migratory bird setting dialog box and the main interface menu.

22. add the login process of migratory birds, console jump, purchase jump and other functional logic multi-language enhanced support.

23. The first time added a new support API interface: [Assign scripts from my script library to the target environment].

Download Url： [Download From Official Website](https://download.mbbrowser.com/release/20230615_MBbrowserSetup_**********.exe "Download From Official Website")

------------



### 【May 11, 2023】Version: **********

#### Version Function Update

For the first time, full support for high resolution, high DPI display. This version overrides and is compatible with all historical version features.

1. Provide based on DPI 100%, 125%, 150%, 175%, 200% high resolution 2K, 4K, 5K, 8K all displays on the market, and support Apple high score display at the same time.

2. Newly added platform services for the storage and management of all historical versions of cookies in all environments, supporting the management of all historical cookies in its own environment through the console, and supporting the online analysis of business cookies.

3. Added COOKIE security service, provides global COOKIE switch, and allows users to enable and disable multi-version COOKIE storage.

4. The plugin_install API interface fails to install and download plug-ins in a small number of cases.

5. Provide member list service to support team collaboration users to automatically query various states of team members in the script.

6. Add the functional logic of "Copy environment id to Clipboard" from the right menu of plug-in management window and script management window list.

7. New plug-in installation API interface: plugin_install interface plug-in version parameter function logic.

8. Provide the configuration data API function to query the specified environment ID, and provide the following interface modes.

9. It is the first time to completely solve the historical problem of blurred font on the interface under high resolution display and high DPI.

10. Provide advanced fingerprint parameter API function for updating environment, and provide the following types of interface modes for updating environment.

11. Process logic of id_script_list, id_script_active, id_plugin_list, plugin_delete, list, plugin_install6 interfaces of Apiserver are provided for the first time.

12. Optimize the function logic of server templates on the client. For the first time, a user-defined management platform for service templates is provided.

13. Modify the display logic of the template drop-down box of the environment panel to enhance the daily use experience. The newly created environment will be displayed at the TOP by default (the newly added environment item in the last week will be displayed after the top item by default).

14. Fix agent manager batch test agent, closing agent manager before completion of test will crash in rare cases.

See：[API Document](https://www.mbbrowser.com/api "API Document")

Download Url： [Download From Official Website](https://download.mbbrowser.com/release/20230511_MBbrowserSetup_**********.exe "Download From Official Website")

**【Old Kernel】**Download Url：[Download From Official Website](https://download.mbbrowser.com/release/20230512_MBbrowserSetup_CoreCustomized_**********.exe "Download From Official Website")

------------



### 【March 23, 2023】Version: *********

#### Version Function Update

1. **For the first time, script automation API/API server/console is fully supported.**

	1. The APISERVER real-time server based on the HTTP protocol supports automatic services in the user-controlled environment running in front and background mode.

	2. Added API credential authentication, which is physically separated from user login names and passwords. In team collaboration scenarios, APP_ID/APP_KEY authentication is used to conduct routine services.

	3. Added the API server log service, enabling users to analyze and debug business logic codes through real-time uploading and logging of the API console during script debugging.

	4. The API server data transfer control function is provided to avoid the accumulation of large amounts of historical garbage data when users run automatic services.

	5. Provides a member list service to support team collaboration users to automatically query the various states of team members in the script.

	6. This section describes how to enable, disable, and forcibly terminate an environment. After the environment is started, you can obtain the browser interfaces to execute selenium and puppeteer automation scripts.

	7. Provides the complete API function to get the environment list.

	8. Provides the configuration data API function for querying the specified environment ID, and provides the following interface modes.

	9. Provides complete environment creation API functions and provides the following types of interface modes to create an environment.

	10. Update environment provides advanced fingerprint parameter API functions, and provides the following types of interface mode update environment.

	11. Provides the update environment API function, and provides the following types of interface patterns.

	12. Provides the update environment agent API function, and provides the following types of interface patterns.

	13. Provides the ability to delete environment apis.

	14. Provides API functions such as importing cookies and exporting cookies, and provides the following interface modes.

	15. Provides API functions such as obtaining random UA11s and clearing environment local cache, and provides the following interface modes.

	16. API server success ERROR/STATUS API code table, enabling users to quickly query exceptions during debugging and operation.

	See：[API Document](https://www.mbbrowser.com/api "API Document")

2. **Provide automatic script encryption and decryption function (script source protection function)**

	1. Users can encrypt automated scripts by themselves. After encryption, scripts can be loaded and run efficiently in the environment of migratory birds.

	2. Automated script encryption and decryption while supporting Migratory Bird has launched the dual WebDriver automation engine.

	3. Encrypted scripts can be exported to the local PC. You can safely send encrypted scripts to colleagues for import without worrying about script content leakage.

	4. Encrypted scripts, support team collaboration, support to share to your team account in the encrypted state of use.

	5. The encryption script is encrypted by bank 128-bit algorithm in the Migratory Bird client. The password of the encryption script is set by you and properly kept. The encrypted script cannot be viewed, modified or decrypted by anyone other than the password setting/owner who can only run the script.

	6. If you tell the other party the password, the other party can decrypt the script and obtain the plaintext content of the script.

	7. The password you set is the only key for script encryption and decryption: please be sure to keep it properly by yourself. If you lose/forget your password, the official customer service of Migratory bird cannot help you decrypt the script, and you will be completely unable to decrypt the script.

3. **The Environment Management panel supports API functions for the first time.**

	1. Add the unique ID list function of the session environment in the environment manager to ensure that users can batch fetch the specified environment ID set through the client during script code maintenance.

	2. The group manager provides the unique ID list function of session environments by group, so that users can batch fetch specified environment ids through clients during script code maintenance.

4. **The WEB console provides background management functions of API services for the first time.**

	1. You can use the console to force the APISERVER offline.

	2. Users can apply for API credentials online, which take effect in real time and provide APISERVER service.

	3. Supports the query of credentials usage logs and online duration of credentials. Provides current status queries for APISERVER credentials, enabling team-based users to efficiently utilize credential resources.

	4. The API console uses logs to query the URL and IP addresses of API requests, and query the request time. Reduce customer service workload.

5. **Optimize the sending and receiving efficiency of the APISERVER console environment, increase the conventional flow limiting logic in the API industry, and avoid the unreasonable massive attacks from API services on commercial machines.**

6. **Increase the charging strategy of API service, amend the free policy of API service, adopt the policy idea of comprehensive free first, and gradually charge the official according to differentiated services later.**

7. **This version of the product iterates to complete a large number of efficiency optimization of various business details, window response efficiency, underlying data processing efficiency, Chinese and English description and standardized display.**

 Download Url： [Download From Official Website](https://download.mbbrowser.com/release/20230323_MBbrowserSetup_*********.exe "Download From Official Website")

------------



### 【November 22, 2022】Version: *********

#### Version Function Update

##### First environmental Advanced Classification Panel (Secondary panel)

<p><img src="6d7b2882624511f09a0d0242ac130006/images/image_f198b56d4283.png" width="360" /></p>

1. Conduct environment operations by environment category. According to different types of environment, you can quickly locate and use target environment groups that meet business needs from your massive environment library in business activities.

2. The advanced classification panel of the environment supports the memory function. Each time you enter this panel, you will memorize by default and display the rules used last time.

3. The advanced environment classification panel supports all functions of the main panel right-click menu (running environment, editing environment, etc.).

4. Supports classification display by time dimension and environment category dimension to quickly locate the required environment.

5. Provide the button in the blue box in Figure 1 to switch between the main panel and the secondary panel.

##### For the first time, automatic script encryption and decryption function (script source protection function) is fully provided.

![](6d7b2882624511f09a0d0242ac130006/images/image_f0aa278c47df.png)

1. Users can encrypt automated scripts by themselves. After encryption, scripts can be loaded and run efficiently in the environment of migratory birds.

2. Automated script encryption and decryption while supporting Migratory Bird has launched the dual WebDriver automation engine.

3. Encrypted scripts can be exported to the local PC. You can safely send encrypted scripts to colleagues for import without worrying about script content leakage.

4. Encrypted scripts, support team collaboration, support to share to your team account in the encrypted state of use.

5. The encryption script is encrypted by bank 128-bit algorithm in the Migratory Bird client. The password of the encryption script is set by you and properly kept. The encrypted script cannot be viewed, modified or decrypted by anyone other than the password setting/owner who can only run the script.

6. If you tell the other party the password, the other party can decrypt the script and obtain the plaintext content of the script.

7. The password you set is the only key for script encryption and decryption: please be sure to keep it properly by yourself. If you lose/forget your password, the official customer service of Migratory bird cannot help you decrypt the script, and you will be completely unable to decrypt the script.

##### For the first time, multiple operations can be performed on the main panel and secondary panel in batches.

<p><img src="6d7b2882624511f09a0d0242ac130006/images/image_7d5ea2f13156.png" width="360" /></p>

1. Hold down **Ctrl Key + Left Mouse Button** and click each environment item. You can quickly operate the environment in batches. The supported operations are: Batch top, batch run, batch stop, batch transfer to group, batch export, batch delete.

2. Hold down **Shift + Left Mouse Button**, you can select massive environments on the main panel at one time. You can quickly perform operations on environments in batches, including batch top, batch Run, batch stop, batch transfer to group, batch export, and batch delete.

![](6d7b2882624511f09a0d0242ac130006/images/image_7fabe5b5f625.png)

Secondary optimization of network proxy server management, this version **provides for the first time the ability to batch change agents** for multiple environments that share the same agent.

If multiple environments use the same agent, you do not need to manually change agents one by one. Instead, you can use the network proxy server manager to change agents in batches for multiple specified environments.


•	Greatly optimize the efficiency of team collaboration sharing and console environment delivery. Environment sharing and delivery will reach your or your sub-account's clients in real time without any waiting time.

•	Fixed the failure to activate the environment script when the environment is not selected in the script management window.

•	Improved the installation process, optimized the installation process and installation interface, to provide a new installation interface.

•	The function of forcing clients offline through the console has been improved twice.

•	This version of the product iterates to complete a large number of efficiency optimization of various business details, window response efficiency, underlying data processing efficiency, Chinese and English description and standardized display.

•	The iteration of this version has carried out a lot of optimization for the client PROXY module and the efficient response of user business behavior.

 Download Url： [Download From Official Website](https://download.mbbrowser.com/release/20221127_MBbrowserSetup_*********.exe "Download From Official Website")

------------

### 【October 20, 2022】Version: *********
Supports backward compatibility with all historical Mbbrowser client versions.


#### Version Function Update

1. This version is the first to support mass environment and bulk agent high speed detection.

2. The new support of 300 agents a minute, higher than domestic and foreign product bulk agent detection speed up to 6 times.

3. New support instant suspension detection, recovery detection function, beyond the peer products can not suspend detection and other functions.

4. New support to detect agents and automatically optimize the automatic IP matching, UA, country, time zone and other key values automatic matching when merchants purchase mass account group import.

5. In addition, it supports only batch proxy detection, which displays the detection in milliseconds and distinguishes the connection rates of proxy servers. New three search engine agent synchronous connectivity efficiency to ensure the effectiveness of proxy detection.

 Download Url： [Download From Official Website](https://download.mbbrowser.com/release/20221018_MBbrowserSetup_*********.exe "Download From Official Website")

------------



### 【September 30, 2022】Version: *********
Supports backward compatibility with all historical Mbbrowser client versions.


#### Version Function Update

1. For the first time, the batch allocation mode for all environments is supported. The account, UA, PROXY, plug-in, and AUTOSCRIPT can be automatically allocated to multiple environments with one click from 2000 to 10000 environments.

2. For the first time, commercial account data sets purchased by commercial customers can be imported in batches in EXCEL format. For the first time, global local accounts can be modified in batches in EXCEL and accounts can be imported in batches in the early stage or environment can be modified in batches in the late stage.
See：[Bulk Local Account Import Using Tutorial](https://help.mbbrowser.com/tutorial/import_data.html "Bulk Local Account Import Using Tutorial")

3. The system automatically responds in the browser kernel after batch or single environment accounts are assigned to the environment for the first time, and automatically logs in to the target website with the account password on the login page.

4. Improved the sharing, management and deletion mechanism of mass accounts, UA, PROXY, plug-in and AUTOSCRIPT under various grouping environments in team cooperation.
See：[Tutorial On Using Browser Plug-in Manager](https://help.mbbrowser.com/tutorial/plugin.html "Tutorial On Using Browser Plug-in Manager")

5. This version of the product has carried out a large number of efficiency optimization of various business details, such as window response efficiency, underlying data processing efficiency, description and standardized display in both Chinese and English.

6. This version has a lot of optimization for the client PROXY module and efficient response to user business behaviors.

7. This version improves the underlying WEBDRIVER that has been open to the outside world for a second time, and also supports Selenium underlying automation driver and Puppeteer underlying automation driver.

8. For the first time, this release fully supports third-party proxy IP for Oxylabsauto(Oxylabs Residential Agent), Lumauto(Dynamic residential), Luminati_http, Luminati_https, and SmartProxy.

 Download Url： [Download From Official Website](https://download.mbbrowser.com/release/20220930_MBbrowserSetup_*********.exe "Download From Official Website")

------------



### New version general installation/upgrade official description

1. Uninstall the old version, MBDATA directory does not need to be touched, do not delete.
2. Install the new version.
3. Log in to the latest version, view the MBDATA directory path on the SETUP Setup panel, and ensure that the directory is correctly directed to the original MBDATA directory.
4. The upgrade is complete.

Note 1: In order to ensure the quality of products and services and the stability of customers' business, the official of Migratory Bird will not forcibly upgrade the version currently used by customers at any time. If you upgrade a new version, your configuration environment and historical service data will not be affected. In daily use, you need to periodically import and export your own service data through the client environment and back it up to a local PC for safekeeping.

Note 2: The MBDATA directory is the key data of your business. Store it on NVME/ SSDS to greatly improve the response speed of your business operation.

#### Mbbrowser Official Technical Department