﻿<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Window size="953,590" caption="0,0,0,50" roundcorner="5,5,5,5" showshadow="true" shadowsize="3" shadowposition="0,0" shadowcolor="#ff6272A4" fademode="true">
	<Include source="Default.xml" />

  <VerticalLayout width="953" height="590" bkcolor="#FF282A36">
    <HorizontalLayout height="37">

    	 <Button name="login" bkimage="icon.png" padding="8,6,0,0" width="28" height="28"/>
      <Label name="session_list_title" padding="6,4,0,0" text="共享会话管理器" width="180" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" align="left" font="8"></Label>

      <Control />
      <Button name="minbtn" width="28" height="26" tooltip="最小化" normalimage="minbtn.png" hotimage="minbtn_hover.png" pushedimage="minbtnpush.png" />
      <Button name="closebtn" width="28" height="26"  tooltip="关闭" normalimage="closebtn.png" hotimage="closebtn_hover.png" pushedimage="closebtnpush.png" />

    </HorizontalLayout>
  <HorizontalLayout name="bkground">


		<VerticalLayout width="953" height="603">


			<HorizontalLayout height="26" >

			</HorizontalLayout>


      <HorizontalLayout inset="0,0,0,0" height="403">
        	<List name="list_session_manager" minheight="403" bordersize="1,1,1,1" itemselectedbkcolor="#FFe0e0e0" itemhotbkcolor="#FFf2f2f2" bordercolor="#FFD9DADB">
						<ListHeader height="36" bordersize="1" bordercolor="#FFD7D7D7" bkcolor="#FFF9F9FA">

              <ListHeaderItem text="登录入口" name="header_device_choice" width="200" align="left" textpadding="40,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="注释" name="header_device_name" width="200" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="统计" name="header_backup_datetime" width="200" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>
              <ListHeaderItem text="已发送" name="header_backup_datetime" width="200" align="left" textpadding="15,0,0,0" sepimage="split.png" sepwidth="2"></ListHeaderItem>

						</ListHeader>


          <ListContainerElement height="40" bordersize="1,0,1,1" bordercolor="#FF6272A4" bkcolor="#FF282A36" selectedbkcolor="#FF44475A">

              <Label text="loginweburl" name="item_device_name" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" width="120" align="left" textpadding="35,0,0,0" />


            <Label text="Comment content" name="item_backup_datetime" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" align="left" textpadding="15,0,0,0" />
            <HorizontalLayout name="operate">
              <Label text="100860" name="openurl" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" width="100" align="left" textpadding="15,0,0,0" />
            </HorizontalLayout>
            <Label text="Sended Count & time" name="copyurl" textcolor="#FFF8F8F2" hottextcolor="#FF8BE9FD" width="100" align="left" textpadding="15,0,0,0" />

           </ListContainerElement>



					</List>
				</HorizontalLayout>






		</VerticalLayout>


	</HorizontalLayout>
    <HorizontalLayout height="52" bkcolor="#ffe9e9e9">


              <VerticalLayout width="30">
				        <CheckBox name="session_share_check" selected="false"  visible="true" padding="10,16,0,0" height="18" width="16" normalimage="file='list_check_normal.png'" hotimage="list_check_hover.png" selectedimage="file='list_checked_actvice_normal.png'" ></CheckBox>
				     </VerticalLayout>
				     <VerticalLayout width="110">
				        <Label name="session_list_title" padding="4,15,0,0" text="替换已存在的会话" width="110" textcolor="#FF616161" hottextcolor="#ff000000" align="left" font="16"></Label>
				     </VerticalLayout>

      <VerticalLayout width="640">

       </VerticalLayout>
      <VerticalLayout width="140">
      		<Control />
          <Button text="导入" name="archiveselected"  float="true" pos="20,10,0,0" width="120" height="30" textcolor="#FF3E3E3E" disabledtextcolor="#FFB3B3B3" align="center" font="14"	normalimage="file=&apos;Dialog_Btn1.png&apos; corner=&apos;5,10,5,10&apos;" hotimage="file=&apos;Dialog_Btn1_Hover.png&apos; corner=&apos;5,10,5,10&apos;" pushedimage="file=&apos;Dialog_Btn1_Click.png&apos; corner=&apos;5,10,5,10&apos;" />
          <Control />
       </VerticalLayout>


<Control />
    </HorizontalLayout>
  </VerticalLayout>
</Window>
