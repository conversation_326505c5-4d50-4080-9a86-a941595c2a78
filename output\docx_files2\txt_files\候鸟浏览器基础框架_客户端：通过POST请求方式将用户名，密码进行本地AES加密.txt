客户端：通过POST请求方式将用户名，密码进行本地AES加密后发送到服务器，服务器通过私钥进行解码，判定，并入库。同时返回JSON格式数据串。

第一章 登录与身份验证：

第一章 登录与身份验证：

测试用户名：

密码：123456

身份验证：

登录流程详述：

客户端：通过POST请求方式将用户名，密码进行本地AES加密后发送到服务器，服务器通过私钥进行解码，判定，并入库。同时返回JSON格式数据串。

Msg：utf8格式消息信息，并实时显示在客户端登录面板的下方。内容包含各种登录状态和验证信息。前期已约定支持ZH/EN两种语言。

Code：状态代码。0表示登录成功。客户端以Code值为判定标准确认用户是否可以合法使用产品。

Data:  add_time 用户创建时间。

Invitecode 邀请码

（1）Token: PC端提交所有加密数据均带上token。Token对外不可见，仍旧为AES加密传输。

（2） PC端POST数据格式：加密后的（“key=val&key1=val1&key2=val2”）

（3）登录密码采用HEX16进制加码方式发送到服务器端。

【附1】登录过程中,PC端请求有三次重试机制：重试第二次延时 500ms 重试第三次延时 1000ms。

【附2】PC端登录请求日志URL。（正式上线后此日志将隐藏）

文件名加了日期，防止文件过大。

【附3】数据加密传输约定：OPENSSL AES128 CBC模式加密保存。

客户端本地数据格式存储前述：

客户端所有元数据以SESSION_UNIQUE_ID 唯一不重复标识进行关联，数据默认存储格式为UTF8的XML标准格式，XML节点中字符串内容进行转义，所有XML数据进行OPENSSL AES128 CBC模式加密保存。

SESSION_UNIQUE_ID 加码方式：

md5 (XMLID + "|" + 6位随机数+"|"+系统时间(毫秒)+“|”+ session name + "|" +MAC+"|"+diskID+"|"+CpuID)

SESSION_UNIQUE_ID 唯一不重复标识 作用：

保证面板中ITEM的唯一性。所有流程根据SESSION_UNIQUE_ID识别ITEM并关联ITEM下会话所有业务逻辑，ITEM间通过SESSION_UNIQUE_ID的不同进行流程区分。

通过SESSION_UNIQUE_ID，进行数据上传与同步到本地加载到内存的重要依据。

唯一不重复标识 仅仅在ITEM被用户创建时生成，并写入到XML中。在之后任何用户进行记录的增删改，此唯一不重复标识不变。

第二章：【数据结构1.0】客户端本地数据格式存储位置详述

第二章：客户端本地数据格式存储位置详述【数据结构1.0】

用户数据 存储根目录 位置：

C:\Users\<USER>\AppData\Roaming\MBbrowser

文件夹结构说明：

C:\Users\<USER>\AppData\Roaming\MBbrowser\

Config： 客户端全局配置数据存放目录(不适用于注册表存储的数据放入此文件夹中)。

LocalStorage：用户数据存储文件夹

Logs：客户端日志文件夹(存放本地/网络/运行调试信息，dump数据等)

System：备用文件夹

C:\Users\<USER>\AppData\Roaming\MBbrowser\LocalStorage

LocalStorage：用户数据存储文件夹

子文件夹：（如图）

[AES-128加码]用户登录名1：

说明：文件夹根据用户登录成功的帐户名，进行AES-128加码后的串进行设定。

约定：所有用户会话、cookies、代理所有需要打包到服务器的数据存放于此文件夹。

C:\Users\<USER>\AppData\Roaming\MBbrowser\LocalStorage\[AES-128加码]用户登录名1\

[AES-256加码]SessionName1: 用户会话数据存储文件夹

子文件夹：（如图）

[AES-256加码]SessionName1：文件夹根据用户登录成功的帐户名，进行AES-256加码后的串进行设定。当前用户会话、cookies、代理所有浏览器保存的数据、需要打包到服务器的数据均存放于此文件夹。

MainData文件夹：保存当前已登录用户下的所有基于此用户的全局数据。

第三章 同步

第三章 同步

数据打包同步服务器流程详述

第一节：

数据存储综述及约定:

数据打包的要求，有以下几点注意事项。

1、支持高并发，多用户上传下载。

2、数据100%校验正确无误。

3、高效率，高稳定性传输，低CPU，磁盘占用

(2) 核心环节实现论述：

1、HASH值的计算建议服务器端使用bash脚本来异步进行。

2、采用多种机制支持upload上传，post,ftps,其它成熟tcp协议，在某一种协议出现意外情况下(上传失败，校验错误 )，可以自动切换上传模式。

3、php仅进行数据库动作，不参与文件计算、合并。

4、服务器端支持断点续传。

5、约定默认用post的方式为主，另外补充另一种方式为辅，用现在做的断点续传的方式在后期进行压力测试来确认。

6、如果hash不一致,post方式进行重试（一共重试三次），如果仍旧不正确，跳入另一种方式进行同步。

7、php.ini中最大允许上传的包设定为50MB.

(3)数据结构：

ID             索引ID

UID            用户表ID

File           上传文件

exts           文件后缀

Size           文件大小

Target_Path    目标路径

SaveName       目标文件名

Version        文件版本

Is_valid       是否有效

Create_Date    创建时间(上传时间)

(4) 服务器端数据存储位置：

收到的所有的PC端传递过来的数据保存在一个完全独立的文件夹中。

文件夹格式约定：

例：/mnt/data/username(用户登录成功的名称，加码的)/用户zip

(5) 关于服务器端如何判定最新的包进行上传下发的流程说明：

前述：每次候鸟产品客户端启动并登录成功后，必须全自动同步（下载）服务器最新版本数据FULL包到本地，并在本地实时加载。同时将服务器最新数据版本号存储在内存中。在任何时侯，本地客户端内存中已保存版本号、TOKEN严禁丢失。如（极端情况下）客户机器内存中缺失版本号/TOKEN信息必须自动让用户重新登录软件产品。

上传流程详述：

上传及存储文件名约定：

版本号_HASH_业务类型.ZIP

版本号：默认起始:1(int型)  以1为单位进行递增。

HASH：文件完整包的MD5码。

业务类型：完整包叫 full 其它业务类型后续约定。

完整文件名例子： 1_EISDJHF234DKSKE_FULL.ZIP

2_DAFADKADLKFAD_FULL.ZIP

ZIP格式：加密的ZIP数据，数据内容已AES_128加密。


================================================== 表格内容 ==================================================

参数名称 | 数据类型 | 说明 | 摘要
VERSION | Number | Xml 版本号 | (此XML数据格式版本号)
ID | Number | 会话序号 | (会话列表显示/管理顺序)
LOGIN_ACCOUNT | varchar | 用户登录名 | 用户登录帐户(购买帐户)
SESSION_UNIQUE_ID | varchar | 会话ID | 会话唯一不重复标识ID
NAME | varchar | 会话名称
COLOR | Varchar | 面板颜色 | ITMENAME,TAB默认显示颜色
ANONYMITY | Bit | 匿名检查程序 | 设置后是否加载匿名检查程序
COMMENT_STRING | Varchar | CITY:Dallas Zip:75207 | 会话个人自定义描述
SPHEREGL | Bit | 1
ENABLE_LOCALHTML5_STORAGE | Bit | 1
SAVE_LOCALHTML5_STORAGE | Bit | 1
SAVE_INDEXDB_STORAGE | Bit | 1
代理服务器
节点：NETWORK | 代理服务器
节点：NETWORK | 代理服务器
节点：NETWORK | 代理服务器
节点：NETWORK
TYPE | varchar | 代理类型
IP | varchar | 代理IP
PORT | varchar | 代理端口
USER | Varchar | wangpeng | 登录用户名
PASSWORD | Varchar | ********** | 用户密码(默认使用*号显示)
PUBLIC_IP | Varchar | *************** | 真实IP
FAKEIP | Varchar | *************** | 虚拟IP
代理服务器控制
节点：NETWORK_CTR | 代理服务器控制
节点：NETWORK_CTR | 代理服务器控制
节点：NETWORK_CTR | 代理服务器控制
节点：NETWORK_CTR
NA | Bit | 是否FAKEIP | DEFAULT=0
FAKE_WRTC | Bit | 是否虚拟wRTC | DEFAULT=1
SAME_IP | Bit | 模拟连接IP和代理IP相同 | DEFAULT=1
IPV6 | Bit | 是否使用IPV6 | DEFAULT=0默认关闭
WRTCOFF | Bit | 关闭WRTC | DEFAULT=0默认关闭
DNS | varchar | 自定义DNS | 默认为空
设定支持浏览器的USERAGENT
节点名：USERAGENT_BROWSER | 设定支持浏览器的USERAGENT
节点名：USERAGENT_BROWSER | 设定支持浏览器的USERAGENT
节点名：USERAGENT_BROWSER | 设定支持浏览器的USERAGENT
节点名：USERAGENT_BROWSER
CHROME | Bit | 1
SAFARI | Bit | 1
MSIE | Bit | 1
OTHER | Bit | 1
REGEN_CONFIG_USERAGENT | Bit | 1
USERAGENT内容数据
节点名：USERAGENT_STRING | USERAGENT内容数据
节点名：USERAGENT_STRING | USERAGENT内容数据
节点名：USERAGENT_STRING | USERAGENT内容数据
节点名：USERAGENT_STRING
UA | Varchar | Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15 reversion 1.77.0
UA_LNG | Varchar | US | 国别代号
UA_LNG_STRING | Varchar | en-US,en;q=0.5 | HEAD包头语言定义
USERAGENT控制区
节点名：USERAGENT_CTR | USERAGENT控制区
节点名：USERAGENT_CTR | USERAGENT控制区
节点名：USERAGENT_CTR | USERAGENT控制区
节点名：USERAGENT_CTR
DISPOPUPS | Bit | 禁用弹窗 | DEFAULT=0 默认关闭
ENABLE_SERVICE | Bit | 启用服务模式 | DEFAULT=0 默认关闭
BLOCKLNGINPUT | Bit | 禁用指定语言输入 | DEFAULT=0 默认关闭
屏幕主要参数
节点名：RESOLUTION | 屏幕主要参数
节点名：RESOLUTION | 屏幕主要参数
节点名：RESOLUTION | 屏幕主要参数
节点名：RESOLUTION
WIGHT | Varchar | 屏幕宽
HEIGHT | Varchar | 屏幕高
屏幕控制
节点名：RESOLUTION_CTR | 屏幕控制
节点名：RESOLUTION_CTR | 屏幕控制
节点名：RESOLUTION_CTR | 屏幕控制
节点名：RESOLUTION_CTR
EMU_SCREEN | Bit | 模拟_虚拟屏幕宽高 | DEFAULT=0 默认关闭
EMU_TOUCH | Varchar | 虚拟触屏 | DEFAULT=0 默认关闭
使用者浏览器摸拟GPS位置
节点名：POSITION | 使用者浏览器摸拟GPS位置
节点名：POSITION | 使用者浏览器摸拟GPS位置
节点名：POSITION | 使用者浏览器摸拟GPS位置
节点名：POSITION
LONGITUDE | Varchar | GPS经度 | 默认值为当前经度值
LATITUDE | Varchar | GPS纬度 | 默认值为当前纬度值
COUNTRY | Varchar | USA states | 此数据从全局COUNTRY.XML中获取
浏览器时区
节点名：TIMEZONE | 浏览器时区
节点名：TIMEZONE | 浏览器时区
节点名：TIMEZONE | 浏览器时区
节点名：TIMEZONE
TIMEZONE_NAME | Varchar | Irish Standard Time | 地域时区指定默认为空[TIMEZONE名称,用户自填写]
ADD_VALUE | Varchar | 0600 | 此数据从全局TIMEZONE.XML中获取
浏览器指纹设定
节点名：FINGERPRINT_CODE | 浏览器指纹设定
节点名：FINGERPRINT_CODE | 浏览器指纹设定
节点名：FINGERPRINT_CODE | 浏览器指纹设定
节点名：FINGERPRINT_CODE
AUDIO | Bit | 1
CANVAS | Bit | 1
FONTS | Bit | 1
RETCS | Bit | 1
其它扩展设定
节点名：OTHER_SETTING | 其它扩展设定
节点名：OTHER_SETTING | 其它扩展设定
节点名：OTHER_SETTING | 其它扩展设定
节点名：OTHER_SETTING
PLUGINS_MIMETYPE | Bit | 0 | DEFAULT=0 默认关闭
SAVE_ENCRYPT_COOKIES | Bit | 1 | DEFAULT=1 默认开启
ENABLE_FLASH | Bit | 0 | DEFAULT=0 默认关闭
DYNAMIC_FINGERPRINTS | Bit | 0 | (此选项置为有效后，主程序需对[DYNAMIC_FINGERPRINTS_CTR节点值进行获取])[默认为0]"
BLOCK_CANVAS_OUTPUT | Bit | 0 | DEFAULT=0 默认关闭
(如设定有效，则FINGERPRINT_CODE节点CANVAS值必须为0)[默认为0]
动态指纹控制
节点名：DYNAMIC_FINGERPRINTS_CTR | 动态指纹控制
节点名：DYNAMIC_FINGERPRINTS_CTR | 动态指纹控制
节点名：DYNAMIC_FINGERPRINTS_CTR | 动态指纹控制
节点名：DYNAMIC_FINGERPRINTS_CTR
D_AUDIO | Bit | 1 | DEFAULT=1 默认开启
D_CANVAS | Bit | 1 | DEFAULT=1 默认开启
D_FONTS | Bit | 1 | DEFAULT=1 默认开启
D_RETCS | Bit | 1 | DEFAULT=1 默认开启
D_MEDIA | Bit | 1 | DEFAULT=1 默认开启
D_WEBGL | Bit | 1 | DEFAULT=1 默认开启
D_MIME | Bit | 1 | DEFAULT=1 默认开启
D_PLUGINS | Bit | 1 | DEFAULT=1 默认开启
IS_VALID | Bit | 1 | DEFAULT=1 默认有效
UPDATETIME | DATETIME | 2021-01-12 12:22:34 | 会话更新时间
CREATETIME | DATETIME | 2021-01-12 12:22:34 | 会话创建时间

<?xml version="1.0" encoding="gb2312" ?>
<VERSION version="11(此XML数据格式版本号)">
<VER ID="2(会话列表显示/管理顺序)" LOGIN_ACCOUNT="用户登录帐户(购买帐户)" SESSION_UNIQUE_ID="会话唯一不重复标识ID">
	<SESSION NAME="<EMAIL>(会话名称)" COLOR="#FF000000(面板ITMENAME,TAB默认显示颜色)[DEFAULT=NONE]" SYSTEM="" />
	<IS_ANONYMITY = "1(设置后是否加载匿名检查程序)[DEFAULT=1]" />
	<COMMENT STRING="CITY:Dallas Zip:75207(会话个人自定义描述)" />
	<SESSION_DEFINE_CODE SPHEREGL="1" ENABLE_LOCALHTML5_STORAGE="1" SAVE_LOCALHTML5_STORAGE="1" SAVE_INDEXDB_STORAGE="1"/>
	<NETWORK TYPE="HTTP(代理类型)" IP="***************(代理IP)" PORT="3128(代理端口)" USER="XXX(登录用户名)" PASSWORD="XXX(用户密码)" PUBLIC_IP="***************(真实IP)" FAKEIP="***********(虚拟IP)" />
	<NETWORK_CTR NA="0 (是否FAKEIP)[DEFAULT=0]" FAKE_WRTC="1 (是否虚拟wRTC)[默认1]" SAME_IP="1(模拟连接IP和代理IP相同)[DEFAULT=1]" IPV6="0(默认关闭)" WRTCOFF="0(默认关闭)" DNS="NULL（默认为空）" />
	<USERAGENT_BROWSER CHROME="1" SAFARI="1" MSIE="1" OTHER="1" REGEN_CONFIG_USERAGENT="1" />
	<USERAGENT_STRING UA="Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15 reversion 1.77.0) Gecko/20100101 Firefox/77.0" UA_LNG="US(国别代号)" UA_LNG_STRING="en-US,en;q=0.5(HEAD包头语言定义)" />
	<USERAGENT_CTR DISPOPUPS="0(禁用弹窗)[默认关闭]" ENABLE_SERVICE="0(启用服务模式)[默认关闭]" BLOCKLNGINPUT="0(禁用指定语言输入)[默认关闭]"  />
	<RESOLUTION WIGHT="1024[屏幕宽]" HEIGHT="768[屏幕高]" />
	<RESOLUTION_CTR EMU_SCREEN="0(模拟_虚拟屏幕宽高)[默认关闭]" EMU_TOUCH="0(虚拟触屏)[默认关闭]" />
	<POSITION LONGITUDE="34.0735(GPS经度)[默认值为当前经度值]" LATITUDE="-118.263(GPS纬度)[默认值为当前纬度值]" COUNTRY="USA states(此数据从全局COUNTRY.XML中获取)" />
	<TIMEZONE NAME="Irish Standard Time(地域时区指定默认为空)[TIMEZONE名称,用户自填写]" ADD_VALUE="-0600[此数据从全局TIMEZONE.XML中获取]"/>
	<FINGERPRINT_CODE AUDIO="1" CANVAS="1" FONTS="1" RETCS="1" />
	<OTHER_SETTING PLUGINS_MIMETYPE="0" SAVE_ENCRYPT_COOKIES="1" ENABLE_FLASH="0" DYNAMIC_FINGERPRINTS="0(此选项置为有效后，主程序需对[DYNAMIC_FINGERPRINTS_CTR节点值进行获取])[默认为0]" BLOCK_CANVAS_OUTPUT="0(如设定有效，则FINGERPRINT_CODE节点CANVAS值必须为0)[默认为0]" />
	<DYNAMIC_FINGERPRINTS_CTR D_AUDIO="1" D_CANVAS="1" D_FONTS="1" D_RETCS="1" D_MEDIA="1" D_WEBGL="1" D_MIME="1" D_PLUGINS="1" />	
	<IS_VALID VALUE="1(此会话是否有效)[默认为1]"/>
	<UPDATETIME VALUE="2021-01-12 12:22:34(会话更新时间)"/>
	<CREATETIME VALUE="2021-01-12 12:22:34(会话创建时间)"/>
</VER>
</VERSION>