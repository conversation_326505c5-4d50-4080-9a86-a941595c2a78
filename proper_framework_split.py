#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的候鸟浏览器基础框架文档切分
根据实际章节内容命名，生成更多文件
"""

import os
import re
from pathlib import Path
from docx import Document
from docx.shared import Inches, Pt

def analyze_document_chapters(doc):
    """分析文档章节结构"""
    chapters = []
    current_chapter = None
    
    print("📋 分析文档章节结构...")
    
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        if not text:
            continue
        
        # 识别章节标题的多种模式
        is_chapter = False
        chapter_title = ""
        
        # 模式1: 明确的章节标记
        if re.match(r'^(第[一二三四五六七八九十\d]+章|第[一二三四五六七八九十\d]+节|[一二三四五六七八九十]、|\d+\.|第\d+部分)', text):
            is_chapter = True
            chapter_title = text
        
        # 模式2: Heading样式
        elif paragraph.style.name.startswith('Heading'):
            is_chapter = True
            chapter_title = text
        
        # 模式3: 特定主题关键词（短标题）
        elif (len(text) < 80 and 
              any(keyword in text for keyword in [
                  '产品定位', '用户群体', '运行环境', '技术规格', '安全特性',
                  '系统架构', '数据库设计', 'API接口', '环境管理', '代理服务',
                  '插件管理', '脚本管理', '用户认证', '安全机制', '网络通信',
                  '日志系统', '性能优化', '客户端', '服务器端', '数据同步',
                  '业务流程', '团队协作', '模板配置', '指纹设置', '监控系统',
                  '错误处理', '版本控制', '部署配置', '维护管理', '故障排除'
              ])):
            is_chapter = True
            chapter_title = text
        
        # 模式4: 包含"管理器"、"系统"、"模块"等关键词的标题
        elif (len(text) < 100 and 
              any(keyword in text for keyword in ['管理器', '系统', '模块', '组件', '服务', '接口', '配置'])):
            is_chapter = True
            chapter_title = text
        
        if is_chapter:
            # 保存前一个章节
            if current_chapter:
                current_chapter['end_index'] = i - 1
                current_chapter['paragraph_count'] = current_chapter['end_index'] - current_chapter['start_index'] + 1
                chapters.append(current_chapter)
            
            # 开始新章节
            current_chapter = {
                'title': chapter_title,
                'start_index': i,
                'end_index': None,
                'paragraph_count': 0
            }
    
    # 处理最后一个章节
    if current_chapter:
        current_chapter['end_index'] = len(doc.paragraphs) - 1
        current_chapter['paragraph_count'] = current_chapter['end_index'] - current_chapter['start_index'] + 1
        chapters.append(current_chapter)
    
    return chapters

def clean_chapter_title(title):
    """清理章节标题，生成合适的文件名"""
    # 移除章节编号
    title = re.sub(r'^(第[一二三四五六七八九十\d]+章|第[一二三四五六七八九十\d]+节|[一二三四五六七八九十]、|\d+\.)\s*', '', title)
    
    # 移除特殊字符
    title = re.sub(r'[<>:"/\\|?*]', '_', title)
    title = re.sub(r'\s+', '_', title)
    title = title.strip('_')
    
    # 限制长度
    if len(title) > 30:
        title = title[:30]
    
    # 如果标题为空，使用默认名称
    if not title:
        title = "未命名章节"
    
    return title

def group_small_chapters(chapters, min_paragraphs=200):
    """将小章节合并成较大的组"""
    grouped_chapters = []
    current_group = []
    current_group_paragraphs = 0
    
    for chapter in chapters:
        current_group.append(chapter)
        current_group_paragraphs += chapter['paragraph_count']
        
        # 如果当前组达到最小段落数，或者是最后一个章节
        if (current_group_paragraphs >= min_paragraphs or 
            chapter == chapters[-1]):
            
            # 创建组标题
            if len(current_group) == 1:
                group_title = current_group[0]['title']
            else:
                # 使用第一个章节的标题，或者创建组合标题
                main_title = current_group[0]['title']
                if len(main_title) > 15:
                    group_title = main_title
                else:
                    # 尝试找到最有代表性的标题
                    representative_titles = []
                    for ch in current_group:
                        if any(keyword in ch['title'] for keyword in 
                               ['系统', '管理', '配置', '接口', '服务']):
                            representative_titles.append(ch['title'])
                    
                    if representative_titles:
                        group_title = representative_titles[0]
                    else:
                        group_title = f"{main_title}等{len(current_group)}个模块"
            
            grouped_chapters.append({
                'title': group_title,
                'chapters': current_group.copy(),
                'start_index': current_group[0]['start_index'],
                'end_index': current_group[-1]['end_index'],
                'paragraph_count': current_group_paragraphs
            })
            
            # 重置当前组
            current_group = []
            current_group_paragraphs = 0
    
    return grouped_chapters

def create_chapter_files(doc, grouped_chapters, output_dir):
    """根据章节创建文件"""
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    output_files = []
    tables_per_group = len(doc.tables) // len(grouped_chapters)
    table_index = 0
    
    for i, group in enumerate(grouped_chapters):
        # 生成文件名
        clean_title = clean_chapter_title(group['title'])
        
        # 如果组内有多个章节，可能需要分割
        if group['paragraph_count'] > 800:
            # 大组分割成多个文件
            num_parts = (group['paragraph_count'] + 600 - 1) // 600
            paragraphs_per_part = group['paragraph_count'] // num_parts
            
            for part_num in range(num_parts):
                start_offset = part_num * paragraphs_per_part
                if part_num == num_parts - 1:
                    end_offset = group['paragraph_count']
                else:
                    end_offset = (part_num + 1) * paragraphs_per_part
                
                part_start = group['start_index'] + start_offset
                part_end = group['start_index'] + end_offset - 1
                
                filename = f"候鸟浏览器基础框架_{clean_title}({part_num + 1}).docx"
                
                # 创建文档
                new_doc = Document()
                new_doc.add_heading(f'{group["title"]} (第{part_num + 1}部分)', 0)
                
                # 复制段落
                for para_idx in range(part_start, min(part_end + 1, len(doc.paragraphs))):
                    try:
                        source_para = doc.paragraphs[para_idx]
                        new_para = new_doc.add_paragraph()
                        new_para.style = source_para.style
                        new_para.alignment = source_para.alignment
                        
                        for run in source_para.runs:
                            new_run = new_para.add_run(run.text)
                            new_run.bold = run.bold
                            new_run.italic = run.italic
                            new_run.underline = run.underline
                    except:
                        new_doc.add_paragraph(doc.paragraphs[para_idx].text)
                
                # 分配表格
                if part_num == 0:  # 只在第一部分添加表格
                    tables_to_add = tables_per_group
                    if i == len(grouped_chapters) - 1:
                        tables_to_add += len(doc.tables) % len(grouped_chapters)
                    
                    for _ in range(tables_to_add):
                        if table_index < len(doc.tables):
                            try:
                                source_table = doc.tables[table_index]
                                rows = len(source_table.rows)
                                cols = len(source_table.columns) if source_table.rows else 0
                                
                                if rows > 0 and cols > 0:
                                    new_table = new_doc.add_table(rows=rows, cols=cols)
                                    new_table.style = 'Table Grid'
                                    
                                    for r, row in enumerate(source_table.rows):
                                        for c, cell in enumerate(row.cells):
                                            if r < len(new_table.rows) and c < len(new_table.rows[r].cells):
                                                new_table.cell(r, c).text = cell.text
                            except:
                                pass
                            table_index += 1
                
                # 保存文件
                filepath = output_path / filename
                new_doc.save(str(filepath))
                output_files.append(str(filepath))
                
                file_size = filepath.stat().st_size / 1024
                print(f"✅ 创建: {filename} ({file_size:.1f} KB)")
        
        else:
            # 小组直接创建一个文件
            filename = f"候鸟浏览器基础框架_{clean_title}.docx"
            
            # 创建文档
            new_doc = Document()
            new_doc.add_heading(group['title'], 0)
            
            # 复制段落
            for para_idx in range(group['start_index'], min(group['end_index'] + 1, len(doc.paragraphs))):
                try:
                    source_para = doc.paragraphs[para_idx]
                    new_para = new_doc.add_paragraph()
                    new_para.style = source_para.style
                    new_para.alignment = source_para.alignment
                    
                    for run in source_para.runs:
                        new_run = new_para.add_run(run.text)
                        new_run.bold = run.bold
                        new_run.italic = run.italic
                        new_run.underline = run.underline
                except:
                    new_doc.add_paragraph(doc.paragraphs[para_idx].text)
            
            # 分配表格
            tables_to_add = tables_per_group
            if i == len(grouped_chapters) - 1:
                tables_to_add += len(doc.tables) % len(grouped_chapters)
            
            for _ in range(tables_to_add):
                if table_index < len(doc.tables):
                    try:
                        source_table = doc.tables[table_index]
                        rows = len(source_table.rows)
                        cols = len(source_table.columns) if source_table.rows else 0
                        
                        if rows > 0 and cols > 0:
                            new_table = new_doc.add_table(rows=rows, cols=cols)
                            new_table.style = 'Table Grid'
                            
                            for r, row in enumerate(source_table.rows):
                                for c, cell in enumerate(row.cells):
                                    if r < len(new_table.rows) and c < len(new_table.rows[r].cells):
                                        new_table.cell(r, c).text = cell.text
                    except:
                        pass
                    table_index += 1
            
            # 保存文件
            filepath = output_path / filename
            new_doc.save(str(filepath))
            output_files.append(str(filepath))
            
            file_size = filepath.stat().st_size / 1024
            print(f"✅ 创建: {filename} ({file_size:.1f} KB)")
    
    return output_files

def split_framework_properly(source_file, output_dir):
    """正确切分候鸟浏览器基础框架文档"""
    try:
        print(f"📄 开始正确切分: {Path(source_file).name}")
        
        # 加载文档
        doc = Document(source_file)
        
        print(f"📊 文档信息:")
        print(f"   段落数: {len(doc.paragraphs)}")
        print(f"   表格数: {len(doc.tables)}")
        
        # 分析章节
        chapters = analyze_document_chapters(doc)
        print(f"   发现章节: {len(chapters)}")
        
        # 显示前20个章节
        print(f"\n📋 主要章节预览:")
        for i, chapter in enumerate(chapters[:20]):
            print(f"   {i+1:2d}. {chapter['title'][:50]}... ({chapter['paragraph_count']}段落)")
        
        if len(chapters) > 20:
            print(f"   ... 还有 {len(chapters)-20} 个章节")
        
        # 将小章节分组
        grouped_chapters = group_small_chapters(chapters, min_paragraphs=200)
        print(f"\n📊 分组后: {len(grouped_chapters)} 个文件组")
        
        for i, group in enumerate(grouped_chapters):
            print(f"   组{i+1}: {group['title'][:40]}... ({group['paragraph_count']}段落)")
        
        print()
        
        # 创建文件
        output_files = create_chapter_files(doc, grouped_chapters, output_dir)
        
        return output_files
        
    except Exception as e:
        print(f"❌ 切分失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return []

if __name__ == "__main__":
    source_file = r"F:\augment\output\docx_files\候鸟浏览器基础框架第七十七版.docx"
    output_directory = r"F:\augment\output\docx_files"
    
    print("📄 候鸟浏览器基础框架文档正确切分工具")
    print(f"📂 源文件: {source_file}")
    print(f"📂 输出目录: {output_directory}")
    print("=" * 60)
    
    if not Path(source_file).exists():
        print(f"❌ 源文件不存在: {source_file}")
        exit(1)
    
    # 删除之前的切分文件
    old_files = list(Path(output_directory).glob("候鸟浏览器基础框架(*.docx")) + \
                list(Path(output_directory).glob("候鸟浏览器基础框架_*.docx"))
    if old_files:
        print(f"🗑️  清理 {len(old_files)} 个旧文件...")
        for old_file in old_files:
            try:
                old_file.unlink()
            except:
                pass
        print()
    
    # 执行正确的切分
    result_files = split_framework_properly(source_file, output_directory)
    
    if result_files:
        print("=" * 60)
        print(f"🎉 切分完成! 生成了 {len(result_files)} 个文件")
        
        total_size = 0
        for file_path in result_files:
            file_size = Path(file_path).stat().st_size / 1024
            total_size += file_size
            print(f"   📄 {Path(file_path).name} ({file_size:.1f} KB)")
        
        print(f"\n📊 总大小: {total_size:.1f} KB")
        print(f"📊 平均大小: {total_size/len(result_files):.1f} KB")
        
        print("\n🎯 文件已准备好用于RAGFlow向量库！")
    else:
        print("❌ 切分失败")
