API文档: 错误码对照表 - 1、帐号登录
URL: https://www.mbbrowser.com/api/code
抓取时间: 2025-07-28 12:35:41
============================================================

API使用文档-候鸟防关联浏览器•

首页

应用

价格

下载

APInew
使用教程

常见问题

佣金计划

博客中心
登录&注册 简体中文
首页

应用

价格

下载

API

使用教程

佣金计划

博客中心

登录&注册

# API
候鸟浏览器API使用文档

API使用须知简介
• 使用须知
• HTTP模式说明
• 常见问题
• API接口文档1、帐号登录
• 2、获取成员列表
• 3、环境开启/关闭
• 4、环境管理
• 5、分组管理
• 6、脚本管理
• 7、插件管理
• 8、附录（国家码、时区、语言、系统和分辨率）
• 9、错误码对照表
• 候鸟API接口实时调试工具POSTMAN下载及安装
• POSTMAN调试候鸟API接口
• 调试接口JSON数据官方更新、下载
• 多种语言脚本示例
• JSON在线格式化工具

## 错误码对照表

### CODE 错误码表
错误码Message值 / 描述说明 0全局成功 -1全局登录失败 / App_ID 或 App_KEY 非法 -2全局登录失败 / 连接候鸟服务器超时[curl 状态码] -3全局当前登录帐户套餐已过期 -4全局Login_Expire -5全局Login_ServerError / 服务器访问错误 -6全局Login_MbSvrError / 启动 mbservice 失败 -7全局Login_Uping / 还有上传在进行，需要等其完成才能登录 -8全局Login_Occupy / 有其它实例已占用登录 -9全局数据同步失败:超时 -10全局数据处理失败：数据损坏导致无法加载 -11全局数据处理失败：当前目录没有写权限，目录位置 -12全局登录失败 / App_ID 处于在线状态 -13全局删除环境失败 -14全局导入 COOKIE 失败: COOKIE 格式不合法 -15全局导出 COOKIE 失败: COOKIE 内容为空 -101创建/更新环境创建/更新环境失败: 环境名称超长 -102创建/更新环境创建/更新环境失败: 环境描述超长 -103创建/更新环境创建/更新环境失败: 分组不存在 -104创建/更新环境创建/更新环境失败: 代理服务器填入值不合法 -105创建/更新环境创建/更新环境失败: 代理检测失败,无效代理 -106创建/更新环境创建/更新环境失败: 初始化时区失败 -107创建/更新环境创建/更新环境失败: 操作系统名称填写错误 -108创建/更新环境创建/更新环境失败: 环境分辨率填写错误 -109创建/更新环境创建/更新环境失败: USERAGENT 未填入有效值 -110创建/更新环境创建/更新环境失败: 环境默认语言未填入有效值 -111创建/更新环境创建/更新环境失败: FingerPrint_Setting 填入值非法 -112创建/更新环境创建/更新环境失败: 导入 COOKIE 文件内容不合法 -10000全局未知异常
### Status 错误码表
错误码描述说明 0成功 -1初始化数据失败 -2启动浏览器内核失败 -3当前浏览器环境：插件下载失败 -4当前浏览器环境：插件加载失败 -5当前浏览器环境：自动化脚本下载失败 -6当前浏览器环境：自动化脚本加载失败 -7当前浏览器环境：已经运行 -8当前浏览器环境：已经加入运行队列 -9当前浏览器环境：初始化 CDP 失败 -10当前浏览器环境：初始化 Service 失败 -11当前浏览器环境：CD 监听失败 -12当前浏览器环境：DP 退出 -13当前浏览器环境：连接失败 -14当前浏览器环境：初始化环境失败 -15当前浏览器环境：GetShortPathName 失败 -16当前浏览器环境：申请内存失败 -17当前浏览器环境：登录退出 -18当前浏览器环境：未收到响应信息 -19当前浏览器环境：关闭失败 -20Headless 打开项无法用 stop 关闭，建议使用 kill 关闭 -21当前浏览器环境：强制关闭失败 -22未找到指定的环境SessionId -5010POST串请求到API SERVER URL地址非法，或请求的目标API URL中有非法字符存在 -10000未知错误
### CURL代理检测错误码表
错误码描述说明 0CURLE_OK成功 1CURLE_UNSUPPORTED_PROTOCOL未支持的协议。此版cURL 不支持这一协议。 2CURLE_FAILED_INIT初始化失败。 3CURLE_URL_MALFORMATURL格式错误。 4CURLE_NOT_BUILT_IN功能未编译进此版本cURL。 5CURLE_COULDNT_RESOLVE_PROXY无法解析代理服务器。 6CURLE_COULDNT_RESOLVE_HOST无法解析主机名。 7CURLE_COULDNT_CONNECT无法连接到远程服务器。 8CURLE_WEIRD_SERVER_REPLY服务器回复异常。 9CURLE_REMOTE_ACCESS_DENIED远程访问被拒绝。 10CURLE_FTP_ACCEPT_FAILEDFTP接受失败。 11CURLE_FTP_WEIRD_PASS_REPLYFTP密码回复异常。 12CURLE_FTP_ACCEPT_TIMEOUTFTP接受超时。 13CURLE_FTP_WEIRD_PASV_REPLYFTP PASV回复异常。 14CURLE_FTP_WEIRD_227_FORMATFTP 227格式异常。 15CURLE_FTP_CANT_GET_HOSTFTP无法获取主机。 16CURLE_HTTP2HTTP/2错误。 17CURLE_FTP_COULDNT_SET_TYPEFTP无法设置类型。 18CURLE_PARTIAL_FILE部分文件传输失败。 19CURLE_FTP_COULDNT_RETR_FILEFTP无法检索文件。 20CURLE_OBSOLETE20已废弃。 21CURLE_QUOTE_ERROR命令执行错误。 22CURLE_HTTP_RETURNED_ERRORHTTP请求返回错误。 23CURLE_WRITE_ERROR写入错误。 24CURLE_OBSOLETE24已废弃。 25CURLE_UPLOAD_FAILED上传失败。 26CURLE_READ_ERROR读取错误。 27CURLE_OUT_OF_MEMORY内存不足。 28CURLE_OPERATION_TIMEDOUT操作超时。 29CURLE_OBSOLETE29已废弃。 30CURLE_FTP_PORT_FAILEDFTP PORT命令失败。 31CURLE_FTP_COULDNT_USE_RESTFTP无法使用REST命令。 32CURLE_OBSOLETE32已废弃。 33CURLE_RANGE_ERROR范围错误。 34CURLE_HTTP_POST_ERRORHTTP POST请求错误。 35CURLE_SSL_CONNECT_ERRORSSL连接错误。 36CURLE_BAD_DOWNLOAD_RESUME下载恢复错误。 37CURLE_FILE_COULDNT_READ_FILE无法读取文件。 38CURLE_LDAP_CANNOT_BINDLDAP无法绑定。 39CURLE_LDAP_SEARCH_FAILEDLDAP搜索失败。 40CURLE_OBSOLETE40已废弃。 41CURLE_FUNCTION_NOT_FOUND函数未找到。 42CURLE_ABORTED_BY_CALLBACK回调函数中断。 43CURLE_BAD_FUNCTION_ARGUMENT函数参数错误。 44CURLE_OBSOLETE44已废弃。 45CURLE_INTERFACE_FAILED接口失败。 46CURLE_OBSOLETE46已废弃。 47CURLE_TOO_MANY_REDIRECTS重定向过多。 48CURLE_UNKNOWN_OPTION未知选项。 49CURLE_SETOPT_OPTION_SYNTAX选项语法错误。 50CURLE_OBSOLETE50已废弃。 51CURLE_OBSOLETE51已废弃。 52CURLE_GOT_NOTHING未收到任何数据。 53CURLE_SSL_ENGINE_NOTFOUNDSSL引擎未找到。 54CURLE_SSL_ENGINE_SETFAILEDSSL引擎设置失败。 55CURLE_SEND_ERROR发送错误。 56CURLE_RECV_ERROR接收错误。 57CURLE_OBSOLETE57已废弃。 58CURLE_SSL_CERTPROBLEMSSL证书问题。 59CURLE_SSL_CIPHERSSL密钥问题。 60CURLE_PEER_FAILED_VERIFICATION对等方验证失败。 61CURLE_BAD_CONTENT_ENCODING内容编码错误。 62CURLE_OBSOLETE62已废弃。 63CURLE_FILESIZE_EXCEEDED文件大小超过限制。 64CURLE_USE_SSL_FAILED使用SSL失败。 65CURLE_SEND_FAIL_REWIND发送重绕失败。 66CURLE_SSL_ENGINE_INITFAILEDSSL引擎初始化失败。 67CURLE_LOGIN_DENIED登录被拒绝。 68CURLE_TFTP_NOTFOUNDTFTP文件未找到。 69CURLE_TFTP_PERMTFTP权限错误。 70CURLE_REMOTE_DISK_FULL远程磁盘空间不足。 71CURLE_TFTP_ILLEGALTFTP非法操作。 72CURLE_TFTP_UNKNOWNIDTFTP未知ID。 73CURLE_REMOTE_FILE_EXISTS远程文件已存在。 74CURLE_TFTP_NOSUCHUSERTFTP用户不存在。 75CURLE_OBSOLETE75已废弃。 76CURLE_OBSOLETE76已废弃。 77CURLE_SSL_CACERT_BADFILESSL CA证书文件错误。 78CURLE_REMOTE_FILE_NOT_FOUND远程文件未找到。 79CURLE_SSHSSH错误。 80CURLE_SSL_SHUTDOWN_FAILEDSSL关闭失败。 81CURLE_AGAIN资源暂时不可用。 82CURLE_SSL_CRL_BADFILESSL CRL文件错误。 83CURLE_SSL_ISSUER_ERRORSSL颁发者错误。 84CURLE_FTP_PRET_FAILEDFTP PRET命令失败。 85CURLE_RTSP_CSEQ_ERRORRTSP CSEQ错误。 86CURLE_RTSP_SESSION_ERRORRTSP会话错误。 87CURLE_FTP_BAD_FILE_LISTFTP文件列表错误。 88CURLE_CHUNK_FAILED分块失败。 89CURLE_NO_CONNECTION_AVAILABLE无可用连接。 90CURLE_SSL_PINNEDPUBKEYNOTMATCHSSL固定公钥不匹配。 91CURLE_SSL_INVALIDCERTSTATUSSSL证书状态无效。 92CURLE_HTTP2_STREAMHTTP/2流错误。 93CURLE_RECURSIVE_API_CALL递归API调用。 94CURLE_AUTH_ERROR认证错误。 95CURLE_HTTP3HTTP/3错误。 96CURLE_QUIC_CONNECT_ERRORQUIC连接错误。 97CURLE_PROXY代理错误。 98CURLE_SSL_CLIENTCERT客户端SSL证书错误。 99CURLE_UNRECOVERABLE_POLL不可恢复的轮询错误。 100CURLE_TOO_LARGE请求过大。 101CURLE_ECH_REQUIRED需要ECH。  支持邮箱: <EMAIL>
©MBBROWSER @2025

京ICP备 2020047947号

本系统不提供代理IP服务，禁止用户使用本系统进行任何违法犯罪活动，用户使用本系统带来的任何责任由用户自行承担。

MBbrowser.com  All Rights Reserved. 候鸟防关联浏览器对网站内容拥有最终解释权。
工作日客服(微信)
工作日09-18点

夜间/周末客服(微信)

工作日 18-24点，周末全天

商务(微信)

mbbrowser_official

###### 全国咨询服务热线

400-112-6050
在线咨询

微信咨询

电话咨询

售后咨询