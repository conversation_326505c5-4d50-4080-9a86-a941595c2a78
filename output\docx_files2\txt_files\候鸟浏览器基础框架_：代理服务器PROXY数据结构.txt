第三节：代理服务器PROXY数据结构

第三节：代理服务器PROXY数据结构

第四节：USER-AGENT数据结构

第五节：会话文档数据结构

第六节：备份日志数据结构

第十章：浏览器内核数据本地存储

第十章：浏览器内核数据本地存储

1、BrowserMetrics 累积的（实时）度量标准数据

C:\Users\<USER>\AppData\Roaming\MBbrowser\LocalStorage\sxEwjM8XllTeWJqEBHvWrQ==\MainData\ChromeData\1\BrowserMetrics

“ BrowserMetrics-identifier.pma ”文件是实际的写入文件。其中一个是实时运行的，其他任何一个都来自以前的运行，一旦从它们中检索到任何最终信息，我们都会将其删除。

“备用”文件是一种优化。创建并完全实现该文件需要花费时间（在最大程度上减少了在使用中出现磁盘错误的可能性），因此需要在Chrome运行一小会儿之后创建一个文件。这个空的（全零）文件将在下一次  运行时使用，以免延迟尝试创建一个文件的启动。

“实时”（即BrowserMetrics-identifier.pma）文件的数据以chrome：// histograms显示。

累积的（实时）度量标准数据通常大约每30分钟定期发送一次，并在关机期间进行缓冲。在正常关闭过程之后，PMA文件通常为“空”（不是零，但没有“未发送”数据）。仅在程序崩溃的情况下，它们才会有未发送的数据。启动后一段时间，将处理以前运行的文件并发送所有最终内容。

CertificateRevocation 自动证书吊销检查

在您的Google Chrome安装和Chromebook上启用自动证书吊销检查。

Chrome浏览器检查HTTPS证书撤销状态的主要机制是。

Chrome浏览器还支持在线证书状态协议（OCSP）。但是，除非客户端无法实时获得有效的OCSP响应而导致客户端失败（拒绝连接），否则OCSP的有效性实质上为0。默认情况下，没有任何浏览器将OCSP设置为硬故障，这是由Adam Langley解释的，有充分的理由（请参阅和）。

与非装订OCSP相比，具有“必须装订”选项的装订OCSP（如果未在证书中装订有效的OCSP响应，则将失败）。CA和浏览器正在努力实现该解决方案（请参阅）。

此外，未装订的OCSP会带来隐私问题：为了检查证书的状态，客户端必须向OCSP响应者查询证书的状态，从而将用户的HTTPS浏览历史暴露给响应者（第三方） 。

也就是说，您可以使用企业策略为和硬失败OCSP 。

如果Chrome 还没有覆盖该域的未过期的CRLSet条目，则对证书进行在线检查。如果Chrome没有收到响应，它只会将安全指示器降级为“已通过域验证”。

有关面向用户的UX的更多讨论，另请参见。

3、Crashpad 事故报告系统

Crashpad是一个崩溃报告系统。

Crashpad是一个库，用于从客户端捕获，存储和传输事后崩溃报告，并将其传输到上游收集服务器。Crashpad旨在使客户能够在崩溃时以尽可能高的保真度和覆盖率和最少的麻烦捕获过程状态。

Crashpad还为客户端提供了一种工具，可用于按需捕获过程状态转储以进行诊断。

Crashpad还为客户端提供了最少的功能，使其可以使用按进程键/值对形式的应用程序特定的元数据来装饰崩溃。更高级的客户端能够通过可扩展性点进一步修饰崩溃报告，这些扩展点使嵌入程序可以使用特定于应用程序的元数据来扩展崩溃报告。

更多内容访问：

4、Crowd Deny人群否认

Crowd Deny是Chrome的独特组件，可处理网站权限。如果网站的信誉数据不佳，则Chrome会启动“拒绝人群”组件以撤消所有权限。

5、Default （CHROMIUM主存储）

FileTypePolicies

Floc

FontLookupTableCache

GrShaderCache

OnDeviceHeadSuggestModel

OriginTrials

Safe Browsing

SafetyTips

ShaderCache

SSLErrorAssistant

Subresource Filter

TLSDeprecationConfig

TrustTokenKeyCommitments

CrashpadMetrics.pma

CrashpadMetrics-active.pma

附录：

林肯球全局设置数据结构报告：

注册表存储节点位置说明：

HKCU\SOFTWARE\Tenebris\Linken Sphere\

closedTabs\recentlyClosedTabs REG_SZ

描述：保存最近浏览器关闭的TAB数据。

Downloadmanager REG_SZ

描述： 保存浏览器下载文件夹

License REG_SZ

Login  保存用户登录名（明文@ByteArray(wangpeng)）

Pass   保存用户登录密码（明文@ByteArray(Wangpeng123)）

useTorForCheck 默认值为0（不连接） 连接tor网络验证登录身份。

ls-webengine REG_SZ

closedWebsocksPorts 已关闭的网络端口：63333,5900,5901,5902,5903,3389,5279,5939,5931,5950

hpSpeed （16进制190/ 10进制400）

screenSize  @Size(1920 1080)  当前浏览器分辨率

main

colorScheme REG_DWORD  value:1 表示默认皮肤。

disableRecently REG_SZ value: false

dontShowUpdates REG_SZ value: false  默认允许弹出版本更新窗口。

Font-size 		界面默认 9号字体。

Is96dpi : 			默认使用96dpi

lastVersion： 		7.996 最近一次版本号

openClosedTabs  	false 默认不打开已关闭的tabs

tmCopies  		默认值：:1

tmInterval  		默认值：0

torport  			默认值：9191 tor网络端口

uproxy 			默认值：no proxy

uproxyHost 		同上

uproxyPassword 	同上

uproxyPort 1080

useDesktopOpengl 默认值: false 默认使用桌面opengL物理渲染驱动：否

useinitforsession 	默认值：true 使用初始的会话环境


================================================== 表格内容 ==================================================

"""
简单解释：
实现一个固定容量的桶，按一定的频率往桶内放令牌直至桶满，每当执行一个限频操作需要从桶中获取一个令牌才能继续操作，若桶中没有令牌，则进行等待
往令牌桶中放令牌的操作不便按照原概念实现，所以放令牌这步放到取令牌的时候进行。我们根据当前取令牌的时间减去上一次取令牌的时间差，就能得知这段时间内增加了多少个令牌。
"""

class TokenBucket(object):

    # rate 是令牌桶生产令牌的速率，capacity 是令牌桶生产令牌的速率
    def __init__(self, rate, capacity):
        self._rate = rate
        self._capacity = capacity
        self._current_amount = 0
        self._last_consume_time = int(time.time())

    # token_amount 是执行一次操作需要的令牌数量
    def consume(self, token_amount):
        # 通过时间差乘速率，得到令牌的增量
        increment = (int(time.time()) - self._last_consume_time) * self._rate
        时间差乘速率，得到令牌的增量  
        self._current_amount = min(
            increment + self._current_amount, self._capacity)
        # 令牌数量不够则不允许操作
        if token_amount > self._current_amount:
            return False
        # 更新最后一次操作时间
        self._last_consume_time = int(time.time())
        # 结算当前的令牌数量
        self._current_amount -= token_amount
        return True

参数名称 | 类型 | 必传 | 样例串/默认值 | 说明
Account | string | 是 | <EMAIL> | 用户凭证Account
APP_ID | string | 是 | 7e147176e1d756eb03c0e18e7b640c23 | 用户凭证app_Id
APP_KEY | string | 是 | kwMTYxNG_MzlkZjhlZmM2YWNiY2M5MTkw | 用户凭证app_key
return | string | 否 | on (default) | on: APISERVER.EXE 所有需返回给用户的数据[JSON/其它]，所有状态信息，返回到CONSOLE界面。（当用户未填写此参数时，默认为ON）
off: 所有返回给用户的数据[JSON/其它]、所有状态信息，不在CONSOLE界面显示。
logs | string | 否 | on (default) | on: APISERVER.EXE 所有需返回给用户的JSON数据，所有状态信息，明文写入API_LOG目录【非LOG目录】，LOG文件前缀例：test04@qq.com_API_log。（当用户未填写此参数时，默认为ON）
off: 所有返回给脚本的JSON数据、所有状态信息，不写入LOG。
hide | string | 否 | on (default) | on: APISERVER.EXE 以(全自动化)模式启动，启动后会同时自动屏蔽已登录的mbbrowser.exe主面板，保障客户在自动化运行中，避免人工通过主面板同时操控环境所带来的业务风险。（当用户未填写此参数时，默认为ON）
off: APISERVER.EXE 以支持(半自动化/全自动化)模式启动，启动后会同时显示mbbrowser.exe主面板，供客户在自动化运行中，可人工通过主面板操控环境，强化业务控制能力。
[此参数仅在产品版本号: 4.8.20.134及以后有效]