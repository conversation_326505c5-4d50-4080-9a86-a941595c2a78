<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>/api/browser</title>
  <style>
/* 基础重置与排版 */
body {
  margin: 0;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.7;
  color: #333;
  background-color: #fff;
  max-width: 960px;
  margin: 0 auto;
  box-sizing: border-box;
}

/* 段落 */
p {
  margin: 1em 0;
}

/* 标题 */
h1, h2, h3, h4, h5, h6 {
  margin: 1.5em 0 0.8em;
  color: #2c3e50;
  font-weight: 600;
  line-height: 1.3;
}

h1 { font-size: 2em; border-bottom: 1px solid #eee; padding-bottom: 0.5em; }
h2 { font-size: 1.6em; }
h3 { font-size: 1.4em; }
h4 { font-size: 1.2em; }

/* 列表 */
ul, ol {
  margin: 1em 0;
  padding-left: 2em;
}

li {
  margin: 0.4em 0;
}

/* 引用块 */
blockquote {
  margin: 1.5em 0;
  padding: 0.8em 1.5em;
  background-color: #f9f9f9;
  border-left: 4px solid #ddd;
  color: #666;
  font-style: italic;
  border-radius: 0 4px 4px 0;
}

/* 代码行内 */
code {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  background-color: #f3f4f6;
  color: #e9602d;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-size: 0.95em;
  white-space: nowrap;
}

/* 代码块 */
pre {
  margin: 1.5em 0;
  padding: 1.2em;
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  overflow-x: auto;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
}

pre code {
  background: none;
  color: inherit;
  padding: 0;
  font-size: inherit;
  white-space: pre;
  display: block;
}

/* 表格 */
table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5em 0;
  font-size: 14px;
  overflow: hidden;
  box-shadow: 0 0 0 1px #e0e0e0;
  border-radius: 6px;
}

th, td {
  padding: 10px 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

th {
  background-color: #f8f9fa;
  color: #2c3e50;
  font-weight: 600;
  white-space: nowrap;
}

tr:nth-child(even) {
  background-color: #f9f9fb;
}

tr:hover {
  background-color: #f0f5ff;
}

/* 链接 */
a {
  color: #1a73e8;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

/* 分隔线 */
hr {
  border: 0;
  height: 1px;
  background: #ddd;
  margin: 2em 0;
}

/* 图片 */
img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 1em 0;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
  </style>
</head>
<body>
  <h2>环境开启/关闭</h2> <div class="api-tabs ant-tabs ant-tabs-top ant-tabs-card ant-tabs-no-animation"><div class="ant-tabs-bar ant-tabs-top-bar ant-tabs-card-bar"><div class="ant-tabs-nav-container"><span class="ant-tabs-tab-prev ant-tabs-tab-btn-disabled"><span class="ant-tabs-tab-prev-icon"><i class="anticon anticon-left ant-tabs-tab-prev-icon-target"><svg class=""><path></path></svg></i></span></span><span class="ant-tabs-tab-next ant-tabs-tab-btn-disabled"><span class="ant-tabs-tab-next-icon"><i class="anticon anticon-right ant-tabs-tab-next-icon-target"><svg class=""><path></path></svg></i></span></span><div class="ant-tabs-nav-wrap"><div class="ant-tabs-nav-scroll"><div class="ant-tabs-nav ant-tabs-nav-animated"><div><div class="ant-tabs-tab">1、打开环境</div><div class="ant-tabs-tab">2、关闭环境</div><div class="ant-tabs-tab-active ant-tabs-tab">3、强制终止环境</div></div><div class="ant-tabs-ink-bar ant-tabs-ink-bar-animated"></div></div></div></div></div></div><div></div><div class="ant-tabs-content ant-tabs-content-no-animated ant-tabs-top-content ant-tabs-card-content"><div class="ant-tabs-tabpane ant-tabs-tabpane-inactive"><ul><li><p>Path：/api/v1/browser/start</p></li> <li><p>Method：POST</p></li> <li><p>Content-Type：application/json</p></li> <li><p>接口描述：用于启动指定的环境，启动成功后可以获取浏览器 debug 接口用于执行 selenium 和 puppeteer 自动化脚本。</p></li></ul> <blockquote><p>请求参数</p></blockquote> <div class="table"><table><thead><tr><td>参数名称</td> <td>类型</td> <td>必传</td> <td>样例串/默认值</td> <td>说明</td></tr></thead> <tbody><tr><td>Session_ID</td> <td>array</td> <td>是</td> <td>373808cb37bd63f5f7d92415e736e85f,705cc4c139e69b729a2fd277f30e1863</td> <td>环境ID</td></tr> <tr><td>isHeadless</td> <td>boolean</td> <td>否</td> <td>true</td> <td><p>true：默认浏览器器无头模式</p><p>false：浏览器有头模式</p></td></tr> <tr><td>args</td> <td>array</td> <td>否</td> <td>"args": ["--disable-extensions", "--blink-settings=imagesEnabled=false"]</td> <td>启动参数</td></tr></tbody></table></div> <blockquote><p>单个环境请求示例</p></blockquote> <div class="code-view"><pre><code>{ "Session_ID": [ "373808cb37bd63f5f7d92415e736e85f" ], "args": [ "--disable-extensions", "--enable-logging", "--v=1", "--blink-settings=imagesEnabled=false" ] }</code></pre> </div> <blockquote><p>多个环境请求示例</p></blockquote> <div class="code-view"><pre><code>{ "Session_ID": [ "373808cb37bd63f5f7d92415e736e85f", "705cc4c139e69b729a2fd277f30e1863" ], "args": [ "--disable-extensions", "--blink-settings=imagesEnabled=false", "--interval-seconds=3" ] }</code></pre> </div> <p>--disable-extensions 禁用插件</p> <p>--blink-settings=imagesEnabled=false 禁止加载图片</p> <p>--interval-seconds 启动各浏览器间隔时间(秒)</p> <blockquote><p>执行成功返回</p></blockquote> <div class="code-view"><pre><code>{ "message": "Success", "code": 0, "data": { "listid": [{ "Session_Name": "商用业务环境一", "Session_ID": "373808cb37bd63f5f7d92415e736e85f", "Group_Name": "default", "Actived_script_id": "O73808cb37bd63f5f7d92415e736e999", "Actiived_script_name": "这是一个脚本例子", "Actiived_script_encode": "true", "Weblogin_Account_Count": "4", "Weblogin_Account_name": "<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>", "Plugins_Count": "4", "Plugin_Id": "jjbnhpnlakcdgfnnldamfeinfmahhdlm,jjbnhpnlakcdgfnnldamfeinfmahhdlm,jjbnhpnlakcdgfnnldamfeinfmahhdlm,jjbnhpnlakcdgfnnldamfeinfmahhdlm", "template_id": "123456", "template_name": "抖音国际版", "browser_Path": "D:\\mbbrowser\\Chromium_x64\\chromium.exe", "browser_CDP_Port": 46973, "MBData_Path": "C:\\MBDATA\xxxxxxxxxx\xxxxxxxxxx\xxxxxxxxxxx", "Public_ip": "************", "Internel_ip": "**************", "isDynamicIp": false, "StartPage": "about:blank", "proxyType": "socks5", "proxy_ip": "127.0.0.1", "proxy_port": "1080", "isHeadless": "true", "webdriver": "C:\\Users\\<USER>\\houniao\\Driver\\100\\chromedriver.exe", //根据当前打开环境的内核返回对应内核 webdriver 驱动路径 "status": 0 }], "total": 1 } }</code></pre> </div></div><div class="ant-tabs-tabpane ant-tabs-tabpane-inactive"><ul><li><p>Path：/api/v1/browser/stop</p></li> <li><p>Method：POST</p></li> <li><p>Content-Type：application/json</p></li> <li><p>接口描述：关闭指定环境。</p></li></ul> <blockquote><p>请求参数</p></blockquote> <div class="table"><table><thead><tr><td>参数名称</td> <td>类型</td> <td>必传</td> <td>样例串/默认值</td> <td>说明</td></tr></thead> <tbody><tr><td>Session_ID</td> <td>string</td> <td>是</td> <td>373808cb37bd63f5f7d92415e736e85f</td> <td>环境ID</td></tr></tbody></table></div> <blockquote><p>请求示例</p></blockquote> <div class="code-view"><pre><code>{ "Session_ID": "373808cb37bd63f5f7d92415e736e85f" }</code></pre> </div> <blockquote><p>执行成功返回</p></blockquote> <div class="code-view"><pre><code>{ "message": "Success", "code": 0, "data": { "action": "StopSession_ID", "status": 0 } }</code></pre> </div></div><div class="ant-tabs-tabpane ant-tabs-tabpane-active"><div></div><ul><li><p>Path：/api/v1/browser/kill</p></li> <li><p>Method：POST</p></li> <li><p>Content-Type：application/json</p></li> <li><p>接口描述：强制关闭指定环境。</p></li></ul> <blockquote><p>请求参数</p></blockquote> <div class="table"><table><thead><tr><td>参数名称</td> <td>类型</td> <td>必传</td> <td>样例串/默认值</td> <td>说明</td></tr></thead> <tbody><tr><td>Session_ID</td> <td>string</td> <td>是</td> <td>373808cb37bd63f5f7d92415e736e85f</td> <td>环境ID</td></tr></tbody></table></div> <blockquote><p>请求示例</p></blockquote> <div class="code-view"><pre><code>{ "Session_ID": "373808cb37bd63f5f7d92415e736e85f" }</code></pre> </div> <blockquote><p>执行成功返回</p></blockquote> <div class="code-view"><pre><code>{ "message": "Successfully terminated", "code": 0, "data": { "action": "StopSession_ID", "status": 0 } }</code></pre> </div> <p><strong>注：在您使用部份浏览器插件扩展后，普通的关闭浏览器会导致浏览器并未真正退出，而是转为在后台运行，因此，您需要使用强制终止环境接口。</strong></p><div></div></div></div><div></div></div> <p><a class="ant-btn ant-btn-primary">使用POSTMAN调试此接口</a></p>
</body>
</html>