<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>/api/plugin</title>
  <style>
/* 基础重置与排版 */
body {
  margin: 0;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.7;
  color: #333;
  background-color: #fff;
  max-width: 960px;
  margin: 0 auto;
  box-sizing: border-box;
}

/* 段落 */
p {
  margin: 1em 0;
}

/* 标题 */
h1, h2, h3, h4, h5, h6 {
  margin: 1.5em 0 0.8em;
  color: #2c3e50;
  font-weight: 600;
  line-height: 1.3;
}

h1 { font-size: 2em; border-bottom: 1px solid #eee; padding-bottom: 0.5em; }
h2 { font-size: 1.6em; }
h3 { font-size: 1.4em; }
h4 { font-size: 1.2em; }

/* 列表 */
ul, ol {
  margin: 1em 0;
  padding-left: 2em;
}

li {
  margin: 0.4em 0;
}

/* 引用块 */
blockquote {
  margin: 1.5em 0;
  padding: 0.8em 1.5em;
  background-color: #f9f9f9;
  border-left: 4px solid #ddd;
  color: #666;
  font-style: italic;
  border-radius: 0 4px 4px 0;
}

/* 代码行内 */
code {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  background-color: #f3f4f6;
  color: #e9602d;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-size: 0.95em;
  white-space: nowrap;
}

/* 代码块 */
pre {
  margin: 1.5em 0;
  padding: 1.2em;
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  overflow-x: auto;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
}

pre code {
  background: none;
  color: inherit;
  padding: 0;
  font-size: inherit;
  white-space: pre;
  display: block;
}

/* 表格 */
table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5em 0;
  font-size: 14px;
  overflow: hidden;
  box-shadow: 0 0 0 1px #e0e0e0;
  border-radius: 6px;
}

th, td {
  padding: 10px 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

th {
  background-color: #f8f9fa;
  color: #2c3e50;
  font-weight: 600;
  white-space: nowrap;
}

tr:nth-child(even) {
  background-color: #f9f9fb;
}

tr:hover {
  background-color: #f0f5ff;
}

/* 链接 */
a {
  color: #1a73e8;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

/* 分隔线 */
hr {
  border: 0;
  height: 1px;
  background: #ddd;
  margin: 2em 0;
}

/* 图片 */
img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 1em 0;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
  </style>
</head>
<body>
  <h2>插件管理</h2> <div class="api-tabs ant-tabs ant-tabs-top ant-tabs-card ant-tabs-no-animation"><div class="ant-tabs-bar ant-tabs-top-bar ant-tabs-card-bar"><div class="ant-tabs-nav-container"><span class="ant-tabs-tab-prev ant-tabs-tab-btn-disabled"><span class="ant-tabs-tab-prev-icon"><i class="anticon anticon-left ant-tabs-tab-prev-icon-target"><svg class=""><path></path></svg></i></span></span><span class="ant-tabs-tab-next ant-tabs-tab-btn-disabled"><span class="ant-tabs-tab-next-icon"><i class="anticon anticon-right ant-tabs-tab-next-icon-target"><svg class=""><path></path></svg></i></span></span><div class="ant-tabs-nav-wrap"><div class="ant-tabs-nav-scroll"><div class="ant-tabs-nav ant-tabs-nav-animated"><div><div class="ant-tabs-tab">1、列出当前帐户下所有已安装的插件(插件 ID，插件名称)</div><div class="ant-tabs-tab">2、查询、列出指定环境中的所有插件(插件 ID，插件名称)</div><div class="ant-tabs-tab">3、安装指定多个插件到指定的环境中</div><div class="ant-tabs-tab-active ant-tabs-tab">4、删除指定环境插件</div></div><div class="ant-tabs-ink-bar ant-tabs-ink-bar-animated"></div></div></div></div></div></div><div></div><div class="ant-tabs-content ant-tabs-content-no-animated ant-tabs-top-content ant-tabs-card-content"><div class="ant-tabs-tabpane ant-tabs-tabpane-inactive"><ul><li><p>Path：/api/v1/plugin/list</p></li> <li><p>Method：POST</p></li> <li><p>Content-Type：application/json</p></li> <li><p>接口描述：列出当前帐户下所有已安装插件。（插件 ID，插件名称）</p></li></ul> <blockquote><p>请求参数</p></blockquote> <div class="table"><table><thead><tr><td>参数名称</td> <td>类型</td> <td>必传</td> <td>样例串/默认值</td> <td>说明</td></tr></thead> <tbody><tr><td></td> <td></td> <td></td> <td></td> <td></td></tr></tbody></table></div> <blockquote><p>执行成功返回</p></blockquote> <div class="code-view"><pre><code>{ "message": "Golbal Installed Plugin List Success", "code": 0, "data": { "listcontainer": [{ "Plugins_Count": "4", "Plugin_list": [{ "Plugin_Name": "AAA", "Plugin_Id": "jjbnhpnlakcdgfnnldamfeinfmahhdlm", "Plugin_Ver": "1.0.0.0" }, { "Plugin_Name": "BBB", "Plugin_Id": "jjbnhpnlakcdgfnnldamfeinfmahhdlm", "Plugin_Ver": "1.0.0.0" }], "status": 0 } } }</code></pre> </div></div><div class="ant-tabs-tabpane ant-tabs-tabpane-inactive"><ul><li><p>Path：/api/v1/session/id_plugin_list</p></li> <li><p>Method：POST</p></li> <li><p>Content-Type：application/json</p></li> <li><p>接口描述：列出指定环境中所有包含的插件。（支持单个或多个环境）</p></li></ul> <blockquote><p>请求参数</p></blockquote> <div class="table"><table><thead><tr><td>参数名称</td> <td>类型</td> <td>必传</td> <td>样例串/默认值</td> <td>说明</td></tr></thead> <tbody><tr><td>Session_ID</td> <td>array</td> <td>是</td> <td>373808cb37bd63f5f7d92415e736e85f</td> <td>指定环境ID查询环境插件集合</td></tr></tbody></table></div> <blockquote><p>请求示例</p></blockquote> <div class="code-view"><pre><code>{ "Session_ID": ["373808cb37bd63f5f7d92415e736e85f","705cc4c139e69b729a2fd277f30e1863"] }</code></pre> </div> <blockquote><p>执行成功返回</p></blockquote> <div class="code-view"><pre><code>{ "message": "Session Plugin List Success", "code": 0, "data": { "listcontainer": [{ "Session_Name": "商用业务环境一", "Session_ID": "373808cb37bd63f5f7d92415e736e85f", "Group_Name": "default", "Plugins_Count": "4", "Plugin_list": [{ "Plugin_Name": "AAA", "Plugin_Id": "jjbnhpnlakcdgfnnldamfeinfmahhdlm", "Plugin_Ver": "1.0.0.0" },{ "Plugin_Name": "BBB", "Plugin_Id": "jjbnhpnlakcdgfnnldamfeinfmahhdlm", "Plugin_Ver": "1.0.0.0" }], "status": 0 } } }</code></pre> </div></div><div class="ant-tabs-tabpane ant-tabs-tabpane-inactive"><ul><li><p>Path：/api/v1/session/plugin_install</p></li> <li><p>Method：POST</p></li> <li><p>Content-Type：application/json</p></li> <li><p>接口描述：安装新插件到指定环境中。（支持单个或多个插件）</p></li></ul> <blockquote><p>请求参数</p></blockquote> <div class="table"><table><thead><tr><td>参数名称</td> <td>类型</td> <td>必传</td> <td>样例串/默认值</td> <td>说明</td></tr></thead> <tbody><tr><td>Session_ID</td> <td>string</td> <td>是</td> <td>373808cb37bd63f5f7d92415e736e85f</td> <td>指定环境ID</td></tr> <tr><td>Plugin_ID</td> <td>array</td> <td>是</td> <td>ncennffkjdiamlpmcbajkmaiiiddgioo</td> <td>指定安装插件 ID（支持多个）</td></tr> <tr><td>Plugin_ver</td> <td>string</td> <td>否</td> <td>1.0.0</td> <td>插件版本号，可选</td></tr></tbody></table></div> <p>Plugin_ver作为可选项，默认安装最新版本。如果填入错误的版本号，默认安装插件最新版本。</p> <blockquote><p>请求示例</p></blockquote> <div class="code-view"><pre><code>{ "Session_ID": "373808cb37bd63f5f7d92415e736e85f", "Plugin_ID": ["ncennffkjdiamlpmcbajkmaiiiddgioo","f994d8e641ce7006acfa36c901829ff2"] }</code></pre> </div> <blockquote><p>执行成功返回</p></blockquote> <div class="code-view"><pre><code>{ "message": "Plugin Installed Success", "code": 0, "data": { "listcontainer": [{ "Session_Name": "商用业务环境一", "Session_ID": "373808cb37bd63f5f7d92415e736e85f", "Group_Name": "default", "Plugins_Count": "4", "Plugin_list": [{ "Plugin_Name": "AAA", "Plugin_Id": "jjbnhpnlakcdgfnnldamfeinfmahhdlm", "Plugin_Ver": "1.0.0.0" },{ "Plugin_Name": "BBB", "Plugin_Id": "jjbnhpnlakcdgfnnldamfeinfmahhdlm", "Plugin_Ver": "1.0.0.0" }], "status": 0 } } }</code></pre> </div></div><div class="ant-tabs-tabpane ant-tabs-tabpane-active"><div></div><ul><li><p>Path：/api/v1/session/plugin_delete</p></li> <li><p>Method：POST</p></li> <li><p>Content-Type：application/json</p></li> <li><p>接口描述：删除指定环境中已安装的插件。删除成功返回code:0 , message: Delete plugin Success。一次性支持删除环境中所有插件。(每次仅针对单个环境进行处理)</p></li></ul> <blockquote><p>请求参数</p></blockquote> <div class="table"><table><thead><tr><td>参数名称</td> <td>类型</td> <td>必传</td> <td>样例串/默认值</td> <td>说明</td></tr></thead> <tbody><tr><td>Session_ID</td> <td>string</td> <td>是</td> <td>373808cb37bd63f5f7d92415e736e85f</td> <td>指定环境ID</td></tr> <tr><td>Plugin_ID</td> <td>array</td> <td>是</td> <td>ncennffkjdiamlpmcbajkmaiiiddgioo</td> <td>指定删除插件ID（支持多个）</td></tr></tbody></table></div> <blockquote><p>请求示例</p></blockquote> <div class="code-view"><pre><code>{ "Session_ID": "373808cb37bd63f5f7d92415e736e85f", "Plugin_ID": ["ncennffkjdiamlpmcbajkmaiiiddgioo","f994d8e641ce7006acfa36c901829ff2"] }</code></pre> </div> <blockquote><p>执行成功返回</p></blockquote> <div class="code-view"><pre><code>{ "message": "Plugin Delete Finished", "code": 0, "data": { "Delete_Plugin_Success": "ncennffkjdiamlpmcbajkmaiiiddgioo", "Delete_Plugin_Failed": "f994d8e641ce7006acfa36c901829ff2" } }</code></pre> </div><div></div></div></div><div></div></div> <p><a class="ant-btn ant-btn-primary">使用POSTMAN调试此接口</a></p>
</body>
</html>